#!/usr/bin/env pwsh
$basedir=Split-Path $MyInvocation.MyCommand.Definition -Parent

$exe=""
$pathsep=":"
$env_node_path=$env:NODE_PATH
$new_node_path="D:\ssh\learn\project-graph-origin\node_modules\.pnpm\uuid@11.1.0\node_modules\uuid\dist\esm\bin\node_modules;D:\ssh\learn\project-graph-origin\node_modules\.pnpm\uuid@11.1.0\node_modules\uuid\dist\esm\node_modules;D:\ssh\learn\project-graph-origin\node_modules\.pnpm\uuid@11.1.0\node_modules\uuid\dist\node_modules;D:\ssh\learn\project-graph-origin\node_modules\.pnpm\uuid@11.1.0\node_modules\uuid\node_modules;D:\ssh\learn\project-graph-origin\node_modules\.pnpm\uuid@11.1.0\node_modules;D:\ssh\learn\project-graph-origin\node_modules\.pnpm\node_modules"
if ($PSVersionTable.PSVersion -lt "6.0" -or $IsWindows) {
  # Fix case when both the Windows and Linux builds of Node
  # are installed in the same directory
  $exe=".exe"
  $pathsep=";"
} else {
  $new_node_path="/mnt/d/ssh/learn/project-graph-origin/node_modules/.pnpm/uuid@11.1.0/node_modules/uuid/dist/esm/bin/node_modules:/mnt/d/ssh/learn/project-graph-origin/node_modules/.pnpm/uuid@11.1.0/node_modules/uuid/dist/esm/node_modules:/mnt/d/ssh/learn/project-graph-origin/node_modules/.pnpm/uuid@11.1.0/node_modules/uuid/dist/node_modules:/mnt/d/ssh/learn/project-graph-origin/node_modules/.pnpm/uuid@11.1.0/node_modules/uuid/node_modules:/mnt/d/ssh/learn/project-graph-origin/node_modules/.pnpm/uuid@11.1.0/node_modules:/mnt/d/ssh/learn/project-graph-origin/node_modules/.pnpm/node_modules"
}
if ([string]::IsNullOrEmpty($env_node_path)) {
  $env:NODE_PATH=$new_node_path
} else {
  $env:NODE_PATH="$new_node_path$pathsep$env_node_path"
}

$ret=0
if (Test-Path "$basedir/node$exe") {
  # Support pipeline input
  if ($MyInvocation.ExpectingInput) {
    $input | & "$basedir/node$exe"  "$basedir/../uuid/dist/esm/bin/uuid" $args
  } else {
    & "$basedir/node$exe"  "$basedir/../uuid/dist/esm/bin/uuid" $args
  }
  $ret=$LASTEXITCODE
} else {
  # Support pipeline input
  if ($MyInvocation.ExpectingInput) {
    $input | & "node$exe"  "$basedir/../uuid/dist/esm/bin/uuid" $args
  } else {
    & "node$exe"  "$basedir/../uuid/dist/esm/bin/uuid" $args
  }
  $ret=$LASTEXITCODE
}
$env:NODE_PATH=$env_node_path
exit $ret
