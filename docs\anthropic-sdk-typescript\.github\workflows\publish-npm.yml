# This workflow publishes the package to NPM.
# You can run this workflow manually by navigating to https://www.github.com/anthropics/anthropic-sdk-typescript/actions/workflows/publish-npm.yml
name: Publish NPM
on:
  workflow_dispatch:
    inputs:
      path:
        description: The path to run the release in, e.g. '.' or 'packages/vertex-sdk'
        required: true

jobs:
  publish:
    name: publish
    runs-on: ubuntu-latest
    environment: production-release

    steps:
      - uses: actions/checkout@v4

      - name: Set up Node
        uses: actions/setup-node@v4
        with:
          node-version: '20'

      - name: Install dependencies
        run: |
          yarn install

      - name: Update internal symlinks in third party packages
        run: ./bin/replace-internal-symlinks

      - name: Publish to NPM
        run: |
          yarn tsn scripts/publish-packages.ts '{ "paths_released": "[\"${{ github.event.inputs.path }}\"]" }'
        env:
          NPM_TOKEN: ${{ secrets.ANTHROPIC_NPM_TOKEN || secrets.NPM_TOKEN }}
