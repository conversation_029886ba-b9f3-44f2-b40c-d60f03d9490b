name: Format code

on:
  push:
    branches: [ 'main' ]
  pull_request:
    branches: [ 'main' ]

jobs:
  format:

    runs-on: ubuntu-latest

    steps:
    - uses: actions/checkout@v4
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: 22.x
        cache: 'npm'
    - run: npm ci
    - name: Run format
      run: npx prettier '**/*.ts' '**/*.mjs' '**/*.mjs' '**/*.json' --check
    - name: Run linter
      run: npm run lint
