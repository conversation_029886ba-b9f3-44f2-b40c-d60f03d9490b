import { IAIEngine, ChatMessage, ChatOptions } from "../IAIEngine";
import { universalFetch } from "../../../../../utils/fetch";
import OpenAI from "openai";
import { Settings } from "../../../Settings";
import { AITools } from "../AITools";

export class OpenAIEngine implements IAIEngine {
  private openai = new OpenAI({
    apiKey: "",
    dangerouslyAllowBrowser: true,
    fetch: universalFetch,
  });

  private async updateConfig() {
    this.openai.baseURL = await Settings.get("aiApiBaseUrl");
    this.openai.apiKey = await Settings.get("aiApiKey");
  }

  async *chat(messages: ChatMessage[], options: ChatOptions): AsyncGenerator<string> {
    await this.updateConfig();
    const enableStream = await Settings.get("enableStream");
    const openaiResponseType = await Settings.get("openaiResponseType");

    const requestBody: OpenAI.Chat.Completions.ChatCompletionCreateParams = {
      messages: messages as OpenAI.ChatCompletionMessageParam[],
      model: options.model,
      tools: AITools.tools,
    };

    if (enableStream) {
      const stream = await this.openai.chat.completions.create({
        ...requestBody,
        stream: true,
        stream_options: {
          include_usage: true,
        },
      });

      for await (const chunk of stream) {
        yield chunk.choices[0]?.delta?.content || "";
      }
    } else {
      const completion = await this.openai.chat.completions.create({
        ...requestBody,
        stream: false,
      });

      if (openaiResponseType === "response") {
        yield JSON.stringify(completion);
      } else {
        yield completion.choices[0]?.message?.content || "";
      }
    }
  }

  async getModels(): Promise<string[]> {
    await this.updateConfig();
    const resp = await this.openai.models.list();
    return resp.data.map((it) => it.id.replaceAll("models/", ""));
  }
}
