/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */

// Code generated by the Google Gen AI SDK generator DO NOT EDIT.

import * as common from '../_common.js';
import * as types from '../types.js';

export function getOperationParametersToMldev(
  fromObject: types.GetOperationParameters,
): Record<string, unknown> {
  const toObject: Record<string, unknown> = {};

  const fromOperationName = common.getValueByPath(fromObject, [
    'operationName',
  ]);
  if (fromOperationName != null) {
    common.setValueByPath(
      toObject,
      ['_url', 'operationName'],
      fromOperationName,
    );
  }

  const fromConfig = common.getValueByPath(fromObject, ['config']);
  if (fromConfig != null) {
    common.setValueByPath(toObject, ['config'], fromConfig);
  }

  return toObject;
}

export function getOperationParametersToVertex(
  fromObject: types.GetOperationParameters,
): Record<string, unknown> {
  const toObject: Record<string, unknown> = {};

  const fromOperationName = common.getValueByPath(fromObject, [
    'operationName',
  ]);
  if (fromOperationName != null) {
    common.setValueByPath(
      toObject,
      ['_url', 'operationName'],
      fromOperationName,
    );
  }

  const fromConfig = common.getValueByPath(fromObject, ['config']);
  if (fromConfig != null) {
    common.setValueByPath(toObject, ['config'], fromConfig);
  }

  return toObject;
}

export function fetchPredictOperationParametersToVertex(
  fromObject: types.FetchPredictOperationParameters,
): Record<string, unknown> {
  const toObject: Record<string, unknown> = {};

  const fromOperationName = common.getValueByPath(fromObject, [
    'operationName',
  ]);
  if (fromOperationName != null) {
    common.setValueByPath(toObject, ['operationName'], fromOperationName);
  }

  const fromResourceName = common.getValueByPath(fromObject, ['resourceName']);
  if (fromResourceName != null) {
    common.setValueByPath(toObject, ['_url', 'resourceName'], fromResourceName);
  }

  const fromConfig = common.getValueByPath(fromObject, ['config']);
  if (fromConfig != null) {
    common.setValueByPath(toObject, ['config'], fromConfig);
  }

  return toObject;
}
