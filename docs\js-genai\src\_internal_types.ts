/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */

// Code generated by the Google Gen AI SDK generator DO NOT EDIT.

import * as types from './types.js';

/** Private class that represents a Reference image that is sent to API. */
export declare interface ReferenceImageAPIInternal {
  /** The reference image for the editing operation. */
  referenceImage?: types.Image;
  /** The id of the reference image. */
  referenceId?: number;
  /** The type of the reference image. Only set by the SDK. */
  referenceType?: string;
  /** Configuration for the mask reference image. */
  maskImageConfig?: types.MaskReferenceConfig;
  /** Configuration for the control reference image. */
  controlImageConfig?: types.ControlReferenceConfig;
  /** Configuration for the style reference image. */
  styleImageConfig?: types.StyleReferenceConfig;
  /** Configuration for the subject reference image. */
  subjectImageConfig?: types.SubjectReferenceConfig;
}

/** Parameters for the request to edit an image. */
export declare interface EditImageParametersInternal {
  /** The model to use. */
  model: string;
  /** A text description of the edit to apply to the image. */
  prompt: string;
  /** The reference images for Imagen 3 editing. */
  referenceImages: ReferenceImageAPIInternal[];
  /** Configuration for editing. */
  config?: types.EditImageConfig;
}

/** Internal API config for UpscaleImage.

  These fields require default values sent to the API which are not intended
  to be modifiable or exposed to users in the SDK method.
   */
export declare interface UpscaleImageAPIConfigInternal {
  /** Used to override HTTP request options. */
  httpOptions?: types.HttpOptions;
  /** Abort signal which can be used to cancel the request.

  NOTE: AbortSignal is a client-only operation. Using it to cancel an
  operation will not cancel the request in the service. You will still
  be charged usage for any applicable operations.
       */
  abortSignal?: AbortSignal;
  /** Whether to include a reason for filtered-out images in the
      response. */
  includeRaiReason?: boolean;
  /** The image format that the output should be saved as. */
  outputMimeType?: string;
  /** The level of compression if the ``output_mime_type`` is
      ``image/jpeg``. */
  outputCompressionQuality?: number;
  /** Whether to add an image enhancing step before upscaling.
      It is expected to suppress the noise and JPEG compression artifacts
      from the input image. */
  enhanceInputImage?: boolean;
  /** With a higher image preservation factor, the original image
      pixels are more respected. With a lower image preservation factor, the
      output image will have be more different from the input image, but
      with finer details and less noise. */
  imagePreservationFactor?: number;
  numberOfImages?: number;
  mode?: string;
}

/** API parameters for UpscaleImage. */
export declare interface UpscaleImageAPIParametersInternal {
  /** The model to use. */
  model: string;
  /** The input image to upscale. */
  image: types.Image;
  /** The factor to upscale the image (x2 or x4). */
  upscaleFactor: string;
  /** Configuration for upscaling. */
  config?: UpscaleImageAPIConfigInternal;
}
