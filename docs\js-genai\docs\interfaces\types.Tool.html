<!DOCTYPE html><html class="default" lang="en" data-base=".."><head><meta charset="utf-8"/><meta http-equiv="x-ua-compatible" content="IE=edge"/><title>Tool | @google/genai</title><meta name="description" content="Documentation for @google/genai"/><meta name="viewport" content="width=device-width, initial-scale=1"/><link rel="stylesheet" href="../assets/style.css"/><link rel="stylesheet" href="../assets/highlight.css"/><script defer src="../assets/main.js"></script><script async src="../assets/icons.js" id="tsd-icons-script"></script><script async src="../assets/search.js" id="tsd-search-script"></script><script async src="../assets/navigation.js" id="tsd-nav-script"></script></head><body><script>document.documentElement.dataset.theme = localStorage.getItem("tsd-theme") || "os";document.body.style.display="none";setTimeout(() => app?app.showPage():document.body.style.removeProperty("display"),500)</script><header class="tsd-page-toolbar"><div class="tsd-toolbar-contents container"><div class="table-cell" id="tsd-search"><div class="field"><label for="tsd-search-field" class="tsd-widget tsd-toolbar-icon search no-caption"><svg width="16" height="16" viewBox="0 0 16 16" fill="none" aria-hidden="true"><use href="../assets/icons.svg#icon-search"></use></svg></label><input type="text" id="tsd-search-field" aria-label="Search"/></div><div class="field"><div id="tsd-toolbar-links"></div></div><ul class="results"><li class="state loading">Preparing search index...</li><li class="state failure">The search index is not available</li></ul><a href="../index.html" class="title">@google/genai</a></div><div class="table-cell" id="tsd-widgets"><a href="#" class="tsd-widget tsd-toolbar-icon menu no-caption" data-toggle="menu" aria-label="Menu"><svg width="16" height="16" viewBox="0 0 16 16" fill="none" aria-hidden="true"><use href="../assets/icons.svg#icon-menu"></use></svg></a></div></div></header><div class="container container-main"><div class="col-content"><div class="tsd-page-title"><ul class="tsd-breadcrumb"><li><a href="../modules.html">@google/genai</a></li><li><a href="../modules/types.html">types</a></li><li><a href="types.Tool.html">Tool</a></li></ul><h1>Interface Tool</h1></div><section class="tsd-panel tsd-comment"><div class="tsd-comment tsd-typography"><p>Tool details of a tool that the model may use to generate a response.</p>
</div><div class="tsd-comment tsd-typography"></div></section><div class="tsd-signature"><span class="tsd-signature-keyword">interface</span> <span class="tsd-kind-interface">Tool</span> <span class="tsd-signature-symbol">{</span><br/>    <a class="tsd-kind-property" href="types.Tool.html#codeexecution">codeExecution</a><span class="tsd-signature-symbol">?:</span> <a href="types.ToolCodeExecution.html" class="tsd-signature-type tsd-kind-interface">ToolCodeExecution</a><span class="tsd-signature-symbol">;</span><br/>    <a class="tsd-kind-property" href="types.Tool.html#computeruse">computerUse</a><span class="tsd-signature-symbol">?:</span> <a href="types.ToolComputerUse.html" class="tsd-signature-type tsd-kind-interface">ToolComputerUse</a><span class="tsd-signature-symbol">;</span><br/>    <a class="tsd-kind-property" href="types.Tool.html#enterprisewebsearch">enterpriseWebSearch</a><span class="tsd-signature-symbol">?:</span> <a href="types.EnterpriseWebSearch.html" class="tsd-signature-type tsd-kind-interface">EnterpriseWebSearch</a><span class="tsd-signature-symbol">;</span><br/>    <a class="tsd-kind-property" href="types.Tool.html#functiondeclarations">functionDeclarations</a><span class="tsd-signature-symbol">?:</span> <a href="types.FunctionDeclaration.html" class="tsd-signature-type tsd-kind-interface">FunctionDeclaration</a><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">;</span><br/>    <a class="tsd-kind-property" href="types.Tool.html#googlemaps">googleMaps</a><span class="tsd-signature-symbol">?:</span> <a href="types.GoogleMaps.html" class="tsd-signature-type tsd-kind-interface">GoogleMaps</a><span class="tsd-signature-symbol">;</span><br/>    <a class="tsd-kind-property" href="types.Tool.html#googlesearch">googleSearch</a><span class="tsd-signature-symbol">?:</span> <a href="types.GoogleSearch.html" class="tsd-signature-type tsd-kind-interface">GoogleSearch</a><span class="tsd-signature-symbol">;</span><br/>    <a class="tsd-kind-property" href="types.Tool.html#googlesearchretrieval">googleSearchRetrieval</a><span class="tsd-signature-symbol">?:</span> <a href="types.GoogleSearchRetrieval.html" class="tsd-signature-type tsd-kind-interface">GoogleSearchRetrieval</a><span class="tsd-signature-symbol">;</span><br/>    <a class="tsd-kind-property" href="types.Tool.html#retrieval">retrieval</a><span class="tsd-signature-symbol">?:</span> <a href="types.Retrieval.html" class="tsd-signature-type tsd-kind-interface">Retrieval</a><span class="tsd-signature-symbol">;</span><br/>    <a class="tsd-kind-property" href="types.Tool.html#urlcontext">urlContext</a><span class="tsd-signature-symbol">?:</span> <a href="types.UrlContext.html" class="tsd-signature-type tsd-kind-interface">UrlContext</a><span class="tsd-signature-symbol">;</span><br/><span class="tsd-signature-symbol">}</span></div><aside class="tsd-sources"><ul><li>Defined in src/types.ts:1530</li></ul></aside><section class="tsd-panel-group tsd-index-group"><section class="tsd-panel tsd-index-panel"><details class="tsd-index-content tsd-accordion" open><summary class="tsd-accordion-summary tsd-index-summary"><h5 class="tsd-index-heading uppercase" role="button" aria-expanded="false" tabIndex="0"><svg width="16" height="16" viewBox="0 0 16 16" fill="none" aria-hidden="true"><use href="../assets/icons.svg#icon-chevronSmall"></use></svg> Index</h5></summary><div class="tsd-accordion-details"><section class="tsd-index-section"><h3 class="tsd-index-heading">Properties</h3><div class="tsd-index-list"><a href="types.Tool.html#codeexecution" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>code<wbr/>Execution?</span></a>
<a href="types.Tool.html#computeruse" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>computer<wbr/>Use?</span></a>
<a href="types.Tool.html#enterprisewebsearch" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>enterprise<wbr/>Web<wbr/>Search?</span></a>
<a href="types.Tool.html#functiondeclarations" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>function<wbr/>Declarations?</span></a>
<a href="types.Tool.html#googlemaps" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>google<wbr/>Maps?</span></a>
<a href="types.Tool.html#googlesearch" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>google<wbr/>Search?</span></a>
<a href="types.Tool.html#googlesearchretrieval" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>google<wbr/>Search<wbr/>Retrieval?</span></a>
<a href="types.Tool.html#retrieval" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>retrieval?</span></a>
<a href="types.Tool.html#urlcontext" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>url<wbr/>Context?</span></a>
</div></section></div></details></section></section><details class="tsd-panel-group tsd-member-group tsd-accordion" open><summary class="tsd-accordion-summary" data-key="section-Properties"><h2><svg width="20" height="20" viewBox="0 0 24 24" fill="none" aria-hidden="true"><use href="../assets/icons.svg#icon-chevronDown"></use></svg> Properties</h2></summary><section><section class="tsd-panel tsd-member"><a id="codeexecution" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><code class="tsd-tag">Optional</code><span>code<wbr/>Execution</span><a href="#codeexecution" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-property">codeExecution</span><span class="tsd-signature-symbol">?:</span> <a href="types.ToolCodeExecution.html" class="tsd-signature-type tsd-kind-interface">ToolCodeExecution</a></div><div class="tsd-comment tsd-typography"><p>Optional. CodeExecution tool type. Enables the model to execute code as part of generation.</p>
</div><div class="tsd-comment tsd-typography"></div><aside class="tsd-sources"><ul><li>Defined in src/types.ts:1549</li></ul></aside></section><section class="tsd-panel tsd-member"><a id="computeruse" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><code class="tsd-tag">Optional</code><span>computer<wbr/>Use</span><a href="#computeruse" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-property">computerUse</span><span class="tsd-signature-symbol">?:</span> <a href="types.ToolComputerUse.html" class="tsd-signature-type tsd-kind-interface">ToolComputerUse</a></div><div class="tsd-comment tsd-typography"><p>Optional. Tool to support the model interacting directly with the computer. If enabled, it automatically populates computer-use specific Function Declarations.</p>
</div><div class="tsd-comment tsd-typography"></div><aside class="tsd-sources"><ul><li>Defined in src/types.ts:1551</li></ul></aside></section><section class="tsd-panel tsd-member"><a id="enterprisewebsearch" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><code class="tsd-tag">Optional</code><span>enterprise<wbr/>Web<wbr/>Search</span><a href="#enterprisewebsearch" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-property">enterpriseWebSearch</span><span class="tsd-signature-symbol">?:</span> <a href="types.EnterpriseWebSearch.html" class="tsd-signature-type tsd-kind-interface">EnterpriseWebSearch</a></div><div class="tsd-comment tsd-typography"><p>Optional. Enterprise web search tool type. Specialized retrieval
tool that is powered by Vertex AI Search and Sec4 compliance.</p>
</div><div class="tsd-comment tsd-typography"></div><aside class="tsd-sources"><ul><li>Defined in src/types.ts:1542</li></ul></aside></section><section class="tsd-panel tsd-member"><a id="functiondeclarations" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><code class="tsd-tag">Optional</code><span>function<wbr/>Declarations</span><a href="#functiondeclarations" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-property">functionDeclarations</span><span class="tsd-signature-symbol">?:</span> <a href="types.FunctionDeclaration.html" class="tsd-signature-type tsd-kind-interface">FunctionDeclaration</a><span class="tsd-signature-symbol">[]</span></div><div class="tsd-comment tsd-typography"><p>List of function declarations that the tool supports.</p>
</div><div class="tsd-comment tsd-typography"></div><aside class="tsd-sources"><ul><li>Defined in src/types.ts:1532</li></ul></aside></section><section class="tsd-panel tsd-member"><a id="googlemaps" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><code class="tsd-tag">Optional</code><span>google<wbr/>Maps</span><a href="#googlemaps" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-property">googleMaps</span><span class="tsd-signature-symbol">?:</span> <a href="types.GoogleMaps.html" class="tsd-signature-type tsd-kind-interface">GoogleMaps</a></div><div class="tsd-comment tsd-typography"><p>Optional. Google Maps tool type. Specialized retrieval tool
that is powered by Google Maps.</p>
</div><div class="tsd-comment tsd-typography"></div><aside class="tsd-sources"><ul><li>Defined in src/types.ts:1545</li></ul></aside></section><section class="tsd-panel tsd-member"><a id="googlesearch" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><code class="tsd-tag">Optional</code><span>google<wbr/>Search</span><a href="#googlesearch" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-property">googleSearch</span><span class="tsd-signature-symbol">?:</span> <a href="types.GoogleSearch.html" class="tsd-signature-type tsd-kind-interface">GoogleSearch</a></div><div class="tsd-comment tsd-typography"><p>Optional. Google Search tool type. Specialized retrieval tool
that is powered by Google Search.</p>
</div><div class="tsd-comment tsd-typography"></div><aside class="tsd-sources"><ul><li>Defined in src/types.ts:1537</li></ul></aside></section><section class="tsd-panel tsd-member"><a id="googlesearchretrieval" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><code class="tsd-tag">Optional</code><span>google<wbr/>Search<wbr/>Retrieval</span><a href="#googlesearchretrieval" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-property">googleSearchRetrieval</span><span class="tsd-signature-symbol">?:</span> <a href="types.GoogleSearchRetrieval.html" class="tsd-signature-type tsd-kind-interface">GoogleSearchRetrieval</a></div><div class="tsd-comment tsd-typography"><p>Optional. GoogleSearchRetrieval tool type. Specialized retrieval tool that is powered by Google search.</p>
</div><div class="tsd-comment tsd-typography"></div><aside class="tsd-sources"><ul><li>Defined in src/types.ts:1539</li></ul></aside></section><section class="tsd-panel tsd-member"><a id="retrieval" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><code class="tsd-tag">Optional</code><span>retrieval</span><a href="#retrieval" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-property">retrieval</span><span class="tsd-signature-symbol">?:</span> <a href="types.Retrieval.html" class="tsd-signature-type tsd-kind-interface">Retrieval</a></div><div class="tsd-comment tsd-typography"><p>Optional. Retrieval tool type. System will always execute the provided retrieval tool(s) to get external knowledge to answer the prompt. Retrieval results are presented to the model for generation.</p>
</div><div class="tsd-comment tsd-typography"></div><aside class="tsd-sources"><ul><li>Defined in src/types.ts:1534</li></ul></aside></section><section class="tsd-panel tsd-member"><a id="urlcontext" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><code class="tsd-tag">Optional</code><span>url<wbr/>Context</span><a href="#urlcontext" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-property">urlContext</span><span class="tsd-signature-symbol">?:</span> <a href="types.UrlContext.html" class="tsd-signature-type tsd-kind-interface">UrlContext</a></div><div class="tsd-comment tsd-typography"><p>Optional. Tool to support URL context retrieval.</p>
</div><div class="tsd-comment tsd-typography"></div><aside class="tsd-sources"><ul><li>Defined in src/types.ts:1547</li></ul></aside></section></section></details></div><div class="col-sidebar"><div class="page-menu"><div class="tsd-navigation settings"><details class="tsd-accordion"><summary class="tsd-accordion-summary"><h3><svg width="20" height="20" viewBox="0 0 24 24" fill="none" aria-hidden="true"><use href="../assets/icons.svg#icon-chevronDown"></use></svg>Settings</h3></summary><div class="tsd-accordion-details"><div class="tsd-theme-toggle"><label class="settings-label" for="tsd-theme">Theme</label><select id="tsd-theme"><option value="os">OS</option><option value="light">Light</option><option value="dark">Dark</option></select></div></div></details></div><details open class="tsd-accordion tsd-page-navigation"><summary class="tsd-accordion-summary"><h3><svg width="20" height="20" viewBox="0 0 24 24" fill="none" aria-hidden="true"><use href="../assets/icons.svg#icon-chevronDown"></use></svg>On This Page</h3></summary><div class="tsd-accordion-details"><details open class="tsd-accordion tsd-page-navigation-section"><summary class="tsd-accordion-summary" data-key="section-Properties"><svg width="20" height="20" viewBox="0 0 24 24" fill="none" aria-hidden="true"><use href="../assets/icons.svg#icon-chevronDown"></use></svg>Properties</summary><div><a href="#codeexecution" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>code<wbr/>Execution</span></a><a href="#computeruse" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>computer<wbr/>Use</span></a><a href="#enterprisewebsearch" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>enterprise<wbr/>Web<wbr/>Search</span></a><a href="#functiondeclarations" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>function<wbr/>Declarations</span></a><a href="#googlemaps" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>google<wbr/>Maps</span></a><a href="#googlesearch" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>google<wbr/>Search</span></a><a href="#googlesearchretrieval" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>google<wbr/>Search<wbr/>Retrieval</span></a><a href="#retrieval" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>retrieval</span></a><a href="#urlcontext" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>url<wbr/>Context</span></a></div></details></div></details></div><div class="site-menu"><nav class="tsd-navigation"><a href="../modules.html">@google/genai</a><ul class="tsd-small-nested-navigation" id="tsd-nav-container"><li>Loading...</li></ul></nav></div></div></div><footer><p class="tsd-generator">Generated using <a href="https://typedoc.org/" target="_blank">TypeDoc</a></p></footer><div class="overlay"></div></body></html>
