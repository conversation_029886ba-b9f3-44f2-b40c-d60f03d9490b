<!DOCTYPE html><html class="default" lang="en" data-base=".."><head><meta charset="utf-8"/><meta http-equiv="x-ua-compatible" content="IE=edge"/><title>Candidate | @google/genai</title><meta name="description" content="Documentation for @google/genai"/><meta name="viewport" content="width=device-width, initial-scale=1"/><link rel="stylesheet" href="../assets/style.css"/><link rel="stylesheet" href="../assets/highlight.css"/><script defer src="../assets/main.js"></script><script async src="../assets/icons.js" id="tsd-icons-script"></script><script async src="../assets/search.js" id="tsd-search-script"></script><script async src="../assets/navigation.js" id="tsd-nav-script"></script></head><body><script>document.documentElement.dataset.theme = localStorage.getItem("tsd-theme") || "os";document.body.style.display="none";setTimeout(() => app?app.showPage():document.body.style.removeProperty("display"),500)</script><header class="tsd-page-toolbar"><div class="tsd-toolbar-contents container"><div class="table-cell" id="tsd-search"><div class="field"><label for="tsd-search-field" class="tsd-widget tsd-toolbar-icon search no-caption"><svg width="16" height="16" viewBox="0 0 16 16" fill="none" aria-hidden="true"><use href="../assets/icons.svg#icon-search"></use></svg></label><input type="text" id="tsd-search-field" aria-label="Search"/></div><div class="field"><div id="tsd-toolbar-links"></div></div><ul class="results"><li class="state loading">Preparing search index...</li><li class="state failure">The search index is not available</li></ul><a href="../index.html" class="title">@google/genai</a></div><div class="table-cell" id="tsd-widgets"><a href="#" class="tsd-widget tsd-toolbar-icon menu no-caption" data-toggle="menu" aria-label="Menu"><svg width="16" height="16" viewBox="0 0 16 16" fill="none" aria-hidden="true"><use href="../assets/icons.svg#icon-menu"></use></svg></a></div></div></header><div class="container container-main"><div class="col-content"><div class="tsd-page-title"><ul class="tsd-breadcrumb"><li><a href="../modules.html">@google/genai</a></li><li><a href="../modules/types.html">types</a></li><li><a href="types.Candidate.html">Candidate</a></li></ul><h1>Interface Candidate</h1></div><section class="tsd-panel tsd-comment"><div class="tsd-comment tsd-typography"><p>A response candidate generated from the model.</p>
</div><div class="tsd-comment tsd-typography"></div></section><div class="tsd-signature"><span class="tsd-signature-keyword">interface</span> <span class="tsd-kind-interface">Candidate</span> <span class="tsd-signature-symbol">{</span><br/>    <a class="tsd-kind-property" href="types.Candidate.html#avglogprobs">avgLogprobs</a><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">;</span><br/>    <a class="tsd-kind-property" href="types.Candidate.html#citationmetadata">citationMetadata</a><span class="tsd-signature-symbol">?:</span> <a href="types.CitationMetadata.html" class="tsd-signature-type tsd-kind-interface">CitationMetadata</a><span class="tsd-signature-symbol">;</span><br/>    <a class="tsd-kind-property" href="types.Candidate.html#content">content</a><span class="tsd-signature-symbol">?:</span> <a href="types.Content.html" class="tsd-signature-type tsd-kind-interface">Content</a><span class="tsd-signature-symbol">;</span><br/>    <a class="tsd-kind-property" href="types.Candidate.html#finishmessage">finishMessage</a><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">;</span><br/>    <a class="tsd-kind-property" href="types.Candidate.html#finishreason">finishReason</a><span class="tsd-signature-symbol">?:</span> <a href="../enums/types.FinishReason.html" class="tsd-signature-type tsd-kind-enum">FinishReason</a><span class="tsd-signature-symbol">;</span><br/>    <a class="tsd-kind-property" href="types.Candidate.html#groundingmetadata">groundingMetadata</a><span class="tsd-signature-symbol">?:</span> <a href="types.GroundingMetadata.html" class="tsd-signature-type tsd-kind-interface">GroundingMetadata</a><span class="tsd-signature-symbol">;</span><br/>    <a class="tsd-kind-property" href="types.Candidate.html#index">index</a><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">;</span><br/>    <a class="tsd-kind-property" href="types.Candidate.html#logprobsresult">logprobsResult</a><span class="tsd-signature-symbol">?:</span> <a href="types.LogprobsResult.html" class="tsd-signature-type tsd-kind-interface">LogprobsResult</a><span class="tsd-signature-symbol">;</span><br/>    <a class="tsd-kind-property" href="types.Candidate.html#safetyratings">safetyRatings</a><span class="tsd-signature-symbol">?:</span> <a href="types.SafetyRating.html" class="tsd-signature-type tsd-kind-interface">SafetyRating</a><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">;</span><br/>    <a class="tsd-kind-property" href="types.Candidate.html#tokencount">tokenCount</a><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">;</span><br/>    <a class="tsd-kind-property" href="types.Candidate.html#urlcontextmetadata">urlContextMetadata</a><span class="tsd-signature-symbol">?:</span> <a href="types.UrlContextMetadata.html" class="tsd-signature-type tsd-kind-interface">UrlContextMetadata</a><span class="tsd-signature-symbol">;</span><br/><span class="tsd-signature-symbol">}</span></div><aside class="tsd-sources"><ul><li>Defined in src/types.ts:2094</li></ul></aside><section class="tsd-panel-group tsd-index-group"><section class="tsd-panel tsd-index-panel"><details class="tsd-index-content tsd-accordion" open><summary class="tsd-accordion-summary tsd-index-summary"><h5 class="tsd-index-heading uppercase" role="button" aria-expanded="false" tabIndex="0"><svg width="16" height="16" viewBox="0 0 16 16" fill="none" aria-hidden="true"><use href="../assets/icons.svg#icon-chevronSmall"></use></svg> Index</h5></summary><div class="tsd-accordion-details"><section class="tsd-index-section"><h3 class="tsd-index-heading">Properties</h3><div class="tsd-index-list"><a href="types.Candidate.html#avglogprobs" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>avg<wbr/>Logprobs?</span></a>
<a href="types.Candidate.html#citationmetadata" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>citation<wbr/>Metadata?</span></a>
<a href="types.Candidate.html#content" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>content?</span></a>
<a href="types.Candidate.html#finishmessage" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>finish<wbr/>Message?</span></a>
<a href="types.Candidate.html#finishreason" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>finish<wbr/>Reason?</span></a>
<a href="types.Candidate.html#groundingmetadata" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>grounding<wbr/>Metadata?</span></a>
<a href="types.Candidate.html#index" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>index?</span></a>
<a href="types.Candidate.html#logprobsresult" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>logprobs<wbr/>Result?</span></a>
<a href="types.Candidate.html#safetyratings" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>safety<wbr/>Ratings?</span></a>
<a href="types.Candidate.html#tokencount" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>token<wbr/>Count?</span></a>
<a href="types.Candidate.html#urlcontextmetadata" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>url<wbr/>Context<wbr/>Metadata?</span></a>
</div></section></div></details></section></section><details class="tsd-panel-group tsd-member-group tsd-accordion" open><summary class="tsd-accordion-summary" data-key="section-Properties"><h2><svg width="20" height="20" viewBox="0 0 24 24" fill="none" aria-hidden="true"><use href="../assets/icons.svg#icon-chevronDown"></use></svg> Properties</h2></summary><section><section class="tsd-panel tsd-member"><a id="avglogprobs" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><code class="tsd-tag">Optional</code><span>avg<wbr/>Logprobs</span><a href="#avglogprobs" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-property">avgLogprobs</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">number</span></div><div class="tsd-comment tsd-typography"><p>Output only. Average log probability score of the candidate.</p>
</div><div class="tsd-comment tsd-typography"></div><aside class="tsd-sources"><ul><li>Defined in src/types.ts:2114</li></ul></aside></section><section class="tsd-panel tsd-member"><a id="citationmetadata" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><code class="tsd-tag">Optional</code><span>citation<wbr/>Metadata</span><a href="#citationmetadata" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-property">citationMetadata</span><span class="tsd-signature-symbol">?:</span> <a href="types.CitationMetadata.html" class="tsd-signature-type tsd-kind-interface">CitationMetadata</a></div><div class="tsd-comment tsd-typography"><p>Source attribution of the generated content.</p>
</div><div class="tsd-comment tsd-typography"></div><aside class="tsd-sources"><ul><li>Defined in src/types.ts:2100</li></ul></aside></section><section class="tsd-panel tsd-member"><a id="content" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><code class="tsd-tag">Optional</code><span>content</span><a href="#content" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-property">content</span><span class="tsd-signature-symbol">?:</span> <a href="types.Content.html" class="tsd-signature-type tsd-kind-interface">Content</a></div><div class="tsd-comment tsd-typography"><p>Contains the multi-part content of the response.</p>
</div><div class="tsd-comment tsd-typography"></div><aside class="tsd-sources"><ul><li>Defined in src/types.ts:2097</li></ul></aside></section><section class="tsd-panel tsd-member"><a id="finishmessage" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><code class="tsd-tag">Optional</code><span>finish<wbr/>Message</span><a href="#finishmessage" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-property">finishMessage</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">string</span></div><div class="tsd-comment tsd-typography"><p>Describes the reason the model stopped generating tokens.</p>
</div><div class="tsd-comment tsd-typography"></div><aside class="tsd-sources"><ul><li>Defined in src/types.ts:2103</li></ul></aside></section><section class="tsd-panel tsd-member"><a id="finishreason" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><code class="tsd-tag">Optional</code><span>finish<wbr/>Reason</span><a href="#finishreason" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-property">finishReason</span><span class="tsd-signature-symbol">?:</span> <a href="../enums/types.FinishReason.html" class="tsd-signature-type tsd-kind-enum">FinishReason</a></div><div class="tsd-comment tsd-typography"><p>The reason why the model stopped generating tokens.
If empty, the model has not stopped generating the tokens.</p>
</div><div class="tsd-comment tsd-typography"></div><aside class="tsd-sources"><ul><li>Defined in src/types.ts:2110</li></ul></aside></section><section class="tsd-panel tsd-member"><a id="groundingmetadata" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><code class="tsd-tag">Optional</code><span>grounding<wbr/>Metadata</span><a href="#groundingmetadata" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-property">groundingMetadata</span><span class="tsd-signature-symbol">?:</span> <a href="types.GroundingMetadata.html" class="tsd-signature-type tsd-kind-interface">GroundingMetadata</a></div><div class="tsd-comment tsd-typography"><p>Output only. Metadata specifies sources used to ground generated content.</p>
</div><div class="tsd-comment tsd-typography"></div><aside class="tsd-sources"><ul><li>Defined in src/types.ts:2116</li></ul></aside></section><section class="tsd-panel tsd-member"><a id="index" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><code class="tsd-tag">Optional</code><span>index</span><a href="#index" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-property">index</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">number</span></div><div class="tsd-comment tsd-typography"><p>Output only. Index of the candidate.</p>
</div><div class="tsd-comment tsd-typography"></div><aside class="tsd-sources"><ul><li>Defined in src/types.ts:2118</li></ul></aside></section><section class="tsd-panel tsd-member"><a id="logprobsresult" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><code class="tsd-tag">Optional</code><span>logprobs<wbr/>Result</span><a href="#logprobsresult" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-property">logprobsResult</span><span class="tsd-signature-symbol">?:</span> <a href="types.LogprobsResult.html" class="tsd-signature-type tsd-kind-interface">LogprobsResult</a></div><div class="tsd-comment tsd-typography"><p>Output only. Log-likelihood scores for the response tokens and top tokens</p>
</div><div class="tsd-comment tsd-typography"></div><aside class="tsd-sources"><ul><li>Defined in src/types.ts:2120</li></ul></aside></section><section class="tsd-panel tsd-member"><a id="safetyratings" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><code class="tsd-tag">Optional</code><span>safety<wbr/>Ratings</span><a href="#safetyratings" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-property">safetyRatings</span><span class="tsd-signature-symbol">?:</span> <a href="types.SafetyRating.html" class="tsd-signature-type tsd-kind-interface">SafetyRating</a><span class="tsd-signature-symbol">[]</span></div><div class="tsd-comment tsd-typography"><p>Output only. List of ratings for the safety of a response candidate. There is at most one rating per category.</p>
</div><div class="tsd-comment tsd-typography"></div><aside class="tsd-sources"><ul><li>Defined in src/types.ts:2122</li></ul></aside></section><section class="tsd-panel tsd-member"><a id="tokencount" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><code class="tsd-tag">Optional</code><span>token<wbr/>Count</span><a href="#tokencount" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-property">tokenCount</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">number</span></div><div class="tsd-comment tsd-typography"><p>Number of tokens for this candidate.</p>
</div><div class="tsd-comment tsd-typography"></div><aside class="tsd-sources"><ul><li>Defined in src/types.ts:2106</li></ul></aside></section><section class="tsd-panel tsd-member"><a id="urlcontextmetadata" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><code class="tsd-tag">Optional</code><span>url<wbr/>Context<wbr/>Metadata</span><a href="#urlcontextmetadata" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-property">urlContextMetadata</span><span class="tsd-signature-symbol">?:</span> <a href="types.UrlContextMetadata.html" class="tsd-signature-type tsd-kind-interface">UrlContextMetadata</a></div><div class="tsd-comment tsd-typography"><p>Metadata related to url context retrieval tool.</p>
</div><div class="tsd-comment tsd-typography"></div><aside class="tsd-sources"><ul><li>Defined in src/types.ts:2112</li></ul></aside></section></section></details></div><div class="col-sidebar"><div class="page-menu"><div class="tsd-navigation settings"><details class="tsd-accordion"><summary class="tsd-accordion-summary"><h3><svg width="20" height="20" viewBox="0 0 24 24" fill="none" aria-hidden="true"><use href="../assets/icons.svg#icon-chevronDown"></use></svg>Settings</h3></summary><div class="tsd-accordion-details"><div class="tsd-theme-toggle"><label class="settings-label" for="tsd-theme">Theme</label><select id="tsd-theme"><option value="os">OS</option><option value="light">Light</option><option value="dark">Dark</option></select></div></div></details></div><details open class="tsd-accordion tsd-page-navigation"><summary class="tsd-accordion-summary"><h3><svg width="20" height="20" viewBox="0 0 24 24" fill="none" aria-hidden="true"><use href="../assets/icons.svg#icon-chevronDown"></use></svg>On This Page</h3></summary><div class="tsd-accordion-details"><details open class="tsd-accordion tsd-page-navigation-section"><summary class="tsd-accordion-summary" data-key="section-Properties"><svg width="20" height="20" viewBox="0 0 24 24" fill="none" aria-hidden="true"><use href="../assets/icons.svg#icon-chevronDown"></use></svg>Properties</summary><div><a href="#avglogprobs" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>avg<wbr/>Logprobs</span></a><a href="#citationmetadata" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>citation<wbr/>Metadata</span></a><a href="#content" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>content</span></a><a href="#finishmessage" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>finish<wbr/>Message</span></a><a href="#finishreason" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>finish<wbr/>Reason</span></a><a href="#groundingmetadata" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>grounding<wbr/>Metadata</span></a><a href="#index" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>index</span></a><a href="#logprobsresult" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>logprobs<wbr/>Result</span></a><a href="#safetyratings" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>safety<wbr/>Ratings</span></a><a href="#tokencount" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>token<wbr/>Count</span></a><a href="#urlcontextmetadata" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>url<wbr/>Context<wbr/>Metadata</span></a></div></details></div></details></div><div class="site-menu"><nav class="tsd-navigation"><a href="../modules.html">@google/genai</a><ul class="tsd-small-nested-navigation" id="tsd-nav-container"><li>Loading...</li></ul></nav></div></div></div><footer><p class="tsd-generator">Generated using <a href="https://typedoc.org/" target="_blank">TypeDoc</a></p></footer><div class="overlay"></div></body></html>
