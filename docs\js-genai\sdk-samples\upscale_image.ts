/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */
import {GoogleGenAI} from '@google/genai';

const GOOGLE_CLOUD_PROJECT = process.env.GOOGLE_CLOUD_PROJECT;
const GOOGLE_CLOUD_LOCATION = process.env.GOOGLE_CLOUD_LOCATION;
const GOOGLE_GENAI_USE_VERTEXAI = process.env.GOOGLE_GENAI_USE_VERTEXAI;

async function upscaleImageFromVertexAI() {
  // Only Vertex AI is currently supported.
  const ai = new GoogleGenAI({
    vertexai: true,
    project: GOOGLE_CLOUD_PROJECT,
    location: GOOGLE_CLOUD_LOCATION,
  });

  // Generate an image first.
  const generatedImagesResponse = await ai.models.generateImages({
    model: 'imagen-3.0-generate-002',
    prompt: 'Red skateboard',
    config: {
      numberOfImages: 1,
      includeRaiReason: true,
    },
  });

  if (!generatedImagesResponse?.generatedImages?.[0]?.image) {
    console.error('Image generation failed.');
    return;
  }

  // Upscale the generated image.
  const upscaledImageResponse = await ai.models.upscaleImage({
    model: 'imagen-3.0-generate-002',
    image: generatedImagesResponse?.generatedImages?.[0]?.image,
    upscaleFactor: 'x2',
    config: {
      includeRaiReason: true,
      outputMimeType: 'image/jpeg',
      enhanceInputImage: true,
      imagePreservationFactor: 0.6,
    },
  });

  console.debug(upscaledImageResponse?.generatedImages?.[0]?.image?.imageBytes);
}

async function main() {
  if (GOOGLE_GENAI_USE_VERTEXAI) {
    await upscaleImageFromVertexAI().catch((e) =>
      console.error('got error', e),
    );
  } else {
    console.error(
      'Upscaling an image is not supported in Gemini Developer API.',
    );
  }
}

main();
