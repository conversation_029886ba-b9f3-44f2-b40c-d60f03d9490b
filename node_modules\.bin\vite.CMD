@SETLOCAL
@IF NOT DEFINED NODE_PATH (
  @SET "NODE_PATH=D:\ssh\learn\project-graph-origin\node_modules\.pnpm\vite@5.4.19_@types+node@24._6d1374eeebe15487e886d2337e556215\node_modules\vite\bin\node_modules;D:\ssh\learn\project-graph-origin\node_modules\.pnpm\vite@5.4.19_@types+node@24._6d1374eeebe15487e886d2337e556215\node_modules\vite\node_modules;D:\ssh\learn\project-graph-origin\node_modules\.pnpm\vite@5.4.19_@types+node@24._6d1374eeebe15487e886d2337e556215\node_modules;D:\ssh\learn\project-graph-origin\node_modules\.pnpm\node_modules"
) ELSE (
  @SET "NODE_PATH=D:\ssh\learn\project-graph-origin\node_modules\.pnpm\vite@5.4.19_@types+node@24._6d1374eeebe15487e886d2337e556215\node_modules\vite\bin\node_modules;D:\ssh\learn\project-graph-origin\node_modules\.pnpm\vite@5.4.19_@types+node@24._6d1374eeebe15487e886d2337e556215\node_modules\vite\node_modules;D:\ssh\learn\project-graph-origin\node_modules\.pnpm\vite@5.4.19_@types+node@24._6d1374eeebe15487e886d2337e556215\node_modules;D:\ssh\learn\project-graph-origin\node_modules\.pnpm\node_modules;%NODE_PATH%"
)
@IF EXIST "%~dp0\node.exe" (
  "%~dp0\node.exe"  "%~dp0\..\vite\bin\vite.js" %*
) ELSE (
  @SET PATHEXT=%PATHEXT:;.JS;=;%
  node  "%~dp0\..\vite\bin\vite.js" %*
)
