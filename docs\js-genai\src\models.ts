/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */

// Code generated by the Google Gen AI SDK generator DO NOT EDIT.

import {
  DEFAULT_MAX_REMOTE_CALLS,
  hasCallableTools,
  hasNonCallableTools,
  isCallableTool,
  shouldAppendAfcHistory,
  shouldDisableAfc,
} from './_afc.js';
import {ApiClient} from './_api_client.js';
import * as common from './_common.js';
import {BaseModule} from './_common.js';
import * as _internal_types from './_internal_types.js';
import {tContents} from './_transformers.js';
import * as converters from './converters/_models_converters.js';
import {hasMcpToolUsage, setMcpUsageHeader} from './mcp/_mcp.js';
import {PagedItem, Pager} from './pagers.js';
import * as types from './types.js';

export class Models extends BaseModule {
  constructor(private readonly apiClient: ApiClient) {
    super();
  }

  /**
   * Makes an API request to generate content with a given model.
   *
   * For the `model` parameter, supported formats for Vertex AI API include:
   * - The Gemini model ID, for example: 'gemini-2.0-flash'
   * - The full resource name starts with 'projects/', for example:
   *  'projects/my-project-id/locations/us-central1/publishers/google/models/gemini-2.0-flash'
   * - The partial resource name with 'publishers/', for example:
   *  'publishers/google/models/gemini-2.0-flash' or
   *  'publishers/meta/models/llama-3.1-405b-instruct-maas'
   * - `/` separated publisher and model name, for example:
   * 'google/gemini-2.0-flash' or 'meta/llama-3.1-405b-instruct-maas'
   *
   * For the `model` parameter, supported formats for Gemini API include:
   * - The Gemini model ID, for example: 'gemini-2.0-flash'
   * - The model name starts with 'models/', for example:
   *  'models/gemini-2.0-flash'
   * - For tuned models, the model name starts with 'tunedModels/',
   * for example:
   * 'tunedModels/1234567890123456789'
   *
   * Some models support multimodal input and output.
   *
   * @param params - The parameters for generating content.
   * @return The response from generating content.
   *
   * @example
   * ```ts
   * const response = await ai.models.generateContent({
   *   model: 'gemini-2.0-flash',
   *   contents: 'why is the sky blue?',
   *   config: {
   *     candidateCount: 2,
   *   }
   * });
   * console.log(response);
   * ```
   */
  generateContent = async (
    params: types.GenerateContentParameters,
  ): Promise<types.GenerateContentResponse> => {
    const transformedParams = await this.processParamsMaybeAddMcpUsage(params);
    this.maybeMoveToResponseJsonSchem(params);
    if (!hasCallableTools(params) || shouldDisableAfc(params.config)) {
      return await this.generateContentInternal(transformedParams);
    }

    if (hasNonCallableTools(params)) {
      throw new Error(
        'Automatic function calling with CallableTools and Tools is not yet supported.',
      );
    }

    let response: types.GenerateContentResponse;
    let functionResponseContent: types.Content;
    const automaticFunctionCallingHistory: types.Content[] = tContents(
      transformedParams.contents,
    );
    const maxRemoteCalls =
      transformedParams.config?.automaticFunctionCalling?.maximumRemoteCalls ??
      DEFAULT_MAX_REMOTE_CALLS;
    let remoteCalls = 0;
    while (remoteCalls < maxRemoteCalls) {
      response = await this.generateContentInternal(transformedParams);
      if (!response.functionCalls || response.functionCalls!.length === 0) {
        break;
      }

      const responseContent: types.Content = response.candidates![0].content!;
      const functionResponseParts: types.Part[] = [];
      for (const tool of params.config?.tools ?? []) {
        if (isCallableTool(tool)) {
          const callableTool = tool as types.CallableTool;
          const parts = await callableTool.callTool(response.functionCalls!);
          functionResponseParts.push(...parts);
        }
      }

      remoteCalls++;

      functionResponseContent = {
        role: 'user',
        parts: functionResponseParts,
      };

      transformedParams.contents = tContents(transformedParams.contents);
      (transformedParams.contents as types.Content[]).push(responseContent);
      (transformedParams.contents as types.Content[]).push(
        functionResponseContent,
      );

      if (shouldAppendAfcHistory(transformedParams.config)) {
        automaticFunctionCallingHistory.push(responseContent);
        automaticFunctionCallingHistory.push(functionResponseContent);
      }
    }
    if (shouldAppendAfcHistory(transformedParams.config)) {
      response!.automaticFunctionCallingHistory =
        automaticFunctionCallingHistory;
    }
    return response!;
  };

  /**
   * This logic is needed for GenerateContentConfig only.
   * Previously we made GenerateContentConfig.responseSchema field to accept
   * unknown. Since v1.9.0, we switch to use backend JSON schema support.
   * To maintain backward compatibility, we move the data that was treated as
   * JSON schema from the responseSchema field to the responseJsonSchema field.
   */
  private maybeMoveToResponseJsonSchem(
    params: types.GenerateContentParameters,
  ): void {
    if (params.config && params.config.responseSchema) {
      if (!params.config.responseJsonSchema) {
        if (Object.keys(params.config.responseSchema).includes('$schema')) {
          params.config.responseJsonSchema = params.config.responseSchema;
          delete params.config.responseSchema;
        }
      }
    }
    return;
  }

  /**
   * Makes an API request to generate content with a given model and yields the
   * response in chunks.
   *
   * For the `model` parameter, supported formats for Vertex AI API include:
   * - The Gemini model ID, for example: 'gemini-2.0-flash'
   * - The full resource name starts with 'projects/', for example:
   *  'projects/my-project-id/locations/us-central1/publishers/google/models/gemini-2.0-flash'
   * - The partial resource name with 'publishers/', for example:
   *  'publishers/google/models/gemini-2.0-flash' or
   *  'publishers/meta/models/llama-3.1-405b-instruct-maas'
   * - `/` separated publisher and model name, for example:
   * 'google/gemini-2.0-flash' or 'meta/llama-3.1-405b-instruct-maas'
   *
   * For the `model` parameter, supported formats for Gemini API include:
   * - The Gemini model ID, for example: 'gemini-2.0-flash'
   * - The model name starts with 'models/', for example:
   *  'models/gemini-2.0-flash'
   * - For tuned models, the model name starts with 'tunedModels/',
   * for example:
   *  'tunedModels/1234567890123456789'
   *
   * Some models support multimodal input and output.
   *
   * @param params - The parameters for generating content with streaming response.
   * @return The response from generating content.
   *
   * @example
   * ```ts
   * const response = await ai.models.generateContentStream({
   *   model: 'gemini-2.0-flash',
   *   contents: 'why is the sky blue?',
   *   config: {
   *     maxOutputTokens: 200,
   *   }
   * });
   * for await (const chunk of response) {
   *   console.log(chunk);
   * }
   * ```
   */
  generateContentStream = async (
    params: types.GenerateContentParameters,
  ): Promise<AsyncGenerator<types.GenerateContentResponse>> => {
    this.maybeMoveToResponseJsonSchem(params);
    if (shouldDisableAfc(params.config)) {
      const transformedParams =
        await this.processParamsMaybeAddMcpUsage(params);
      return await this.generateContentStreamInternal(transformedParams);
    } else {
      return await this.processAfcStream(params);
    }
  };

  /**
   * Transforms the CallableTools in the parameters to be simply Tools, it
   * copies the params into a new object and replaces the tools, it does not
   * modify the original params. Also sets the MCP usage header if there are
   * MCP tools in the parameters.
   */
  private async processParamsMaybeAddMcpUsage(
    params: types.GenerateContentParameters,
  ): Promise<types.GenerateContentParameters> {
    const tools = params.config?.tools;
    if (!tools) {
      return params;
    }
    const transformedTools = await Promise.all(
      tools.map(async (tool) => {
        if (isCallableTool(tool)) {
          const callableTool = tool as types.CallableTool;
          return await callableTool.tool();
        }
        return tool;
      }),
    );
    const newParams: types.GenerateContentParameters = {
      model: params.model,
      contents: params.contents,
      config: {
        ...params.config,
        tools: transformedTools,
      },
    };
    newParams.config!.tools = transformedTools;

    if (
      params.config &&
      params.config.tools &&
      hasMcpToolUsage(params.config.tools)
    ) {
      const headers = params.config.httpOptions?.headers ?? {};
      let newHeaders = {...headers};
      if (Object.keys(newHeaders).length === 0) {
        newHeaders = this.apiClient.getDefaultHeaders();
      }
      setMcpUsageHeader(newHeaders);
      newParams.config!.httpOptions = {
        ...params.config.httpOptions,
        headers: newHeaders,
      };
    }
    return newParams;
  }

  private async initAfcToolsMap(
    params: types.GenerateContentParameters,
  ): Promise<Map<string, types.CallableTool>> {
    const afcTools: Map<string, types.CallableTool> = new Map();
    for (const tool of params.config?.tools ?? []) {
      if (isCallableTool(tool)) {
        const callableTool = tool as types.CallableTool;
        const toolDeclaration = await callableTool.tool();
        for (const declaration of toolDeclaration.functionDeclarations ?? []) {
          if (!declaration.name) {
            throw new Error('Function declaration name is required.');
          }
          if (afcTools.has(declaration.name)) {
            throw new Error(
              `Duplicate tool declaration name: ${declaration.name}`,
            );
          }
          afcTools.set(declaration.name, callableTool);
        }
      }
    }
    return afcTools;
  }

  private async processAfcStream(
    params: types.GenerateContentParameters,
  ): Promise<AsyncGenerator<types.GenerateContentResponse>> {
    const maxRemoteCalls =
      params.config?.automaticFunctionCalling?.maximumRemoteCalls ??
      DEFAULT_MAX_REMOTE_CALLS;
    let wereFunctionsCalled = false;
    let remoteCallCount = 0;
    const afcToolsMap = await this.initAfcToolsMap(params);
    return (async function* (
      models: Models,
      afcTools: Map<string, types.CallableTool>,
      params: types.GenerateContentParameters,
    ) {
      while (remoteCallCount < maxRemoteCalls) {
        if (wereFunctionsCalled) {
          remoteCallCount++;
          wereFunctionsCalled = false;
        }
        const transformedParams =
          await models.processParamsMaybeAddMcpUsage(params);
        const response =
          await models.generateContentStreamInternal(transformedParams);

        const functionResponses: types.Part[] = [];
        const responseContents: types.Content[] = [];

        for await (const chunk of response) {
          yield chunk;
          if (chunk.candidates && chunk.candidates[0]?.content) {
            responseContents.push(chunk.candidates[0].content);
            for (const part of chunk.candidates[0].content.parts ?? []) {
              if (remoteCallCount < maxRemoteCalls && part.functionCall) {
                if (!part.functionCall.name) {
                  throw new Error(
                    'Function call name was not returned by the model.',
                  );
                }
                if (!afcTools.has(part.functionCall.name)) {
                  throw new Error(
                    `Automatic function calling was requested, but not all the tools the model used implement the CallableTool interface. Available tools: ${afcTools.keys()}, mising tool: ${
                      part.functionCall.name
                    }`,
                  );
                } else {
                  const responseParts = await afcTools
                    .get(part.functionCall.name)!
                    .callTool([part.functionCall]);
                  functionResponses.push(...responseParts);
                }
              }
            }
          }
        }

        if (functionResponses.length > 0) {
          wereFunctionsCalled = true;
          const typedResponseChunk = new types.GenerateContentResponse();
          typedResponseChunk.candidates = [
            {
              content: {
                role: 'user',
                parts: functionResponses,
              },
            },
          ];

          yield typedResponseChunk;

          const newContents: types.Content[] = [];
          newContents.push(...responseContents);
          newContents.push({
            role: 'user',
            parts: functionResponses,
          });
          const updatedContents = tContents(params.contents).concat(
            newContents,
          );

          params.contents = updatedContents;
        } else {
          break;
        }
      }
    })(this, afcToolsMap, params);
  }

  /**
   * Generates an image based on a text description and configuration.
   *
   * @param params - The parameters for generating images.
   * @return The response from the API.
   *
   * @example
   * ```ts
   * const response = await client.models.generateImages({
   *  model: 'imagen-3.0-generate-002',
   *  prompt: 'Robot holding a red skateboard',
   *  config: {
   *    numberOfImages: 1,
   *    includeRaiReason: true,
   *  },
   * });
   * console.log(response?.generatedImages?.[0]?.image?.imageBytes);
   * ```
   */
  generateImages = async (
    params: types.GenerateImagesParameters,
  ): Promise<types.GenerateImagesResponse> => {
    return await this.generateImagesInternal(params).then((apiResponse) => {
      let positivePromptSafetyAttributes;
      const generatedImages = [];

      if (apiResponse?.generatedImages) {
        for (const generatedImage of apiResponse.generatedImages) {
          if (
            generatedImage &&
            generatedImage?.safetyAttributes &&
            generatedImage?.safetyAttributes?.contentType === 'Positive Prompt'
          ) {
            positivePromptSafetyAttributes = generatedImage?.safetyAttributes;
          } else {
            generatedImages.push(generatedImage);
          }
        }
      }
      let response: types.GenerateImagesResponse;

      if (positivePromptSafetyAttributes) {
        response = {
          generatedImages: generatedImages,
          positivePromptSafetyAttributes: positivePromptSafetyAttributes,
          sdkHttpResponse: apiResponse.sdkHttpResponse,
        };
      } else {
        response = {
          generatedImages: generatedImages,
          sdkHttpResponse: apiResponse.sdkHttpResponse,
        };
      }
      return response;
    });
  };

  list = async (
    params?: types.ListModelsParameters,
  ): Promise<Pager<types.Model>> => {
    const defaultConfig: types.ListModelsConfig = {
      queryBase: true,
    };
    const actualConfig: types.ListModelsConfig = {
      ...defaultConfig,
      ...params?.config,
    };
    const actualParams: types.ListModelsParameters = {
      config: actualConfig,
    };

    if (this.apiClient.isVertexAI()) {
      if (!actualParams.config!.queryBase) {
        if (actualParams.config?.filter) {
          throw new Error(
            'Filtering tuned models list for Vertex AI is not currently supported',
          );
        } else {
          actualParams.config!.filter = 'labels.tune-type:*';
        }
      }
    }

    return new Pager<types.Model>(
      PagedItem.PAGED_ITEM_MODELS,
      (x: types.ListModelsParameters) => this.listInternal(x),
      await this.listInternal(actualParams),
      actualParams,
    );
  };

  /**
   * Edits an image based on a prompt, list of reference images, and configuration.
   *
   * @param params - The parameters for editing an image.
   * @return The response from the API.
   *
   * @example
   * ```ts
   * const response = await client.models.editImage({
   *  model: 'imagen-3.0-capability-001',
   *  prompt: 'Generate an image containing a mug with the product logo [1] visible on the side of the mug.',
   *  referenceImages: [subjectReferenceImage]
   *  config: {
   *    numberOfImages: 1,
   *    includeRaiReason: true,
   *  },
   * });
   * console.log(response?.generatedImages?.[0]?.image?.imageBytes);
   * ```
   */
  editImage = async (
    params: types.EditImageParameters,
  ): Promise<types.EditImageResponse> => {
    const paramsInternal: _internal_types.EditImageParametersInternal = {
      model: params.model,
      prompt: params.prompt,
      referenceImages: [],
      config: params.config,
    };
    if (params.referenceImages) {
      if (params.referenceImages) {
        paramsInternal.referenceImages = params.referenceImages.map((img) =>
          img.toReferenceImageAPI(),
        );
      }
    }
    return await this.editImageInternal(paramsInternal);
  };

  /**
   * Upscales an image based on an image, upscale factor, and configuration.
   * Only supported in Vertex AI currently.
   *
   * @param params - The parameters for upscaling an image.
   * @return The response from the API.
   *
   * @example
   * ```ts
   * const response = await client.models.upscaleImage({
   *  model: 'imagen-3.0-generate-002',
   *  image: image,
   *  upscaleFactor: 'x2',
   *  config: {
   *    includeRaiReason: true,
   *  },
   * });
   * console.log(response?.generatedImages?.[0]?.image?.imageBytes);
   * ```
   */
  upscaleImage = async (
    params: types.UpscaleImageParameters,
  ): Promise<types.UpscaleImageResponse> => {
    let apiConfig: _internal_types.UpscaleImageAPIConfigInternal = {
      numberOfImages: 1,
      mode: 'upscale',
    };

    if (params.config) {
      apiConfig = {...apiConfig, ...params.config};
    }

    const apiParams: _internal_types.UpscaleImageAPIParametersInternal = {
      model: params.model,
      image: params.image,
      upscaleFactor: params.upscaleFactor,
      config: apiConfig,
    };
    return await this.upscaleImageInternal(apiParams);
  };

  /**
   *  Generates videos based on a text description and configuration.
   *
   * @param params - The parameters for generating videos.
   * @return A Promise<GenerateVideosOperation> which allows you to track the progress and eventually retrieve the generated videos using the operations.get method.
   *
   * @example
   * ```ts
   * const operation = await ai.models.generateVideos({
   *  model: 'veo-2.0-generate-001',
   *  prompt: 'A neon hologram of a cat driving at top speed',
   *  config: {
   *    numberOfVideos: 1
   * });
   *
   * while (!operation.done) {
   *   await new Promise(resolve => setTimeout(resolve, 10000));
   *   operation = await ai.operations.getVideosOperation({operation: operation});
   * }
   *
   * console.log(operation.response?.generatedVideos?.[0]?.video?.uri);
   * ```
   */

  generateVideos = async (
    params: types.GenerateVideosParameters,
  ): Promise<types.GenerateVideosOperation> => {
    return await this.generateVideosInternal(params);
  };

  private async generateContentInternal(
    params: types.GenerateContentParameters,
  ): Promise<types.GenerateContentResponse> {
    let response: Promise<types.GenerateContentResponse>;

    let path: string = '';
    let queryParams: Record<string, string> = {};
    if (this.apiClient.isVertexAI()) {
      const body = converters.generateContentParametersToVertex(
        this.apiClient,
        params,
      );
      path = common.formatMap(
        '{model}:generateContent',
        body['_url'] as Record<string, unknown>,
      );
      queryParams = body['_query'] as Record<string, string>;
      delete body['config'];
      delete body['_url'];
      delete body['_query'];

      response = this.apiClient
        .request({
          path: path,
          queryParams: queryParams,
          body: JSON.stringify(body),
          httpMethod: 'POST',
          httpOptions: params.config?.httpOptions,
          abortSignal: params.config?.abortSignal,
        })
        .then((httpResponse) => {
          return httpResponse.json().then((jsonResponse) => {
            const response = jsonResponse as types.GenerateContentResponse;
            response.sdkHttpResponse = {
              headers: httpResponse.headers,
            } as types.HttpResponse;
            return response;
          });
        }) as Promise<types.GenerateContentResponse>;

      return response.then((apiResponse) => {
        const resp = converters.generateContentResponseFromVertex(apiResponse);
        const typedResp = new types.GenerateContentResponse();
        Object.assign(typedResp, resp);
        return typedResp;
      });
    } else {
      const body = converters.generateContentParametersToMldev(
        this.apiClient,
        params,
      );
      path = common.formatMap(
        '{model}:generateContent',
        body['_url'] as Record<string, unknown>,
      );
      queryParams = body['_query'] as Record<string, string>;
      delete body['config'];
      delete body['_url'];
      delete body['_query'];

      response = this.apiClient
        .request({
          path: path,
          queryParams: queryParams,
          body: JSON.stringify(body),
          httpMethod: 'POST',
          httpOptions: params.config?.httpOptions,
          abortSignal: params.config?.abortSignal,
        })
        .then((httpResponse) => {
          return httpResponse.json().then((jsonResponse) => {
            const response = jsonResponse as types.GenerateContentResponse;
            response.sdkHttpResponse = {
              headers: httpResponse.headers,
            } as types.HttpResponse;
            return response;
          });
        }) as Promise<types.GenerateContentResponse>;

      return response.then((apiResponse) => {
        const resp = converters.generateContentResponseFromMldev(apiResponse);
        const typedResp = new types.GenerateContentResponse();
        Object.assign(typedResp, resp);
        return typedResp;
      });
    }
  }

  private async generateContentStreamInternal(
    params: types.GenerateContentParameters,
  ): Promise<AsyncGenerator<types.GenerateContentResponse>> {
    let response: Promise<AsyncGenerator<types.HttpResponse>>;

    let path: string = '';
    let queryParams: Record<string, string> = {};
    if (this.apiClient.isVertexAI()) {
      const body = converters.generateContentParametersToVertex(
        this.apiClient,
        params,
      );
      path = common.formatMap(
        '{model}:streamGenerateContent?alt=sse',
        body['_url'] as Record<string, unknown>,
      );
      queryParams = body['_query'] as Record<string, string>;
      delete body['config'];
      delete body['_url'];
      delete body['_query'];

      const apiClient = this.apiClient;
      response = apiClient.requestStream({
        path: path,
        queryParams: queryParams,
        body: JSON.stringify(body),
        httpMethod: 'POST',
        httpOptions: params.config?.httpOptions,
        abortSignal: params.config?.abortSignal,
      }) as Promise<AsyncGenerator<types.HttpResponse>>;

      return response.then(async function* (
        apiResponse: AsyncGenerator<types.HttpResponse>,
      ) {
        for await (const chunk of apiResponse) {
          const resp = converters.generateContentResponseFromVertex(
            (await chunk.json()) as types.GenerateContentResponse,
          );

          resp['sdkHttpResponse'] = {
            headers: chunk.headers,
          } as types.HttpResponse;

          const typedResp = new types.GenerateContentResponse();
          Object.assign(typedResp, resp);
          yield typedResp;
        }
      });
    } else {
      const body = converters.generateContentParametersToMldev(
        this.apiClient,
        params,
      );
      path = common.formatMap(
        '{model}:streamGenerateContent?alt=sse',
        body['_url'] as Record<string, unknown>,
      );
      queryParams = body['_query'] as Record<string, string>;
      delete body['config'];
      delete body['_url'];
      delete body['_query'];

      const apiClient = this.apiClient;
      response = apiClient.requestStream({
        path: path,
        queryParams: queryParams,
        body: JSON.stringify(body),
        httpMethod: 'POST',
        httpOptions: params.config?.httpOptions,
        abortSignal: params.config?.abortSignal,
      }) as Promise<AsyncGenerator<types.HttpResponse>>;

      return response.then(async function* (
        apiResponse: AsyncGenerator<types.HttpResponse>,
      ) {
        for await (const chunk of apiResponse) {
          const resp = converters.generateContentResponseFromMldev(
            (await chunk.json()) as types.GenerateContentResponse,
          );

          resp['sdkHttpResponse'] = {
            headers: chunk.headers,
          } as types.HttpResponse;

          const typedResp = new types.GenerateContentResponse();
          Object.assign(typedResp, resp);
          yield typedResp;
        }
      });
    }
  }

  /**
   * Calculates embeddings for the given contents. Only text is supported.
   *
   * @param params - The parameters for embedding contents.
   * @return The response from the API.
   *
   * @example
   * ```ts
   * const response = await ai.models.embedContent({
   *  model: 'text-embedding-004',
   *  contents: [
   *    'What is your name?',
   *    'What is your favorite color?',
   *  ],
   *  config: {
   *    outputDimensionality: 64,
   *  },
   * });
   * console.log(response);
   * ```
   */
  async embedContent(
    params: types.EmbedContentParameters,
  ): Promise<types.EmbedContentResponse> {
    let response: Promise<types.EmbedContentResponse>;

    let path: string = '';
    let queryParams: Record<string, string> = {};
    if (this.apiClient.isVertexAI()) {
      const body = converters.embedContentParametersToVertex(
        this.apiClient,
        params,
      );
      path = common.formatMap(
        '{model}:predict',
        body['_url'] as Record<string, unknown>,
      );
      queryParams = body['_query'] as Record<string, string>;
      delete body['config'];
      delete body['_url'];
      delete body['_query'];

      response = this.apiClient
        .request({
          path: path,
          queryParams: queryParams,
          body: JSON.stringify(body),
          httpMethod: 'POST',
          httpOptions: params.config?.httpOptions,
          abortSignal: params.config?.abortSignal,
        })
        .then((httpResponse) => {
          return httpResponse.json().then((jsonResponse) => {
            const response = jsonResponse as types.EmbedContentResponse;
            response.sdkHttpResponse = {
              headers: httpResponse.headers,
            } as types.HttpResponse;
            return response;
          });
        }) as Promise<types.EmbedContentResponse>;

      return response.then((apiResponse) => {
        const resp = converters.embedContentResponseFromVertex(apiResponse);
        const typedResp = new types.EmbedContentResponse();
        Object.assign(typedResp, resp);
        return typedResp;
      });
    } else {
      const body = converters.embedContentParametersToMldev(
        this.apiClient,
        params,
      );
      path = common.formatMap(
        '{model}:batchEmbedContents',
        body['_url'] as Record<string, unknown>,
      );
      queryParams = body['_query'] as Record<string, string>;
      delete body['config'];
      delete body['_url'];
      delete body['_query'];

      response = this.apiClient
        .request({
          path: path,
          queryParams: queryParams,
          body: JSON.stringify(body),
          httpMethod: 'POST',
          httpOptions: params.config?.httpOptions,
          abortSignal: params.config?.abortSignal,
        })
        .then((httpResponse) => {
          return httpResponse.json().then((jsonResponse) => {
            const response = jsonResponse as types.EmbedContentResponse;
            response.sdkHttpResponse = {
              headers: httpResponse.headers,
            } as types.HttpResponse;
            return response;
          });
        }) as Promise<types.EmbedContentResponse>;

      return response.then((apiResponse) => {
        const resp = converters.embedContentResponseFromMldev(apiResponse);
        const typedResp = new types.EmbedContentResponse();
        Object.assign(typedResp, resp);
        return typedResp;
      });
    }
  }

  /**
   * Generates an image based on a text description and configuration.
   *
   * @param params - The parameters for generating images.
   * @return The response from the API.
   *
   * @example
   * ```ts
   * const response = await ai.models.generateImages({
   *  model: 'imagen-3.0-generate-002',
   *  prompt: 'Robot holding a red skateboard',
   *  config: {
   *    numberOfImages: 1,
   *    includeRaiReason: true,
   *  },
   * });
   * console.log(response?.generatedImages?.[0]?.image?.imageBytes);
   * ```
   */
  private async generateImagesInternal(
    params: types.GenerateImagesParameters,
  ): Promise<types.GenerateImagesResponse> {
    let response: Promise<types.GenerateImagesResponse>;

    let path: string = '';
    let queryParams: Record<string, string> = {};
    if (this.apiClient.isVertexAI()) {
      const body = converters.generateImagesParametersToVertex(
        this.apiClient,
        params,
      );
      path = common.formatMap(
        '{model}:predict',
        body['_url'] as Record<string, unknown>,
      );
      queryParams = body['_query'] as Record<string, string>;
      delete body['config'];
      delete body['_url'];
      delete body['_query'];

      response = this.apiClient
        .request({
          path: path,
          queryParams: queryParams,
          body: JSON.stringify(body),
          httpMethod: 'POST',
          httpOptions: params.config?.httpOptions,
          abortSignal: params.config?.abortSignal,
        })
        .then((httpResponse) => {
          return httpResponse.json().then((jsonResponse) => {
            const response = jsonResponse as types.GenerateImagesResponse;
            response.sdkHttpResponse = {
              headers: httpResponse.headers,
            } as types.HttpResponse;
            return response;
          });
        }) as Promise<types.GenerateImagesResponse>;

      return response.then((apiResponse) => {
        const resp = converters.generateImagesResponseFromVertex(apiResponse);
        const typedResp = new types.GenerateImagesResponse();
        Object.assign(typedResp, resp);
        return typedResp;
      });
    } else {
      const body = converters.generateImagesParametersToMldev(
        this.apiClient,
        params,
      );
      path = common.formatMap(
        '{model}:predict',
        body['_url'] as Record<string, unknown>,
      );
      queryParams = body['_query'] as Record<string, string>;
      delete body['config'];
      delete body['_url'];
      delete body['_query'];

      response = this.apiClient
        .request({
          path: path,
          queryParams: queryParams,
          body: JSON.stringify(body),
          httpMethod: 'POST',
          httpOptions: params.config?.httpOptions,
          abortSignal: params.config?.abortSignal,
        })
        .then((httpResponse) => {
          return httpResponse.json().then((jsonResponse) => {
            const response = jsonResponse as types.GenerateImagesResponse;
            response.sdkHttpResponse = {
              headers: httpResponse.headers,
            } as types.HttpResponse;
            return response;
          });
        }) as Promise<types.GenerateImagesResponse>;

      return response.then((apiResponse) => {
        const resp = converters.generateImagesResponseFromMldev(apiResponse);
        const typedResp = new types.GenerateImagesResponse();
        Object.assign(typedResp, resp);
        return typedResp;
      });
    }
  }

  private async editImageInternal(
    params: _internal_types.EditImageParametersInternal,
  ): Promise<types.EditImageResponse> {
    let response: Promise<types.EditImageResponse>;

    let path: string = '';
    let queryParams: Record<string, string> = {};
    if (this.apiClient.isVertexAI()) {
      const body = converters.editImageParametersInternalToVertex(
        this.apiClient,
        params,
      );
      path = common.formatMap(
        '{model}:predict',
        body['_url'] as Record<string, unknown>,
      );
      queryParams = body['_query'] as Record<string, string>;
      delete body['config'];
      delete body['_url'];
      delete body['_query'];

      response = this.apiClient
        .request({
          path: path,
          queryParams: queryParams,
          body: JSON.stringify(body),
          httpMethod: 'POST',
          httpOptions: params.config?.httpOptions,
          abortSignal: params.config?.abortSignal,
        })
        .then((httpResponse) => {
          return httpResponse.json().then((jsonResponse) => {
            const response = jsonResponse as types.EditImageResponse;
            response.sdkHttpResponse = {
              headers: httpResponse.headers,
            } as types.HttpResponse;
            return response;
          });
        }) as Promise<types.EditImageResponse>;

      return response.then((apiResponse) => {
        const resp = converters.editImageResponseFromVertex(apiResponse);
        const typedResp = new types.EditImageResponse();
        Object.assign(typedResp, resp);
        return typedResp;
      });
    } else {
      throw new Error('This method is only supported by the Vertex AI.');
    }
  }

  private async upscaleImageInternal(
    params: _internal_types.UpscaleImageAPIParametersInternal,
  ): Promise<types.UpscaleImageResponse> {
    let response: Promise<types.UpscaleImageResponse>;

    let path: string = '';
    let queryParams: Record<string, string> = {};
    if (this.apiClient.isVertexAI()) {
      const body = converters.upscaleImageAPIParametersInternalToVertex(
        this.apiClient,
        params,
      );
      path = common.formatMap(
        '{model}:predict',
        body['_url'] as Record<string, unknown>,
      );
      queryParams = body['_query'] as Record<string, string>;
      delete body['config'];
      delete body['_url'];
      delete body['_query'];

      response = this.apiClient
        .request({
          path: path,
          queryParams: queryParams,
          body: JSON.stringify(body),
          httpMethod: 'POST',
          httpOptions: params.config?.httpOptions,
          abortSignal: params.config?.abortSignal,
        })
        .then((httpResponse) => {
          return httpResponse.json().then((jsonResponse) => {
            const response = jsonResponse as types.UpscaleImageResponse;
            response.sdkHttpResponse = {
              headers: httpResponse.headers,
            } as types.HttpResponse;
            return response;
          });
        }) as Promise<types.UpscaleImageResponse>;

      return response.then((apiResponse) => {
        const resp = converters.upscaleImageResponseFromVertex(apiResponse);
        const typedResp = new types.UpscaleImageResponse();
        Object.assign(typedResp, resp);
        return typedResp;
      });
    } else {
      throw new Error('This method is only supported by the Vertex AI.');
    }
  }

  /**
   * Fetches information about a model by name.
   *
   * @example
   * ```ts
   * const modelInfo = await ai.models.get({model: 'gemini-2.0-flash'});
   * ```
   */
  async get(params: types.GetModelParameters): Promise<types.Model> {
    let response: Promise<types.Model>;

    let path: string = '';
    let queryParams: Record<string, string> = {};
    if (this.apiClient.isVertexAI()) {
      const body = converters.getModelParametersToVertex(
        this.apiClient,
        params,
      );
      path = common.formatMap(
        '{name}',
        body['_url'] as Record<string, unknown>,
      );
      queryParams = body['_query'] as Record<string, string>;
      delete body['config'];
      delete body['_url'];
      delete body['_query'];

      response = this.apiClient
        .request({
          path: path,
          queryParams: queryParams,
          body: JSON.stringify(body),
          httpMethod: 'GET',
          httpOptions: params.config?.httpOptions,
          abortSignal: params.config?.abortSignal,
        })
        .then((httpResponse) => {
          return httpResponse.json();
        }) as Promise<types.Model>;

      return response.then((apiResponse) => {
        const resp = converters.modelFromVertex(apiResponse);

        return resp as types.Model;
      });
    } else {
      const body = converters.getModelParametersToMldev(this.apiClient, params);
      path = common.formatMap(
        '{name}',
        body['_url'] as Record<string, unknown>,
      );
      queryParams = body['_query'] as Record<string, string>;
      delete body['config'];
      delete body['_url'];
      delete body['_query'];

      response = this.apiClient
        .request({
          path: path,
          queryParams: queryParams,
          body: JSON.stringify(body),
          httpMethod: 'GET',
          httpOptions: params.config?.httpOptions,
          abortSignal: params.config?.abortSignal,
        })
        .then((httpResponse) => {
          return httpResponse.json();
        }) as Promise<types.Model>;

      return response.then((apiResponse) => {
        const resp = converters.modelFromMldev(apiResponse);

        return resp as types.Model;
      });
    }
  }

  private async listInternal(
    params: types.ListModelsParameters,
  ): Promise<types.ListModelsResponse> {
    let response: Promise<types.ListModelsResponse>;

    let path: string = '';
    let queryParams: Record<string, string> = {};
    if (this.apiClient.isVertexAI()) {
      const body = converters.listModelsParametersToVertex(
        this.apiClient,
        params,
      );
      path = common.formatMap(
        '{models_url}',
        body['_url'] as Record<string, unknown>,
      );
      queryParams = body['_query'] as Record<string, string>;
      delete body['config'];
      delete body['_url'];
      delete body['_query'];

      response = this.apiClient
        .request({
          path: path,
          queryParams: queryParams,
          body: JSON.stringify(body),
          httpMethod: 'GET',
          httpOptions: params.config?.httpOptions,
          abortSignal: params.config?.abortSignal,
        })
        .then((httpResponse) => {
          return httpResponse.json().then((jsonResponse) => {
            const response = jsonResponse as types.ListModelsResponse;
            response.sdkHttpResponse = {
              headers: httpResponse.headers,
            } as types.HttpResponse;
            return response;
          });
        }) as Promise<types.ListModelsResponse>;

      return response.then((apiResponse) => {
        const resp = converters.listModelsResponseFromVertex(apiResponse);
        const typedResp = new types.ListModelsResponse();
        Object.assign(typedResp, resp);
        return typedResp;
      });
    } else {
      const body = converters.listModelsParametersToMldev(
        this.apiClient,
        params,
      );
      path = common.formatMap(
        '{models_url}',
        body['_url'] as Record<string, unknown>,
      );
      queryParams = body['_query'] as Record<string, string>;
      delete body['config'];
      delete body['_url'];
      delete body['_query'];

      response = this.apiClient
        .request({
          path: path,
          queryParams: queryParams,
          body: JSON.stringify(body),
          httpMethod: 'GET',
          httpOptions: params.config?.httpOptions,
          abortSignal: params.config?.abortSignal,
        })
        .then((httpResponse) => {
          return httpResponse.json().then((jsonResponse) => {
            const response = jsonResponse as types.ListModelsResponse;
            response.sdkHttpResponse = {
              headers: httpResponse.headers,
            } as types.HttpResponse;
            return response;
          });
        }) as Promise<types.ListModelsResponse>;

      return response.then((apiResponse) => {
        const resp = converters.listModelsResponseFromMldev(apiResponse);
        const typedResp = new types.ListModelsResponse();
        Object.assign(typedResp, resp);
        return typedResp;
      });
    }
  }

  /**
   * Updates a tuned model by its name.
   *
   * @param params - The parameters for updating the model.
   * @return The response from the API.
   *
   * @example
   * ```ts
   * const response = await ai.models.update({
   *   model: 'tuned-model-name',
   *   config: {
   *     displayName: 'New display name',
   *     description: 'New description',
   *   },
   * });
   * ```
   */
  async update(params: types.UpdateModelParameters): Promise<types.Model> {
    let response: Promise<types.Model>;

    let path: string = '';
    let queryParams: Record<string, string> = {};
    if (this.apiClient.isVertexAI()) {
      const body = converters.updateModelParametersToVertex(
        this.apiClient,
        params,
      );
      path = common.formatMap(
        '{model}',
        body['_url'] as Record<string, unknown>,
      );
      queryParams = body['_query'] as Record<string, string>;
      delete body['config'];
      delete body['_url'];
      delete body['_query'];

      response = this.apiClient
        .request({
          path: path,
          queryParams: queryParams,
          body: JSON.stringify(body),
          httpMethod: 'PATCH',
          httpOptions: params.config?.httpOptions,
          abortSignal: params.config?.abortSignal,
        })
        .then((httpResponse) => {
          return httpResponse.json();
        }) as Promise<types.Model>;

      return response.then((apiResponse) => {
        const resp = converters.modelFromVertex(apiResponse);

        return resp as types.Model;
      });
    } else {
      const body = converters.updateModelParametersToMldev(
        this.apiClient,
        params,
      );
      path = common.formatMap(
        '{name}',
        body['_url'] as Record<string, unknown>,
      );
      queryParams = body['_query'] as Record<string, string>;
      delete body['config'];
      delete body['_url'];
      delete body['_query'];

      response = this.apiClient
        .request({
          path: path,
          queryParams: queryParams,
          body: JSON.stringify(body),
          httpMethod: 'PATCH',
          httpOptions: params.config?.httpOptions,
          abortSignal: params.config?.abortSignal,
        })
        .then((httpResponse) => {
          return httpResponse.json();
        }) as Promise<types.Model>;

      return response.then((apiResponse) => {
        const resp = converters.modelFromMldev(apiResponse);

        return resp as types.Model;
      });
    }
  }

  /**
   * Deletes a tuned model by its name.
   *
   * @param params - The parameters for deleting the model.
   * @return The response from the API.
   *
   * @example
   * ```ts
   * const response = await ai.models.delete({model: 'tuned-model-name'});
   * ```
   */
  async delete(
    params: types.DeleteModelParameters,
  ): Promise<types.DeleteModelResponse> {
    let response: Promise<types.DeleteModelResponse>;

    let path: string = '';
    let queryParams: Record<string, string> = {};
    if (this.apiClient.isVertexAI()) {
      const body = converters.deleteModelParametersToVertex(
        this.apiClient,
        params,
      );
      path = common.formatMap(
        '{name}',
        body['_url'] as Record<string, unknown>,
      );
      queryParams = body['_query'] as Record<string, string>;
      delete body['config'];
      delete body['_url'];
      delete body['_query'];

      response = this.apiClient
        .request({
          path: path,
          queryParams: queryParams,
          body: JSON.stringify(body),
          httpMethod: 'DELETE',
          httpOptions: params.config?.httpOptions,
          abortSignal: params.config?.abortSignal,
        })
        .then((httpResponse) => {
          return httpResponse.json();
        }) as Promise<types.DeleteModelResponse>;

      return response.then(() => {
        const resp = converters.deleteModelResponseFromVertex();
        const typedResp = new types.DeleteModelResponse();
        Object.assign(typedResp, resp);
        return typedResp;
      });
    } else {
      const body = converters.deleteModelParametersToMldev(
        this.apiClient,
        params,
      );
      path = common.formatMap(
        '{name}',
        body['_url'] as Record<string, unknown>,
      );
      queryParams = body['_query'] as Record<string, string>;
      delete body['config'];
      delete body['_url'];
      delete body['_query'];

      response = this.apiClient
        .request({
          path: path,
          queryParams: queryParams,
          body: JSON.stringify(body),
          httpMethod: 'DELETE',
          httpOptions: params.config?.httpOptions,
          abortSignal: params.config?.abortSignal,
        })
        .then((httpResponse) => {
          return httpResponse.json();
        }) as Promise<types.DeleteModelResponse>;

      return response.then(() => {
        const resp = converters.deleteModelResponseFromMldev();
        const typedResp = new types.DeleteModelResponse();
        Object.assign(typedResp, resp);
        return typedResp;
      });
    }
  }

  /**
   * Counts the number of tokens in the given contents. Multimodal input is
   * supported for Gemini models.
   *
   * @param params - The parameters for counting tokens.
   * @return The response from the API.
   *
   * @example
   * ```ts
   * const response = await ai.models.countTokens({
   *  model: 'gemini-2.0-flash',
   *  contents: 'The quick brown fox jumps over the lazy dog.'
   * });
   * console.log(response);
   * ```
   */
  async countTokens(
    params: types.CountTokensParameters,
  ): Promise<types.CountTokensResponse> {
    let response: Promise<types.CountTokensResponse>;

    let path: string = '';
    let queryParams: Record<string, string> = {};
    if (this.apiClient.isVertexAI()) {
      const body = converters.countTokensParametersToVertex(
        this.apiClient,
        params,
      );
      path = common.formatMap(
        '{model}:countTokens',
        body['_url'] as Record<string, unknown>,
      );
      queryParams = body['_query'] as Record<string, string>;
      delete body['config'];
      delete body['_url'];
      delete body['_query'];

      response = this.apiClient
        .request({
          path: path,
          queryParams: queryParams,
          body: JSON.stringify(body),
          httpMethod: 'POST',
          httpOptions: params.config?.httpOptions,
          abortSignal: params.config?.abortSignal,
        })
        .then((httpResponse) => {
          return httpResponse.json().then((jsonResponse) => {
            const response = jsonResponse as types.CountTokensResponse;
            response.sdkHttpResponse = {
              headers: httpResponse.headers,
            } as types.HttpResponse;
            return response;
          });
        }) as Promise<types.CountTokensResponse>;

      return response.then((apiResponse) => {
        const resp = converters.countTokensResponseFromVertex(apiResponse);
        const typedResp = new types.CountTokensResponse();
        Object.assign(typedResp, resp);
        return typedResp;
      });
    } else {
      const body = converters.countTokensParametersToMldev(
        this.apiClient,
        params,
      );
      path = common.formatMap(
        '{model}:countTokens',
        body['_url'] as Record<string, unknown>,
      );
      queryParams = body['_query'] as Record<string, string>;
      delete body['config'];
      delete body['_url'];
      delete body['_query'];

      response = this.apiClient
        .request({
          path: path,
          queryParams: queryParams,
          body: JSON.stringify(body),
          httpMethod: 'POST',
          httpOptions: params.config?.httpOptions,
          abortSignal: params.config?.abortSignal,
        })
        .then((httpResponse) => {
          return httpResponse.json().then((jsonResponse) => {
            const response = jsonResponse as types.CountTokensResponse;
            response.sdkHttpResponse = {
              headers: httpResponse.headers,
            } as types.HttpResponse;
            return response;
          });
        }) as Promise<types.CountTokensResponse>;

      return response.then((apiResponse) => {
        const resp = converters.countTokensResponseFromMldev(apiResponse);
        const typedResp = new types.CountTokensResponse();
        Object.assign(typedResp, resp);
        return typedResp;
      });
    }
  }

  /**
   * Given a list of contents, returns a corresponding TokensInfo containing
   * the list of tokens and list of token ids.
   *
   * This method is not supported by the Gemini Developer API.
   *
   * @param params - The parameters for computing tokens.
   * @return The response from the API.
   *
   * @example
   * ```ts
   * const response = await ai.models.computeTokens({
   *  model: 'gemini-2.0-flash',
   *  contents: 'What is your name?'
   * });
   * console.log(response);
   * ```
   */
  async computeTokens(
    params: types.ComputeTokensParameters,
  ): Promise<types.ComputeTokensResponse> {
    let response: Promise<types.ComputeTokensResponse>;

    let path: string = '';
    let queryParams: Record<string, string> = {};
    if (this.apiClient.isVertexAI()) {
      const body = converters.computeTokensParametersToVertex(
        this.apiClient,
        params,
      );
      path = common.formatMap(
        '{model}:computeTokens',
        body['_url'] as Record<string, unknown>,
      );
      queryParams = body['_query'] as Record<string, string>;
      delete body['config'];
      delete body['_url'];
      delete body['_query'];

      response = this.apiClient
        .request({
          path: path,
          queryParams: queryParams,
          body: JSON.stringify(body),
          httpMethod: 'POST',
          httpOptions: params.config?.httpOptions,
          abortSignal: params.config?.abortSignal,
        })
        .then((httpResponse) => {
          return httpResponse.json().then((jsonResponse) => {
            const response = jsonResponse as types.ComputeTokensResponse;
            response.sdkHttpResponse = {
              headers: httpResponse.headers,
            } as types.HttpResponse;
            return response;
          });
        }) as Promise<types.ComputeTokensResponse>;

      return response.then((apiResponse) => {
        const resp = converters.computeTokensResponseFromVertex(apiResponse);
        const typedResp = new types.ComputeTokensResponse();
        Object.assign(typedResp, resp);
        return typedResp;
      });
    } else {
      throw new Error('This method is only supported by the Vertex AI.');
    }
  }

  /**
   *  Generates videos based on a text description and configuration.
   *
   * @param params - The parameters for generating videos.
   * @return A Promise<GenerateVideosOperation> which allows you to track the progress and eventually retrieve the generated videos using the operations.get method.
   *
   * @example
   * ```ts
   * const operation = await ai.models.generateVideos({
   *  model: 'veo-2.0-generate-001',
   *  prompt: 'A neon hologram of a cat driving at top speed',
   *  config: {
   *    numberOfVideos: 1
   * });
   *
   * while (!operation.done) {
   *   await new Promise(resolve => setTimeout(resolve, 10000));
   *   operation = await ai.operations.getVideosOperation({operation: operation});
   * }
   *
   * console.log(operation.response?.generatedVideos?.[0]?.video?.uri);
   * ```
   */

  private async generateVideosInternal(
    params: types.GenerateVideosParameters,
  ): Promise<types.GenerateVideosOperation> {
    let response: Promise<types.GenerateVideosOperation>;

    let path: string = '';
    let queryParams: Record<string, string> = {};
    if (this.apiClient.isVertexAI()) {
      const body = converters.generateVideosParametersToVertex(
        this.apiClient,
        params,
      );
      path = common.formatMap(
        '{model}:predictLongRunning',
        body['_url'] as Record<string, unknown>,
      );
      queryParams = body['_query'] as Record<string, string>;
      delete body['config'];
      delete body['_url'];
      delete body['_query'];

      response = this.apiClient
        .request({
          path: path,
          queryParams: queryParams,
          body: JSON.stringify(body),
          httpMethod: 'POST',
          httpOptions: params.config?.httpOptions,
          abortSignal: params.config?.abortSignal,
        })
        .then((httpResponse) => {
          return httpResponse.json();
        }) as Promise<types.GenerateVideosOperation>;

      return response.then((apiResponse) => {
        const resp = converters.generateVideosOperationFromVertex(apiResponse);
        const typedResp = new types.GenerateVideosOperation();
        Object.assign(typedResp, resp);
        return typedResp;
      });
    } else {
      const body = converters.generateVideosParametersToMldev(
        this.apiClient,
        params,
      );
      path = common.formatMap(
        '{model}:predictLongRunning',
        body['_url'] as Record<string, unknown>,
      );
      queryParams = body['_query'] as Record<string, string>;
      delete body['config'];
      delete body['_url'];
      delete body['_query'];

      response = this.apiClient
        .request({
          path: path,
          queryParams: queryParams,
          body: JSON.stringify(body),
          httpMethod: 'POST',
          httpOptions: params.config?.httpOptions,
          abortSignal: params.config?.abortSignal,
        })
        .then((httpResponse) => {
          return httpResponse.json();
        }) as Promise<types.GenerateVideosOperation>;

      return response.then((apiResponse) => {
        const resp = converters.generateVideosOperationFromMldev(apiResponse);
        const typedResp = new types.GenerateVideosOperation();
        Object.assign(typedResp, resp);
        return typedResp;
      });
    }
  }
}
