{"include": ["src", "tests", "examples"], "exclude": ["dist/src/internal/detect-platform.ts"], "compilerOptions": {"target": "es2020", "lib": ["es2020"], "module": "commonjs", "moduleResolution": "node", "esModuleInterop": true, "baseUrl": "./", "paths": {"@anthropic-ai/bedrock-sdk/*": ["src/*"], "@anthropic-ai/bedrock-sdk": ["src/index.ts"]}, "noEmit": true, "resolveJsonModule": true, "forceConsistentCasingInFileNames": true, "strict": true, "noImplicitAny": true, "strictNullChecks": true, "strictFunctionTypes": true, "strictBindCallApply": true, "strictPropertyInitialization": true, "noImplicitThis": true, "noImplicitReturns": true, "alwaysStrict": true, "exactOptionalPropertyTypes": true, "noUncheckedIndexedAccess": true, "noImplicitOverride": true, "noPropertyAccessFromIndexSignature": true, "skipLibCheck": true}}