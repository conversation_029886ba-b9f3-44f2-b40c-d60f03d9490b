{"version": 3, "sources": ["../../../../node_modules/.pnpm/fast-content-type-parse@3.0.0/node_modules/fast-content-type-parse/index.js", "../../../../node_modules/.pnpm/universal-user-agent@7.0.3/node_modules/universal-user-agent/index.js", "../../../../node_modules/.pnpm/before-after-hook@4.0.0/node_modules/before-after-hook/lib/register.js", "../../../../node_modules/.pnpm/before-after-hook@4.0.0/node_modules/before-after-hook/lib/add.js", "../../../../node_modules/.pnpm/before-after-hook@4.0.0/node_modules/before-after-hook/lib/remove.js", "../../../../node_modules/.pnpm/before-after-hook@4.0.0/node_modules/before-after-hook/index.js", "../../../../node_modules/.pnpm/@octokit+endpoint@11.0.0/node_modules/@octokit/endpoint/dist-bundle/index.js", "../../../../node_modules/.pnpm/@octokit+request@10.0.3/node_modules/@octokit/request/dist-bundle/index.js", "../../../../node_modules/.pnpm/@octokit+request-error@7.0.0/node_modules/@octokit/request-error/dist-src/index.js", "../../../../node_modules/.pnpm/@octokit+graphql@9.0.1/node_modules/@octokit/graphql/dist-bundle/index.js", "../../../../node_modules/.pnpm/@octokit+auth-token@6.0.0/node_modules/@octokit/auth-token/dist-bundle/index.js", "../../../../node_modules/.pnpm/@octokit+core@7.0.3/node_modules/@octokit/core/dist-src/version.js", "../../../../node_modules/.pnpm/@octokit+core@7.0.3/node_modules/@octokit/core/dist-src/index.js", "../../../../node_modules/.pnpm/@octokit+plugin-request-log@6.0.0_@octokit+core@7.0.3/node_modules/@octokit/plugin-request-log/dist-src/version.js", "../../../../node_modules/.pnpm/@octokit+plugin-request-log@6.0.0_@octokit+core@7.0.3/node_modules/@octokit/plugin-request-log/dist-src/index.js", "../../../../node_modules/.pnpm/@octokit+plugin-paginate-rest@13.1.1_@octokit+core@7.0.3/node_modules/@octokit/plugin-paginate-rest/dist-bundle/index.js", "../../../../node_modules/.pnpm/@octokit+plugin-rest-endpoi_2593fe8d39b69713cb6e3cebbdaed9cc/node_modules/@octokit/src/version.ts", "../../../../node_modules/.pnpm/@octokit+plugin-rest-endpoi_2593fe8d39b69713cb6e3cebbdaed9cc/node_modules/@octokit/src/generated/endpoints.ts", "../../../../node_modules/.pnpm/@octokit+plugin-rest-endpoi_2593fe8d39b69713cb6e3cebbdaed9cc/node_modules/@octokit/src/endpoints-to-methods.ts", "../../../../node_modules/.pnpm/@octokit+plugin-rest-endpoi_2593fe8d39b69713cb6e3cebbdaed9cc/node_modules/@octokit/src/index.ts", "../../../../node_modules/.pnpm/@octokit+rest@22.0.0/node_modules/@octokit/rest/dist-src/version.js", "../../../../node_modules/.pnpm/@octokit+rest@22.0.0/node_modules/@octokit/rest/dist-src/index.js"], "sourcesContent": ["'use strict'\n\nconst NullObject = function NullObject () { }\nNullObject.prototype = Object.create(null)\n\n/**\n * RegExp to match *( \";\" parameter ) in RFC 7231 sec 3.1.1.1\n *\n * parameter     = token \"=\" ( token / quoted-string )\n * token         = 1*tchar\n * tchar         = \"!\" / \"#\" / \"$\" / \"%\" / \"&\" / \"'\" / \"*\"\n *               / \"+\" / \"-\" / \".\" / \"^\" / \"_\" / \"`\" / \"|\" / \"~\"\n *               / DIGIT / ALPHA\n *               ; any VCHAR, except delimiters\n * quoted-string = DQUOTE *( qdtext / quoted-pair ) DQUOTE\n * qdtext        = HTAB / SP / %x21 / %x23-5B / %x5D-7E / obs-text\n * obs-text      = %x80-FF\n * quoted-pair   = \"\\\" ( HTAB / SP / VCHAR / obs-text )\n */\nconst paramRE = /; *([!#$%&'*+.^\\w`|~-]+)=(\"(?:[\\v\\u0020\\u0021\\u0023-\\u005b\\u005d-\\u007e\\u0080-\\u00ff]|\\\\[\\v\\u0020-\\u00ff])*\"|[!#$%&'*+.^\\w`|~-]+) */gu\n\n/**\n * RegExp to match quoted-pair in RFC 7230 sec 3.2.6\n *\n * quoted-pair = \"\\\" ( HTAB / SP / VCHAR / obs-text )\n * obs-text    = %x80-FF\n */\nconst quotedPairRE = /\\\\([\\v\\u0020-\\u00ff])/gu\n\n/**\n * RegExp to match type in RFC 7231 sec 3.1.1.1\n *\n * media-type = type \"/\" subtype\n * type       = token\n * subtype    = token\n */\nconst mediaTypeRE = /^[!#$%&'*+.^\\w|~-]+\\/[!#$%&'*+.^\\w|~-]+$/u\n\n// default ContentType to prevent repeated object creation\nconst defaultContentType = { type: '', parameters: new NullObject() }\nObject.freeze(defaultContentType.parameters)\nObject.freeze(defaultContentType)\n\n/**\n * Parse media type to object.\n *\n * @param {string|object} header\n * @return {Object}\n * @public\n */\n\nfunction parse (header) {\n  if (typeof header !== 'string') {\n    throw new TypeError('argument header is required and must be a string')\n  }\n\n  let index = header.indexOf(';')\n  const type = index !== -1\n    ? header.slice(0, index).trim()\n    : header.trim()\n\n  if (mediaTypeRE.test(type) === false) {\n    throw new TypeError('invalid media type')\n  }\n\n  const result = {\n    type: type.toLowerCase(),\n    parameters: new NullObject()\n  }\n\n  // parse parameters\n  if (index === -1) {\n    return result\n  }\n\n  let key\n  let match\n  let value\n\n  paramRE.lastIndex = index\n\n  while ((match = paramRE.exec(header))) {\n    if (match.index !== index) {\n      throw new TypeError('invalid parameter format')\n    }\n\n    index += match[0].length\n    key = match[1].toLowerCase()\n    value = match[2]\n\n    if (value[0] === '\"') {\n      // remove quotes and escapes\n      value = value\n        .slice(1, value.length - 1)\n\n      quotedPairRE.test(value) && (value = value.replace(quotedPairRE, '$1'))\n    }\n\n    result.parameters[key] = value\n  }\n\n  if (index !== header.length) {\n    throw new TypeError('invalid parameter format')\n  }\n\n  return result\n}\n\nfunction safeParse (header) {\n  if (typeof header !== 'string') {\n    return defaultContentType\n  }\n\n  let index = header.indexOf(';')\n  const type = index !== -1\n    ? header.slice(0, index).trim()\n    : header.trim()\n\n  if (mediaTypeRE.test(type) === false) {\n    return defaultContentType\n  }\n\n  const result = {\n    type: type.toLowerCase(),\n    parameters: new NullObject()\n  }\n\n  // parse parameters\n  if (index === -1) {\n    return result\n  }\n\n  let key\n  let match\n  let value\n\n  paramRE.lastIndex = index\n\n  while ((match = paramRE.exec(header))) {\n    if (match.index !== index) {\n      return defaultContentType\n    }\n\n    index += match[0].length\n    key = match[1].toLowerCase()\n    value = match[2]\n\n    if (value[0] === '\"') {\n      // remove quotes and escapes\n      value = value\n        .slice(1, value.length - 1)\n\n      quotedPairRE.test(value) && (value = value.replace(quotedPairRE, '$1'))\n    }\n\n    result.parameters[key] = value\n  }\n\n  if (index !== header.length) {\n    return defaultContentType\n  }\n\n  return result\n}\n\nmodule.exports.default = { parse, safeParse }\nmodule.exports.parse = parse\nmodule.exports.safeParse = safeParse\nmodule.exports.defaultContentType = defaultContentType\n", "export function getUserAgent() {\n  if (typeof navigator === \"object\" && \"userAgent\" in navigator) {\n    return navigator.userAgent;\n  }\n\n  if (typeof process === \"object\" && process.version !== undefined) {\n    return `Node.js/${process.version.substr(1)} (${process.platform}; ${\n      process.arch\n    })`;\n  }\n\n  return \"<environment undetectable>\";\n}\n", "// @ts-check\n\nexport function register(state, name, method, options) {\n  if (typeof method !== \"function\") {\n    throw new Error(\"method for before hook must be a function\");\n  }\n\n  if (!options) {\n    options = {};\n  }\n\n  if (Array.isArray(name)) {\n    return name.reverse().reduce((callback, name) => {\n      return register.bind(null, state, name, callback, options);\n    }, method)();\n  }\n\n  return Promise.resolve().then(() => {\n    if (!state.registry[name]) {\n      return method(options);\n    }\n\n    return state.registry[name].reduce((method, registered) => {\n      return registered.hook.bind(null, method, options);\n    }, method)();\n  });\n}\n", "// @ts-check\n\nexport function addHook(state, kind, name, hook) {\n  const orig = hook;\n  if (!state.registry[name]) {\n    state.registry[name] = [];\n  }\n\n  if (kind === \"before\") {\n    hook = (method, options) => {\n      return Promise.resolve()\n        .then(orig.bind(null, options))\n        .then(method.bind(null, options));\n    };\n  }\n\n  if (kind === \"after\") {\n    hook = (method, options) => {\n      let result;\n      return Promise.resolve()\n        .then(method.bind(null, options))\n        .then((result_) => {\n          result = result_;\n          return orig(result, options);\n        })\n        .then(() => {\n          return result;\n        });\n    };\n  }\n\n  if (kind === \"error\") {\n    hook = (method, options) => {\n      return Promise.resolve()\n        .then(method.bind(null, options))\n        .catch((error) => {\n          return orig(error, options);\n        });\n    };\n  }\n\n  state.registry[name].push({\n    hook: hook,\n    orig: orig,\n  });\n}\n", "// @ts-check\n\nexport function removeHook(state, name, method) {\n  if (!state.registry[name]) {\n    return;\n  }\n\n  const index = state.registry[name]\n    .map((registered) => {\n      return registered.orig;\n    })\n    .indexOf(method);\n\n  if (index === -1) {\n    return;\n  }\n\n  state.registry[name].splice(index, 1);\n}\n", "// @ts-check\n\nimport { register } from \"./lib/register.js\";\nimport { addHook } from \"./lib/add.js\";\nimport { removeHook } from \"./lib/remove.js\";\n\n// bind with array of arguments: https://stackoverflow.com/a/21792913\nconst bind = Function.bind;\nconst bindable = bind.bind(bind);\n\nfunction bindApi(hook, state, name) {\n  const removeHookRef = bindable(removeHook, null).apply(\n    null,\n    name ? [state, name] : [state]\n  );\n  hook.api = { remove: removeHookRef };\n  hook.remove = removeHookRef;\n  [\"before\", \"error\", \"after\", \"wrap\"].forEach((kind) => {\n    const args = name ? [state, kind, name] : [state, kind];\n    hook[kind] = hook.api[kind] = bindable(addHook, null).apply(null, args);\n  });\n}\n\nfunction Singular() {\n  const singularHookName = Symbol(\"Singular\");\n  const singularHookState = {\n    registry: {},\n  };\n  const singularHook = register.bind(null, singularHookState, singularHookName);\n  bindApi(singularHook, singularHookState, singularHookName);\n  return singularHook;\n}\n\nfunction Collection() {\n  const state = {\n    registry: {},\n  };\n\n  const hook = register.bind(null, state);\n  bindApi(hook, state);\n\n  return hook;\n}\n\nexport default { Singular, Collection };\n", "// pkg/dist-src/defaults.js\nimport { getUserAgent } from \"universal-user-agent\";\n\n// pkg/dist-src/version.js\nvar VERSION = \"0.0.0-development\";\n\n// pkg/dist-src/defaults.js\nvar userAgent = `octokit-endpoint.js/${VERSION} ${getUserAgent()}`;\nvar DEFAULTS = {\n  method: \"GET\",\n  baseUrl: \"https://api.github.com\",\n  headers: {\n    accept: \"application/vnd.github.v3+json\",\n    \"user-agent\": userAgent\n  },\n  mediaType: {\n    format: \"\"\n  }\n};\n\n// pkg/dist-src/util/lowercase-keys.js\nfunction lowercaseKeys(object) {\n  if (!object) {\n    return {};\n  }\n  return Object.keys(object).reduce((newObj, key) => {\n    newObj[key.toLowerCase()] = object[key];\n    return newObj;\n  }, {});\n}\n\n// pkg/dist-src/util/is-plain-object.js\nfunction isPlainObject(value) {\n  if (typeof value !== \"object\" || value === null) return false;\n  if (Object.prototype.toString.call(value) !== \"[object Object]\") return false;\n  const proto = Object.getPrototypeOf(value);\n  if (proto === null) return true;\n  const Ctor = Object.prototype.hasOwnProperty.call(proto, \"constructor\") && proto.constructor;\n  return typeof Ctor === \"function\" && Ctor instanceof Ctor && Function.prototype.call(Ctor) === Function.prototype.call(value);\n}\n\n// pkg/dist-src/util/merge-deep.js\nfunction mergeDeep(defaults, options) {\n  const result = Object.assign({}, defaults);\n  Object.keys(options).forEach((key) => {\n    if (isPlainObject(options[key])) {\n      if (!(key in defaults)) Object.assign(result, { [key]: options[key] });\n      else result[key] = mergeDeep(defaults[key], options[key]);\n    } else {\n      Object.assign(result, { [key]: options[key] });\n    }\n  });\n  return result;\n}\n\n// pkg/dist-src/util/remove-undefined-properties.js\nfunction removeUndefinedProperties(obj) {\n  for (const key in obj) {\n    if (obj[key] === void 0) {\n      delete obj[key];\n    }\n  }\n  return obj;\n}\n\n// pkg/dist-src/merge.js\nfunction merge(defaults, route, options) {\n  if (typeof route === \"string\") {\n    let [method, url] = route.split(\" \");\n    options = Object.assign(url ? { method, url } : { url: method }, options);\n  } else {\n    options = Object.assign({}, route);\n  }\n  options.headers = lowercaseKeys(options.headers);\n  removeUndefinedProperties(options);\n  removeUndefinedProperties(options.headers);\n  const mergedOptions = mergeDeep(defaults || {}, options);\n  if (options.url === \"/graphql\") {\n    if (defaults && defaults.mediaType.previews?.length) {\n      mergedOptions.mediaType.previews = defaults.mediaType.previews.filter(\n        (preview) => !mergedOptions.mediaType.previews.includes(preview)\n      ).concat(mergedOptions.mediaType.previews);\n    }\n    mergedOptions.mediaType.previews = (mergedOptions.mediaType.previews || []).map((preview) => preview.replace(/-preview/, \"\"));\n  }\n  return mergedOptions;\n}\n\n// pkg/dist-src/util/add-query-parameters.js\nfunction addQueryParameters(url, parameters) {\n  const separator = /\\?/.test(url) ? \"&\" : \"?\";\n  const names = Object.keys(parameters);\n  if (names.length === 0) {\n    return url;\n  }\n  return url + separator + names.map((name) => {\n    if (name === \"q\") {\n      return \"q=\" + parameters.q.split(\"+\").map(encodeURIComponent).join(\"+\");\n    }\n    return `${name}=${encodeURIComponent(parameters[name])}`;\n  }).join(\"&\");\n}\n\n// pkg/dist-src/util/extract-url-variable-names.js\nvar urlVariableRegex = /\\{[^{}}]+\\}/g;\nfunction removeNonChars(variableName) {\n  return variableName.replace(/(?:^\\W+)|(?:(?<!\\W)\\W+$)/g, \"\").split(/,/);\n}\nfunction extractUrlVariableNames(url) {\n  const matches = url.match(urlVariableRegex);\n  if (!matches) {\n    return [];\n  }\n  return matches.map(removeNonChars).reduce((a, b) => a.concat(b), []);\n}\n\n// pkg/dist-src/util/omit.js\nfunction omit(object, keysToOmit) {\n  const result = { __proto__: null };\n  for (const key of Object.keys(object)) {\n    if (keysToOmit.indexOf(key) === -1) {\n      result[key] = object[key];\n    }\n  }\n  return result;\n}\n\n// pkg/dist-src/util/url-template.js\nfunction encodeReserved(str) {\n  return str.split(/(%[0-9A-Fa-f]{2})/g).map(function(part) {\n    if (!/%[0-9A-Fa-f]/.test(part)) {\n      part = encodeURI(part).replace(/%5B/g, \"[\").replace(/%5D/g, \"]\");\n    }\n    return part;\n  }).join(\"\");\n}\nfunction encodeUnreserved(str) {\n  return encodeURIComponent(str).replace(/[!'()*]/g, function(c) {\n    return \"%\" + c.charCodeAt(0).toString(16).toUpperCase();\n  });\n}\nfunction encodeValue(operator, value, key) {\n  value = operator === \"+\" || operator === \"#\" ? encodeReserved(value) : encodeUnreserved(value);\n  if (key) {\n    return encodeUnreserved(key) + \"=\" + value;\n  } else {\n    return value;\n  }\n}\nfunction isDefined(value) {\n  return value !== void 0 && value !== null;\n}\nfunction isKeyOperator(operator) {\n  return operator === \";\" || operator === \"&\" || operator === \"?\";\n}\nfunction getValues(context, operator, key, modifier) {\n  var value = context[key], result = [];\n  if (isDefined(value) && value !== \"\") {\n    if (typeof value === \"string\" || typeof value === \"number\" || typeof value === \"boolean\") {\n      value = value.toString();\n      if (modifier && modifier !== \"*\") {\n        value = value.substring(0, parseInt(modifier, 10));\n      }\n      result.push(\n        encodeValue(operator, value, isKeyOperator(operator) ? key : \"\")\n      );\n    } else {\n      if (modifier === \"*\") {\n        if (Array.isArray(value)) {\n          value.filter(isDefined).forEach(function(value2) {\n            result.push(\n              encodeValue(operator, value2, isKeyOperator(operator) ? key : \"\")\n            );\n          });\n        } else {\n          Object.keys(value).forEach(function(k) {\n            if (isDefined(value[k])) {\n              result.push(encodeValue(operator, value[k], k));\n            }\n          });\n        }\n      } else {\n        const tmp = [];\n        if (Array.isArray(value)) {\n          value.filter(isDefined).forEach(function(value2) {\n            tmp.push(encodeValue(operator, value2));\n          });\n        } else {\n          Object.keys(value).forEach(function(k) {\n            if (isDefined(value[k])) {\n              tmp.push(encodeUnreserved(k));\n              tmp.push(encodeValue(operator, value[k].toString()));\n            }\n          });\n        }\n        if (isKeyOperator(operator)) {\n          result.push(encodeUnreserved(key) + \"=\" + tmp.join(\",\"));\n        } else if (tmp.length !== 0) {\n          result.push(tmp.join(\",\"));\n        }\n      }\n    }\n  } else {\n    if (operator === \";\") {\n      if (isDefined(value)) {\n        result.push(encodeUnreserved(key));\n      }\n    } else if (value === \"\" && (operator === \"&\" || operator === \"?\")) {\n      result.push(encodeUnreserved(key) + \"=\");\n    } else if (value === \"\") {\n      result.push(\"\");\n    }\n  }\n  return result;\n}\nfunction parseUrl(template) {\n  return {\n    expand: expand.bind(null, template)\n  };\n}\nfunction expand(template, context) {\n  var operators = [\"+\", \"#\", \".\", \"/\", \";\", \"?\", \"&\"];\n  template = template.replace(\n    /\\{([^\\{\\}]+)\\}|([^\\{\\}]+)/g,\n    function(_, expression, literal) {\n      if (expression) {\n        let operator = \"\";\n        const values = [];\n        if (operators.indexOf(expression.charAt(0)) !== -1) {\n          operator = expression.charAt(0);\n          expression = expression.substr(1);\n        }\n        expression.split(/,/g).forEach(function(variable) {\n          var tmp = /([^:\\*]*)(?::(\\d+)|(\\*))?/.exec(variable);\n          values.push(getValues(context, operator, tmp[1], tmp[2] || tmp[3]));\n        });\n        if (operator && operator !== \"+\") {\n          var separator = \",\";\n          if (operator === \"?\") {\n            separator = \"&\";\n          } else if (operator !== \"#\") {\n            separator = operator;\n          }\n          return (values.length !== 0 ? operator : \"\") + values.join(separator);\n        } else {\n          return values.join(\",\");\n        }\n      } else {\n        return encodeReserved(literal);\n      }\n    }\n  );\n  if (template === \"/\") {\n    return template;\n  } else {\n    return template.replace(/\\/$/, \"\");\n  }\n}\n\n// pkg/dist-src/parse.js\nfunction parse(options) {\n  let method = options.method.toUpperCase();\n  let url = (options.url || \"/\").replace(/:([a-z]\\w+)/g, \"{$1}\");\n  let headers = Object.assign({}, options.headers);\n  let body;\n  let parameters = omit(options, [\n    \"method\",\n    \"baseUrl\",\n    \"url\",\n    \"headers\",\n    \"request\",\n    \"mediaType\"\n  ]);\n  const urlVariableNames = extractUrlVariableNames(url);\n  url = parseUrl(url).expand(parameters);\n  if (!/^http/.test(url)) {\n    url = options.baseUrl + url;\n  }\n  const omittedParameters = Object.keys(options).filter((option) => urlVariableNames.includes(option)).concat(\"baseUrl\");\n  const remainingParameters = omit(parameters, omittedParameters);\n  const isBinaryRequest = /application\\/octet-stream/i.test(headers.accept);\n  if (!isBinaryRequest) {\n    if (options.mediaType.format) {\n      headers.accept = headers.accept.split(/,/).map(\n        (format) => format.replace(\n          /application\\/vnd(\\.\\w+)(\\.v3)?(\\.\\w+)?(\\+json)?$/,\n          `application/vnd$1$2.${options.mediaType.format}`\n        )\n      ).join(\",\");\n    }\n    if (url.endsWith(\"/graphql\")) {\n      if (options.mediaType.previews?.length) {\n        const previewsFromAcceptHeader = headers.accept.match(/(?<![\\w-])[\\w-]+(?=-preview)/g) || [];\n        headers.accept = previewsFromAcceptHeader.concat(options.mediaType.previews).map((preview) => {\n          const format = options.mediaType.format ? `.${options.mediaType.format}` : \"+json\";\n          return `application/vnd.github.${preview}-preview${format}`;\n        }).join(\",\");\n      }\n    }\n  }\n  if ([\"GET\", \"HEAD\"].includes(method)) {\n    url = addQueryParameters(url, remainingParameters);\n  } else {\n    if (\"data\" in remainingParameters) {\n      body = remainingParameters.data;\n    } else {\n      if (Object.keys(remainingParameters).length) {\n        body = remainingParameters;\n      }\n    }\n  }\n  if (!headers[\"content-type\"] && typeof body !== \"undefined\") {\n    headers[\"content-type\"] = \"application/json; charset=utf-8\";\n  }\n  if ([\"PATCH\", \"PUT\"].includes(method) && typeof body === \"undefined\") {\n    body = \"\";\n  }\n  return Object.assign(\n    { method, url, headers },\n    typeof body !== \"undefined\" ? { body } : null,\n    options.request ? { request: options.request } : null\n  );\n}\n\n// pkg/dist-src/endpoint-with-defaults.js\nfunction endpointWithDefaults(defaults, route, options) {\n  return parse(merge(defaults, route, options));\n}\n\n// pkg/dist-src/with-defaults.js\nfunction withDefaults(oldDefaults, newDefaults) {\n  const DEFAULTS2 = merge(oldDefaults, newDefaults);\n  const endpoint2 = endpointWithDefaults.bind(null, DEFAULTS2);\n  return Object.assign(endpoint2, {\n    DEFAULTS: DEFAULTS2,\n    defaults: withDefaults.bind(null, DEFAULTS2),\n    merge: merge.bind(null, DEFAULTS2),\n    parse\n  });\n}\n\n// pkg/dist-src/index.js\nvar endpoint = withDefaults(null, DEFAULTS);\nexport {\n  endpoint\n};\n", "// pkg/dist-src/index.js\nimport { endpoint } from \"@octokit/endpoint\";\n\n// pkg/dist-src/defaults.js\nimport { getUserAgent } from \"universal-user-agent\";\n\n// pkg/dist-src/version.js\nvar VERSION = \"10.0.3\";\n\n// pkg/dist-src/defaults.js\nvar defaults_default = {\n  headers: {\n    \"user-agent\": `octokit-request.js/${VERSION} ${getUserAgent()}`\n  }\n};\n\n// pkg/dist-src/fetch-wrapper.js\nimport { safeParse } from \"fast-content-type-parse\";\n\n// pkg/dist-src/is-plain-object.js\nfunction isPlainObject(value) {\n  if (typeof value !== \"object\" || value === null) return false;\n  if (Object.prototype.toString.call(value) !== \"[object Object]\") return false;\n  const proto = Object.getPrototypeOf(value);\n  if (proto === null) return true;\n  const Ctor = Object.prototype.hasOwnProperty.call(proto, \"constructor\") && proto.constructor;\n  return typeof Ctor === \"function\" && Ctor instanceof Ctor && Function.prototype.call(Ctor) === Function.prototype.call(value);\n}\n\n// pkg/dist-src/fetch-wrapper.js\nimport { RequestError } from \"@octokit/request-error\";\nasync function fetchWrapper(requestOptions) {\n  const fetch = requestOptions.request?.fetch || globalThis.fetch;\n  if (!fetch) {\n    throw new Error(\n      \"fetch is not set. Please pass a fetch implementation as new Octokit({ request: { fetch }}). Learn more at https://github.com/octokit/octokit.js/#fetch-missing\"\n    );\n  }\n  const log = requestOptions.request?.log || console;\n  const parseSuccessResponseBody = requestOptions.request?.parseSuccessResponseBody !== false;\n  const body = isPlainObject(requestOptions.body) || Array.isArray(requestOptions.body) ? JSON.stringify(requestOptions.body) : requestOptions.body;\n  const requestHeaders = Object.fromEntries(\n    Object.entries(requestOptions.headers).map(([name, value]) => [\n      name,\n      String(value)\n    ])\n  );\n  let fetchResponse;\n  try {\n    fetchResponse = await fetch(requestOptions.url, {\n      method: requestOptions.method,\n      body,\n      redirect: requestOptions.request?.redirect,\n      headers: requestHeaders,\n      signal: requestOptions.request?.signal,\n      // duplex must be set if request.body is ReadableStream or Async Iterables.\n      // See https://fetch.spec.whatwg.org/#dom-requestinit-duplex.\n      ...requestOptions.body && { duplex: \"half\" }\n    });\n  } catch (error) {\n    let message = \"Unknown Error\";\n    if (error instanceof Error) {\n      if (error.name === \"AbortError\") {\n        error.status = 500;\n        throw error;\n      }\n      message = error.message;\n      if (error.name === \"TypeError\" && \"cause\" in error) {\n        if (error.cause instanceof Error) {\n          message = error.cause.message;\n        } else if (typeof error.cause === \"string\") {\n          message = error.cause;\n        }\n      }\n    }\n    const requestError = new RequestError(message, 500, {\n      request: requestOptions\n    });\n    requestError.cause = error;\n    throw requestError;\n  }\n  const status = fetchResponse.status;\n  const url = fetchResponse.url;\n  const responseHeaders = {};\n  for (const [key, value] of fetchResponse.headers) {\n    responseHeaders[key] = value;\n  }\n  const octokitResponse = {\n    url,\n    status,\n    headers: responseHeaders,\n    data: \"\"\n  };\n  if (\"deprecation\" in responseHeaders) {\n    const matches = responseHeaders.link && responseHeaders.link.match(/<([^<>]+)>; rel=\"deprecation\"/);\n    const deprecationLink = matches && matches.pop();\n    log.warn(\n      `[@octokit/request] \"${requestOptions.method} ${requestOptions.url}\" is deprecated. It is scheduled to be removed on ${responseHeaders.sunset}${deprecationLink ? `. See ${deprecationLink}` : \"\"}`\n    );\n  }\n  if (status === 204 || status === 205) {\n    return octokitResponse;\n  }\n  if (requestOptions.method === \"HEAD\") {\n    if (status < 400) {\n      return octokitResponse;\n    }\n    throw new RequestError(fetchResponse.statusText, status, {\n      response: octokitResponse,\n      request: requestOptions\n    });\n  }\n  if (status === 304) {\n    octokitResponse.data = await getResponseData(fetchResponse);\n    throw new RequestError(\"Not modified\", status, {\n      response: octokitResponse,\n      request: requestOptions\n    });\n  }\n  if (status >= 400) {\n    octokitResponse.data = await getResponseData(fetchResponse);\n    throw new RequestError(toErrorMessage(octokitResponse.data), status, {\n      response: octokitResponse,\n      request: requestOptions\n    });\n  }\n  octokitResponse.data = parseSuccessResponseBody ? await getResponseData(fetchResponse) : fetchResponse.body;\n  return octokitResponse;\n}\nasync function getResponseData(response) {\n  const contentType = response.headers.get(\"content-type\");\n  if (!contentType) {\n    return response.text().catch(() => \"\");\n  }\n  const mimetype = safeParse(contentType);\n  if (isJSONResponse(mimetype)) {\n    let text = \"\";\n    try {\n      text = await response.text();\n      return JSON.parse(text);\n    } catch (err) {\n      return text;\n    }\n  } else if (mimetype.type.startsWith(\"text/\") || mimetype.parameters.charset?.toLowerCase() === \"utf-8\") {\n    return response.text().catch(() => \"\");\n  } else {\n    return response.arrayBuffer().catch(() => new ArrayBuffer(0));\n  }\n}\nfunction isJSONResponse(mimetype) {\n  return mimetype.type === \"application/json\" || mimetype.type === \"application/scim+json\";\n}\nfunction toErrorMessage(data) {\n  if (typeof data === \"string\") {\n    return data;\n  }\n  if (data instanceof ArrayBuffer) {\n    return \"Unknown error\";\n  }\n  if (\"message\" in data) {\n    const suffix = \"documentation_url\" in data ? ` - ${data.documentation_url}` : \"\";\n    return Array.isArray(data.errors) ? `${data.message}: ${data.errors.map((v) => JSON.stringify(v)).join(\", \")}${suffix}` : `${data.message}${suffix}`;\n  }\n  return `Unknown error: ${JSON.stringify(data)}`;\n}\n\n// pkg/dist-src/with-defaults.js\nfunction withDefaults(oldEndpoint, newDefaults) {\n  const endpoint2 = oldEndpoint.defaults(newDefaults);\n  const newApi = function(route, parameters) {\n    const endpointOptions = endpoint2.merge(route, parameters);\n    if (!endpointOptions.request || !endpointOptions.request.hook) {\n      return fetchWrapper(endpoint2.parse(endpointOptions));\n    }\n    const request2 = (route2, parameters2) => {\n      return fetchWrapper(\n        endpoint2.parse(endpoint2.merge(route2, parameters2))\n      );\n    };\n    Object.assign(request2, {\n      endpoint: endpoint2,\n      defaults: withDefaults.bind(null, endpoint2)\n    });\n    return endpointOptions.request.hook(request2, endpointOptions);\n  };\n  return Object.assign(newApi, {\n    endpoint: endpoint2,\n    defaults: withDefaults.bind(null, endpoint2)\n  });\n}\n\n// pkg/dist-src/index.js\nvar request = withDefaults(endpoint, defaults_default);\nexport {\n  request\n};\n", "class RequestError extends <PERSON>rror {\n  name;\n  /**\n   * http status code\n   */\n  status;\n  /**\n   * Request options that lead to the error.\n   */\n  request;\n  /**\n   * Response object if a response was received\n   */\n  response;\n  constructor(message, statusCode, options) {\n    super(message);\n    this.name = \"HttpError\";\n    this.status = Number.parseInt(statusCode);\n    if (Number.isNaN(this.status)) {\n      this.status = 0;\n    }\n    if (\"response\" in options) {\n      this.response = options.response;\n    }\n    const requestCopy = Object.assign({}, options.request);\n    if (options.request.headers.authorization) {\n      requestCopy.headers = Object.assign({}, options.request.headers, {\n        authorization: options.request.headers.authorization.replace(\n          /(?<! ) .*$/,\n          \" [REDACTED]\"\n        )\n      });\n    }\n    requestCopy.url = requestCopy.url.replace(/\\bclient_secret=\\w+/g, \"client_secret=[REDACTED]\").replace(/\\baccess_token=\\w+/g, \"access_token=[REDACTED]\");\n    this.request = requestCopy;\n  }\n}\nexport {\n  RequestError\n};\n", "// pkg/dist-src/index.js\nimport { request } from \"@octokit/request\";\nimport { getUserAgent } from \"universal-user-agent\";\n\n// pkg/dist-src/version.js\nvar VERSION = \"0.0.0-development\";\n\n// pkg/dist-src/with-defaults.js\nimport { request as Request2 } from \"@octokit/request\";\n\n// pkg/dist-src/graphql.js\nimport { request as Request } from \"@octokit/request\";\n\n// pkg/dist-src/error.js\nfunction _buildMessageForResponseErrors(data) {\n  return `Request failed due to following response errors:\n` + data.errors.map((e) => ` - ${e.message}`).join(\"\\n\");\n}\nvar GraphqlResponseError = class extends Error {\n  constructor(request2, headers, response) {\n    super(_buildMessageForResponseErrors(response));\n    this.request = request2;\n    this.headers = headers;\n    this.response = response;\n    this.errors = response.errors;\n    this.data = response.data;\n    if (Error.captureStackTrace) {\n      Error.captureStackTrace(this, this.constructor);\n    }\n  }\n  name = \"GraphqlResponseError\";\n  errors;\n  data;\n};\n\n// pkg/dist-src/graphql.js\nvar NON_VARIABLE_OPTIONS = [\n  \"method\",\n  \"baseUrl\",\n  \"url\",\n  \"headers\",\n  \"request\",\n  \"query\",\n  \"mediaType\",\n  \"operationName\"\n];\nvar FORBIDDEN_VARIABLE_OPTIONS = [\"query\", \"method\", \"url\"];\nvar GHES_V3_SUFFIX_REGEX = /\\/api\\/v3\\/?$/;\nfunction graphql(request2, query, options) {\n  if (options) {\n    if (typeof query === \"string\" && \"query\" in options) {\n      return Promise.reject(\n        new Error(`[@octokit/graphql] \"query\" cannot be used as variable name`)\n      );\n    }\n    for (const key in options) {\n      if (!FORBIDDEN_VARIABLE_OPTIONS.includes(key)) continue;\n      return Promise.reject(\n        new Error(\n          `[@octokit/graphql] \"${key}\" cannot be used as variable name`\n        )\n      );\n    }\n  }\n  const parsedOptions = typeof query === \"string\" ? Object.assign({ query }, options) : query;\n  const requestOptions = Object.keys(\n    parsedOptions\n  ).reduce((result, key) => {\n    if (NON_VARIABLE_OPTIONS.includes(key)) {\n      result[key] = parsedOptions[key];\n      return result;\n    }\n    if (!result.variables) {\n      result.variables = {};\n    }\n    result.variables[key] = parsedOptions[key];\n    return result;\n  }, {});\n  const baseUrl = parsedOptions.baseUrl || request2.endpoint.DEFAULTS.baseUrl;\n  if (GHES_V3_SUFFIX_REGEX.test(baseUrl)) {\n    requestOptions.url = baseUrl.replace(GHES_V3_SUFFIX_REGEX, \"/api/graphql\");\n  }\n  return request2(requestOptions).then((response) => {\n    if (response.data.errors) {\n      const headers = {};\n      for (const key of Object.keys(response.headers)) {\n        headers[key] = response.headers[key];\n      }\n      throw new GraphqlResponseError(\n        requestOptions,\n        headers,\n        response.data\n      );\n    }\n    return response.data.data;\n  });\n}\n\n// pkg/dist-src/with-defaults.js\nfunction withDefaults(request2, newDefaults) {\n  const newRequest = request2.defaults(newDefaults);\n  const newApi = (query, options) => {\n    return graphql(newRequest, query, options);\n  };\n  return Object.assign(newApi, {\n    defaults: withDefaults.bind(null, newRequest),\n    endpoint: newRequest.endpoint\n  });\n}\n\n// pkg/dist-src/index.js\nvar graphql2 = withDefaults(request, {\n  headers: {\n    \"user-agent\": `octokit-graphql.js/${VERSION} ${getUserAgent()}`\n  },\n  method: \"POST\",\n  url: \"/graphql\"\n});\nfunction withCustomRequest(customRequest) {\n  return withDefaults(customRequest, {\n    method: \"POST\",\n    url: \"/graphql\"\n  });\n}\nexport {\n  GraphqlResponseError,\n  graphql2 as graphql,\n  withCustomRequest\n};\n", "// pkg/dist-src/is-jwt.js\nvar b64url = \"(?:[a-zA-Z0-9_-]+)\";\nvar sep = \"\\\\.\";\nvar jwtRE = new RegExp(`^${b64url}${sep}${b64url}${sep}${b64url}$`);\nvar isJWT = jwtRE.test.bind(jwtRE);\n\n// pkg/dist-src/auth.js\nasync function auth(token) {\n  const isApp = isJWT(token);\n  const isInstallation = token.startsWith(\"v1.\") || token.startsWith(\"ghs_\");\n  const isUserToServer = token.startsWith(\"ghu_\");\n  const tokenType = isApp ? \"app\" : isInstallation ? \"installation\" : isUserToServer ? \"user-to-server\" : \"oauth\";\n  return {\n    type: \"token\",\n    token,\n    tokenType\n  };\n}\n\n// pkg/dist-src/with-authorization-prefix.js\nfunction withAuthorizationPrefix(token) {\n  if (token.split(/\\./).length === 3) {\n    return `bearer ${token}`;\n  }\n  return `token ${token}`;\n}\n\n// pkg/dist-src/hook.js\nasync function hook(token, request, route, parameters) {\n  const endpoint = request.endpoint.merge(\n    route,\n    parameters\n  );\n  endpoint.headers.authorization = withAuthorizationPrefix(token);\n  return request(endpoint);\n}\n\n// pkg/dist-src/index.js\nvar createTokenAuth = function createTokenAuth2(token) {\n  if (!token) {\n    throw new Error(\"[@octokit/auth-token] No token passed to createTokenAuth\");\n  }\n  if (typeof token !== \"string\") {\n    throw new Error(\n      \"[@octokit/auth-token] Token passed to createTokenAuth is not a string\"\n    );\n  }\n  token = token.replace(/^(token|bearer) +/i, \"\");\n  return Object.assign(auth.bind(null, token), {\n    hook: hook.bind(null, token)\n  });\n};\nexport {\n  createTokenAuth\n};\n", "const VERSION = \"7.0.3\";\nexport {\n  VERSION\n};\n", "import { getUserAgent } from \"universal-user-agent\";\nimport <PERSON> from \"before-after-hook\";\nimport { request } from \"@octokit/request\";\nimport { withCustomRequest } from \"@octokit/graphql\";\nimport { createTokenAuth } from \"@octokit/auth-token\";\nimport { VERSION } from \"./version.js\";\nconst noop = () => {\n};\nconst consoleWarn = console.warn.bind(console);\nconst consoleError = console.error.bind(console);\nfunction createLogger(logger = {}) {\n  if (typeof logger.debug !== \"function\") {\n    logger.debug = noop;\n  }\n  if (typeof logger.info !== \"function\") {\n    logger.info = noop;\n  }\n  if (typeof logger.warn !== \"function\") {\n    logger.warn = consoleWarn;\n  }\n  if (typeof logger.error !== \"function\") {\n    logger.error = consoleError;\n  }\n  return logger;\n}\nconst userAgentTrail = `octokit-core.js/${VERSION} ${getUserAgent()}`;\nclass Octokit {\n  static VERSION = VERSION;\n  static defaults(defaults) {\n    const OctokitWithDefaults = class extends this {\n      constructor(...args) {\n        const options = args[0] || {};\n        if (typeof defaults === \"function\") {\n          super(defaults(options));\n          return;\n        }\n        super(\n          Object.assign(\n            {},\n            defaults,\n            options,\n            options.userAgent && defaults.userAgent ? {\n              userAgent: `${options.userAgent} ${defaults.userAgent}`\n            } : null\n          )\n        );\n      }\n    };\n    return OctokitWithDefaults;\n  }\n  static plugins = [];\n  /**\n   * Attach a plugin (or many) to your Octokit instance.\n   *\n   * @example\n   * const API = Octokit.plugin(plugin1, plugin2, plugin3, ...)\n   */\n  static plugin(...newPlugins) {\n    const currentPlugins = this.plugins;\n    const NewOctokit = class extends this {\n      static plugins = currentPlugins.concat(\n        newPlugins.filter((plugin) => !currentPlugins.includes(plugin))\n      );\n    };\n    return NewOctokit;\n  }\n  constructor(options = {}) {\n    const hook = new Hook.Collection();\n    const requestDefaults = {\n      baseUrl: request.endpoint.DEFAULTS.baseUrl,\n      headers: {},\n      request: Object.assign({}, options.request, {\n        // @ts-ignore internal usage only, no need to type\n        hook: hook.bind(null, \"request\")\n      }),\n      mediaType: {\n        previews: [],\n        format: \"\"\n      }\n    };\n    requestDefaults.headers[\"user-agent\"] = options.userAgent ? `${options.userAgent} ${userAgentTrail}` : userAgentTrail;\n    if (options.baseUrl) {\n      requestDefaults.baseUrl = options.baseUrl;\n    }\n    if (options.previews) {\n      requestDefaults.mediaType.previews = options.previews;\n    }\n    if (options.timeZone) {\n      requestDefaults.headers[\"time-zone\"] = options.timeZone;\n    }\n    this.request = request.defaults(requestDefaults);\n    this.graphql = withCustomRequest(this.request).defaults(requestDefaults);\n    this.log = createLogger(options.log);\n    this.hook = hook;\n    if (!options.authStrategy) {\n      if (!options.auth) {\n        this.auth = async () => ({\n          type: \"unauthenticated\"\n        });\n      } else {\n        const auth = createTokenAuth(options.auth);\n        hook.wrap(\"request\", auth.hook);\n        this.auth = auth;\n      }\n    } else {\n      const { authStrategy, ...otherOptions } = options;\n      const auth = authStrategy(\n        Object.assign(\n          {\n            request: this.request,\n            log: this.log,\n            // we pass the current octokit instance as well as its constructor options\n            // to allow for authentication strategies that return a new octokit instance\n            // that shares the same internal state as the current one. The original\n            // requirement for this was the \"event-octokit\" authentication strategy\n            // of https://github.com/probot/octokit-auth-probot.\n            octokit: this,\n            octokitOptions: otherOptions\n          },\n          options.auth\n        )\n      );\n      hook.wrap(\"request\", auth.hook);\n      this.auth = auth;\n    }\n    const classConstructor = this.constructor;\n    for (let i = 0; i < classConstructor.plugins.length; ++i) {\n      Object.assign(this, classConstructor.plugins[i](this, options));\n    }\n  }\n  // assigned during constructor\n  request;\n  graphql;\n  log;\n  hook;\n  // TODO: type `octokit.auth` based on passed options.authStrategy\n  auth;\n}\nexport {\n  Octokit\n};\n", "const VERSION = \"6.0.0\";\nexport {\n  VERSION\n};\n", "import { VERSION } from \"./version.js\";\nfunction requestLog(octokit) {\n  octokit.hook.wrap(\"request\", (request, options) => {\n    octokit.log.debug(\"request\", options);\n    const start = Date.now();\n    const requestOptions = octokit.request.endpoint.parse(options);\n    const path = requestOptions.url.replace(options.baseUrl, \"\");\n    return request(options).then((response) => {\n      const requestId = response.headers[\"x-github-request-id\"];\n      octokit.log.info(\n        `${requestOptions.method} ${path} - ${response.status} with id ${requestId} in ${Date.now() - start}ms`\n      );\n      return response;\n    }).catch((error) => {\n      const requestId = error.response?.headers[\"x-github-request-id\"] || \"UNKNOWN\";\n      octokit.log.error(\n        `${requestOptions.method} ${path} - ${error.status} with id ${requestId} in ${Date.now() - start}ms`\n      );\n      throw error;\n    });\n  });\n}\nrequestLog.VERSION = VERSION;\nexport {\n  requestLog\n};\n", "// pkg/dist-src/version.js\nvar VERSION = \"0.0.0-development\";\n\n// pkg/dist-src/normalize-paginated-list-response.js\nfunction normalizePaginatedListResponse(response) {\n  if (!response.data) {\n    return {\n      ...response,\n      data: []\n    };\n  }\n  const responseNeedsNormalization = (\"total_count\" in response.data || \"total_commits\" in response.data) && !(\"url\" in response.data);\n  if (!responseNeedsNormalization) return response;\n  const incompleteResults = response.data.incomplete_results;\n  const repositorySelection = response.data.repository_selection;\n  const totalCount = response.data.total_count;\n  const totalCommits = response.data.total_commits;\n  delete response.data.incomplete_results;\n  delete response.data.repository_selection;\n  delete response.data.total_count;\n  delete response.data.total_commits;\n  const namespaceKey = Object.keys(response.data)[0];\n  const data = response.data[namespaceKey];\n  response.data = data;\n  if (typeof incompleteResults !== \"undefined\") {\n    response.data.incomplete_results = incompleteResults;\n  }\n  if (typeof repositorySelection !== \"undefined\") {\n    response.data.repository_selection = repositorySelection;\n  }\n  response.data.total_count = totalCount;\n  response.data.total_commits = totalCommits;\n  return response;\n}\n\n// pkg/dist-src/iterator.js\nfunction iterator(octokit, route, parameters) {\n  const options = typeof route === \"function\" ? route.endpoint(parameters) : octokit.request.endpoint(route, parameters);\n  const requestMethod = typeof route === \"function\" ? route : octokit.request;\n  const method = options.method;\n  const headers = options.headers;\n  let url = options.url;\n  return {\n    [Symbol.asyncIterator]: () => ({\n      async next() {\n        if (!url) return { done: true };\n        try {\n          const response = await requestMethod({ method, url, headers });\n          const normalizedResponse = normalizePaginatedListResponse(response);\n          url = ((normalizedResponse.headers.link || \"\").match(\n            /<([^<>]+)>;\\s*rel=\"next\"/\n          ) || [])[1];\n          if (!url && \"total_commits\" in normalizedResponse.data) {\n            const parsedUrl = new URL(normalizedResponse.url);\n            const params = parsedUrl.searchParams;\n            const page = parseInt(params.get(\"page\") || \"1\", 10);\n            const per_page = parseInt(params.get(\"per_page\") || \"250\", 10);\n            if (page * per_page < normalizedResponse.data.total_commits) {\n              params.set(\"page\", String(page + 1));\n              url = parsedUrl.toString();\n            }\n          }\n          return { value: normalizedResponse };\n        } catch (error) {\n          if (error.status !== 409) throw error;\n          url = \"\";\n          return {\n            value: {\n              status: 200,\n              headers: {},\n              data: []\n            }\n          };\n        }\n      }\n    })\n  };\n}\n\n// pkg/dist-src/paginate.js\nfunction paginate(octokit, route, parameters, mapFn) {\n  if (typeof parameters === \"function\") {\n    mapFn = parameters;\n    parameters = void 0;\n  }\n  return gather(\n    octokit,\n    [],\n    iterator(octokit, route, parameters)[Symbol.asyncIterator](),\n    mapFn\n  );\n}\nfunction gather(octokit, results, iterator2, mapFn) {\n  return iterator2.next().then((result) => {\n    if (result.done) {\n      return results;\n    }\n    let earlyExit = false;\n    function done() {\n      earlyExit = true;\n    }\n    results = results.concat(\n      mapFn ? mapFn(result.value, done) : result.value.data\n    );\n    if (earlyExit) {\n      return results;\n    }\n    return gather(octokit, results, iterator2, mapFn);\n  });\n}\n\n// pkg/dist-src/compose-paginate.js\nvar composePaginateRest = Object.assign(paginate, {\n  iterator\n});\n\n// pkg/dist-src/generated/paginating-endpoints.js\nvar paginatingEndpoints = [\n  \"GET /advisories\",\n  \"GET /app/hook/deliveries\",\n  \"GET /app/installation-requests\",\n  \"GET /app/installations\",\n  \"GET /assignments/{assignment_id}/accepted_assignments\",\n  \"GET /classrooms\",\n  \"GET /classrooms/{classroom_id}/assignments\",\n  \"GET /enterprises/{enterprise}/code-security/configurations\",\n  \"GET /enterprises/{enterprise}/code-security/configurations/{configuration_id}/repositories\",\n  \"GET /enterprises/{enterprise}/dependabot/alerts\",\n  \"GET /enterprises/{enterprise}/secret-scanning/alerts\",\n  \"GET /events\",\n  \"GET /gists\",\n  \"GET /gists/public\",\n  \"GET /gists/starred\",\n  \"GET /gists/{gist_id}/comments\",\n  \"GET /gists/{gist_id}/commits\",\n  \"GET /gists/{gist_id}/forks\",\n  \"GET /installation/repositories\",\n  \"GET /issues\",\n  \"GET /licenses\",\n  \"GET /marketplace_listing/plans\",\n  \"GET /marketplace_listing/plans/{plan_id}/accounts\",\n  \"GET /marketplace_listing/stubbed/plans\",\n  \"GET /marketplace_listing/stubbed/plans/{plan_id}/accounts\",\n  \"GET /networks/{owner}/{repo}/events\",\n  \"GET /notifications\",\n  \"GET /organizations\",\n  \"GET /orgs/{org}/actions/cache/usage-by-repository\",\n  \"GET /orgs/{org}/actions/hosted-runners\",\n  \"GET /orgs/{org}/actions/permissions/repositories\",\n  \"GET /orgs/{org}/actions/runner-groups\",\n  \"GET /orgs/{org}/actions/runner-groups/{runner_group_id}/hosted-runners\",\n  \"GET /orgs/{org}/actions/runner-groups/{runner_group_id}/repositories\",\n  \"GET /orgs/{org}/actions/runner-groups/{runner_group_id}/runners\",\n  \"GET /orgs/{org}/actions/runners\",\n  \"GET /orgs/{org}/actions/secrets\",\n  \"GET /orgs/{org}/actions/secrets/{secret_name}/repositories\",\n  \"GET /orgs/{org}/actions/variables\",\n  \"GET /orgs/{org}/actions/variables/{name}/repositories\",\n  \"GET /orgs/{org}/attestations/{subject_digest}\",\n  \"GET /orgs/{org}/blocks\",\n  \"GET /orgs/{org}/campaigns\",\n  \"GET /orgs/{org}/code-scanning/alerts\",\n  \"GET /orgs/{org}/code-security/configurations\",\n  \"GET /orgs/{org}/code-security/configurations/{configuration_id}/repositories\",\n  \"GET /orgs/{org}/codespaces\",\n  \"GET /orgs/{org}/codespaces/secrets\",\n  \"GET /orgs/{org}/codespaces/secrets/{secret_name}/repositories\",\n  \"GET /orgs/{org}/copilot/billing/seats\",\n  \"GET /orgs/{org}/copilot/metrics\",\n  \"GET /orgs/{org}/dependabot/alerts\",\n  \"GET /orgs/{org}/dependabot/secrets\",\n  \"GET /orgs/{org}/dependabot/secrets/{secret_name}/repositories\",\n  \"GET /orgs/{org}/events\",\n  \"GET /orgs/{org}/failed_invitations\",\n  \"GET /orgs/{org}/hooks\",\n  \"GET /orgs/{org}/hooks/{hook_id}/deliveries\",\n  \"GET /orgs/{org}/insights/api/route-stats/{actor_type}/{actor_id}\",\n  \"GET /orgs/{org}/insights/api/subject-stats\",\n  \"GET /orgs/{org}/insights/api/user-stats/{user_id}\",\n  \"GET /orgs/{org}/installations\",\n  \"GET /orgs/{org}/invitations\",\n  \"GET /orgs/{org}/invitations/{invitation_id}/teams\",\n  \"GET /orgs/{org}/issues\",\n  \"GET /orgs/{org}/members\",\n  \"GET /orgs/{org}/members/{username}/codespaces\",\n  \"GET /orgs/{org}/migrations\",\n  \"GET /orgs/{org}/migrations/{migration_id}/repositories\",\n  \"GET /orgs/{org}/organization-roles/{role_id}/teams\",\n  \"GET /orgs/{org}/organization-roles/{role_id}/users\",\n  \"GET /orgs/{org}/outside_collaborators\",\n  \"GET /orgs/{org}/packages\",\n  \"GET /orgs/{org}/packages/{package_type}/{package_name}/versions\",\n  \"GET /orgs/{org}/personal-access-token-requests\",\n  \"GET /orgs/{org}/personal-access-token-requests/{pat_request_id}/repositories\",\n  \"GET /orgs/{org}/personal-access-tokens\",\n  \"GET /orgs/{org}/personal-access-tokens/{pat_id}/repositories\",\n  \"GET /orgs/{org}/private-registries\",\n  \"GET /orgs/{org}/projects\",\n  \"GET /orgs/{org}/properties/values\",\n  \"GET /orgs/{org}/public_members\",\n  \"GET /orgs/{org}/repos\",\n  \"GET /orgs/{org}/rulesets\",\n  \"GET /orgs/{org}/rulesets/rule-suites\",\n  \"GET /orgs/{org}/rulesets/{ruleset_id}/history\",\n  \"GET /orgs/{org}/secret-scanning/alerts\",\n  \"GET /orgs/{org}/security-advisories\",\n  \"GET /orgs/{org}/settings/network-configurations\",\n  \"GET /orgs/{org}/team/{team_slug}/copilot/metrics\",\n  \"GET /orgs/{org}/teams\",\n  \"GET /orgs/{org}/teams/{team_slug}/discussions\",\n  \"GET /orgs/{org}/teams/{team_slug}/discussions/{discussion_number}/comments\",\n  \"GET /orgs/{org}/teams/{team_slug}/discussions/{discussion_number}/comments/{comment_number}/reactions\",\n  \"GET /orgs/{org}/teams/{team_slug}/discussions/{discussion_number}/reactions\",\n  \"GET /orgs/{org}/teams/{team_slug}/invitations\",\n  \"GET /orgs/{org}/teams/{team_slug}/members\",\n  \"GET /orgs/{org}/teams/{team_slug}/projects\",\n  \"GET /orgs/{org}/teams/{team_slug}/repos\",\n  \"GET /orgs/{org}/teams/{team_slug}/teams\",\n  \"GET /projects/columns/{column_id}/cards\",\n  \"GET /projects/{project_id}/collaborators\",\n  \"GET /projects/{project_id}/columns\",\n  \"GET /repos/{owner}/{repo}/actions/artifacts\",\n  \"GET /repos/{owner}/{repo}/actions/caches\",\n  \"GET /repos/{owner}/{repo}/actions/organization-secrets\",\n  \"GET /repos/{owner}/{repo}/actions/organization-variables\",\n  \"GET /repos/{owner}/{repo}/actions/runners\",\n  \"GET /repos/{owner}/{repo}/actions/runs\",\n  \"GET /repos/{owner}/{repo}/actions/runs/{run_id}/artifacts\",\n  \"GET /repos/{owner}/{repo}/actions/runs/{run_id}/attempts/{attempt_number}/jobs\",\n  \"GET /repos/{owner}/{repo}/actions/runs/{run_id}/jobs\",\n  \"GET /repos/{owner}/{repo}/actions/secrets\",\n  \"GET /repos/{owner}/{repo}/actions/variables\",\n  \"GET /repos/{owner}/{repo}/actions/workflows\",\n  \"GET /repos/{owner}/{repo}/actions/workflows/{workflow_id}/runs\",\n  \"GET /repos/{owner}/{repo}/activity\",\n  \"GET /repos/{owner}/{repo}/assignees\",\n  \"GET /repos/{owner}/{repo}/attestations/{subject_digest}\",\n  \"GET /repos/{owner}/{repo}/branches\",\n  \"GET /repos/{owner}/{repo}/check-runs/{check_run_id}/annotations\",\n  \"GET /repos/{owner}/{repo}/check-suites/{check_suite_id}/check-runs\",\n  \"GET /repos/{owner}/{repo}/code-scanning/alerts\",\n  \"GET /repos/{owner}/{repo}/code-scanning/alerts/{alert_number}/instances\",\n  \"GET /repos/{owner}/{repo}/code-scanning/analyses\",\n  \"GET /repos/{owner}/{repo}/codespaces\",\n  \"GET /repos/{owner}/{repo}/codespaces/devcontainers\",\n  \"GET /repos/{owner}/{repo}/codespaces/secrets\",\n  \"GET /repos/{owner}/{repo}/collaborators\",\n  \"GET /repos/{owner}/{repo}/comments\",\n  \"GET /repos/{owner}/{repo}/comments/{comment_id}/reactions\",\n  \"GET /repos/{owner}/{repo}/commits\",\n  \"GET /repos/{owner}/{repo}/commits/{commit_sha}/comments\",\n  \"GET /repos/{owner}/{repo}/commits/{commit_sha}/pulls\",\n  \"GET /repos/{owner}/{repo}/commits/{ref}/check-runs\",\n  \"GET /repos/{owner}/{repo}/commits/{ref}/check-suites\",\n  \"GET /repos/{owner}/{repo}/commits/{ref}/status\",\n  \"GET /repos/{owner}/{repo}/commits/{ref}/statuses\",\n  \"GET /repos/{owner}/{repo}/compare/{basehead}\",\n  \"GET /repos/{owner}/{repo}/compare/{base}...{head}\",\n  \"GET /repos/{owner}/{repo}/contributors\",\n  \"GET /repos/{owner}/{repo}/dependabot/alerts\",\n  \"GET /repos/{owner}/{repo}/dependabot/secrets\",\n  \"GET /repos/{owner}/{repo}/deployments\",\n  \"GET /repos/{owner}/{repo}/deployments/{deployment_id}/statuses\",\n  \"GET /repos/{owner}/{repo}/environments\",\n  \"GET /repos/{owner}/{repo}/environments/{environment_name}/deployment-branch-policies\",\n  \"GET /repos/{owner}/{repo}/environments/{environment_name}/deployment_protection_rules/apps\",\n  \"GET /repos/{owner}/{repo}/environments/{environment_name}/secrets\",\n  \"GET /repos/{owner}/{repo}/environments/{environment_name}/variables\",\n  \"GET /repos/{owner}/{repo}/events\",\n  \"GET /repos/{owner}/{repo}/forks\",\n  \"GET /repos/{owner}/{repo}/hooks\",\n  \"GET /repos/{owner}/{repo}/hooks/{hook_id}/deliveries\",\n  \"GET /repos/{owner}/{repo}/invitations\",\n  \"GET /repos/{owner}/{repo}/issues\",\n  \"GET /repos/{owner}/{repo}/issues/comments\",\n  \"GET /repos/{owner}/{repo}/issues/comments/{comment_id}/reactions\",\n  \"GET /repos/{owner}/{repo}/issues/events\",\n  \"GET /repos/{owner}/{repo}/issues/{issue_number}/comments\",\n  \"GET /repos/{owner}/{repo}/issues/{issue_number}/events\",\n  \"GET /repos/{owner}/{repo}/issues/{issue_number}/labels\",\n  \"GET /repos/{owner}/{repo}/issues/{issue_number}/reactions\",\n  \"GET /repos/{owner}/{repo}/issues/{issue_number}/sub_issues\",\n  \"GET /repos/{owner}/{repo}/issues/{issue_number}/timeline\",\n  \"GET /repos/{owner}/{repo}/keys\",\n  \"GET /repos/{owner}/{repo}/labels\",\n  \"GET /repos/{owner}/{repo}/milestones\",\n  \"GET /repos/{owner}/{repo}/milestones/{milestone_number}/labels\",\n  \"GET /repos/{owner}/{repo}/notifications\",\n  \"GET /repos/{owner}/{repo}/pages/builds\",\n  \"GET /repos/{owner}/{repo}/projects\",\n  \"GET /repos/{owner}/{repo}/pulls\",\n  \"GET /repos/{owner}/{repo}/pulls/comments\",\n  \"GET /repos/{owner}/{repo}/pulls/comments/{comment_id}/reactions\",\n  \"GET /repos/{owner}/{repo}/pulls/{pull_number}/comments\",\n  \"GET /repos/{owner}/{repo}/pulls/{pull_number}/commits\",\n  \"GET /repos/{owner}/{repo}/pulls/{pull_number}/files\",\n  \"GET /repos/{owner}/{repo}/pulls/{pull_number}/reviews\",\n  \"GET /repos/{owner}/{repo}/pulls/{pull_number}/reviews/{review_id}/comments\",\n  \"GET /repos/{owner}/{repo}/releases\",\n  \"GET /repos/{owner}/{repo}/releases/{release_id}/assets\",\n  \"GET /repos/{owner}/{repo}/releases/{release_id}/reactions\",\n  \"GET /repos/{owner}/{repo}/rules/branches/{branch}\",\n  \"GET /repos/{owner}/{repo}/rulesets\",\n  \"GET /repos/{owner}/{repo}/rulesets/rule-suites\",\n  \"GET /repos/{owner}/{repo}/rulesets/{ruleset_id}/history\",\n  \"GET /repos/{owner}/{repo}/secret-scanning/alerts\",\n  \"GET /repos/{owner}/{repo}/secret-scanning/alerts/{alert_number}/locations\",\n  \"GET /repos/{owner}/{repo}/security-advisories\",\n  \"GET /repos/{owner}/{repo}/stargazers\",\n  \"GET /repos/{owner}/{repo}/subscribers\",\n  \"GET /repos/{owner}/{repo}/tags\",\n  \"GET /repos/{owner}/{repo}/teams\",\n  \"GET /repos/{owner}/{repo}/topics\",\n  \"GET /repositories\",\n  \"GET /search/code\",\n  \"GET /search/commits\",\n  \"GET /search/issues\",\n  \"GET /search/labels\",\n  \"GET /search/repositories\",\n  \"GET /search/topics\",\n  \"GET /search/users\",\n  \"GET /teams/{team_id}/discussions\",\n  \"GET /teams/{team_id}/discussions/{discussion_number}/comments\",\n  \"GET /teams/{team_id}/discussions/{discussion_number}/comments/{comment_number}/reactions\",\n  \"GET /teams/{team_id}/discussions/{discussion_number}/reactions\",\n  \"GET /teams/{team_id}/invitations\",\n  \"GET /teams/{team_id}/members\",\n  \"GET /teams/{team_id}/projects\",\n  \"GET /teams/{team_id}/repos\",\n  \"GET /teams/{team_id}/teams\",\n  \"GET /user/blocks\",\n  \"GET /user/codespaces\",\n  \"GET /user/codespaces/secrets\",\n  \"GET /user/emails\",\n  \"GET /user/followers\",\n  \"GET /user/following\",\n  \"GET /user/gpg_keys\",\n  \"GET /user/installations\",\n  \"GET /user/installations/{installation_id}/repositories\",\n  \"GET /user/issues\",\n  \"GET /user/keys\",\n  \"GET /user/marketplace_purchases\",\n  \"GET /user/marketplace_purchases/stubbed\",\n  \"GET /user/memberships/orgs\",\n  \"GET /user/migrations\",\n  \"GET /user/migrations/{migration_id}/repositories\",\n  \"GET /user/orgs\",\n  \"GET /user/packages\",\n  \"GET /user/packages/{package_type}/{package_name}/versions\",\n  \"GET /user/public_emails\",\n  \"GET /user/repos\",\n  \"GET /user/repository_invitations\",\n  \"GET /user/social_accounts\",\n  \"GET /user/ssh_signing_keys\",\n  \"GET /user/starred\",\n  \"GET /user/subscriptions\",\n  \"GET /user/teams\",\n  \"GET /users\",\n  \"GET /users/{username}/attestations/{subject_digest}\",\n  \"GET /users/{username}/events\",\n  \"GET /users/{username}/events/orgs/{org}\",\n  \"GET /users/{username}/events/public\",\n  \"GET /users/{username}/followers\",\n  \"GET /users/{username}/following\",\n  \"GET /users/{username}/gists\",\n  \"GET /users/{username}/gpg_keys\",\n  \"GET /users/{username}/keys\",\n  \"GET /users/{username}/orgs\",\n  \"GET /users/{username}/packages\",\n  \"GET /users/{username}/projects\",\n  \"GET /users/{username}/received_events\",\n  \"GET /users/{username}/received_events/public\",\n  \"GET /users/{username}/repos\",\n  \"GET /users/{username}/social_accounts\",\n  \"GET /users/{username}/ssh_signing_keys\",\n  \"GET /users/{username}/starred\",\n  \"GET /users/{username}/subscriptions\"\n];\n\n// pkg/dist-src/paginating-endpoints.js\nfunction isPaginatingEndpoint(arg) {\n  if (typeof arg === \"string\") {\n    return paginatingEndpoints.includes(arg);\n  } else {\n    return false;\n  }\n}\n\n// pkg/dist-src/index.js\nfunction paginateRest(octokit) {\n  return {\n    paginate: Object.assign(paginate.bind(null, octokit), {\n      iterator: iterator.bind(null, octokit)\n    })\n  };\n}\npaginateRest.VERSION = VERSION;\nexport {\n  composePaginateRest,\n  isPaginatingEndpoint,\n  paginateRest,\n  paginatingEndpoints\n};\n", "export const VERSION = \"16.0.0\";\n", "import type { EndpointsDefaultsAndDecorations } from \"../types.js\";\nconst Endpoints: EndpointsDefaultsAndDecorations = {\n  actions: {\n    addCustomLabelsToSelfHostedRunnerForOrg: [\n      \"POST /orgs/{org}/actions/runners/{runner_id}/labels\",\n    ],\n    addCustomLabelsToSelfHostedRunnerForRepo: [\n      \"POST /repos/{owner}/{repo}/actions/runners/{runner_id}/labels\",\n    ],\n    addRepoAccessToSelfHostedRunnerGroupInOrg: [\n      \"PUT /orgs/{org}/actions/runner-groups/{runner_group_id}/repositories/{repository_id}\",\n    ],\n    addSelectedRepoToOrgSecret: [\n      \"PUT /orgs/{org}/actions/secrets/{secret_name}/repositories/{repository_id}\",\n    ],\n    addSelectedRepoToOrgVariable: [\n      \"PUT /orgs/{org}/actions/variables/{name}/repositories/{repository_id}\",\n    ],\n    approveWorkflowRun: [\n      \"POST /repos/{owner}/{repo}/actions/runs/{run_id}/approve\",\n    ],\n    cancelWorkflowRun: [\n      \"POST /repos/{owner}/{repo}/actions/runs/{run_id}/cancel\",\n    ],\n    createEnvironmentVariable: [\n      \"POST /repos/{owner}/{repo}/environments/{environment_name}/variables\",\n    ],\n    createHostedRunnerForOrg: [\"POST /orgs/{org}/actions/hosted-runners\"],\n    createOrUpdateEnvironmentSecret: [\n      \"PUT /repos/{owner}/{repo}/environments/{environment_name}/secrets/{secret_name}\",\n    ],\n    createOrUpdateOrgSecret: [\"PUT /orgs/{org}/actions/secrets/{secret_name}\"],\n    createOrUpdateRepoSecret: [\n      \"PUT /repos/{owner}/{repo}/actions/secrets/{secret_name}\",\n    ],\n    createOrgVariable: [\"POST /orgs/{org}/actions/variables\"],\n    createRegistrationTokenForOrg: [\n      \"POST /orgs/{org}/actions/runners/registration-token\",\n    ],\n    createRegistrationTokenForRepo: [\n      \"POST /repos/{owner}/{repo}/actions/runners/registration-token\",\n    ],\n    createRemoveTokenForOrg: [\"POST /orgs/{org}/actions/runners/remove-token\"],\n    createRemoveTokenForRepo: [\n      \"POST /repos/{owner}/{repo}/actions/runners/remove-token\",\n    ],\n    createRepoVariable: [\"POST /repos/{owner}/{repo}/actions/variables\"],\n    createWorkflowDispatch: [\n      \"POST /repos/{owner}/{repo}/actions/workflows/{workflow_id}/dispatches\",\n    ],\n    deleteActionsCacheById: [\n      \"DELETE /repos/{owner}/{repo}/actions/caches/{cache_id}\",\n    ],\n    deleteActionsCacheByKey: [\n      \"DELETE /repos/{owner}/{repo}/actions/caches{?key,ref}\",\n    ],\n    deleteArtifact: [\n      \"DELETE /repos/{owner}/{repo}/actions/artifacts/{artifact_id}\",\n    ],\n    deleteEnvironmentSecret: [\n      \"DELETE /repos/{owner}/{repo}/environments/{environment_name}/secrets/{secret_name}\",\n    ],\n    deleteEnvironmentVariable: [\n      \"DELETE /repos/{owner}/{repo}/environments/{environment_name}/variables/{name}\",\n    ],\n    deleteHostedRunnerForOrg: [\n      \"DELETE /orgs/{org}/actions/hosted-runners/{hosted_runner_id}\",\n    ],\n    deleteOrgSecret: [\"DELETE /orgs/{org}/actions/secrets/{secret_name}\"],\n    deleteOrgVariable: [\"DELETE /orgs/{org}/actions/variables/{name}\"],\n    deleteRepoSecret: [\n      \"DELETE /repos/{owner}/{repo}/actions/secrets/{secret_name}\",\n    ],\n    deleteRepoVariable: [\n      \"DELETE /repos/{owner}/{repo}/actions/variables/{name}\",\n    ],\n    deleteSelfHostedRunnerFromOrg: [\n      \"DELETE /orgs/{org}/actions/runners/{runner_id}\",\n    ],\n    deleteSelfHostedRunnerFromRepo: [\n      \"DELETE /repos/{owner}/{repo}/actions/runners/{runner_id}\",\n    ],\n    deleteWorkflowRun: [\"DELETE /repos/{owner}/{repo}/actions/runs/{run_id}\"],\n    deleteWorkflowRunLogs: [\n      \"DELETE /repos/{owner}/{repo}/actions/runs/{run_id}/logs\",\n    ],\n    disableSelectedRepositoryGithubActionsOrganization: [\n      \"DELETE /orgs/{org}/actions/permissions/repositories/{repository_id}\",\n    ],\n    disableWorkflow: [\n      \"PUT /repos/{owner}/{repo}/actions/workflows/{workflow_id}/disable\",\n    ],\n    downloadArtifact: [\n      \"GET /repos/{owner}/{repo}/actions/artifacts/{artifact_id}/{archive_format}\",\n    ],\n    downloadJobLogsForWorkflowRun: [\n      \"GET /repos/{owner}/{repo}/actions/jobs/{job_id}/logs\",\n    ],\n    downloadWorkflowRunAttemptLogs: [\n      \"GET /repos/{owner}/{repo}/actions/runs/{run_id}/attempts/{attempt_number}/logs\",\n    ],\n    downloadWorkflowRunLogs: [\n      \"GET /repos/{owner}/{repo}/actions/runs/{run_id}/logs\",\n    ],\n    enableSelectedRepositoryGithubActionsOrganization: [\n      \"PUT /orgs/{org}/actions/permissions/repositories/{repository_id}\",\n    ],\n    enableWorkflow: [\n      \"PUT /repos/{owner}/{repo}/actions/workflows/{workflow_id}/enable\",\n    ],\n    forceCancelWorkflowRun: [\n      \"POST /repos/{owner}/{repo}/actions/runs/{run_id}/force-cancel\",\n    ],\n    generateRunnerJitconfigForOrg: [\n      \"POST /orgs/{org}/actions/runners/generate-jitconfig\",\n    ],\n    generateRunnerJitconfigForRepo: [\n      \"POST /repos/{owner}/{repo}/actions/runners/generate-jitconfig\",\n    ],\n    getActionsCacheList: [\"GET /repos/{owner}/{repo}/actions/caches\"],\n    getActionsCacheUsage: [\"GET /repos/{owner}/{repo}/actions/cache/usage\"],\n    getActionsCacheUsageByRepoForOrg: [\n      \"GET /orgs/{org}/actions/cache/usage-by-repository\",\n    ],\n    getActionsCacheUsageForOrg: [\"GET /orgs/{org}/actions/cache/usage\"],\n    getAllowedActionsOrganization: [\n      \"GET /orgs/{org}/actions/permissions/selected-actions\",\n    ],\n    getAllowedActionsRepository: [\n      \"GET /repos/{owner}/{repo}/actions/permissions/selected-actions\",\n    ],\n    getArtifact: [\"GET /repos/{owner}/{repo}/actions/artifacts/{artifact_id}\"],\n    getCustomOidcSubClaimForRepo: [\n      \"GET /repos/{owner}/{repo}/actions/oidc/customization/sub\",\n    ],\n    getEnvironmentPublicKey: [\n      \"GET /repos/{owner}/{repo}/environments/{environment_name}/secrets/public-key\",\n    ],\n    getEnvironmentSecret: [\n      \"GET /repos/{owner}/{repo}/environments/{environment_name}/secrets/{secret_name}\",\n    ],\n    getEnvironmentVariable: [\n      \"GET /repos/{owner}/{repo}/environments/{environment_name}/variables/{name}\",\n    ],\n    getGithubActionsDefaultWorkflowPermissionsOrganization: [\n      \"GET /orgs/{org}/actions/permissions/workflow\",\n    ],\n    getGithubActionsDefaultWorkflowPermissionsRepository: [\n      \"GET /repos/{owner}/{repo}/actions/permissions/workflow\",\n    ],\n    getGithubActionsPermissionsOrganization: [\n      \"GET /orgs/{org}/actions/permissions\",\n    ],\n    getGithubActionsPermissionsRepository: [\n      \"GET /repos/{owner}/{repo}/actions/permissions\",\n    ],\n    getHostedRunnerForOrg: [\n      \"GET /orgs/{org}/actions/hosted-runners/{hosted_runner_id}\",\n    ],\n    getHostedRunnersGithubOwnedImagesForOrg: [\n      \"GET /orgs/{org}/actions/hosted-runners/images/github-owned\",\n    ],\n    getHostedRunnersLimitsForOrg: [\n      \"GET /orgs/{org}/actions/hosted-runners/limits\",\n    ],\n    getHostedRunnersMachineSpecsForOrg: [\n      \"GET /orgs/{org}/actions/hosted-runners/machine-sizes\",\n    ],\n    getHostedRunnersPartnerImagesForOrg: [\n      \"GET /orgs/{org}/actions/hosted-runners/images/partner\",\n    ],\n    getHostedRunnersPlatformsForOrg: [\n      \"GET /orgs/{org}/actions/hosted-runners/platforms\",\n    ],\n    getJobForWorkflowRun: [\"GET /repos/{owner}/{repo}/actions/jobs/{job_id}\"],\n    getOrgPublicKey: [\"GET /orgs/{org}/actions/secrets/public-key\"],\n    getOrgSecret: [\"GET /orgs/{org}/actions/secrets/{secret_name}\"],\n    getOrgVariable: [\"GET /orgs/{org}/actions/variables/{name}\"],\n    getPendingDeploymentsForRun: [\n      \"GET /repos/{owner}/{repo}/actions/runs/{run_id}/pending_deployments\",\n    ],\n    getRepoPermissions: [\n      \"GET /repos/{owner}/{repo}/actions/permissions\",\n      {},\n      { renamed: [\"actions\", \"getGithubActionsPermissionsRepository\"] },\n    ],\n    getRepoPublicKey: [\"GET /repos/{owner}/{repo}/actions/secrets/public-key\"],\n    getRepoSecret: [\"GET /repos/{owner}/{repo}/actions/secrets/{secret_name}\"],\n    getRepoVariable: [\"GET /repos/{owner}/{repo}/actions/variables/{name}\"],\n    getReviewsForRun: [\n      \"GET /repos/{owner}/{repo}/actions/runs/{run_id}/approvals\",\n    ],\n    getSelfHostedRunnerForOrg: [\"GET /orgs/{org}/actions/runners/{runner_id}\"],\n    getSelfHostedRunnerForRepo: [\n      \"GET /repos/{owner}/{repo}/actions/runners/{runner_id}\",\n    ],\n    getWorkflow: [\"GET /repos/{owner}/{repo}/actions/workflows/{workflow_id}\"],\n    getWorkflowAccessToRepository: [\n      \"GET /repos/{owner}/{repo}/actions/permissions/access\",\n    ],\n    getWorkflowRun: [\"GET /repos/{owner}/{repo}/actions/runs/{run_id}\"],\n    getWorkflowRunAttempt: [\n      \"GET /repos/{owner}/{repo}/actions/runs/{run_id}/attempts/{attempt_number}\",\n    ],\n    getWorkflowRunUsage: [\n      \"GET /repos/{owner}/{repo}/actions/runs/{run_id}/timing\",\n    ],\n    getWorkflowUsage: [\n      \"GET /repos/{owner}/{repo}/actions/workflows/{workflow_id}/timing\",\n    ],\n    listArtifactsForRepo: [\"GET /repos/{owner}/{repo}/actions/artifacts\"],\n    listEnvironmentSecrets: [\n      \"GET /repos/{owner}/{repo}/environments/{environment_name}/secrets\",\n    ],\n    listEnvironmentVariables: [\n      \"GET /repos/{owner}/{repo}/environments/{environment_name}/variables\",\n    ],\n    listGithubHostedRunnersInGroupForOrg: [\n      \"GET /orgs/{org}/actions/runner-groups/{runner_group_id}/hosted-runners\",\n    ],\n    listHostedRunnersForOrg: [\"GET /orgs/{org}/actions/hosted-runners\"],\n    listJobsForWorkflowRun: [\n      \"GET /repos/{owner}/{repo}/actions/runs/{run_id}/jobs\",\n    ],\n    listJobsForWorkflowRunAttempt: [\n      \"GET /repos/{owner}/{repo}/actions/runs/{run_id}/attempts/{attempt_number}/jobs\",\n    ],\n    listLabelsForSelfHostedRunnerForOrg: [\n      \"GET /orgs/{org}/actions/runners/{runner_id}/labels\",\n    ],\n    listLabelsForSelfHostedRunnerForRepo: [\n      \"GET /repos/{owner}/{repo}/actions/runners/{runner_id}/labels\",\n    ],\n    listOrgSecrets: [\"GET /orgs/{org}/actions/secrets\"],\n    listOrgVariables: [\"GET /orgs/{org}/actions/variables\"],\n    listRepoOrganizationSecrets: [\n      \"GET /repos/{owner}/{repo}/actions/organization-secrets\",\n    ],\n    listRepoOrganizationVariables: [\n      \"GET /repos/{owner}/{repo}/actions/organization-variables\",\n    ],\n    listRepoSecrets: [\"GET /repos/{owner}/{repo}/actions/secrets\"],\n    listRepoVariables: [\"GET /repos/{owner}/{repo}/actions/variables\"],\n    listRepoWorkflows: [\"GET /repos/{owner}/{repo}/actions/workflows\"],\n    listRunnerApplicationsForOrg: [\"GET /orgs/{org}/actions/runners/downloads\"],\n    listRunnerApplicationsForRepo: [\n      \"GET /repos/{owner}/{repo}/actions/runners/downloads\",\n    ],\n    listSelectedReposForOrgSecret: [\n      \"GET /orgs/{org}/actions/secrets/{secret_name}/repositories\",\n    ],\n    listSelectedReposForOrgVariable: [\n      \"GET /orgs/{org}/actions/variables/{name}/repositories\",\n    ],\n    listSelectedRepositoriesEnabledGithubActionsOrganization: [\n      \"GET /orgs/{org}/actions/permissions/repositories\",\n    ],\n    listSelfHostedRunnersForOrg: [\"GET /orgs/{org}/actions/runners\"],\n    listSelfHostedRunnersForRepo: [\"GET /repos/{owner}/{repo}/actions/runners\"],\n    listWorkflowRunArtifacts: [\n      \"GET /repos/{owner}/{repo}/actions/runs/{run_id}/artifacts\",\n    ],\n    listWorkflowRuns: [\n      \"GET /repos/{owner}/{repo}/actions/workflows/{workflow_id}/runs\",\n    ],\n    listWorkflowRunsForRepo: [\"GET /repos/{owner}/{repo}/actions/runs\"],\n    reRunJobForWorkflowRun: [\n      \"POST /repos/{owner}/{repo}/actions/jobs/{job_id}/rerun\",\n    ],\n    reRunWorkflow: [\"POST /repos/{owner}/{repo}/actions/runs/{run_id}/rerun\"],\n    reRunWorkflowFailedJobs: [\n      \"POST /repos/{owner}/{repo}/actions/runs/{run_id}/rerun-failed-jobs\",\n    ],\n    removeAllCustomLabelsFromSelfHostedRunnerForOrg: [\n      \"DELETE /orgs/{org}/actions/runners/{runner_id}/labels\",\n    ],\n    removeAllCustomLabelsFromSelfHostedRunnerForRepo: [\n      \"DELETE /repos/{owner}/{repo}/actions/runners/{runner_id}/labels\",\n    ],\n    removeCustomLabelFromSelfHostedRunnerForOrg: [\n      \"DELETE /orgs/{org}/actions/runners/{runner_id}/labels/{name}\",\n    ],\n    removeCustomLabelFromSelfHostedRunnerForRepo: [\n      \"DELETE /repos/{owner}/{repo}/actions/runners/{runner_id}/labels/{name}\",\n    ],\n    removeSelectedRepoFromOrgSecret: [\n      \"DELETE /orgs/{org}/actions/secrets/{secret_name}/repositories/{repository_id}\",\n    ],\n    removeSelectedRepoFromOrgVariable: [\n      \"DELETE /orgs/{org}/actions/variables/{name}/repositories/{repository_id}\",\n    ],\n    reviewCustomGatesForRun: [\n      \"POST /repos/{owner}/{repo}/actions/runs/{run_id}/deployment_protection_rule\",\n    ],\n    reviewPendingDeploymentsForRun: [\n      \"POST /repos/{owner}/{repo}/actions/runs/{run_id}/pending_deployments\",\n    ],\n    setAllowedActionsOrganization: [\n      \"PUT /orgs/{org}/actions/permissions/selected-actions\",\n    ],\n    setAllowedActionsRepository: [\n      \"PUT /repos/{owner}/{repo}/actions/permissions/selected-actions\",\n    ],\n    setCustomLabelsForSelfHostedRunnerForOrg: [\n      \"PUT /orgs/{org}/actions/runners/{runner_id}/labels\",\n    ],\n    setCustomLabelsForSelfHostedRunnerForRepo: [\n      \"PUT /repos/{owner}/{repo}/actions/runners/{runner_id}/labels\",\n    ],\n    setCustomOidcSubClaimForRepo: [\n      \"PUT /repos/{owner}/{repo}/actions/oidc/customization/sub\",\n    ],\n    setGithubActionsDefaultWorkflowPermissionsOrganization: [\n      \"PUT /orgs/{org}/actions/permissions/workflow\",\n    ],\n    setGithubActionsDefaultWorkflowPermissionsRepository: [\n      \"PUT /repos/{owner}/{repo}/actions/permissions/workflow\",\n    ],\n    setGithubActionsPermissionsOrganization: [\n      \"PUT /orgs/{org}/actions/permissions\",\n    ],\n    setGithubActionsPermissionsRepository: [\n      \"PUT /repos/{owner}/{repo}/actions/permissions\",\n    ],\n    setSelectedReposForOrgSecret: [\n      \"PUT /orgs/{org}/actions/secrets/{secret_name}/repositories\",\n    ],\n    setSelectedReposForOrgVariable: [\n      \"PUT /orgs/{org}/actions/variables/{name}/repositories\",\n    ],\n    setSelectedRepositoriesEnabledGithubActionsOrganization: [\n      \"PUT /orgs/{org}/actions/permissions/repositories\",\n    ],\n    setWorkflowAccessToRepository: [\n      \"PUT /repos/{owner}/{repo}/actions/permissions/access\",\n    ],\n    updateEnvironmentVariable: [\n      \"PATCH /repos/{owner}/{repo}/environments/{environment_name}/variables/{name}\",\n    ],\n    updateHostedRunnerForOrg: [\n      \"PATCH /orgs/{org}/actions/hosted-runners/{hosted_runner_id}\",\n    ],\n    updateOrgVariable: [\"PATCH /orgs/{org}/actions/variables/{name}\"],\n    updateRepoVariable: [\n      \"PATCH /repos/{owner}/{repo}/actions/variables/{name}\",\n    ],\n  },\n  activity: {\n    checkRepoIsStarredByAuthenticatedUser: [\"GET /user/starred/{owner}/{repo}\"],\n    deleteRepoSubscription: [\"DELETE /repos/{owner}/{repo}/subscription\"],\n    deleteThreadSubscription: [\n      \"DELETE /notifications/threads/{thread_id}/subscription\",\n    ],\n    getFeeds: [\"GET /feeds\"],\n    getRepoSubscription: [\"GET /repos/{owner}/{repo}/subscription\"],\n    getThread: [\"GET /notifications/threads/{thread_id}\"],\n    getThreadSubscriptionForAuthenticatedUser: [\n      \"GET /notifications/threads/{thread_id}/subscription\",\n    ],\n    listEventsForAuthenticatedUser: [\"GET /users/{username}/events\"],\n    listNotificationsForAuthenticatedUser: [\"GET /notifications\"],\n    listOrgEventsForAuthenticatedUser: [\n      \"GET /users/{username}/events/orgs/{org}\",\n    ],\n    listPublicEvents: [\"GET /events\"],\n    listPublicEventsForRepoNetwork: [\"GET /networks/{owner}/{repo}/events\"],\n    listPublicEventsForUser: [\"GET /users/{username}/events/public\"],\n    listPublicOrgEvents: [\"GET /orgs/{org}/events\"],\n    listReceivedEventsForUser: [\"GET /users/{username}/received_events\"],\n    listReceivedPublicEventsForUser: [\n      \"GET /users/{username}/received_events/public\",\n    ],\n    listRepoEvents: [\"GET /repos/{owner}/{repo}/events\"],\n    listRepoNotificationsForAuthenticatedUser: [\n      \"GET /repos/{owner}/{repo}/notifications\",\n    ],\n    listReposStarredByAuthenticatedUser: [\"GET /user/starred\"],\n    listReposStarredByUser: [\"GET /users/{username}/starred\"],\n    listReposWatchedByUser: [\"GET /users/{username}/subscriptions\"],\n    listStargazersForRepo: [\"GET /repos/{owner}/{repo}/stargazers\"],\n    listWatchedReposForAuthenticatedUser: [\"GET /user/subscriptions\"],\n    listWatchersForRepo: [\"GET /repos/{owner}/{repo}/subscribers\"],\n    markNotificationsAsRead: [\"PUT /notifications\"],\n    markRepoNotificationsAsRead: [\"PUT /repos/{owner}/{repo}/notifications\"],\n    markThreadAsDone: [\"DELETE /notifications/threads/{thread_id}\"],\n    markThreadAsRead: [\"PATCH /notifications/threads/{thread_id}\"],\n    setRepoSubscription: [\"PUT /repos/{owner}/{repo}/subscription\"],\n    setThreadSubscription: [\n      \"PUT /notifications/threads/{thread_id}/subscription\",\n    ],\n    starRepoForAuthenticatedUser: [\"PUT /user/starred/{owner}/{repo}\"],\n    unstarRepoForAuthenticatedUser: [\"DELETE /user/starred/{owner}/{repo}\"],\n  },\n  apps: {\n    addRepoToInstallation: [\n      \"PUT /user/installations/{installation_id}/repositories/{repository_id}\",\n      {},\n      { renamed: [\"apps\", \"addRepoToInstallationForAuthenticatedUser\"] },\n    ],\n    addRepoToInstallationForAuthenticatedUser: [\n      \"PUT /user/installations/{installation_id}/repositories/{repository_id}\",\n    ],\n    checkToken: [\"POST /applications/{client_id}/token\"],\n    createFromManifest: [\"POST /app-manifests/{code}/conversions\"],\n    createInstallationAccessToken: [\n      \"POST /app/installations/{installation_id}/access_tokens\",\n    ],\n    deleteAuthorization: [\"DELETE /applications/{client_id}/grant\"],\n    deleteInstallation: [\"DELETE /app/installations/{installation_id}\"],\n    deleteToken: [\"DELETE /applications/{client_id}/token\"],\n    getAuthenticated: [\"GET /app\"],\n    getBySlug: [\"GET /apps/{app_slug}\"],\n    getInstallation: [\"GET /app/installations/{installation_id}\"],\n    getOrgInstallation: [\"GET /orgs/{org}/installation\"],\n    getRepoInstallation: [\"GET /repos/{owner}/{repo}/installation\"],\n    getSubscriptionPlanForAccount: [\n      \"GET /marketplace_listing/accounts/{account_id}\",\n    ],\n    getSubscriptionPlanForAccountStubbed: [\n      \"GET /marketplace_listing/stubbed/accounts/{account_id}\",\n    ],\n    getUserInstallation: [\"GET /users/{username}/installation\"],\n    getWebhookConfigForApp: [\"GET /app/hook/config\"],\n    getWebhookDelivery: [\"GET /app/hook/deliveries/{delivery_id}\"],\n    listAccountsForPlan: [\"GET /marketplace_listing/plans/{plan_id}/accounts\"],\n    listAccountsForPlanStubbed: [\n      \"GET /marketplace_listing/stubbed/plans/{plan_id}/accounts\",\n    ],\n    listInstallationReposForAuthenticatedUser: [\n      \"GET /user/installations/{installation_id}/repositories\",\n    ],\n    listInstallationRequestsForAuthenticatedApp: [\n      \"GET /app/installation-requests\",\n    ],\n    listInstallations: [\"GET /app/installations\"],\n    listInstallationsForAuthenticatedUser: [\"GET /user/installations\"],\n    listPlans: [\"GET /marketplace_listing/plans\"],\n    listPlansStubbed: [\"GET /marketplace_listing/stubbed/plans\"],\n    listReposAccessibleToInstallation: [\"GET /installation/repositories\"],\n    listSubscriptionsForAuthenticatedUser: [\"GET /user/marketplace_purchases\"],\n    listSubscriptionsForAuthenticatedUserStubbed: [\n      \"GET /user/marketplace_purchases/stubbed\",\n    ],\n    listWebhookDeliveries: [\"GET /app/hook/deliveries\"],\n    redeliverWebhookDelivery: [\n      \"POST /app/hook/deliveries/{delivery_id}/attempts\",\n    ],\n    removeRepoFromInstallation: [\n      \"DELETE /user/installations/{installation_id}/repositories/{repository_id}\",\n      {},\n      { renamed: [\"apps\", \"removeRepoFromInstallationForAuthenticatedUser\"] },\n    ],\n    removeRepoFromInstallationForAuthenticatedUser: [\n      \"DELETE /user/installations/{installation_id}/repositories/{repository_id}\",\n    ],\n    resetToken: [\"PATCH /applications/{client_id}/token\"],\n    revokeInstallationAccessToken: [\"DELETE /installation/token\"],\n    scopeToken: [\"POST /applications/{client_id}/token/scoped\"],\n    suspendInstallation: [\"PUT /app/installations/{installation_id}/suspended\"],\n    unsuspendInstallation: [\n      \"DELETE /app/installations/{installation_id}/suspended\",\n    ],\n    updateWebhookConfigForApp: [\"PATCH /app/hook/config\"],\n  },\n  billing: {\n    getGithubActionsBillingOrg: [\"GET /orgs/{org}/settings/billing/actions\"],\n    getGithubActionsBillingUser: [\n      \"GET /users/{username}/settings/billing/actions\",\n    ],\n    getGithubBillingUsageReportOrg: [\n      \"GET /organizations/{org}/settings/billing/usage\",\n    ],\n    getGithubBillingUsageReportUser: [\n      \"GET /users/{username}/settings/billing/usage\",\n    ],\n    getGithubPackagesBillingOrg: [\"GET /orgs/{org}/settings/billing/packages\"],\n    getGithubPackagesBillingUser: [\n      \"GET /users/{username}/settings/billing/packages\",\n    ],\n    getSharedStorageBillingOrg: [\n      \"GET /orgs/{org}/settings/billing/shared-storage\",\n    ],\n    getSharedStorageBillingUser: [\n      \"GET /users/{username}/settings/billing/shared-storage\",\n    ],\n  },\n  campaigns: {\n    createCampaign: [\"POST /orgs/{org}/campaigns\"],\n    deleteCampaign: [\"DELETE /orgs/{org}/campaigns/{campaign_number}\"],\n    getCampaignSummary: [\"GET /orgs/{org}/campaigns/{campaign_number}\"],\n    listOrgCampaigns: [\"GET /orgs/{org}/campaigns\"],\n    updateCampaign: [\"PATCH /orgs/{org}/campaigns/{campaign_number}\"],\n  },\n  checks: {\n    create: [\"POST /repos/{owner}/{repo}/check-runs\"],\n    createSuite: [\"POST /repos/{owner}/{repo}/check-suites\"],\n    get: [\"GET /repos/{owner}/{repo}/check-runs/{check_run_id}\"],\n    getSuite: [\"GET /repos/{owner}/{repo}/check-suites/{check_suite_id}\"],\n    listAnnotations: [\n      \"GET /repos/{owner}/{repo}/check-runs/{check_run_id}/annotations\",\n    ],\n    listForRef: [\"GET /repos/{owner}/{repo}/commits/{ref}/check-runs\"],\n    listForSuite: [\n      \"GET /repos/{owner}/{repo}/check-suites/{check_suite_id}/check-runs\",\n    ],\n    listSuitesForRef: [\"GET /repos/{owner}/{repo}/commits/{ref}/check-suites\"],\n    rerequestRun: [\n      \"POST /repos/{owner}/{repo}/check-runs/{check_run_id}/rerequest\",\n    ],\n    rerequestSuite: [\n      \"POST /repos/{owner}/{repo}/check-suites/{check_suite_id}/rerequest\",\n    ],\n    setSuitesPreferences: [\n      \"PATCH /repos/{owner}/{repo}/check-suites/preferences\",\n    ],\n    update: [\"PATCH /repos/{owner}/{repo}/check-runs/{check_run_id}\"],\n  },\n  codeScanning: {\n    commitAutofix: [\n      \"POST /repos/{owner}/{repo}/code-scanning/alerts/{alert_number}/autofix/commits\",\n    ],\n    createAutofix: [\n      \"POST /repos/{owner}/{repo}/code-scanning/alerts/{alert_number}/autofix\",\n    ],\n    createVariantAnalysis: [\n      \"POST /repos/{owner}/{repo}/code-scanning/codeql/variant-analyses\",\n    ],\n    deleteAnalysis: [\n      \"DELETE /repos/{owner}/{repo}/code-scanning/analyses/{analysis_id}{?confirm_delete}\",\n    ],\n    deleteCodeqlDatabase: [\n      \"DELETE /repos/{owner}/{repo}/code-scanning/codeql/databases/{language}\",\n    ],\n    getAlert: [\n      \"GET /repos/{owner}/{repo}/code-scanning/alerts/{alert_number}\",\n      {},\n      { renamedParameters: { alert_id: \"alert_number\" } },\n    ],\n    getAnalysis: [\n      \"GET /repos/{owner}/{repo}/code-scanning/analyses/{analysis_id}\",\n    ],\n    getAutofix: [\n      \"GET /repos/{owner}/{repo}/code-scanning/alerts/{alert_number}/autofix\",\n    ],\n    getCodeqlDatabase: [\n      \"GET /repos/{owner}/{repo}/code-scanning/codeql/databases/{language}\",\n    ],\n    getDefaultSetup: [\"GET /repos/{owner}/{repo}/code-scanning/default-setup\"],\n    getSarif: [\"GET /repos/{owner}/{repo}/code-scanning/sarifs/{sarif_id}\"],\n    getVariantAnalysis: [\n      \"GET /repos/{owner}/{repo}/code-scanning/codeql/variant-analyses/{codeql_variant_analysis_id}\",\n    ],\n    getVariantAnalysisRepoTask: [\n      \"GET /repos/{owner}/{repo}/code-scanning/codeql/variant-analyses/{codeql_variant_analysis_id}/repos/{repo_owner}/{repo_name}\",\n    ],\n    listAlertInstances: [\n      \"GET /repos/{owner}/{repo}/code-scanning/alerts/{alert_number}/instances\",\n    ],\n    listAlertsForOrg: [\"GET /orgs/{org}/code-scanning/alerts\"],\n    listAlertsForRepo: [\"GET /repos/{owner}/{repo}/code-scanning/alerts\"],\n    listAlertsInstances: [\n      \"GET /repos/{owner}/{repo}/code-scanning/alerts/{alert_number}/instances\",\n      {},\n      { renamed: [\"codeScanning\", \"listAlertInstances\"] },\n    ],\n    listCodeqlDatabases: [\n      \"GET /repos/{owner}/{repo}/code-scanning/codeql/databases\",\n    ],\n    listRecentAnalyses: [\"GET /repos/{owner}/{repo}/code-scanning/analyses\"],\n    updateAlert: [\n      \"PATCH /repos/{owner}/{repo}/code-scanning/alerts/{alert_number}\",\n    ],\n    updateDefaultSetup: [\n      \"PATCH /repos/{owner}/{repo}/code-scanning/default-setup\",\n    ],\n    uploadSarif: [\"POST /repos/{owner}/{repo}/code-scanning/sarifs\"],\n  },\n  codeSecurity: {\n    attachConfiguration: [\n      \"POST /orgs/{org}/code-security/configurations/{configuration_id}/attach\",\n    ],\n    attachEnterpriseConfiguration: [\n      \"POST /enterprises/{enterprise}/code-security/configurations/{configuration_id}/attach\",\n    ],\n    createConfiguration: [\"POST /orgs/{org}/code-security/configurations\"],\n    createConfigurationForEnterprise: [\n      \"POST /enterprises/{enterprise}/code-security/configurations\",\n    ],\n    deleteConfiguration: [\n      \"DELETE /orgs/{org}/code-security/configurations/{configuration_id}\",\n    ],\n    deleteConfigurationForEnterprise: [\n      \"DELETE /enterprises/{enterprise}/code-security/configurations/{configuration_id}\",\n    ],\n    detachConfiguration: [\n      \"DELETE /orgs/{org}/code-security/configurations/detach\",\n    ],\n    getConfiguration: [\n      \"GET /orgs/{org}/code-security/configurations/{configuration_id}\",\n    ],\n    getConfigurationForRepository: [\n      \"GET /repos/{owner}/{repo}/code-security-configuration\",\n    ],\n    getConfigurationsForEnterprise: [\n      \"GET /enterprises/{enterprise}/code-security/configurations\",\n    ],\n    getConfigurationsForOrg: [\"GET /orgs/{org}/code-security/configurations\"],\n    getDefaultConfigurations: [\n      \"GET /orgs/{org}/code-security/configurations/defaults\",\n    ],\n    getDefaultConfigurationsForEnterprise: [\n      \"GET /enterprises/{enterprise}/code-security/configurations/defaults\",\n    ],\n    getRepositoriesForConfiguration: [\n      \"GET /orgs/{org}/code-security/configurations/{configuration_id}/repositories\",\n    ],\n    getRepositoriesForEnterpriseConfiguration: [\n      \"GET /enterprises/{enterprise}/code-security/configurations/{configuration_id}/repositories\",\n    ],\n    getSingleConfigurationForEnterprise: [\n      \"GET /enterprises/{enterprise}/code-security/configurations/{configuration_id}\",\n    ],\n    setConfigurationAsDefault: [\n      \"PUT /orgs/{org}/code-security/configurations/{configuration_id}/defaults\",\n    ],\n    setConfigurationAsDefaultForEnterprise: [\n      \"PUT /enterprises/{enterprise}/code-security/configurations/{configuration_id}/defaults\",\n    ],\n    updateConfiguration: [\n      \"PATCH /orgs/{org}/code-security/configurations/{configuration_id}\",\n    ],\n    updateEnterpriseConfiguration: [\n      \"PATCH /enterprises/{enterprise}/code-security/configurations/{configuration_id}\",\n    ],\n  },\n  codesOfConduct: {\n    getAllCodesOfConduct: [\"GET /codes_of_conduct\"],\n    getConductCode: [\"GET /codes_of_conduct/{key}\"],\n  },\n  codespaces: {\n    addRepositoryForSecretForAuthenticatedUser: [\n      \"PUT /user/codespaces/secrets/{secret_name}/repositories/{repository_id}\",\n    ],\n    addSelectedRepoToOrgSecret: [\n      \"PUT /orgs/{org}/codespaces/secrets/{secret_name}/repositories/{repository_id}\",\n    ],\n    checkPermissionsForDevcontainer: [\n      \"GET /repos/{owner}/{repo}/codespaces/permissions_check\",\n    ],\n    codespaceMachinesForAuthenticatedUser: [\n      \"GET /user/codespaces/{codespace_name}/machines\",\n    ],\n    createForAuthenticatedUser: [\"POST /user/codespaces\"],\n    createOrUpdateOrgSecret: [\n      \"PUT /orgs/{org}/codespaces/secrets/{secret_name}\",\n    ],\n    createOrUpdateRepoSecret: [\n      \"PUT /repos/{owner}/{repo}/codespaces/secrets/{secret_name}\",\n    ],\n    createOrUpdateSecretForAuthenticatedUser: [\n      \"PUT /user/codespaces/secrets/{secret_name}\",\n    ],\n    createWithPrForAuthenticatedUser: [\n      \"POST /repos/{owner}/{repo}/pulls/{pull_number}/codespaces\",\n    ],\n    createWithRepoForAuthenticatedUser: [\n      \"POST /repos/{owner}/{repo}/codespaces\",\n    ],\n    deleteForAuthenticatedUser: [\"DELETE /user/codespaces/{codespace_name}\"],\n    deleteFromOrganization: [\n      \"DELETE /orgs/{org}/members/{username}/codespaces/{codespace_name}\",\n    ],\n    deleteOrgSecret: [\"DELETE /orgs/{org}/codespaces/secrets/{secret_name}\"],\n    deleteRepoSecret: [\n      \"DELETE /repos/{owner}/{repo}/codespaces/secrets/{secret_name}\",\n    ],\n    deleteSecretForAuthenticatedUser: [\n      \"DELETE /user/codespaces/secrets/{secret_name}\",\n    ],\n    exportForAuthenticatedUser: [\n      \"POST /user/codespaces/{codespace_name}/exports\",\n    ],\n    getCodespacesForUserInOrg: [\n      \"GET /orgs/{org}/members/{username}/codespaces\",\n    ],\n    getExportDetailsForAuthenticatedUser: [\n      \"GET /user/codespaces/{codespace_name}/exports/{export_id}\",\n    ],\n    getForAuthenticatedUser: [\"GET /user/codespaces/{codespace_name}\"],\n    getOrgPublicKey: [\"GET /orgs/{org}/codespaces/secrets/public-key\"],\n    getOrgSecret: [\"GET /orgs/{org}/codespaces/secrets/{secret_name}\"],\n    getPublicKeyForAuthenticatedUser: [\n      \"GET /user/codespaces/secrets/public-key\",\n    ],\n    getRepoPublicKey: [\n      \"GET /repos/{owner}/{repo}/codespaces/secrets/public-key\",\n    ],\n    getRepoSecret: [\n      \"GET /repos/{owner}/{repo}/codespaces/secrets/{secret_name}\",\n    ],\n    getSecretForAuthenticatedUser: [\n      \"GET /user/codespaces/secrets/{secret_name}\",\n    ],\n    listDevcontainersInRepositoryForAuthenticatedUser: [\n      \"GET /repos/{owner}/{repo}/codespaces/devcontainers\",\n    ],\n    listForAuthenticatedUser: [\"GET /user/codespaces\"],\n    listInOrganization: [\n      \"GET /orgs/{org}/codespaces\",\n      {},\n      { renamedParameters: { org_id: \"org\" } },\n    ],\n    listInRepositoryForAuthenticatedUser: [\n      \"GET /repos/{owner}/{repo}/codespaces\",\n    ],\n    listOrgSecrets: [\"GET /orgs/{org}/codespaces/secrets\"],\n    listRepoSecrets: [\"GET /repos/{owner}/{repo}/codespaces/secrets\"],\n    listRepositoriesForSecretForAuthenticatedUser: [\n      \"GET /user/codespaces/secrets/{secret_name}/repositories\",\n    ],\n    listSecretsForAuthenticatedUser: [\"GET /user/codespaces/secrets\"],\n    listSelectedReposForOrgSecret: [\n      \"GET /orgs/{org}/codespaces/secrets/{secret_name}/repositories\",\n    ],\n    preFlightWithRepoForAuthenticatedUser: [\n      \"GET /repos/{owner}/{repo}/codespaces/new\",\n    ],\n    publishForAuthenticatedUser: [\n      \"POST /user/codespaces/{codespace_name}/publish\",\n    ],\n    removeRepositoryForSecretForAuthenticatedUser: [\n      \"DELETE /user/codespaces/secrets/{secret_name}/repositories/{repository_id}\",\n    ],\n    removeSelectedRepoFromOrgSecret: [\n      \"DELETE /orgs/{org}/codespaces/secrets/{secret_name}/repositories/{repository_id}\",\n    ],\n    repoMachinesForAuthenticatedUser: [\n      \"GET /repos/{owner}/{repo}/codespaces/machines\",\n    ],\n    setRepositoriesForSecretForAuthenticatedUser: [\n      \"PUT /user/codespaces/secrets/{secret_name}/repositories\",\n    ],\n    setSelectedReposForOrgSecret: [\n      \"PUT /orgs/{org}/codespaces/secrets/{secret_name}/repositories\",\n    ],\n    startForAuthenticatedUser: [\"POST /user/codespaces/{codespace_name}/start\"],\n    stopForAuthenticatedUser: [\"POST /user/codespaces/{codespace_name}/stop\"],\n    stopInOrganization: [\n      \"POST /orgs/{org}/members/{username}/codespaces/{codespace_name}/stop\",\n    ],\n    updateForAuthenticatedUser: [\"PATCH /user/codespaces/{codespace_name}\"],\n  },\n  copilot: {\n    addCopilotSeatsForTeams: [\n      \"POST /orgs/{org}/copilot/billing/selected_teams\",\n    ],\n    addCopilotSeatsForUsers: [\n      \"POST /orgs/{org}/copilot/billing/selected_users\",\n    ],\n    cancelCopilotSeatAssignmentForTeams: [\n      \"DELETE /orgs/{org}/copilot/billing/selected_teams\",\n    ],\n    cancelCopilotSeatAssignmentForUsers: [\n      \"DELETE /orgs/{org}/copilot/billing/selected_users\",\n    ],\n    copilotMetricsForOrganization: [\"GET /orgs/{org}/copilot/metrics\"],\n    copilotMetricsForTeam: [\"GET /orgs/{org}/team/{team_slug}/copilot/metrics\"],\n    getCopilotOrganizationDetails: [\"GET /orgs/{org}/copilot/billing\"],\n    getCopilotSeatDetailsForUser: [\n      \"GET /orgs/{org}/members/{username}/copilot\",\n    ],\n    listCopilotSeats: [\"GET /orgs/{org}/copilot/billing/seats\"],\n  },\n  credentials: { revoke: [\"POST /credentials/revoke\"] },\n  dependabot: {\n    addSelectedRepoToOrgSecret: [\n      \"PUT /orgs/{org}/dependabot/secrets/{secret_name}/repositories/{repository_id}\",\n    ],\n    createOrUpdateOrgSecret: [\n      \"PUT /orgs/{org}/dependabot/secrets/{secret_name}\",\n    ],\n    createOrUpdateRepoSecret: [\n      \"PUT /repos/{owner}/{repo}/dependabot/secrets/{secret_name}\",\n    ],\n    deleteOrgSecret: [\"DELETE /orgs/{org}/dependabot/secrets/{secret_name}\"],\n    deleteRepoSecret: [\n      \"DELETE /repos/{owner}/{repo}/dependabot/secrets/{secret_name}\",\n    ],\n    getAlert: [\"GET /repos/{owner}/{repo}/dependabot/alerts/{alert_number}\"],\n    getOrgPublicKey: [\"GET /orgs/{org}/dependabot/secrets/public-key\"],\n    getOrgSecret: [\"GET /orgs/{org}/dependabot/secrets/{secret_name}\"],\n    getRepoPublicKey: [\n      \"GET /repos/{owner}/{repo}/dependabot/secrets/public-key\",\n    ],\n    getRepoSecret: [\n      \"GET /repos/{owner}/{repo}/dependabot/secrets/{secret_name}\",\n    ],\n    listAlertsForEnterprise: [\n      \"GET /enterprises/{enterprise}/dependabot/alerts\",\n    ],\n    listAlertsForOrg: [\"GET /orgs/{org}/dependabot/alerts\"],\n    listAlertsForRepo: [\"GET /repos/{owner}/{repo}/dependabot/alerts\"],\n    listOrgSecrets: [\"GET /orgs/{org}/dependabot/secrets\"],\n    listRepoSecrets: [\"GET /repos/{owner}/{repo}/dependabot/secrets\"],\n    listSelectedReposForOrgSecret: [\n      \"GET /orgs/{org}/dependabot/secrets/{secret_name}/repositories\",\n    ],\n    removeSelectedRepoFromOrgSecret: [\n      \"DELETE /orgs/{org}/dependabot/secrets/{secret_name}/repositories/{repository_id}\",\n    ],\n    setSelectedReposForOrgSecret: [\n      \"PUT /orgs/{org}/dependabot/secrets/{secret_name}/repositories\",\n    ],\n    updateAlert: [\n      \"PATCH /repos/{owner}/{repo}/dependabot/alerts/{alert_number}\",\n    ],\n  },\n  dependencyGraph: {\n    createRepositorySnapshot: [\n      \"POST /repos/{owner}/{repo}/dependency-graph/snapshots\",\n    ],\n    diffRange: [\n      \"GET /repos/{owner}/{repo}/dependency-graph/compare/{basehead}\",\n    ],\n    exportSbom: [\"GET /repos/{owner}/{repo}/dependency-graph/sbom\"],\n  },\n  emojis: { get: [\"GET /emojis\"] },\n  gists: {\n    checkIsStarred: [\"GET /gists/{gist_id}/star\"],\n    create: [\"POST /gists\"],\n    createComment: [\"POST /gists/{gist_id}/comments\"],\n    delete: [\"DELETE /gists/{gist_id}\"],\n    deleteComment: [\"DELETE /gists/{gist_id}/comments/{comment_id}\"],\n    fork: [\"POST /gists/{gist_id}/forks\"],\n    get: [\"GET /gists/{gist_id}\"],\n    getComment: [\"GET /gists/{gist_id}/comments/{comment_id}\"],\n    getRevision: [\"GET /gists/{gist_id}/{sha}\"],\n    list: [\"GET /gists\"],\n    listComments: [\"GET /gists/{gist_id}/comments\"],\n    listCommits: [\"GET /gists/{gist_id}/commits\"],\n    listForUser: [\"GET /users/{username}/gists\"],\n    listForks: [\"GET /gists/{gist_id}/forks\"],\n    listPublic: [\"GET /gists/public\"],\n    listStarred: [\"GET /gists/starred\"],\n    star: [\"PUT /gists/{gist_id}/star\"],\n    unstar: [\"DELETE /gists/{gist_id}/star\"],\n    update: [\"PATCH /gists/{gist_id}\"],\n    updateComment: [\"PATCH /gists/{gist_id}/comments/{comment_id}\"],\n  },\n  git: {\n    createBlob: [\"POST /repos/{owner}/{repo}/git/blobs\"],\n    createCommit: [\"POST /repos/{owner}/{repo}/git/commits\"],\n    createRef: [\"POST /repos/{owner}/{repo}/git/refs\"],\n    createTag: [\"POST /repos/{owner}/{repo}/git/tags\"],\n    createTree: [\"POST /repos/{owner}/{repo}/git/trees\"],\n    deleteRef: [\"DELETE /repos/{owner}/{repo}/git/refs/{ref}\"],\n    getBlob: [\"GET /repos/{owner}/{repo}/git/blobs/{file_sha}\"],\n    getCommit: [\"GET /repos/{owner}/{repo}/git/commits/{commit_sha}\"],\n    getRef: [\"GET /repos/{owner}/{repo}/git/ref/{ref}\"],\n    getTag: [\"GET /repos/{owner}/{repo}/git/tags/{tag_sha}\"],\n    getTree: [\"GET /repos/{owner}/{repo}/git/trees/{tree_sha}\"],\n    listMatchingRefs: [\"GET /repos/{owner}/{repo}/git/matching-refs/{ref}\"],\n    updateRef: [\"PATCH /repos/{owner}/{repo}/git/refs/{ref}\"],\n  },\n  gitignore: {\n    getAllTemplates: [\"GET /gitignore/templates\"],\n    getTemplate: [\"GET /gitignore/templates/{name}\"],\n  },\n  hostedCompute: {\n    createNetworkConfigurationForOrg: [\n      \"POST /orgs/{org}/settings/network-configurations\",\n    ],\n    deleteNetworkConfigurationFromOrg: [\n      \"DELETE /orgs/{org}/settings/network-configurations/{network_configuration_id}\",\n    ],\n    getNetworkConfigurationForOrg: [\n      \"GET /orgs/{org}/settings/network-configurations/{network_configuration_id}\",\n    ],\n    getNetworkSettingsForOrg: [\n      \"GET /orgs/{org}/settings/network-settings/{network_settings_id}\",\n    ],\n    listNetworkConfigurationsForOrg: [\n      \"GET /orgs/{org}/settings/network-configurations\",\n    ],\n    updateNetworkConfigurationForOrg: [\n      \"PATCH /orgs/{org}/settings/network-configurations/{network_configuration_id}\",\n    ],\n  },\n  interactions: {\n    getRestrictionsForAuthenticatedUser: [\"GET /user/interaction-limits\"],\n    getRestrictionsForOrg: [\"GET /orgs/{org}/interaction-limits\"],\n    getRestrictionsForRepo: [\"GET /repos/{owner}/{repo}/interaction-limits\"],\n    getRestrictionsForYourPublicRepos: [\n      \"GET /user/interaction-limits\",\n      {},\n      { renamed: [\"interactions\", \"getRestrictionsForAuthenticatedUser\"] },\n    ],\n    removeRestrictionsForAuthenticatedUser: [\"DELETE /user/interaction-limits\"],\n    removeRestrictionsForOrg: [\"DELETE /orgs/{org}/interaction-limits\"],\n    removeRestrictionsForRepo: [\n      \"DELETE /repos/{owner}/{repo}/interaction-limits\",\n    ],\n    removeRestrictionsForYourPublicRepos: [\n      \"DELETE /user/interaction-limits\",\n      {},\n      { renamed: [\"interactions\", \"removeRestrictionsForAuthenticatedUser\"] },\n    ],\n    setRestrictionsForAuthenticatedUser: [\"PUT /user/interaction-limits\"],\n    setRestrictionsForOrg: [\"PUT /orgs/{org}/interaction-limits\"],\n    setRestrictionsForRepo: [\"PUT /repos/{owner}/{repo}/interaction-limits\"],\n    setRestrictionsForYourPublicRepos: [\n      \"PUT /user/interaction-limits\",\n      {},\n      { renamed: [\"interactions\", \"setRestrictionsForAuthenticatedUser\"] },\n    ],\n  },\n  issues: {\n    addAssignees: [\n      \"POST /repos/{owner}/{repo}/issues/{issue_number}/assignees\",\n    ],\n    addLabels: [\"POST /repos/{owner}/{repo}/issues/{issue_number}/labels\"],\n    addSubIssue: [\n      \"POST /repos/{owner}/{repo}/issues/{issue_number}/sub_issues\",\n    ],\n    checkUserCanBeAssigned: [\"GET /repos/{owner}/{repo}/assignees/{assignee}\"],\n    checkUserCanBeAssignedToIssue: [\n      \"GET /repos/{owner}/{repo}/issues/{issue_number}/assignees/{assignee}\",\n    ],\n    create: [\"POST /repos/{owner}/{repo}/issues\"],\n    createComment: [\n      \"POST /repos/{owner}/{repo}/issues/{issue_number}/comments\",\n    ],\n    createLabel: [\"POST /repos/{owner}/{repo}/labels\"],\n    createMilestone: [\"POST /repos/{owner}/{repo}/milestones\"],\n    deleteComment: [\n      \"DELETE /repos/{owner}/{repo}/issues/comments/{comment_id}\",\n    ],\n    deleteLabel: [\"DELETE /repos/{owner}/{repo}/labels/{name}\"],\n    deleteMilestone: [\n      \"DELETE /repos/{owner}/{repo}/milestones/{milestone_number}\",\n    ],\n    get: [\"GET /repos/{owner}/{repo}/issues/{issue_number}\"],\n    getComment: [\"GET /repos/{owner}/{repo}/issues/comments/{comment_id}\"],\n    getEvent: [\"GET /repos/{owner}/{repo}/issues/events/{event_id}\"],\n    getLabel: [\"GET /repos/{owner}/{repo}/labels/{name}\"],\n    getMilestone: [\"GET /repos/{owner}/{repo}/milestones/{milestone_number}\"],\n    list: [\"GET /issues\"],\n    listAssignees: [\"GET /repos/{owner}/{repo}/assignees\"],\n    listComments: [\"GET /repos/{owner}/{repo}/issues/{issue_number}/comments\"],\n    listCommentsForRepo: [\"GET /repos/{owner}/{repo}/issues/comments\"],\n    listEvents: [\"GET /repos/{owner}/{repo}/issues/{issue_number}/events\"],\n    listEventsForRepo: [\"GET /repos/{owner}/{repo}/issues/events\"],\n    listEventsForTimeline: [\n      \"GET /repos/{owner}/{repo}/issues/{issue_number}/timeline\",\n    ],\n    listForAuthenticatedUser: [\"GET /user/issues\"],\n    listForOrg: [\"GET /orgs/{org}/issues\"],\n    listForRepo: [\"GET /repos/{owner}/{repo}/issues\"],\n    listLabelsForMilestone: [\n      \"GET /repos/{owner}/{repo}/milestones/{milestone_number}/labels\",\n    ],\n    listLabelsForRepo: [\"GET /repos/{owner}/{repo}/labels\"],\n    listLabelsOnIssue: [\n      \"GET /repos/{owner}/{repo}/issues/{issue_number}/labels\",\n    ],\n    listMilestones: [\"GET /repos/{owner}/{repo}/milestones\"],\n    listSubIssues: [\n      \"GET /repos/{owner}/{repo}/issues/{issue_number}/sub_issues\",\n    ],\n    lock: [\"PUT /repos/{owner}/{repo}/issues/{issue_number}/lock\"],\n    removeAllLabels: [\n      \"DELETE /repos/{owner}/{repo}/issues/{issue_number}/labels\",\n    ],\n    removeAssignees: [\n      \"DELETE /repos/{owner}/{repo}/issues/{issue_number}/assignees\",\n    ],\n    removeLabel: [\n      \"DELETE /repos/{owner}/{repo}/issues/{issue_number}/labels/{name}\",\n    ],\n    removeSubIssue: [\n      \"DELETE /repos/{owner}/{repo}/issues/{issue_number}/sub_issue\",\n    ],\n    reprioritizeSubIssue: [\n      \"PATCH /repos/{owner}/{repo}/issues/{issue_number}/sub_issues/priority\",\n    ],\n    setLabels: [\"PUT /repos/{owner}/{repo}/issues/{issue_number}/labels\"],\n    unlock: [\"DELETE /repos/{owner}/{repo}/issues/{issue_number}/lock\"],\n    update: [\"PATCH /repos/{owner}/{repo}/issues/{issue_number}\"],\n    updateComment: [\"PATCH /repos/{owner}/{repo}/issues/comments/{comment_id}\"],\n    updateLabel: [\"PATCH /repos/{owner}/{repo}/labels/{name}\"],\n    updateMilestone: [\n      \"PATCH /repos/{owner}/{repo}/milestones/{milestone_number}\",\n    ],\n  },\n  licenses: {\n    get: [\"GET /licenses/{license}\"],\n    getAllCommonlyUsed: [\"GET /licenses\"],\n    getForRepo: [\"GET /repos/{owner}/{repo}/license\"],\n  },\n  markdown: {\n    render: [\"POST /markdown\"],\n    renderRaw: [\n      \"POST /markdown/raw\",\n      { headers: { \"content-type\": \"text/plain; charset=utf-8\" } },\n    ],\n  },\n  meta: {\n    get: [\"GET /meta\"],\n    getAllVersions: [\"GET /versions\"],\n    getOctocat: [\"GET /octocat\"],\n    getZen: [\"GET /zen\"],\n    root: [\"GET /\"],\n  },\n  migrations: {\n    deleteArchiveForAuthenticatedUser: [\n      \"DELETE /user/migrations/{migration_id}/archive\",\n    ],\n    deleteArchiveForOrg: [\n      \"DELETE /orgs/{org}/migrations/{migration_id}/archive\",\n    ],\n    downloadArchiveForOrg: [\n      \"GET /orgs/{org}/migrations/{migration_id}/archive\",\n    ],\n    getArchiveForAuthenticatedUser: [\n      \"GET /user/migrations/{migration_id}/archive\",\n    ],\n    getStatusForAuthenticatedUser: [\"GET /user/migrations/{migration_id}\"],\n    getStatusForOrg: [\"GET /orgs/{org}/migrations/{migration_id}\"],\n    listForAuthenticatedUser: [\"GET /user/migrations\"],\n    listForOrg: [\"GET /orgs/{org}/migrations\"],\n    listReposForAuthenticatedUser: [\n      \"GET /user/migrations/{migration_id}/repositories\",\n    ],\n    listReposForOrg: [\"GET /orgs/{org}/migrations/{migration_id}/repositories\"],\n    listReposForUser: [\n      \"GET /user/migrations/{migration_id}/repositories\",\n      {},\n      { renamed: [\"migrations\", \"listReposForAuthenticatedUser\"] },\n    ],\n    startForAuthenticatedUser: [\"POST /user/migrations\"],\n    startForOrg: [\"POST /orgs/{org}/migrations\"],\n    unlockRepoForAuthenticatedUser: [\n      \"DELETE /user/migrations/{migration_id}/repos/{repo_name}/lock\",\n    ],\n    unlockRepoForOrg: [\n      \"DELETE /orgs/{org}/migrations/{migration_id}/repos/{repo_name}/lock\",\n    ],\n  },\n  oidc: {\n    getOidcCustomSubTemplateForOrg: [\n      \"GET /orgs/{org}/actions/oidc/customization/sub\",\n    ],\n    updateOidcCustomSubTemplateForOrg: [\n      \"PUT /orgs/{org}/actions/oidc/customization/sub\",\n    ],\n  },\n  orgs: {\n    addSecurityManagerTeam: [\n      \"PUT /orgs/{org}/security-managers/teams/{team_slug}\",\n      {},\n      {\n        deprecated:\n          \"octokit.rest.orgs.addSecurityManagerTeam() is deprecated, see https://docs.github.com/rest/orgs/security-managers#add-a-security-manager-team\",\n      },\n    ],\n    assignTeamToOrgRole: [\n      \"PUT /orgs/{org}/organization-roles/teams/{team_slug}/{role_id}\",\n    ],\n    assignUserToOrgRole: [\n      \"PUT /orgs/{org}/organization-roles/users/{username}/{role_id}\",\n    ],\n    blockUser: [\"PUT /orgs/{org}/blocks/{username}\"],\n    cancelInvitation: [\"DELETE /orgs/{org}/invitations/{invitation_id}\"],\n    checkBlockedUser: [\"GET /orgs/{org}/blocks/{username}\"],\n    checkMembershipForUser: [\"GET /orgs/{org}/members/{username}\"],\n    checkPublicMembershipForUser: [\"GET /orgs/{org}/public_members/{username}\"],\n    convertMemberToOutsideCollaborator: [\n      \"PUT /orgs/{org}/outside_collaborators/{username}\",\n    ],\n    createInvitation: [\"POST /orgs/{org}/invitations\"],\n    createIssueType: [\"POST /orgs/{org}/issue-types\"],\n    createOrUpdateCustomProperties: [\"PATCH /orgs/{org}/properties/schema\"],\n    createOrUpdateCustomPropertiesValuesForRepos: [\n      \"PATCH /orgs/{org}/properties/values\",\n    ],\n    createOrUpdateCustomProperty: [\n      \"PUT /orgs/{org}/properties/schema/{custom_property_name}\",\n    ],\n    createWebhook: [\"POST /orgs/{org}/hooks\"],\n    delete: [\"DELETE /orgs/{org}\"],\n    deleteIssueType: [\"DELETE /orgs/{org}/issue-types/{issue_type_id}\"],\n    deleteWebhook: [\"DELETE /orgs/{org}/hooks/{hook_id}\"],\n    enableOrDisableSecurityProductOnAllOrgRepos: [\n      \"POST /orgs/{org}/{security_product}/{enablement}\",\n      {},\n      {\n        deprecated:\n          \"octokit.rest.orgs.enableOrDisableSecurityProductOnAllOrgRepos() is deprecated, see https://docs.github.com/rest/orgs/orgs#enable-or-disable-a-security-feature-for-an-organization\",\n      },\n    ],\n    get: [\"GET /orgs/{org}\"],\n    getAllCustomProperties: [\"GET /orgs/{org}/properties/schema\"],\n    getCustomProperty: [\n      \"GET /orgs/{org}/properties/schema/{custom_property_name}\",\n    ],\n    getMembershipForAuthenticatedUser: [\"GET /user/memberships/orgs/{org}\"],\n    getMembershipForUser: [\"GET /orgs/{org}/memberships/{username}\"],\n    getOrgRole: [\"GET /orgs/{org}/organization-roles/{role_id}\"],\n    getOrgRulesetHistory: [\"GET /orgs/{org}/rulesets/{ruleset_id}/history\"],\n    getOrgRulesetVersion: [\n      \"GET /orgs/{org}/rulesets/{ruleset_id}/history/{version_id}\",\n    ],\n    getWebhook: [\"GET /orgs/{org}/hooks/{hook_id}\"],\n    getWebhookConfigForOrg: [\"GET /orgs/{org}/hooks/{hook_id}/config\"],\n    getWebhookDelivery: [\n      \"GET /orgs/{org}/hooks/{hook_id}/deliveries/{delivery_id}\",\n    ],\n    list: [\"GET /organizations\"],\n    listAppInstallations: [\"GET /orgs/{org}/installations\"],\n    listAttestations: [\"GET /orgs/{org}/attestations/{subject_digest}\"],\n    listBlockedUsers: [\"GET /orgs/{org}/blocks\"],\n    listCustomPropertiesValuesForRepos: [\"GET /orgs/{org}/properties/values\"],\n    listFailedInvitations: [\"GET /orgs/{org}/failed_invitations\"],\n    listForAuthenticatedUser: [\"GET /user/orgs\"],\n    listForUser: [\"GET /users/{username}/orgs\"],\n    listInvitationTeams: [\"GET /orgs/{org}/invitations/{invitation_id}/teams\"],\n    listIssueTypes: [\"GET /orgs/{org}/issue-types\"],\n    listMembers: [\"GET /orgs/{org}/members\"],\n    listMembershipsForAuthenticatedUser: [\"GET /user/memberships/orgs\"],\n    listOrgRoleTeams: [\"GET /orgs/{org}/organization-roles/{role_id}/teams\"],\n    listOrgRoleUsers: [\"GET /orgs/{org}/organization-roles/{role_id}/users\"],\n    listOrgRoles: [\"GET /orgs/{org}/organization-roles\"],\n    listOrganizationFineGrainedPermissions: [\n      \"GET /orgs/{org}/organization-fine-grained-permissions\",\n    ],\n    listOutsideCollaborators: [\"GET /orgs/{org}/outside_collaborators\"],\n    listPatGrantRepositories: [\n      \"GET /orgs/{org}/personal-access-tokens/{pat_id}/repositories\",\n    ],\n    listPatGrantRequestRepositories: [\n      \"GET /orgs/{org}/personal-access-token-requests/{pat_request_id}/repositories\",\n    ],\n    listPatGrantRequests: [\"GET /orgs/{org}/personal-access-token-requests\"],\n    listPatGrants: [\"GET /orgs/{org}/personal-access-tokens\"],\n    listPendingInvitations: [\"GET /orgs/{org}/invitations\"],\n    listPublicMembers: [\"GET /orgs/{org}/public_members\"],\n    listSecurityManagerTeams: [\n      \"GET /orgs/{org}/security-managers\",\n      {},\n      {\n        deprecated:\n          \"octokit.rest.orgs.listSecurityManagerTeams() is deprecated, see https://docs.github.com/rest/orgs/security-managers#list-security-manager-teams\",\n      },\n    ],\n    listWebhookDeliveries: [\"GET /orgs/{org}/hooks/{hook_id}/deliveries\"],\n    listWebhooks: [\"GET /orgs/{org}/hooks\"],\n    pingWebhook: [\"POST /orgs/{org}/hooks/{hook_id}/pings\"],\n    redeliverWebhookDelivery: [\n      \"POST /orgs/{org}/hooks/{hook_id}/deliveries/{delivery_id}/attempts\",\n    ],\n    removeCustomProperty: [\n      \"DELETE /orgs/{org}/properties/schema/{custom_property_name}\",\n    ],\n    removeMember: [\"DELETE /orgs/{org}/members/{username}\"],\n    removeMembershipForUser: [\"DELETE /orgs/{org}/memberships/{username}\"],\n    removeOutsideCollaborator: [\n      \"DELETE /orgs/{org}/outside_collaborators/{username}\",\n    ],\n    removePublicMembershipForAuthenticatedUser: [\n      \"DELETE /orgs/{org}/public_members/{username}\",\n    ],\n    removeSecurityManagerTeam: [\n      \"DELETE /orgs/{org}/security-managers/teams/{team_slug}\",\n      {},\n      {\n        deprecated:\n          \"octokit.rest.orgs.removeSecurityManagerTeam() is deprecated, see https://docs.github.com/rest/orgs/security-managers#remove-a-security-manager-team\",\n      },\n    ],\n    reviewPatGrantRequest: [\n      \"POST /orgs/{org}/personal-access-token-requests/{pat_request_id}\",\n    ],\n    reviewPatGrantRequestsInBulk: [\n      \"POST /orgs/{org}/personal-access-token-requests\",\n    ],\n    revokeAllOrgRolesTeam: [\n      \"DELETE /orgs/{org}/organization-roles/teams/{team_slug}\",\n    ],\n    revokeAllOrgRolesUser: [\n      \"DELETE /orgs/{org}/organization-roles/users/{username}\",\n    ],\n    revokeOrgRoleTeam: [\n      \"DELETE /orgs/{org}/organization-roles/teams/{team_slug}/{role_id}\",\n    ],\n    revokeOrgRoleUser: [\n      \"DELETE /orgs/{org}/organization-roles/users/{username}/{role_id}\",\n    ],\n    setMembershipForUser: [\"PUT /orgs/{org}/memberships/{username}\"],\n    setPublicMembershipForAuthenticatedUser: [\n      \"PUT /orgs/{org}/public_members/{username}\",\n    ],\n    unblockUser: [\"DELETE /orgs/{org}/blocks/{username}\"],\n    update: [\"PATCH /orgs/{org}\"],\n    updateIssueType: [\"PUT /orgs/{org}/issue-types/{issue_type_id}\"],\n    updateMembershipForAuthenticatedUser: [\n      \"PATCH /user/memberships/orgs/{org}\",\n    ],\n    updatePatAccess: [\"POST /orgs/{org}/personal-access-tokens/{pat_id}\"],\n    updatePatAccesses: [\"POST /orgs/{org}/personal-access-tokens\"],\n    updateWebhook: [\"PATCH /orgs/{org}/hooks/{hook_id}\"],\n    updateWebhookConfigForOrg: [\"PATCH /orgs/{org}/hooks/{hook_id}/config\"],\n  },\n  packages: {\n    deletePackageForAuthenticatedUser: [\n      \"DELETE /user/packages/{package_type}/{package_name}\",\n    ],\n    deletePackageForOrg: [\n      \"DELETE /orgs/{org}/packages/{package_type}/{package_name}\",\n    ],\n    deletePackageForUser: [\n      \"DELETE /users/{username}/packages/{package_type}/{package_name}\",\n    ],\n    deletePackageVersionForAuthenticatedUser: [\n      \"DELETE /user/packages/{package_type}/{package_name}/versions/{package_version_id}\",\n    ],\n    deletePackageVersionForOrg: [\n      \"DELETE /orgs/{org}/packages/{package_type}/{package_name}/versions/{package_version_id}\",\n    ],\n    deletePackageVersionForUser: [\n      \"DELETE /users/{username}/packages/{package_type}/{package_name}/versions/{package_version_id}\",\n    ],\n    getAllPackageVersionsForAPackageOwnedByAnOrg: [\n      \"GET /orgs/{org}/packages/{package_type}/{package_name}/versions\",\n      {},\n      { renamed: [\"packages\", \"getAllPackageVersionsForPackageOwnedByOrg\"] },\n    ],\n    getAllPackageVersionsForAPackageOwnedByTheAuthenticatedUser: [\n      \"GET /user/packages/{package_type}/{package_name}/versions\",\n      {},\n      {\n        renamed: [\n          \"packages\",\n          \"getAllPackageVersionsForPackageOwnedByAuthenticatedUser\",\n        ],\n      },\n    ],\n    getAllPackageVersionsForPackageOwnedByAuthenticatedUser: [\n      \"GET /user/packages/{package_type}/{package_name}/versions\",\n    ],\n    getAllPackageVersionsForPackageOwnedByOrg: [\n      \"GET /orgs/{org}/packages/{package_type}/{package_name}/versions\",\n    ],\n    getAllPackageVersionsForPackageOwnedByUser: [\n      \"GET /users/{username}/packages/{package_type}/{package_name}/versions\",\n    ],\n    getPackageForAuthenticatedUser: [\n      \"GET /user/packages/{package_type}/{package_name}\",\n    ],\n    getPackageForOrganization: [\n      \"GET /orgs/{org}/packages/{package_type}/{package_name}\",\n    ],\n    getPackageForUser: [\n      \"GET /users/{username}/packages/{package_type}/{package_name}\",\n    ],\n    getPackageVersionForAuthenticatedUser: [\n      \"GET /user/packages/{package_type}/{package_name}/versions/{package_version_id}\",\n    ],\n    getPackageVersionForOrganization: [\n      \"GET /orgs/{org}/packages/{package_type}/{package_name}/versions/{package_version_id}\",\n    ],\n    getPackageVersionForUser: [\n      \"GET /users/{username}/packages/{package_type}/{package_name}/versions/{package_version_id}\",\n    ],\n    listDockerMigrationConflictingPackagesForAuthenticatedUser: [\n      \"GET /user/docker/conflicts\",\n    ],\n    listDockerMigrationConflictingPackagesForOrganization: [\n      \"GET /orgs/{org}/docker/conflicts\",\n    ],\n    listDockerMigrationConflictingPackagesForUser: [\n      \"GET /users/{username}/docker/conflicts\",\n    ],\n    listPackagesForAuthenticatedUser: [\"GET /user/packages\"],\n    listPackagesForOrganization: [\"GET /orgs/{org}/packages\"],\n    listPackagesForUser: [\"GET /users/{username}/packages\"],\n    restorePackageForAuthenticatedUser: [\n      \"POST /user/packages/{package_type}/{package_name}/restore{?token}\",\n    ],\n    restorePackageForOrg: [\n      \"POST /orgs/{org}/packages/{package_type}/{package_name}/restore{?token}\",\n    ],\n    restorePackageForUser: [\n      \"POST /users/{username}/packages/{package_type}/{package_name}/restore{?token}\",\n    ],\n    restorePackageVersionForAuthenticatedUser: [\n      \"POST /user/packages/{package_type}/{package_name}/versions/{package_version_id}/restore\",\n    ],\n    restorePackageVersionForOrg: [\n      \"POST /orgs/{org}/packages/{package_type}/{package_name}/versions/{package_version_id}/restore\",\n    ],\n    restorePackageVersionForUser: [\n      \"POST /users/{username}/packages/{package_type}/{package_name}/versions/{package_version_id}/restore\",\n    ],\n  },\n  privateRegistries: {\n    createOrgPrivateRegistry: [\"POST /orgs/{org}/private-registries\"],\n    deleteOrgPrivateRegistry: [\n      \"DELETE /orgs/{org}/private-registries/{secret_name}\",\n    ],\n    getOrgPrivateRegistry: [\"GET /orgs/{org}/private-registries/{secret_name}\"],\n    getOrgPublicKey: [\"GET /orgs/{org}/private-registries/public-key\"],\n    listOrgPrivateRegistries: [\"GET /orgs/{org}/private-registries\"],\n    updateOrgPrivateRegistry: [\n      \"PATCH /orgs/{org}/private-registries/{secret_name}\",\n    ],\n  },\n  pulls: {\n    checkIfMerged: [\"GET /repos/{owner}/{repo}/pulls/{pull_number}/merge\"],\n    create: [\"POST /repos/{owner}/{repo}/pulls\"],\n    createReplyForReviewComment: [\n      \"POST /repos/{owner}/{repo}/pulls/{pull_number}/comments/{comment_id}/replies\",\n    ],\n    createReview: [\"POST /repos/{owner}/{repo}/pulls/{pull_number}/reviews\"],\n    createReviewComment: [\n      \"POST /repos/{owner}/{repo}/pulls/{pull_number}/comments\",\n    ],\n    deletePendingReview: [\n      \"DELETE /repos/{owner}/{repo}/pulls/{pull_number}/reviews/{review_id}\",\n    ],\n    deleteReviewComment: [\n      \"DELETE /repos/{owner}/{repo}/pulls/comments/{comment_id}\",\n    ],\n    dismissReview: [\n      \"PUT /repos/{owner}/{repo}/pulls/{pull_number}/reviews/{review_id}/dismissals\",\n    ],\n    get: [\"GET /repos/{owner}/{repo}/pulls/{pull_number}\"],\n    getReview: [\n      \"GET /repos/{owner}/{repo}/pulls/{pull_number}/reviews/{review_id}\",\n    ],\n    getReviewComment: [\"GET /repos/{owner}/{repo}/pulls/comments/{comment_id}\"],\n    list: [\"GET /repos/{owner}/{repo}/pulls\"],\n    listCommentsForReview: [\n      \"GET /repos/{owner}/{repo}/pulls/{pull_number}/reviews/{review_id}/comments\",\n    ],\n    listCommits: [\"GET /repos/{owner}/{repo}/pulls/{pull_number}/commits\"],\n    listFiles: [\"GET /repos/{owner}/{repo}/pulls/{pull_number}/files\"],\n    listRequestedReviewers: [\n      \"GET /repos/{owner}/{repo}/pulls/{pull_number}/requested_reviewers\",\n    ],\n    listReviewComments: [\n      \"GET /repos/{owner}/{repo}/pulls/{pull_number}/comments\",\n    ],\n    listReviewCommentsForRepo: [\"GET /repos/{owner}/{repo}/pulls/comments\"],\n    listReviews: [\"GET /repos/{owner}/{repo}/pulls/{pull_number}/reviews\"],\n    merge: [\"PUT /repos/{owner}/{repo}/pulls/{pull_number}/merge\"],\n    removeRequestedReviewers: [\n      \"DELETE /repos/{owner}/{repo}/pulls/{pull_number}/requested_reviewers\",\n    ],\n    requestReviewers: [\n      \"POST /repos/{owner}/{repo}/pulls/{pull_number}/requested_reviewers\",\n    ],\n    submitReview: [\n      \"POST /repos/{owner}/{repo}/pulls/{pull_number}/reviews/{review_id}/events\",\n    ],\n    update: [\"PATCH /repos/{owner}/{repo}/pulls/{pull_number}\"],\n    updateBranch: [\n      \"PUT /repos/{owner}/{repo}/pulls/{pull_number}/update-branch\",\n    ],\n    updateReview: [\n      \"PUT /repos/{owner}/{repo}/pulls/{pull_number}/reviews/{review_id}\",\n    ],\n    updateReviewComment: [\n      \"PATCH /repos/{owner}/{repo}/pulls/comments/{comment_id}\",\n    ],\n  },\n  rateLimit: { get: [\"GET /rate_limit\"] },\n  reactions: {\n    createForCommitComment: [\n      \"POST /repos/{owner}/{repo}/comments/{comment_id}/reactions\",\n    ],\n    createForIssue: [\n      \"POST /repos/{owner}/{repo}/issues/{issue_number}/reactions\",\n    ],\n    createForIssueComment: [\n      \"POST /repos/{owner}/{repo}/issues/comments/{comment_id}/reactions\",\n    ],\n    createForPullRequestReviewComment: [\n      \"POST /repos/{owner}/{repo}/pulls/comments/{comment_id}/reactions\",\n    ],\n    createForRelease: [\n      \"POST /repos/{owner}/{repo}/releases/{release_id}/reactions\",\n    ],\n    createForTeamDiscussionCommentInOrg: [\n      \"POST /orgs/{org}/teams/{team_slug}/discussions/{discussion_number}/comments/{comment_number}/reactions\",\n    ],\n    createForTeamDiscussionInOrg: [\n      \"POST /orgs/{org}/teams/{team_slug}/discussions/{discussion_number}/reactions\",\n    ],\n    deleteForCommitComment: [\n      \"DELETE /repos/{owner}/{repo}/comments/{comment_id}/reactions/{reaction_id}\",\n    ],\n    deleteForIssue: [\n      \"DELETE /repos/{owner}/{repo}/issues/{issue_number}/reactions/{reaction_id}\",\n    ],\n    deleteForIssueComment: [\n      \"DELETE /repos/{owner}/{repo}/issues/comments/{comment_id}/reactions/{reaction_id}\",\n    ],\n    deleteForPullRequestComment: [\n      \"DELETE /repos/{owner}/{repo}/pulls/comments/{comment_id}/reactions/{reaction_id}\",\n    ],\n    deleteForRelease: [\n      \"DELETE /repos/{owner}/{repo}/releases/{release_id}/reactions/{reaction_id}\",\n    ],\n    deleteForTeamDiscussion: [\n      \"DELETE /orgs/{org}/teams/{team_slug}/discussions/{discussion_number}/reactions/{reaction_id}\",\n    ],\n    deleteForTeamDiscussionComment: [\n      \"DELETE /orgs/{org}/teams/{team_slug}/discussions/{discussion_number}/comments/{comment_number}/reactions/{reaction_id}\",\n    ],\n    listForCommitComment: [\n      \"GET /repos/{owner}/{repo}/comments/{comment_id}/reactions\",\n    ],\n    listForIssue: [\"GET /repos/{owner}/{repo}/issues/{issue_number}/reactions\"],\n    listForIssueComment: [\n      \"GET /repos/{owner}/{repo}/issues/comments/{comment_id}/reactions\",\n    ],\n    listForPullRequestReviewComment: [\n      \"GET /repos/{owner}/{repo}/pulls/comments/{comment_id}/reactions\",\n    ],\n    listForRelease: [\n      \"GET /repos/{owner}/{repo}/releases/{release_id}/reactions\",\n    ],\n    listForTeamDiscussionCommentInOrg: [\n      \"GET /orgs/{org}/teams/{team_slug}/discussions/{discussion_number}/comments/{comment_number}/reactions\",\n    ],\n    listForTeamDiscussionInOrg: [\n      \"GET /orgs/{org}/teams/{team_slug}/discussions/{discussion_number}/reactions\",\n    ],\n  },\n  repos: {\n    acceptInvitation: [\n      \"PATCH /user/repository_invitations/{invitation_id}\",\n      {},\n      { renamed: [\"repos\", \"acceptInvitationForAuthenticatedUser\"] },\n    ],\n    acceptInvitationForAuthenticatedUser: [\n      \"PATCH /user/repository_invitations/{invitation_id}\",\n    ],\n    addAppAccessRestrictions: [\n      \"POST /repos/{owner}/{repo}/branches/{branch}/protection/restrictions/apps\",\n      {},\n      { mapToData: \"apps\" },\n    ],\n    addCollaborator: [\"PUT /repos/{owner}/{repo}/collaborators/{username}\"],\n    addStatusCheckContexts: [\n      \"POST /repos/{owner}/{repo}/branches/{branch}/protection/required_status_checks/contexts\",\n      {},\n      { mapToData: \"contexts\" },\n    ],\n    addTeamAccessRestrictions: [\n      \"POST /repos/{owner}/{repo}/branches/{branch}/protection/restrictions/teams\",\n      {},\n      { mapToData: \"teams\" },\n    ],\n    addUserAccessRestrictions: [\n      \"POST /repos/{owner}/{repo}/branches/{branch}/protection/restrictions/users\",\n      {},\n      { mapToData: \"users\" },\n    ],\n    cancelPagesDeployment: [\n      \"POST /repos/{owner}/{repo}/pages/deployments/{pages_deployment_id}/cancel\",\n    ],\n    checkAutomatedSecurityFixes: [\n      \"GET /repos/{owner}/{repo}/automated-security-fixes\",\n    ],\n    checkCollaborator: [\"GET /repos/{owner}/{repo}/collaborators/{username}\"],\n    checkPrivateVulnerabilityReporting: [\n      \"GET /repos/{owner}/{repo}/private-vulnerability-reporting\",\n    ],\n    checkVulnerabilityAlerts: [\n      \"GET /repos/{owner}/{repo}/vulnerability-alerts\",\n    ],\n    codeownersErrors: [\"GET /repos/{owner}/{repo}/codeowners/errors\"],\n    compareCommits: [\"GET /repos/{owner}/{repo}/compare/{base}...{head}\"],\n    compareCommitsWithBasehead: [\n      \"GET /repos/{owner}/{repo}/compare/{basehead}\",\n    ],\n    createAttestation: [\"POST /repos/{owner}/{repo}/attestations\"],\n    createAutolink: [\"POST /repos/{owner}/{repo}/autolinks\"],\n    createCommitComment: [\n      \"POST /repos/{owner}/{repo}/commits/{commit_sha}/comments\",\n    ],\n    createCommitSignatureProtection: [\n      \"POST /repos/{owner}/{repo}/branches/{branch}/protection/required_signatures\",\n    ],\n    createCommitStatus: [\"POST /repos/{owner}/{repo}/statuses/{sha}\"],\n    createDeployKey: [\"POST /repos/{owner}/{repo}/keys\"],\n    createDeployment: [\"POST /repos/{owner}/{repo}/deployments\"],\n    createDeploymentBranchPolicy: [\n      \"POST /repos/{owner}/{repo}/environments/{environment_name}/deployment-branch-policies\",\n    ],\n    createDeploymentProtectionRule: [\n      \"POST /repos/{owner}/{repo}/environments/{environment_name}/deployment_protection_rules\",\n    ],\n    createDeploymentStatus: [\n      \"POST /repos/{owner}/{repo}/deployments/{deployment_id}/statuses\",\n    ],\n    createDispatchEvent: [\"POST /repos/{owner}/{repo}/dispatches\"],\n    createForAuthenticatedUser: [\"POST /user/repos\"],\n    createFork: [\"POST /repos/{owner}/{repo}/forks\"],\n    createInOrg: [\"POST /orgs/{org}/repos\"],\n    createOrUpdateCustomPropertiesValues: [\n      \"PATCH /repos/{owner}/{repo}/properties/values\",\n    ],\n    createOrUpdateEnvironment: [\n      \"PUT /repos/{owner}/{repo}/environments/{environment_name}\",\n    ],\n    createOrUpdateFileContents: [\"PUT /repos/{owner}/{repo}/contents/{path}\"],\n    createOrgRuleset: [\"POST /orgs/{org}/rulesets\"],\n    createPagesDeployment: [\"POST /repos/{owner}/{repo}/pages/deployments\"],\n    createPagesSite: [\"POST /repos/{owner}/{repo}/pages\"],\n    createRelease: [\"POST /repos/{owner}/{repo}/releases\"],\n    createRepoRuleset: [\"POST /repos/{owner}/{repo}/rulesets\"],\n    createUsingTemplate: [\n      \"POST /repos/{template_owner}/{template_repo}/generate\",\n    ],\n    createWebhook: [\"POST /repos/{owner}/{repo}/hooks\"],\n    declineInvitation: [\n      \"DELETE /user/repository_invitations/{invitation_id}\",\n      {},\n      { renamed: [\"repos\", \"declineInvitationForAuthenticatedUser\"] },\n    ],\n    declineInvitationForAuthenticatedUser: [\n      \"DELETE /user/repository_invitations/{invitation_id}\",\n    ],\n    delete: [\"DELETE /repos/{owner}/{repo}\"],\n    deleteAccessRestrictions: [\n      \"DELETE /repos/{owner}/{repo}/branches/{branch}/protection/restrictions\",\n    ],\n    deleteAdminBranchProtection: [\n      \"DELETE /repos/{owner}/{repo}/branches/{branch}/protection/enforce_admins\",\n    ],\n    deleteAnEnvironment: [\n      \"DELETE /repos/{owner}/{repo}/environments/{environment_name}\",\n    ],\n    deleteAutolink: [\"DELETE /repos/{owner}/{repo}/autolinks/{autolink_id}\"],\n    deleteBranchProtection: [\n      \"DELETE /repos/{owner}/{repo}/branches/{branch}/protection\",\n    ],\n    deleteCommitComment: [\"DELETE /repos/{owner}/{repo}/comments/{comment_id}\"],\n    deleteCommitSignatureProtection: [\n      \"DELETE /repos/{owner}/{repo}/branches/{branch}/protection/required_signatures\",\n    ],\n    deleteDeployKey: [\"DELETE /repos/{owner}/{repo}/keys/{key_id}\"],\n    deleteDeployment: [\n      \"DELETE /repos/{owner}/{repo}/deployments/{deployment_id}\",\n    ],\n    deleteDeploymentBranchPolicy: [\n      \"DELETE /repos/{owner}/{repo}/environments/{environment_name}/deployment-branch-policies/{branch_policy_id}\",\n    ],\n    deleteFile: [\"DELETE /repos/{owner}/{repo}/contents/{path}\"],\n    deleteInvitation: [\n      \"DELETE /repos/{owner}/{repo}/invitations/{invitation_id}\",\n    ],\n    deleteOrgRuleset: [\"DELETE /orgs/{org}/rulesets/{ruleset_id}\"],\n    deletePagesSite: [\"DELETE /repos/{owner}/{repo}/pages\"],\n    deletePullRequestReviewProtection: [\n      \"DELETE /repos/{owner}/{repo}/branches/{branch}/protection/required_pull_request_reviews\",\n    ],\n    deleteRelease: [\"DELETE /repos/{owner}/{repo}/releases/{release_id}\"],\n    deleteReleaseAsset: [\n      \"DELETE /repos/{owner}/{repo}/releases/assets/{asset_id}\",\n    ],\n    deleteRepoRuleset: [\"DELETE /repos/{owner}/{repo}/rulesets/{ruleset_id}\"],\n    deleteWebhook: [\"DELETE /repos/{owner}/{repo}/hooks/{hook_id}\"],\n    disableAutomatedSecurityFixes: [\n      \"DELETE /repos/{owner}/{repo}/automated-security-fixes\",\n    ],\n    disableDeploymentProtectionRule: [\n      \"DELETE /repos/{owner}/{repo}/environments/{environment_name}/deployment_protection_rules/{protection_rule_id}\",\n    ],\n    disablePrivateVulnerabilityReporting: [\n      \"DELETE /repos/{owner}/{repo}/private-vulnerability-reporting\",\n    ],\n    disableVulnerabilityAlerts: [\n      \"DELETE /repos/{owner}/{repo}/vulnerability-alerts\",\n    ],\n    downloadArchive: [\n      \"GET /repos/{owner}/{repo}/zipball/{ref}\",\n      {},\n      { renamed: [\"repos\", \"downloadZipballArchive\"] },\n    ],\n    downloadTarballArchive: [\"GET /repos/{owner}/{repo}/tarball/{ref}\"],\n    downloadZipballArchive: [\"GET /repos/{owner}/{repo}/zipball/{ref}\"],\n    enableAutomatedSecurityFixes: [\n      \"PUT /repos/{owner}/{repo}/automated-security-fixes\",\n    ],\n    enablePrivateVulnerabilityReporting: [\n      \"PUT /repos/{owner}/{repo}/private-vulnerability-reporting\",\n    ],\n    enableVulnerabilityAlerts: [\n      \"PUT /repos/{owner}/{repo}/vulnerability-alerts\",\n    ],\n    generateReleaseNotes: [\n      \"POST /repos/{owner}/{repo}/releases/generate-notes\",\n    ],\n    get: [\"GET /repos/{owner}/{repo}\"],\n    getAccessRestrictions: [\n      \"GET /repos/{owner}/{repo}/branches/{branch}/protection/restrictions\",\n    ],\n    getAdminBranchProtection: [\n      \"GET /repos/{owner}/{repo}/branches/{branch}/protection/enforce_admins\",\n    ],\n    getAllDeploymentProtectionRules: [\n      \"GET /repos/{owner}/{repo}/environments/{environment_name}/deployment_protection_rules\",\n    ],\n    getAllEnvironments: [\"GET /repos/{owner}/{repo}/environments\"],\n    getAllStatusCheckContexts: [\n      \"GET /repos/{owner}/{repo}/branches/{branch}/protection/required_status_checks/contexts\",\n    ],\n    getAllTopics: [\"GET /repos/{owner}/{repo}/topics\"],\n    getAppsWithAccessToProtectedBranch: [\n      \"GET /repos/{owner}/{repo}/branches/{branch}/protection/restrictions/apps\",\n    ],\n    getAutolink: [\"GET /repos/{owner}/{repo}/autolinks/{autolink_id}\"],\n    getBranch: [\"GET /repos/{owner}/{repo}/branches/{branch}\"],\n    getBranchProtection: [\n      \"GET /repos/{owner}/{repo}/branches/{branch}/protection\",\n    ],\n    getBranchRules: [\"GET /repos/{owner}/{repo}/rules/branches/{branch}\"],\n    getClones: [\"GET /repos/{owner}/{repo}/traffic/clones\"],\n    getCodeFrequencyStats: [\"GET /repos/{owner}/{repo}/stats/code_frequency\"],\n    getCollaboratorPermissionLevel: [\n      \"GET /repos/{owner}/{repo}/collaborators/{username}/permission\",\n    ],\n    getCombinedStatusForRef: [\"GET /repos/{owner}/{repo}/commits/{ref}/status\"],\n    getCommit: [\"GET /repos/{owner}/{repo}/commits/{ref}\"],\n    getCommitActivityStats: [\"GET /repos/{owner}/{repo}/stats/commit_activity\"],\n    getCommitComment: [\"GET /repos/{owner}/{repo}/comments/{comment_id}\"],\n    getCommitSignatureProtection: [\n      \"GET /repos/{owner}/{repo}/branches/{branch}/protection/required_signatures\",\n    ],\n    getCommunityProfileMetrics: [\"GET /repos/{owner}/{repo}/community/profile\"],\n    getContent: [\"GET /repos/{owner}/{repo}/contents/{path}\"],\n    getContributorsStats: [\"GET /repos/{owner}/{repo}/stats/contributors\"],\n    getCustomDeploymentProtectionRule: [\n      \"GET /repos/{owner}/{repo}/environments/{environment_name}/deployment_protection_rules/{protection_rule_id}\",\n    ],\n    getCustomPropertiesValues: [\"GET /repos/{owner}/{repo}/properties/values\"],\n    getDeployKey: [\"GET /repos/{owner}/{repo}/keys/{key_id}\"],\n    getDeployment: [\"GET /repos/{owner}/{repo}/deployments/{deployment_id}\"],\n    getDeploymentBranchPolicy: [\n      \"GET /repos/{owner}/{repo}/environments/{environment_name}/deployment-branch-policies/{branch_policy_id}\",\n    ],\n    getDeploymentStatus: [\n      \"GET /repos/{owner}/{repo}/deployments/{deployment_id}/statuses/{status_id}\",\n    ],\n    getEnvironment: [\n      \"GET /repos/{owner}/{repo}/environments/{environment_name}\",\n    ],\n    getLatestPagesBuild: [\"GET /repos/{owner}/{repo}/pages/builds/latest\"],\n    getLatestRelease: [\"GET /repos/{owner}/{repo}/releases/latest\"],\n    getOrgRuleSuite: [\"GET /orgs/{org}/rulesets/rule-suites/{rule_suite_id}\"],\n    getOrgRuleSuites: [\"GET /orgs/{org}/rulesets/rule-suites\"],\n    getOrgRuleset: [\"GET /orgs/{org}/rulesets/{ruleset_id}\"],\n    getOrgRulesets: [\"GET /orgs/{org}/rulesets\"],\n    getPages: [\"GET /repos/{owner}/{repo}/pages\"],\n    getPagesBuild: [\"GET /repos/{owner}/{repo}/pages/builds/{build_id}\"],\n    getPagesDeployment: [\n      \"GET /repos/{owner}/{repo}/pages/deployments/{pages_deployment_id}\",\n    ],\n    getPagesHealthCheck: [\"GET /repos/{owner}/{repo}/pages/health\"],\n    getParticipationStats: [\"GET /repos/{owner}/{repo}/stats/participation\"],\n    getPullRequestReviewProtection: [\n      \"GET /repos/{owner}/{repo}/branches/{branch}/protection/required_pull_request_reviews\",\n    ],\n    getPunchCardStats: [\"GET /repos/{owner}/{repo}/stats/punch_card\"],\n    getReadme: [\"GET /repos/{owner}/{repo}/readme\"],\n    getReadmeInDirectory: [\"GET /repos/{owner}/{repo}/readme/{dir}\"],\n    getRelease: [\"GET /repos/{owner}/{repo}/releases/{release_id}\"],\n    getReleaseAsset: [\"GET /repos/{owner}/{repo}/releases/assets/{asset_id}\"],\n    getReleaseByTag: [\"GET /repos/{owner}/{repo}/releases/tags/{tag}\"],\n    getRepoRuleSuite: [\n      \"GET /repos/{owner}/{repo}/rulesets/rule-suites/{rule_suite_id}\",\n    ],\n    getRepoRuleSuites: [\"GET /repos/{owner}/{repo}/rulesets/rule-suites\"],\n    getRepoRuleset: [\"GET /repos/{owner}/{repo}/rulesets/{ruleset_id}\"],\n    getRepoRulesetHistory: [\n      \"GET /repos/{owner}/{repo}/rulesets/{ruleset_id}/history\",\n    ],\n    getRepoRulesetVersion: [\n      \"GET /repos/{owner}/{repo}/rulesets/{ruleset_id}/history/{version_id}\",\n    ],\n    getRepoRulesets: [\"GET /repos/{owner}/{repo}/rulesets\"],\n    getStatusChecksProtection: [\n      \"GET /repos/{owner}/{repo}/branches/{branch}/protection/required_status_checks\",\n    ],\n    getTeamsWithAccessToProtectedBranch: [\n      \"GET /repos/{owner}/{repo}/branches/{branch}/protection/restrictions/teams\",\n    ],\n    getTopPaths: [\"GET /repos/{owner}/{repo}/traffic/popular/paths\"],\n    getTopReferrers: [\"GET /repos/{owner}/{repo}/traffic/popular/referrers\"],\n    getUsersWithAccessToProtectedBranch: [\n      \"GET /repos/{owner}/{repo}/branches/{branch}/protection/restrictions/users\",\n    ],\n    getViews: [\"GET /repos/{owner}/{repo}/traffic/views\"],\n    getWebhook: [\"GET /repos/{owner}/{repo}/hooks/{hook_id}\"],\n    getWebhookConfigForRepo: [\n      \"GET /repos/{owner}/{repo}/hooks/{hook_id}/config\",\n    ],\n    getWebhookDelivery: [\n      \"GET /repos/{owner}/{repo}/hooks/{hook_id}/deliveries/{delivery_id}\",\n    ],\n    listActivities: [\"GET /repos/{owner}/{repo}/activity\"],\n    listAttestations: [\n      \"GET /repos/{owner}/{repo}/attestations/{subject_digest}\",\n    ],\n    listAutolinks: [\"GET /repos/{owner}/{repo}/autolinks\"],\n    listBranches: [\"GET /repos/{owner}/{repo}/branches\"],\n    listBranchesForHeadCommit: [\n      \"GET /repos/{owner}/{repo}/commits/{commit_sha}/branches-where-head\",\n    ],\n    listCollaborators: [\"GET /repos/{owner}/{repo}/collaborators\"],\n    listCommentsForCommit: [\n      \"GET /repos/{owner}/{repo}/commits/{commit_sha}/comments\",\n    ],\n    listCommitCommentsForRepo: [\"GET /repos/{owner}/{repo}/comments\"],\n    listCommitStatusesForRef: [\n      \"GET /repos/{owner}/{repo}/commits/{ref}/statuses\",\n    ],\n    listCommits: [\"GET /repos/{owner}/{repo}/commits\"],\n    listContributors: [\"GET /repos/{owner}/{repo}/contributors\"],\n    listCustomDeploymentRuleIntegrations: [\n      \"GET /repos/{owner}/{repo}/environments/{environment_name}/deployment_protection_rules/apps\",\n    ],\n    listDeployKeys: [\"GET /repos/{owner}/{repo}/keys\"],\n    listDeploymentBranchPolicies: [\n      \"GET /repos/{owner}/{repo}/environments/{environment_name}/deployment-branch-policies\",\n    ],\n    listDeploymentStatuses: [\n      \"GET /repos/{owner}/{repo}/deployments/{deployment_id}/statuses\",\n    ],\n    listDeployments: [\"GET /repos/{owner}/{repo}/deployments\"],\n    listForAuthenticatedUser: [\"GET /user/repos\"],\n    listForOrg: [\"GET /orgs/{org}/repos\"],\n    listForUser: [\"GET /users/{username}/repos\"],\n    listForks: [\"GET /repos/{owner}/{repo}/forks\"],\n    listInvitations: [\"GET /repos/{owner}/{repo}/invitations\"],\n    listInvitationsForAuthenticatedUser: [\"GET /user/repository_invitations\"],\n    listLanguages: [\"GET /repos/{owner}/{repo}/languages\"],\n    listPagesBuilds: [\"GET /repos/{owner}/{repo}/pages/builds\"],\n    listPublic: [\"GET /repositories\"],\n    listPullRequestsAssociatedWithCommit: [\n      \"GET /repos/{owner}/{repo}/commits/{commit_sha}/pulls\",\n    ],\n    listReleaseAssets: [\n      \"GET /repos/{owner}/{repo}/releases/{release_id}/assets\",\n    ],\n    listReleases: [\"GET /repos/{owner}/{repo}/releases\"],\n    listTags: [\"GET /repos/{owner}/{repo}/tags\"],\n    listTeams: [\"GET /repos/{owner}/{repo}/teams\"],\n    listWebhookDeliveries: [\n      \"GET /repos/{owner}/{repo}/hooks/{hook_id}/deliveries\",\n    ],\n    listWebhooks: [\"GET /repos/{owner}/{repo}/hooks\"],\n    merge: [\"POST /repos/{owner}/{repo}/merges\"],\n    mergeUpstream: [\"POST /repos/{owner}/{repo}/merge-upstream\"],\n    pingWebhook: [\"POST /repos/{owner}/{repo}/hooks/{hook_id}/pings\"],\n    redeliverWebhookDelivery: [\n      \"POST /repos/{owner}/{repo}/hooks/{hook_id}/deliveries/{delivery_id}/attempts\",\n    ],\n    removeAppAccessRestrictions: [\n      \"DELETE /repos/{owner}/{repo}/branches/{branch}/protection/restrictions/apps\",\n      {},\n      { mapToData: \"apps\" },\n    ],\n    removeCollaborator: [\n      \"DELETE /repos/{owner}/{repo}/collaborators/{username}\",\n    ],\n    removeStatusCheckContexts: [\n      \"DELETE /repos/{owner}/{repo}/branches/{branch}/protection/required_status_checks/contexts\",\n      {},\n      { mapToData: \"contexts\" },\n    ],\n    removeStatusCheckProtection: [\n      \"DELETE /repos/{owner}/{repo}/branches/{branch}/protection/required_status_checks\",\n    ],\n    removeTeamAccessRestrictions: [\n      \"DELETE /repos/{owner}/{repo}/branches/{branch}/protection/restrictions/teams\",\n      {},\n      { mapToData: \"teams\" },\n    ],\n    removeUserAccessRestrictions: [\n      \"DELETE /repos/{owner}/{repo}/branches/{branch}/protection/restrictions/users\",\n      {},\n      { mapToData: \"users\" },\n    ],\n    renameBranch: [\"POST /repos/{owner}/{repo}/branches/{branch}/rename\"],\n    replaceAllTopics: [\"PUT /repos/{owner}/{repo}/topics\"],\n    requestPagesBuild: [\"POST /repos/{owner}/{repo}/pages/builds\"],\n    setAdminBranchProtection: [\n      \"POST /repos/{owner}/{repo}/branches/{branch}/protection/enforce_admins\",\n    ],\n    setAppAccessRestrictions: [\n      \"PUT /repos/{owner}/{repo}/branches/{branch}/protection/restrictions/apps\",\n      {},\n      { mapToData: \"apps\" },\n    ],\n    setStatusCheckContexts: [\n      \"PUT /repos/{owner}/{repo}/branches/{branch}/protection/required_status_checks/contexts\",\n      {},\n      { mapToData: \"contexts\" },\n    ],\n    setTeamAccessRestrictions: [\n      \"PUT /repos/{owner}/{repo}/branches/{branch}/protection/restrictions/teams\",\n      {},\n      { mapToData: \"teams\" },\n    ],\n    setUserAccessRestrictions: [\n      \"PUT /repos/{owner}/{repo}/branches/{branch}/protection/restrictions/users\",\n      {},\n      { mapToData: \"users\" },\n    ],\n    testPushWebhook: [\"POST /repos/{owner}/{repo}/hooks/{hook_id}/tests\"],\n    transfer: [\"POST /repos/{owner}/{repo}/transfer\"],\n    update: [\"PATCH /repos/{owner}/{repo}\"],\n    updateBranchProtection: [\n      \"PUT /repos/{owner}/{repo}/branches/{branch}/protection\",\n    ],\n    updateCommitComment: [\"PATCH /repos/{owner}/{repo}/comments/{comment_id}\"],\n    updateDeploymentBranchPolicy: [\n      \"PUT /repos/{owner}/{repo}/environments/{environment_name}/deployment-branch-policies/{branch_policy_id}\",\n    ],\n    updateInformationAboutPagesSite: [\"PUT /repos/{owner}/{repo}/pages\"],\n    updateInvitation: [\n      \"PATCH /repos/{owner}/{repo}/invitations/{invitation_id}\",\n    ],\n    updateOrgRuleset: [\"PUT /orgs/{org}/rulesets/{ruleset_id}\"],\n    updatePullRequestReviewProtection: [\n      \"PATCH /repos/{owner}/{repo}/branches/{branch}/protection/required_pull_request_reviews\",\n    ],\n    updateRelease: [\"PATCH /repos/{owner}/{repo}/releases/{release_id}\"],\n    updateReleaseAsset: [\n      \"PATCH /repos/{owner}/{repo}/releases/assets/{asset_id}\",\n    ],\n    updateRepoRuleset: [\"PUT /repos/{owner}/{repo}/rulesets/{ruleset_id}\"],\n    updateStatusCheckPotection: [\n      \"PATCH /repos/{owner}/{repo}/branches/{branch}/protection/required_status_checks\",\n      {},\n      { renamed: [\"repos\", \"updateStatusCheckProtection\"] },\n    ],\n    updateStatusCheckProtection: [\n      \"PATCH /repos/{owner}/{repo}/branches/{branch}/protection/required_status_checks\",\n    ],\n    updateWebhook: [\"PATCH /repos/{owner}/{repo}/hooks/{hook_id}\"],\n    updateWebhookConfigForRepo: [\n      \"PATCH /repos/{owner}/{repo}/hooks/{hook_id}/config\",\n    ],\n    uploadReleaseAsset: [\n      \"POST /repos/{owner}/{repo}/releases/{release_id}/assets{?name,label}\",\n      { baseUrl: \"https://uploads.github.com\" },\n    ],\n  },\n  search: {\n    code: [\"GET /search/code\"],\n    commits: [\"GET /search/commits\"],\n    issuesAndPullRequests: [\n      \"GET /search/issues\",\n      {},\n      {\n        deprecated:\n          \"octokit.rest.search.issuesAndPullRequests() is deprecated, see https://docs.github.com/rest/search/search#search-issues-and-pull-requests\",\n      },\n    ],\n    labels: [\"GET /search/labels\"],\n    repos: [\"GET /search/repositories\"],\n    topics: [\"GET /search/topics\"],\n    users: [\"GET /search/users\"],\n  },\n  secretScanning: {\n    createPushProtectionBypass: [\n      \"POST /repos/{owner}/{repo}/secret-scanning/push-protection-bypasses\",\n    ],\n    getAlert: [\n      \"GET /repos/{owner}/{repo}/secret-scanning/alerts/{alert_number}\",\n    ],\n    getScanHistory: [\"GET /repos/{owner}/{repo}/secret-scanning/scan-history\"],\n    listAlertsForEnterprise: [\n      \"GET /enterprises/{enterprise}/secret-scanning/alerts\",\n    ],\n    listAlertsForOrg: [\"GET /orgs/{org}/secret-scanning/alerts\"],\n    listAlertsForRepo: [\"GET /repos/{owner}/{repo}/secret-scanning/alerts\"],\n    listLocationsForAlert: [\n      \"GET /repos/{owner}/{repo}/secret-scanning/alerts/{alert_number}/locations\",\n    ],\n    updateAlert: [\n      \"PATCH /repos/{owner}/{repo}/secret-scanning/alerts/{alert_number}\",\n    ],\n  },\n  securityAdvisories: {\n    createFork: [\n      \"POST /repos/{owner}/{repo}/security-advisories/{ghsa_id}/forks\",\n    ],\n    createPrivateVulnerabilityReport: [\n      \"POST /repos/{owner}/{repo}/security-advisories/reports\",\n    ],\n    createRepositoryAdvisory: [\n      \"POST /repos/{owner}/{repo}/security-advisories\",\n    ],\n    createRepositoryAdvisoryCveRequest: [\n      \"POST /repos/{owner}/{repo}/security-advisories/{ghsa_id}/cve\",\n    ],\n    getGlobalAdvisory: [\"GET /advisories/{ghsa_id}\"],\n    getRepositoryAdvisory: [\n      \"GET /repos/{owner}/{repo}/security-advisories/{ghsa_id}\",\n    ],\n    listGlobalAdvisories: [\"GET /advisories\"],\n    listOrgRepositoryAdvisories: [\"GET /orgs/{org}/security-advisories\"],\n    listRepositoryAdvisories: [\"GET /repos/{owner}/{repo}/security-advisories\"],\n    updateRepositoryAdvisory: [\n      \"PATCH /repos/{owner}/{repo}/security-advisories/{ghsa_id}\",\n    ],\n  },\n  teams: {\n    addOrUpdateMembershipForUserInOrg: [\n      \"PUT /orgs/{org}/teams/{team_slug}/memberships/{username}\",\n    ],\n    addOrUpdateRepoPermissionsInOrg: [\n      \"PUT /orgs/{org}/teams/{team_slug}/repos/{owner}/{repo}\",\n    ],\n    checkPermissionsForRepoInOrg: [\n      \"GET /orgs/{org}/teams/{team_slug}/repos/{owner}/{repo}\",\n    ],\n    create: [\"POST /orgs/{org}/teams\"],\n    createDiscussionCommentInOrg: [\n      \"POST /orgs/{org}/teams/{team_slug}/discussions/{discussion_number}/comments\",\n    ],\n    createDiscussionInOrg: [\"POST /orgs/{org}/teams/{team_slug}/discussions\"],\n    deleteDiscussionCommentInOrg: [\n      \"DELETE /orgs/{org}/teams/{team_slug}/discussions/{discussion_number}/comments/{comment_number}\",\n    ],\n    deleteDiscussionInOrg: [\n      \"DELETE /orgs/{org}/teams/{team_slug}/discussions/{discussion_number}\",\n    ],\n    deleteInOrg: [\"DELETE /orgs/{org}/teams/{team_slug}\"],\n    getByName: [\"GET /orgs/{org}/teams/{team_slug}\"],\n    getDiscussionCommentInOrg: [\n      \"GET /orgs/{org}/teams/{team_slug}/discussions/{discussion_number}/comments/{comment_number}\",\n    ],\n    getDiscussionInOrg: [\n      \"GET /orgs/{org}/teams/{team_slug}/discussions/{discussion_number}\",\n    ],\n    getMembershipForUserInOrg: [\n      \"GET /orgs/{org}/teams/{team_slug}/memberships/{username}\",\n    ],\n    list: [\"GET /orgs/{org}/teams\"],\n    listChildInOrg: [\"GET /orgs/{org}/teams/{team_slug}/teams\"],\n    listDiscussionCommentsInOrg: [\n      \"GET /orgs/{org}/teams/{team_slug}/discussions/{discussion_number}/comments\",\n    ],\n    listDiscussionsInOrg: [\"GET /orgs/{org}/teams/{team_slug}/discussions\"],\n    listForAuthenticatedUser: [\"GET /user/teams\"],\n    listMembersInOrg: [\"GET /orgs/{org}/teams/{team_slug}/members\"],\n    listPendingInvitationsInOrg: [\n      \"GET /orgs/{org}/teams/{team_slug}/invitations\",\n    ],\n    listReposInOrg: [\"GET /orgs/{org}/teams/{team_slug}/repos\"],\n    removeMembershipForUserInOrg: [\n      \"DELETE /orgs/{org}/teams/{team_slug}/memberships/{username}\",\n    ],\n    removeRepoInOrg: [\n      \"DELETE /orgs/{org}/teams/{team_slug}/repos/{owner}/{repo}\",\n    ],\n    updateDiscussionCommentInOrg: [\n      \"PATCH /orgs/{org}/teams/{team_slug}/discussions/{discussion_number}/comments/{comment_number}\",\n    ],\n    updateDiscussionInOrg: [\n      \"PATCH /orgs/{org}/teams/{team_slug}/discussions/{discussion_number}\",\n    ],\n    updateInOrg: [\"PATCH /orgs/{org}/teams/{team_slug}\"],\n  },\n  users: {\n    addEmailForAuthenticated: [\n      \"POST /user/emails\",\n      {},\n      { renamed: [\"users\", \"addEmailForAuthenticatedUser\"] },\n    ],\n    addEmailForAuthenticatedUser: [\"POST /user/emails\"],\n    addSocialAccountForAuthenticatedUser: [\"POST /user/social_accounts\"],\n    block: [\"PUT /user/blocks/{username}\"],\n    checkBlocked: [\"GET /user/blocks/{username}\"],\n    checkFollowingForUser: [\"GET /users/{username}/following/{target_user}\"],\n    checkPersonIsFollowedByAuthenticated: [\"GET /user/following/{username}\"],\n    createGpgKeyForAuthenticated: [\n      \"POST /user/gpg_keys\",\n      {},\n      { renamed: [\"users\", \"createGpgKeyForAuthenticatedUser\"] },\n    ],\n    createGpgKeyForAuthenticatedUser: [\"POST /user/gpg_keys\"],\n    createPublicSshKeyForAuthenticated: [\n      \"POST /user/keys\",\n      {},\n      { renamed: [\"users\", \"createPublicSshKeyForAuthenticatedUser\"] },\n    ],\n    createPublicSshKeyForAuthenticatedUser: [\"POST /user/keys\"],\n    createSshSigningKeyForAuthenticatedUser: [\"POST /user/ssh_signing_keys\"],\n    deleteEmailForAuthenticated: [\n      \"DELETE /user/emails\",\n      {},\n      { renamed: [\"users\", \"deleteEmailForAuthenticatedUser\"] },\n    ],\n    deleteEmailForAuthenticatedUser: [\"DELETE /user/emails\"],\n    deleteGpgKeyForAuthenticated: [\n      \"DELETE /user/gpg_keys/{gpg_key_id}\",\n      {},\n      { renamed: [\"users\", \"deleteGpgKeyForAuthenticatedUser\"] },\n    ],\n    deleteGpgKeyForAuthenticatedUser: [\"DELETE /user/gpg_keys/{gpg_key_id}\"],\n    deletePublicSshKeyForAuthenticated: [\n      \"DELETE /user/keys/{key_id}\",\n      {},\n      { renamed: [\"users\", \"deletePublicSshKeyForAuthenticatedUser\"] },\n    ],\n    deletePublicSshKeyForAuthenticatedUser: [\"DELETE /user/keys/{key_id}\"],\n    deleteSocialAccountForAuthenticatedUser: [\"DELETE /user/social_accounts\"],\n    deleteSshSigningKeyForAuthenticatedUser: [\n      \"DELETE /user/ssh_signing_keys/{ssh_signing_key_id}\",\n    ],\n    follow: [\"PUT /user/following/{username}\"],\n    getAuthenticated: [\"GET /user\"],\n    getById: [\"GET /user/{account_id}\"],\n    getByUsername: [\"GET /users/{username}\"],\n    getContextForUser: [\"GET /users/{username}/hovercard\"],\n    getGpgKeyForAuthenticated: [\n      \"GET /user/gpg_keys/{gpg_key_id}\",\n      {},\n      { renamed: [\"users\", \"getGpgKeyForAuthenticatedUser\"] },\n    ],\n    getGpgKeyForAuthenticatedUser: [\"GET /user/gpg_keys/{gpg_key_id}\"],\n    getPublicSshKeyForAuthenticated: [\n      \"GET /user/keys/{key_id}\",\n      {},\n      { renamed: [\"users\", \"getPublicSshKeyForAuthenticatedUser\"] },\n    ],\n    getPublicSshKeyForAuthenticatedUser: [\"GET /user/keys/{key_id}\"],\n    getSshSigningKeyForAuthenticatedUser: [\n      \"GET /user/ssh_signing_keys/{ssh_signing_key_id}\",\n    ],\n    list: [\"GET /users\"],\n    listAttestations: [\"GET /users/{username}/attestations/{subject_digest}\"],\n    listBlockedByAuthenticated: [\n      \"GET /user/blocks\",\n      {},\n      { renamed: [\"users\", \"listBlockedByAuthenticatedUser\"] },\n    ],\n    listBlockedByAuthenticatedUser: [\"GET /user/blocks\"],\n    listEmailsForAuthenticated: [\n      \"GET /user/emails\",\n      {},\n      { renamed: [\"users\", \"listEmailsForAuthenticatedUser\"] },\n    ],\n    listEmailsForAuthenticatedUser: [\"GET /user/emails\"],\n    listFollowedByAuthenticated: [\n      \"GET /user/following\",\n      {},\n      { renamed: [\"users\", \"listFollowedByAuthenticatedUser\"] },\n    ],\n    listFollowedByAuthenticatedUser: [\"GET /user/following\"],\n    listFollowersForAuthenticatedUser: [\"GET /user/followers\"],\n    listFollowersForUser: [\"GET /users/{username}/followers\"],\n    listFollowingForUser: [\"GET /users/{username}/following\"],\n    listGpgKeysForAuthenticated: [\n      \"GET /user/gpg_keys\",\n      {},\n      { renamed: [\"users\", \"listGpgKeysForAuthenticatedUser\"] },\n    ],\n    listGpgKeysForAuthenticatedUser: [\"GET /user/gpg_keys\"],\n    listGpgKeysForUser: [\"GET /users/{username}/gpg_keys\"],\n    listPublicEmailsForAuthenticated: [\n      \"GET /user/public_emails\",\n      {},\n      { renamed: [\"users\", \"listPublicEmailsForAuthenticatedUser\"] },\n    ],\n    listPublicEmailsForAuthenticatedUser: [\"GET /user/public_emails\"],\n    listPublicKeysForUser: [\"GET /users/{username}/keys\"],\n    listPublicSshKeysForAuthenticated: [\n      \"GET /user/keys\",\n      {},\n      { renamed: [\"users\", \"listPublicSshKeysForAuthenticatedUser\"] },\n    ],\n    listPublicSshKeysForAuthenticatedUser: [\"GET /user/keys\"],\n    listSocialAccountsForAuthenticatedUser: [\"GET /user/social_accounts\"],\n    listSocialAccountsForUser: [\"GET /users/{username}/social_accounts\"],\n    listSshSigningKeysForAuthenticatedUser: [\"GET /user/ssh_signing_keys\"],\n    listSshSigningKeysForUser: [\"GET /users/{username}/ssh_signing_keys\"],\n    setPrimaryEmailVisibilityForAuthenticated: [\n      \"PATCH /user/email/visibility\",\n      {},\n      { renamed: [\"users\", \"setPrimaryEmailVisibilityForAuthenticatedUser\"] },\n    ],\n    setPrimaryEmailVisibilityForAuthenticatedUser: [\n      \"PATCH /user/email/visibility\",\n    ],\n    unblock: [\"DELETE /user/blocks/{username}\"],\n    unfollow: [\"DELETE /user/following/{username}\"],\n    updateAuthenticated: [\"PATCH /user\"],\n  },\n};\n\nexport default Endpoints;\n", "import type { Octokit } from \"@octokit/core\";\nimport type { EndpointOptions, RequestParameters, Route } from \"@octokit/types\";\nimport ENDPOINTS from \"./generated/endpoints.js\";\nimport type { RestEndpointMethods } from \"./generated/method-types.js\";\nimport type { EndpointDecorations } from \"./types.js\";\n\n// The following code was refactored in: https://github.com/octokit/plugin-rest-endpoint-methods.js/pull/622\n// to optimise the runtime performance of Octokit initialization.\n//\n// This optimization involves two key changes:\n// 1. Pre-Computation: The endpoint methods are pre-computed once at module load\n//    time instead of each invocation of `endpointsToMethods()`.\n// 2. Lazy initialization and caching: We use a Proxy for each scope to only\n//    initialize methods that are actually called. This reduces runtime overhead\n//    as the initialization involves deep merging of objects. The initialized\n//    methods are then cached for future use.\n\nconst endpointMethodsMap = new Map();\nfor (const [scope, endpoints] of Object.entries(ENDPOINTS)) {\n  for (const [methodName, endpoint] of Object.entries(endpoints)) {\n    const [route, defaults, decorations] = endpoint;\n    const [method, url] = route.split(/ /);\n    const endpointDefaults = Object.assign(\n      {\n        method,\n        url,\n      },\n      defaults,\n    );\n\n    if (!endpointMethodsMap.has(scope)) {\n      endpointMethodsMap.set(scope, new Map());\n    }\n\n    endpointMethodsMap.get(scope).set(methodName, {\n      scope,\n      methodName,\n      endpointDefaults,\n      decorations,\n    });\n  }\n}\n\ntype ProxyTarget = {\n  octokit: Octokit;\n  scope: string;\n  cache: Record<string, (...args: any[]) => any>;\n};\n\nconst handler = {\n  has({ scope }: ProxyTarget, methodName: string) {\n    return endpointMethodsMap.get(scope).has(methodName);\n  },\n  getOwnPropertyDescriptor(target: ProxyTarget, methodName: string) {\n    return {\n      value: this.get(target, methodName), // ensures method is in the cache\n      configurable: true,\n      writable: true,\n      enumerable: true,\n    };\n  },\n  defineProperty(\n    target: ProxyTarget,\n    methodName: string,\n    descriptor: PropertyDescriptor,\n  ) {\n    Object.defineProperty(target.cache, methodName, descriptor);\n    return true;\n  },\n  deleteProperty(target: ProxyTarget, methodName: string) {\n    delete target.cache[methodName];\n    return true;\n  },\n  ownKeys({ scope }: ProxyTarget) {\n    return [...endpointMethodsMap.get(scope).keys()];\n  },\n  set(target: ProxyTarget, methodName: string, value: any) {\n    return (target.cache[methodName] = value);\n  },\n  get({ octokit, scope, cache }: ProxyTarget, methodName: string) {\n    if (cache[methodName]) {\n      return cache[methodName];\n    }\n\n    const method = endpointMethodsMap.get(scope).get(methodName);\n    if (!method) {\n      return undefined;\n    }\n\n    const { endpointDefaults, decorations } = method;\n\n    if (decorations) {\n      cache[methodName] = decorate(\n        octokit,\n        scope,\n        methodName,\n        endpointDefaults,\n        decorations,\n      );\n    } else {\n      cache[methodName] = octokit.request.defaults(endpointDefaults);\n    }\n\n    return cache[methodName];\n  },\n};\n\nexport function endpointsToMethods(octokit: Octokit): RestEndpointMethods {\n  const newMethods = {} as { [key: string]: object };\n\n  for (const scope of endpointMethodsMap.keys()) {\n    newMethods[scope] = new Proxy({ octokit, scope, cache: {} }, handler);\n  }\n\n  return newMethods as RestEndpointMethods;\n}\n\nfunction decorate(\n  octokit: Octokit,\n  scope: string,\n  methodName: string,\n  defaults: EndpointOptions,\n  decorations: EndpointDecorations,\n) {\n  const requestWithDefaults = octokit.request.defaults(defaults);\n\n  /* istanbul ignore next */\n  function withDecorations(\n    ...args: [Route, RequestParameters?] | [EndpointOptions]\n  ) {\n    // @ts-ignore https://github.com/microsoft/TypeScript/issues/25488\n    let options = requestWithDefaults.endpoint.merge(...args);\n\n    // There are currently no other decorations than `.mapToData`\n    if (decorations.mapToData) {\n      options = Object.assign({}, options, {\n        data: options[decorations.mapToData],\n        [decorations.mapToData]: undefined,\n      });\n      return requestWithDefaults(options);\n    }\n\n    if (decorations.renamed) {\n      const [newScope, newMethodName] = decorations.renamed;\n      octokit.log.warn(\n        `octokit.${scope}.${methodName}() has been renamed to octokit.${newScope}.${newMethodName}()`,\n      );\n    }\n    if (decorations.deprecated) {\n      octokit.log.warn(decorations.deprecated);\n    }\n\n    if (decorations.renamedParameters) {\n      // @ts-ignore https://github.com/microsoft/TypeScript/issues/25488\n      const options = requestWithDefaults.endpoint.merge(...args);\n\n      for (const [name, alias] of Object.entries(\n        decorations.renamedParameters,\n      )) {\n        if (name in options) {\n          octokit.log.warn(\n            `\"${name}\" parameter is deprecated for \"octokit.${scope}.${methodName}()\". Use \"${alias}\" instead`,\n          );\n          if (!(alias in options)) {\n            options[alias] = options[name];\n          }\n          delete options[name];\n        }\n      }\n      return requestWithDefaults(options);\n    }\n\n    // @ts-ignore https://github.com/microsoft/TypeScript/issues/25488\n    return requestWithDefaults(...args);\n  }\n  return Object.assign(withDecorations, requestWithDefaults);\n}\n", "import type { Octokit } from \"@octokit/core\";\n\nexport type { RestEndpointMethodTypes } from \"./generated/parameters-and-response-types.js\";\nimport { VERSION } from \"./version.js\";\nimport type { Api } from \"./types.js\";\nimport { endpointsToMethods } from \"./endpoints-to-methods.js\";\n\n// Export the type for downstream users in order to fix a TypeScript error\n// The inferred type of 'Octokit' cannot be named without a reference to '../node_modules/@octokit/plugin-rest-endpoint-methods/dist-types/types.js'. This is likely not portable. A type annotation is necessary.\nexport type { Api };\n\nexport function restEndpointMethods(octokit: Octokit): Api {\n  const api = endpointsToMethods(octokit);\n  return {\n    rest: api,\n  };\n}\nrestEndpointMethods.VERSION = VERSION;\n\nexport function legacyRestEndpointMethods(octokit: Octokit): Api[\"rest\"] & Api {\n  const api = endpointsToMethods(octokit);\n  return {\n    ...api,\n    rest: api,\n  };\n}\nlegacyRestEndpointMethods.VERSION = VERSION;\n", "const VERSION = \"22.0.0\";\nexport {\n  VERSION\n};\n", "import { Octokit as Core } from \"@octokit/core\";\nimport { requestLog } from \"@octokit/plugin-request-log\";\nimport {\n  paginateRest\n} from \"@octokit/plugin-paginate-rest\";\nimport { legacyRestEndpointMethods } from \"@octokit/plugin-rest-endpoint-methods\";\nimport { VERSION } from \"./version.js\";\nconst Octokit = Core.plugin(requestLog, legacyRestEndpointMethods, paginateRest).defaults(\n  {\n    userAgent: `octokit-rest.js/${VERSION}`\n  }\n);\nexport {\n  Octokit\n};\n"], "mappings": ";;;;;;;AAAA;AAAA;AAAA;AAEA,QAAM,aAAa,SAASA,cAAc;AAAA,IAAE;AAC5C,eAAW,YAAY,uBAAO,OAAO,IAAI;AAgBzC,QAAM,UAAU;AAQhB,QAAM,eAAe;AASrB,QAAM,cAAc;AAGpB,QAAM,qBAAqB,EAAE,MAAM,IAAI,YAAY,IAAI,WAAW,EAAE;AACpE,WAAO,OAAO,mBAAmB,UAAU;AAC3C,WAAO,OAAO,kBAAkB;AAUhC,aAASC,OAAO,QAAQ;AACtB,UAAI,OAAO,WAAW,UAAU;AAC9B,cAAM,IAAI,UAAU,kDAAkD;AAAA,MACxE;AAEA,UAAI,QAAQ,OAAO,QAAQ,GAAG;AAC9B,YAAM,OAAO,UAAU,KACnB,OAAO,MAAM,GAAG,KAAK,EAAE,KAAK,IAC5B,OAAO,KAAK;AAEhB,UAAI,YAAY,KAAK,IAAI,MAAM,OAAO;AACpC,cAAM,IAAI,UAAU,oBAAoB;AAAA,MAC1C;AAEA,YAAM,SAAS;AAAA,QACb,MAAM,KAAK,YAAY;AAAA,QACvB,YAAY,IAAI,WAAW;AAAA,MAC7B;AAGA,UAAI,UAAU,IAAI;AAChB,eAAO;AAAA,MACT;AAEA,UAAI;AACJ,UAAI;AACJ,UAAI;AAEJ,cAAQ,YAAY;AAEpB,aAAQ,QAAQ,QAAQ,KAAK,MAAM,GAAI;AACrC,YAAI,MAAM,UAAU,OAAO;AACzB,gBAAM,IAAI,UAAU,0BAA0B;AAAA,QAChD;AAEA,iBAAS,MAAM,CAAC,EAAE;AAClB,cAAM,MAAM,CAAC,EAAE,YAAY;AAC3B,gBAAQ,MAAM,CAAC;AAEf,YAAI,MAAM,CAAC,MAAM,KAAK;AAEpB,kBAAQ,MACL,MAAM,GAAG,MAAM,SAAS,CAAC;AAE5B,uBAAa,KAAK,KAAK,MAAM,QAAQ,MAAM,QAAQ,cAAc,IAAI;AAAA,QACvE;AAEA,eAAO,WAAW,GAAG,IAAI;AAAA,MAC3B;AAEA,UAAI,UAAU,OAAO,QAAQ;AAC3B,cAAM,IAAI,UAAU,0BAA0B;AAAA,MAChD;AAEA,aAAO;AAAA,IACT;AAEA,aAASC,WAAW,QAAQ;AAC1B,UAAI,OAAO,WAAW,UAAU;AAC9B,eAAO;AAAA,MACT;AAEA,UAAI,QAAQ,OAAO,QAAQ,GAAG;AAC9B,YAAM,OAAO,UAAU,KACnB,OAAO,MAAM,GAAG,KAAK,EAAE,KAAK,IAC5B,OAAO,KAAK;AAEhB,UAAI,YAAY,KAAK,IAAI,MAAM,OAAO;AACpC,eAAO;AAAA,MACT;AAEA,YAAM,SAAS;AAAA,QACb,MAAM,KAAK,YAAY;AAAA,QACvB,YAAY,IAAI,WAAW;AAAA,MAC7B;AAGA,UAAI,UAAU,IAAI;AAChB,eAAO;AAAA,MACT;AAEA,UAAI;AACJ,UAAI;AACJ,UAAI;AAEJ,cAAQ,YAAY;AAEpB,aAAQ,QAAQ,QAAQ,KAAK,MAAM,GAAI;AACrC,YAAI,MAAM,UAAU,OAAO;AACzB,iBAAO;AAAA,QACT;AAEA,iBAAS,MAAM,CAAC,EAAE;AAClB,cAAM,MAAM,CAAC,EAAE,YAAY;AAC3B,gBAAQ,MAAM,CAAC;AAEf,YAAI,MAAM,CAAC,MAAM,KAAK;AAEpB,kBAAQ,MACL,MAAM,GAAG,MAAM,SAAS,CAAC;AAE5B,uBAAa,KAAK,KAAK,MAAM,QAAQ,MAAM,QAAQ,cAAc,IAAI;AAAA,QACvE;AAEA,eAAO,WAAW,GAAG,IAAI;AAAA,MAC3B;AAEA,UAAI,UAAU,OAAO,QAAQ;AAC3B,eAAO;AAAA,MACT;AAEA,aAAO;AAAA,IACT;AAEA,WAAO,QAAQ,UAAU,EAAE,OAAAD,QAAO,WAAAC,WAAU;AAC5C,WAAO,QAAQ,QAAQD;AACvB,WAAO,QAAQ,YAAYC;AAC3B,WAAO,QAAQ,qBAAqB;AAAA;AAAA;;;ACxK7B,SAAS,eAAe;AAC7B,MAAI,OAAO,cAAc,YAAY,eAAe,WAAW;AAC7D,WAAO,UAAU;AAAA,EACnB;AAEA,MAAI,OAAO,YAAY,YAAY,QAAQ,YAAY,QAAW;AAChE,WAAO,WAAW,QAAQ,QAAQ,OAAO,CAAC,CAAC,KAAK,QAAQ,QAAQ,KAC9D,QAAQ,IACV;AAAA,EACF;AAEA,SAAO;AACT;;;ACVO,SAAS,SAAS,OAAO,MAAM,QAAQ,SAAS;AACrD,MAAI,OAAO,WAAW,YAAY;AAChC,UAAM,IAAI,MAAM,2CAA2C;AAAA,EAC7D;AAEA,MAAI,CAAC,SAAS;AACZ,cAAU,CAAC;AAAA,EACb;AAEA,MAAI,MAAM,QAAQ,IAAI,GAAG;AACvB,WAAO,KAAK,QAAQ,EAAE,OAAO,CAAC,UAAUC,UAAS;AAC/C,aAAO,SAAS,KAAK,MAAM,OAAOA,OAAM,UAAU,OAAO;AAAA,IAC3D,GAAG,MAAM,EAAE;AAAA,EACb;AAEA,SAAO,QAAQ,QAAQ,EAAE,KAAK,MAAM;AAClC,QAAI,CAAC,MAAM,SAAS,IAAI,GAAG;AACzB,aAAO,OAAO,OAAO;AAAA,IACvB;AAEA,WAAO,MAAM,SAAS,IAAI,EAAE,OAAO,CAACC,SAAQ,eAAe;AACzD,aAAO,WAAW,KAAK,KAAK,MAAMA,SAAQ,OAAO;AAAA,IACnD,GAAG,MAAM,EAAE;AAAA,EACb,CAAC;AACH;;;ACxBO,SAAS,QAAQ,OAAO,MAAM,MAAMC,OAAM;AAC/C,QAAM,OAAOA;AACb,MAAI,CAAC,MAAM,SAAS,IAAI,GAAG;AACzB,UAAM,SAAS,IAAI,IAAI,CAAC;AAAA,EAC1B;AAEA,MAAI,SAAS,UAAU;AACrB,IAAAA,QAAO,CAAC,QAAQ,YAAY;AAC1B,aAAO,QAAQ,QAAQ,EACpB,KAAK,KAAK,KAAK,MAAM,OAAO,CAAC,EAC7B,KAAK,OAAO,KAAK,MAAM,OAAO,CAAC;AAAA,IACpC;AAAA,EACF;AAEA,MAAI,SAAS,SAAS;AACpB,IAAAA,QAAO,CAAC,QAAQ,YAAY;AAC1B,UAAI;AACJ,aAAO,QAAQ,QAAQ,EACpB,KAAK,OAAO,KAAK,MAAM,OAAO,CAAC,EAC/B,KAAK,CAAC,YAAY;AACjB,iBAAS;AACT,eAAO,KAAK,QAAQ,OAAO;AAAA,MAC7B,CAAC,EACA,KAAK,MAAM;AACV,eAAO;AAAA,MACT,CAAC;AAAA,IACL;AAAA,EACF;AAEA,MAAI,SAAS,SAAS;AACpB,IAAAA,QAAO,CAAC,QAAQ,YAAY;AAC1B,aAAO,QAAQ,QAAQ,EACpB,KAAK,OAAO,KAAK,MAAM,OAAO,CAAC,EAC/B,MAAM,CAAC,UAAU;AAChB,eAAO,KAAK,OAAO,OAAO;AAAA,MAC5B,CAAC;AAAA,IACL;AAAA,EACF;AAEA,QAAM,SAAS,IAAI,EAAE,KAAK;AAAA,IACxB,MAAMA;AAAA,IACN;AAAA,EACF,CAAC;AACH;;;AC3CO,SAAS,WAAW,OAAO,MAAM,QAAQ;AAC9C,MAAI,CAAC,MAAM,SAAS,IAAI,GAAG;AACzB;AAAA,EACF;AAEA,QAAM,QAAQ,MAAM,SAAS,IAAI,EAC9B,IAAI,CAAC,eAAe;AACnB,WAAO,WAAW;AAAA,EACpB,CAAC,EACA,QAAQ,MAAM;AAEjB,MAAI,UAAU,IAAI;AAChB;AAAA,EACF;AAEA,QAAM,SAAS,IAAI,EAAE,OAAO,OAAO,CAAC;AACtC;;;ACXA,IAAM,OAAO,SAAS;AACtB,IAAM,WAAW,KAAK,KAAK,IAAI;AAE/B,SAAS,QAAQC,OAAM,OAAO,MAAM;AAClC,QAAM,gBAAgB,SAAS,YAAY,IAAI,EAAE;AAAA,IAC/C;AAAA,IACA,OAAO,CAAC,OAAO,IAAI,IAAI,CAAC,KAAK;AAAA,EAC/B;AACA,EAAAA,MAAK,MAAM,EAAE,QAAQ,cAAc;AACnC,EAAAA,MAAK,SAAS;AACd,GAAC,UAAU,SAAS,SAAS,MAAM,EAAE,QAAQ,CAAC,SAAS;AACrD,UAAM,OAAO,OAAO,CAAC,OAAO,MAAM,IAAI,IAAI,CAAC,OAAO,IAAI;AACtD,IAAAA,MAAK,IAAI,IAAIA,MAAK,IAAI,IAAI,IAAI,SAAS,SAAS,IAAI,EAAE,MAAM,MAAM,IAAI;AAAA,EACxE,CAAC;AACH;AAEA,SAAS,WAAW;AAClB,QAAM,mBAAmB,OAAO,UAAU;AAC1C,QAAM,oBAAoB;AAAA,IACxB,UAAU,CAAC;AAAA,EACb;AACA,QAAM,eAAe,SAAS,KAAK,MAAM,mBAAmB,gBAAgB;AAC5E,UAAQ,cAAc,mBAAmB,gBAAgB;AACzD,SAAO;AACT;AAEA,SAAS,aAAa;AACpB,QAAM,QAAQ;AAAA,IACZ,UAAU,CAAC;AAAA,EACb;AAEA,QAAMA,QAAO,SAAS,KAAK,MAAM,KAAK;AACtC,UAAQA,OAAM,KAAK;AAEnB,SAAOA;AACT;AAEA,IAAO,4BAAQ,EAAE,UAAU,WAAW;;;ACxCtC,IAAI,UAAU;AAGd,IAAI,YAAY,uBAAuB,OAAO,IAAI,aAAa,CAAC;AAChE,IAAI,WAAW;AAAA,EACb,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,SAAS;AAAA,IACP,QAAQ;AAAA,IACR,cAAc;AAAA,EAChB;AAAA,EACA,WAAW;AAAA,IACT,QAAQ;AAAA,EACV;AACF;AAGA,SAAS,cAAc,QAAQ;AAC7B,MAAI,CAAC,QAAQ;AACX,WAAO,CAAC;AAAA,EACV;AACA,SAAO,OAAO,KAAK,MAAM,EAAE,OAAO,CAAC,QAAQ,QAAQ;AACjD,WAAO,IAAI,YAAY,CAAC,IAAI,OAAO,GAAG;AACtC,WAAO;AAAA,EACT,GAAG,CAAC,CAAC;AACP;AAGA,SAAS,cAAc,OAAO;AAC5B,MAAI,OAAO,UAAU,YAAY,UAAU,KAAM,QAAO;AACxD,MAAI,OAAO,UAAU,SAAS,KAAK,KAAK,MAAM,kBAAmB,QAAO;AACxE,QAAM,QAAQ,OAAO,eAAe,KAAK;AACzC,MAAI,UAAU,KAAM,QAAO;AAC3B,QAAM,OAAO,OAAO,UAAU,eAAe,KAAK,OAAO,aAAa,KAAK,MAAM;AACjF,SAAO,OAAO,SAAS,cAAc,gBAAgB,QAAQ,SAAS,UAAU,KAAK,IAAI,MAAM,SAAS,UAAU,KAAK,KAAK;AAC9H;AAGA,SAAS,UAAU,UAAU,SAAS;AACpC,QAAM,SAAS,OAAO,OAAO,CAAC,GAAG,QAAQ;AACzC,SAAO,KAAK,OAAO,EAAE,QAAQ,CAAC,QAAQ;AACpC,QAAI,cAAc,QAAQ,GAAG,CAAC,GAAG;AAC/B,UAAI,EAAE,OAAO,UAAW,QAAO,OAAO,QAAQ,EAAE,CAAC,GAAG,GAAG,QAAQ,GAAG,EAAE,CAAC;AAAA,UAChE,QAAO,GAAG,IAAI,UAAU,SAAS,GAAG,GAAG,QAAQ,GAAG,CAAC;AAAA,IAC1D,OAAO;AACL,aAAO,OAAO,QAAQ,EAAE,CAAC,GAAG,GAAG,QAAQ,GAAG,EAAE,CAAC;AAAA,IAC/C;AAAA,EACF,CAAC;AACD,SAAO;AACT;AAGA,SAAS,0BAA0B,KAAK;AACtC,aAAW,OAAO,KAAK;AACrB,QAAI,IAAI,GAAG,MAAM,QAAQ;AACvB,aAAO,IAAI,GAAG;AAAA,IAChB;AAAA,EACF;AACA,SAAO;AACT;AAGA,SAAS,MAAM,UAAU,OAAO,SAAS;AAlEzC;AAmEE,MAAI,OAAO,UAAU,UAAU;AAC7B,QAAI,CAAC,QAAQ,GAAG,IAAI,MAAM,MAAM,GAAG;AACnC,cAAU,OAAO,OAAO,MAAM,EAAE,QAAQ,IAAI,IAAI,EAAE,KAAK,OAAO,GAAG,OAAO;AAAA,EAC1E,OAAO;AACL,cAAU,OAAO,OAAO,CAAC,GAAG,KAAK;AAAA,EACnC;AACA,UAAQ,UAAU,cAAc,QAAQ,OAAO;AAC/C,4BAA0B,OAAO;AACjC,4BAA0B,QAAQ,OAAO;AACzC,QAAM,gBAAgB,UAAU,YAAY,CAAC,GAAG,OAAO;AACvD,MAAI,QAAQ,QAAQ,YAAY;AAC9B,QAAI,cAAY,cAAS,UAAU,aAAnB,mBAA6B,SAAQ;AACnD,oBAAc,UAAU,WAAW,SAAS,UAAU,SAAS;AAAA,QAC7D,CAAC,YAAY,CAAC,cAAc,UAAU,SAAS,SAAS,OAAO;AAAA,MACjE,EAAE,OAAO,cAAc,UAAU,QAAQ;AAAA,IAC3C;AACA,kBAAc,UAAU,YAAY,cAAc,UAAU,YAAY,CAAC,GAAG,IAAI,CAAC,YAAY,QAAQ,QAAQ,YAAY,EAAE,CAAC;AAAA,EAC9H;AACA,SAAO;AACT;AAGA,SAAS,mBAAmB,KAAK,YAAY;AAC3C,QAAM,YAAY,KAAK,KAAK,GAAG,IAAI,MAAM;AACzC,QAAM,QAAQ,OAAO,KAAK,UAAU;AACpC,MAAI,MAAM,WAAW,GAAG;AACtB,WAAO;AAAA,EACT;AACA,SAAO,MAAM,YAAY,MAAM,IAAI,CAAC,SAAS;AAC3C,QAAI,SAAS,KAAK;AAChB,aAAO,OAAO,WAAW,EAAE,MAAM,GAAG,EAAE,IAAI,kBAAkB,EAAE,KAAK,GAAG;AAAA,IACxE;AACA,WAAO,GAAG,IAAI,IAAI,mBAAmB,WAAW,IAAI,CAAC,CAAC;AAAA,EACxD,CAAC,EAAE,KAAK,GAAG;AACb;AAGA,IAAI,mBAAmB;AACvB,SAAS,eAAe,cAAc;AACpC,SAAO,aAAa,QAAQ,WAAC,+BAAyB,GAAC,GAAE,EAAE,EAAE,MAAM,GAAG;AACxE;AACA,SAAS,wBAAwB,KAAK;AACpC,QAAM,UAAU,IAAI,MAAM,gBAAgB;AAC1C,MAAI,CAAC,SAAS;AACZ,WAAO,CAAC;AAAA,EACV;AACA,SAAO,QAAQ,IAAI,cAAc,EAAE,OAAO,CAAC,GAAG,MAAM,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;AACrE;AAGA,SAAS,KAAK,QAAQ,YAAY;AAChC,QAAM,SAAS,EAAE,WAAW,KAAK;AACjC,aAAW,OAAO,OAAO,KAAK,MAAM,GAAG;AACrC,QAAI,WAAW,QAAQ,GAAG,MAAM,IAAI;AAClC,aAAO,GAAG,IAAI,OAAO,GAAG;AAAA,IAC1B;AAAA,EACF;AACA,SAAO;AACT;AAGA,SAAS,eAAe,KAAK;AAC3B,SAAO,IAAI,MAAM,oBAAoB,EAAE,IAAI,SAAS,MAAM;AACxD,QAAI,CAAC,eAAe,KAAK,IAAI,GAAG;AAC9B,aAAO,UAAU,IAAI,EAAE,QAAQ,QAAQ,GAAG,EAAE,QAAQ,QAAQ,GAAG;AAAA,IACjE;AACA,WAAO;AAAA,EACT,CAAC,EAAE,KAAK,EAAE;AACZ;AACA,SAAS,iBAAiB,KAAK;AAC7B,SAAO,mBAAmB,GAAG,EAAE,QAAQ,YAAY,SAAS,GAAG;AAC7D,WAAO,MAAM,EAAE,WAAW,CAAC,EAAE,SAAS,EAAE,EAAE,YAAY;AAAA,EACxD,CAAC;AACH;AACA,SAAS,YAAY,UAAU,OAAO,KAAK;AACzC,UAAQ,aAAa,OAAO,aAAa,MAAM,eAAe,KAAK,IAAI,iBAAiB,KAAK;AAC7F,MAAI,KAAK;AACP,WAAO,iBAAiB,GAAG,IAAI,MAAM;AAAA,EACvC,OAAO;AACL,WAAO;AAAA,EACT;AACF;AACA,SAAS,UAAU,OAAO;AACxB,SAAO,UAAU,UAAU,UAAU;AACvC;AACA,SAAS,cAAc,UAAU;AAC/B,SAAO,aAAa,OAAO,aAAa,OAAO,aAAa;AAC9D;AACA,SAAS,UAAU,SAAS,UAAU,KAAK,UAAU;AACnD,MAAI,QAAQ,QAAQ,GAAG,GAAG,SAAS,CAAC;AACpC,MAAI,UAAU,KAAK,KAAK,UAAU,IAAI;AACpC,QAAI,OAAO,UAAU,YAAY,OAAO,UAAU,YAAY,OAAO,UAAU,WAAW;AACxF,cAAQ,MAAM,SAAS;AACvB,UAAI,YAAY,aAAa,KAAK;AAChC,gBAAQ,MAAM,UAAU,GAAG,SAAS,UAAU,EAAE,CAAC;AAAA,MACnD;AACA,aAAO;AAAA,QACL,YAAY,UAAU,OAAO,cAAc,QAAQ,IAAI,MAAM,EAAE;AAAA,MACjE;AAAA,IACF,OAAO;AACL,UAAI,aAAa,KAAK;AACpB,YAAI,MAAM,QAAQ,KAAK,GAAG;AACxB,gBAAM,OAAO,SAAS,EAAE,QAAQ,SAAS,QAAQ;AAC/C,mBAAO;AAAA,cACL,YAAY,UAAU,QAAQ,cAAc,QAAQ,IAAI,MAAM,EAAE;AAAA,YAClE;AAAA,UACF,CAAC;AAAA,QACH,OAAO;AACL,iBAAO,KAAK,KAAK,EAAE,QAAQ,SAAS,GAAG;AACrC,gBAAI,UAAU,MAAM,CAAC,CAAC,GAAG;AACvB,qBAAO,KAAK,YAAY,UAAU,MAAM,CAAC,GAAG,CAAC,CAAC;AAAA,YAChD;AAAA,UACF,CAAC;AAAA,QACH;AAAA,MACF,OAAO;AACL,cAAM,MAAM,CAAC;AACb,YAAI,MAAM,QAAQ,KAAK,GAAG;AACxB,gBAAM,OAAO,SAAS,EAAE,QAAQ,SAAS,QAAQ;AAC/C,gBAAI,KAAK,YAAY,UAAU,MAAM,CAAC;AAAA,UACxC,CAAC;AAAA,QACH,OAAO;AACL,iBAAO,KAAK,KAAK,EAAE,QAAQ,SAAS,GAAG;AACrC,gBAAI,UAAU,MAAM,CAAC,CAAC,GAAG;AACvB,kBAAI,KAAK,iBAAiB,CAAC,CAAC;AAC5B,kBAAI,KAAK,YAAY,UAAU,MAAM,CAAC,EAAE,SAAS,CAAC,CAAC;AAAA,YACrD;AAAA,UACF,CAAC;AAAA,QACH;AACA,YAAI,cAAc,QAAQ,GAAG;AAC3B,iBAAO,KAAK,iBAAiB,GAAG,IAAI,MAAM,IAAI,KAAK,GAAG,CAAC;AAAA,QACzD,WAAW,IAAI,WAAW,GAAG;AAC3B,iBAAO,KAAK,IAAI,KAAK,GAAG,CAAC;AAAA,QAC3B;AAAA,MACF;AAAA,IACF;AAAA,EACF,OAAO;AACL,QAAI,aAAa,KAAK;AACpB,UAAI,UAAU,KAAK,GAAG;AACpB,eAAO,KAAK,iBAAiB,GAAG,CAAC;AAAA,MACnC;AAAA,IACF,WAAW,UAAU,OAAO,aAAa,OAAO,aAAa,MAAM;AACjE,aAAO,KAAK,iBAAiB,GAAG,IAAI,GAAG;AAAA,IACzC,WAAW,UAAU,IAAI;AACvB,aAAO,KAAK,EAAE;AAAA,IAChB;AAAA,EACF;AACA,SAAO;AACT;AACA,SAAS,SAAS,UAAU;AAC1B,SAAO;AAAA,IACL,QAAQ,OAAO,KAAK,MAAM,QAAQ;AAAA,EACpC;AACF;AACA,SAAS,OAAO,UAAU,SAAS;AACjC,MAAI,YAAY,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,GAAG;AAClD,aAAW,SAAS;AAAA,IAClB;AAAA,IACA,SAAS,GAAG,YAAY,SAAS;AAC/B,UAAI,YAAY;AACd,YAAI,WAAW;AACf,cAAM,SAAS,CAAC;AAChB,YAAI,UAAU,QAAQ,WAAW,OAAO,CAAC,CAAC,MAAM,IAAI;AAClD,qBAAW,WAAW,OAAO,CAAC;AAC9B,uBAAa,WAAW,OAAO,CAAC;AAAA,QAClC;AACA,mBAAW,MAAM,IAAI,EAAE,QAAQ,SAAS,UAAU;AAChD,cAAI,MAAM,4BAA4B,KAAK,QAAQ;AACnD,iBAAO,KAAK,UAAU,SAAS,UAAU,IAAI,CAAC,GAAG,IAAI,CAAC,KAAK,IAAI,CAAC,CAAC,CAAC;AAAA,QACpE,CAAC;AACD,YAAI,YAAY,aAAa,KAAK;AAChC,cAAI,YAAY;AAChB,cAAI,aAAa,KAAK;AACpB,wBAAY;AAAA,UACd,WAAW,aAAa,KAAK;AAC3B,wBAAY;AAAA,UACd;AACA,kBAAQ,OAAO,WAAW,IAAI,WAAW,MAAM,OAAO,KAAK,SAAS;AAAA,QACtE,OAAO;AACL,iBAAO,OAAO,KAAK,GAAG;AAAA,QACxB;AAAA,MACF,OAAO;AACL,eAAO,eAAe,OAAO;AAAA,MAC/B;AAAA,IACF;AAAA,EACF;AACA,MAAI,aAAa,KAAK;AACpB,WAAO;AAAA,EACT,OAAO;AACL,WAAO,SAAS,QAAQ,OAAO,EAAE;AAAA,EACnC;AACF;AAGA,SAAS,MAAM,SAAS;AApQxB;AAqQE,MAAI,SAAS,QAAQ,OAAO,YAAY;AACxC,MAAI,OAAO,QAAQ,OAAO,KAAK,QAAQ,gBAAgB,MAAM;AAC7D,MAAI,UAAU,OAAO,OAAO,CAAC,GAAG,QAAQ,OAAO;AAC/C,MAAI;AACJ,MAAI,aAAa,KAAK,SAAS;AAAA,IAC7B;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC;AACD,QAAM,mBAAmB,wBAAwB,GAAG;AACpD,QAAM,SAAS,GAAG,EAAE,OAAO,UAAU;AACrC,MAAI,CAAC,QAAQ,KAAK,GAAG,GAAG;AACtB,UAAM,QAAQ,UAAU;AAAA,EAC1B;AACA,QAAM,oBAAoB,OAAO,KAAK,OAAO,EAAE,OAAO,CAAC,WAAW,iBAAiB,SAAS,MAAM,CAAC,EAAE,OAAO,SAAS;AACrH,QAAM,sBAAsB,KAAK,YAAY,iBAAiB;AAC9D,QAAM,kBAAkB,6BAA6B,KAAK,QAAQ,MAAM;AACxE,MAAI,CAAC,iBAAiB;AACpB,QAAI,QAAQ,UAAU,QAAQ;AAC5B,cAAQ,SAAS,QAAQ,OAAO,MAAM,GAAG,EAAE;AAAA,QACzC,CAAC,WAAW,OAAO;AAAA,UACjB;AAAA,UACA,uBAAuB,QAAQ,UAAU,MAAM;AAAA,QACjD;AAAA,MACF,EAAE,KAAK,GAAG;AAAA,IACZ;AACA,QAAI,IAAI,SAAS,UAAU,GAAG;AAC5B,WAAI,aAAQ,UAAU,aAAlB,mBAA4B,QAAQ;AACtC,cAAM,2BAA2B,QAAQ,OAAO,MAAM,WAAC,kCAA6B,GAAC,MAAK,CAAC;AAC3F,gBAAQ,SAAS,yBAAyB,OAAO,QAAQ,UAAU,QAAQ,EAAE,IAAI,CAAC,YAAY;AAC5F,gBAAM,SAAS,QAAQ,UAAU,SAAS,IAAI,QAAQ,UAAU,MAAM,KAAK;AAC3E,iBAAO,0BAA0B,OAAO,WAAW,MAAM;AAAA,QAC3D,CAAC,EAAE,KAAK,GAAG;AAAA,MACb;AAAA,IACF;AAAA,EACF;AACA,MAAI,CAAC,OAAO,MAAM,EAAE,SAAS,MAAM,GAAG;AACpC,UAAM,mBAAmB,KAAK,mBAAmB;AAAA,EACnD,OAAO;AACL,QAAI,UAAU,qBAAqB;AACjC,aAAO,oBAAoB;AAAA,IAC7B,OAAO;AACL,UAAI,OAAO,KAAK,mBAAmB,EAAE,QAAQ;AAC3C,eAAO;AAAA,MACT;AAAA,IACF;AAAA,EACF;AACA,MAAI,CAAC,QAAQ,cAAc,KAAK,OAAO,SAAS,aAAa;AAC3D,YAAQ,cAAc,IAAI;AAAA,EAC5B;AACA,MAAI,CAAC,SAAS,KAAK,EAAE,SAAS,MAAM,KAAK,OAAO,SAAS,aAAa;AACpE,WAAO;AAAA,EACT;AACA,SAAO,OAAO;AAAA,IACZ,EAAE,QAAQ,KAAK,QAAQ;AAAA,IACvB,OAAO,SAAS,cAAc,EAAE,KAAK,IAAI;AAAA,IACzC,QAAQ,UAAU,EAAE,SAAS,QAAQ,QAAQ,IAAI;AAAA,EACnD;AACF;AAGA,SAAS,qBAAqB,UAAU,OAAO,SAAS;AACtD,SAAO,MAAM,MAAM,UAAU,OAAO,OAAO,CAAC;AAC9C;AAGA,SAAS,aAAa,aAAa,aAAa;AAC9C,QAAM,YAAY,MAAM,aAAa,WAAW;AAChD,QAAM,YAAY,qBAAqB,KAAK,MAAM,SAAS;AAC3D,SAAO,OAAO,OAAO,WAAW;AAAA,IAC9B,UAAU;AAAA,IACV,UAAU,aAAa,KAAK,MAAM,SAAS;AAAA,IAC3C,OAAO,MAAM,KAAK,MAAM,SAAS;AAAA,IACjC;AAAA,EACF,CAAC;AACH;AAGA,IAAI,WAAW,aAAa,MAAM,QAAQ;;;ACrU1C,qCAA0B;;;ACjB1B,IAAM,eAAN,cAA2B,MAAM;AAAA,EAc/B,YAAY,SAAS,YAAY,SAAS;AACxC,UAAM,OAAO;AAdf;AAIA;AAAA;AAAA;AAAA;AAIA;AAAA;AAAA;AAAA;AAIA;AAAA;AAAA;AAAA;AAGE,SAAK,OAAO;AACZ,SAAK,SAAS,OAAO,SAAS,UAAU;AACxC,QAAI,OAAO,MAAM,KAAK,MAAM,GAAG;AAC7B,WAAK,SAAS;AAAA,IAChB;AACA,QAAI,cAAc,SAAS;AACzB,WAAK,WAAW,QAAQ;AAAA,IAC1B;AACA,UAAM,cAAc,OAAO,OAAO,CAAC,GAAG,QAAQ,OAAO;AACrD,QAAI,QAAQ,QAAQ,QAAQ,eAAe;AACzC,kBAAY,UAAU,OAAO,OAAO,CAAC,GAAG,QAAQ,QAAQ,SAAS;AAAA,QAC/D,eAAe,QAAQ,QAAQ,QAAQ,cAAc;AAAA,UACnD,WAAC,YAAW;AAAA,UACZ;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACH;AACA,gBAAY,MAAM,YAAY,IAAI,QAAQ,wBAAwB,0BAA0B,EAAE,QAAQ,uBAAuB,yBAAyB;AACtJ,SAAK,UAAU;AAAA,EACjB;AACF;;;AD7BA,IAAIC,WAAU;AAGd,IAAI,mBAAmB;AAAA,EACrB,SAAS;AAAA,IACP,cAAc,sBAAsBA,QAAO,IAAI,aAAa,CAAC;AAAA,EAC/D;AACF;AAMA,SAASC,eAAc,OAAO;AAC5B,MAAI,OAAO,UAAU,YAAY,UAAU,KAAM,QAAO;AACxD,MAAI,OAAO,UAAU,SAAS,KAAK,KAAK,MAAM,kBAAmB,QAAO;AACxE,QAAM,QAAQ,OAAO,eAAe,KAAK;AACzC,MAAI,UAAU,KAAM,QAAO;AAC3B,QAAM,OAAO,OAAO,UAAU,eAAe,KAAK,OAAO,aAAa,KAAK,MAAM;AACjF,SAAO,OAAO,SAAS,cAAc,gBAAgB,QAAQ,SAAS,UAAU,KAAK,IAAI,MAAM,SAAS,UAAU,KAAK,KAAK;AAC9H;AAIA,eAAe,aAAa,gBAAgB;AA/B5C;AAgCE,QAAM,UAAQ,oBAAe,YAAf,mBAAwB,UAAS,WAAW;AAC1D,MAAI,CAAC,OAAO;AACV,UAAM,IAAI;AAAA,MACR;AAAA,IACF;AAAA,EACF;AACA,QAAM,QAAM,oBAAe,YAAf,mBAAwB,QAAO;AAC3C,QAAM,6BAA2B,oBAAe,YAAf,mBAAwB,8BAA6B;AACtF,QAAM,OAAOA,eAAc,eAAe,IAAI,KAAK,MAAM,QAAQ,eAAe,IAAI,IAAI,KAAK,UAAU,eAAe,IAAI,IAAI,eAAe;AAC7I,QAAM,iBAAiB,OAAO;AAAA,IAC5B,OAAO,QAAQ,eAAe,OAAO,EAAE,IAAI,CAAC,CAAC,MAAM,KAAK,MAAM;AAAA,MAC5D;AAAA,MACA,OAAO,KAAK;AAAA,IACd,CAAC;AAAA,EACH;AACA,MAAI;AACJ,MAAI;AACF,oBAAgB,MAAM,MAAM,eAAe,KAAK;AAAA,MAC9C,QAAQ,eAAe;AAAA,MACvB;AAAA,MACA,WAAU,oBAAe,YAAf,mBAAwB;AAAA,MAClC,SAAS;AAAA,MACT,SAAQ,oBAAe,YAAf,mBAAwB;AAAA;AAAA;AAAA,MAGhC,GAAG,eAAe,QAAQ,EAAE,QAAQ,OAAO;AAAA,IAC7C,CAAC;AAAA,EACH,SAAS,OAAO;AACd,QAAI,UAAU;AACd,QAAI,iBAAiB,OAAO;AAC1B,UAAI,MAAM,SAAS,cAAc;AAC/B,cAAM,SAAS;AACf,cAAM;AAAA,MACR;AACA,gBAAU,MAAM;AAChB,UAAI,MAAM,SAAS,eAAe,WAAW,OAAO;AAClD,YAAI,MAAM,iBAAiB,OAAO;AAChC,oBAAU,MAAM,MAAM;AAAA,QACxB,WAAW,OAAO,MAAM,UAAU,UAAU;AAC1C,oBAAU,MAAM;AAAA,QAClB;AAAA,MACF;AAAA,IACF;AACA,UAAM,eAAe,IAAI,aAAa,SAAS,KAAK;AAAA,MAClD,SAAS;AAAA,IACX,CAAC;AACD,iBAAa,QAAQ;AACrB,UAAM;AAAA,EACR;AACA,QAAM,SAAS,cAAc;AAC7B,QAAM,MAAM,cAAc;AAC1B,QAAM,kBAAkB,CAAC;AACzB,aAAW,CAAC,KAAK,KAAK,KAAK,cAAc,SAAS;AAChD,oBAAgB,GAAG,IAAI;AAAA,EACzB;AACA,QAAM,kBAAkB;AAAA,IACtB;AAAA,IACA;AAAA,IACA,SAAS;AAAA,IACT,MAAM;AAAA,EACR;AACA,MAAI,iBAAiB,iBAAiB;AACpC,UAAM,UAAU,gBAAgB,QAAQ,gBAAgB,KAAK,MAAM,+BAA+B;AAClG,UAAM,kBAAkB,WAAW,QAAQ,IAAI;AAC/C,QAAI;AAAA,MACF,uBAAuB,eAAe,MAAM,IAAI,eAAe,GAAG,qDAAqD,gBAAgB,MAAM,GAAG,kBAAkB,SAAS,eAAe,KAAK,EAAE;AAAA,IACnM;AAAA,EACF;AACA,MAAI,WAAW,OAAO,WAAW,KAAK;AACpC,WAAO;AAAA,EACT;AACA,MAAI,eAAe,WAAW,QAAQ;AACpC,QAAI,SAAS,KAAK;AAChB,aAAO;AAAA,IACT;AACA,UAAM,IAAI,aAAa,cAAc,YAAY,QAAQ;AAAA,MACvD,UAAU;AAAA,MACV,SAAS;AAAA,IACX,CAAC;AAAA,EACH;AACA,MAAI,WAAW,KAAK;AAClB,oBAAgB,OAAO,MAAM,gBAAgB,aAAa;AAC1D,UAAM,IAAI,aAAa,gBAAgB,QAAQ;AAAA,MAC7C,UAAU;AAAA,MACV,SAAS;AAAA,IACX,CAAC;AAAA,EACH;AACA,MAAI,UAAU,KAAK;AACjB,oBAAgB,OAAO,MAAM,gBAAgB,aAAa;AAC1D,UAAM,IAAI,aAAa,eAAe,gBAAgB,IAAI,GAAG,QAAQ;AAAA,MACnE,UAAU;AAAA,MACV,SAAS;AAAA,IACX,CAAC;AAAA,EACH;AACA,kBAAgB,OAAO,2BAA2B,MAAM,gBAAgB,aAAa,IAAI,cAAc;AACvG,SAAO;AACT;AACA,eAAe,gBAAgB,UAAU;AAjIzC;AAkIE,QAAM,cAAc,SAAS,QAAQ,IAAI,cAAc;AACvD,MAAI,CAAC,aAAa;AAChB,WAAO,SAAS,KAAK,EAAE,MAAM,MAAM,EAAE;AAAA,EACvC;AACA,QAAM,eAAW,0CAAU,WAAW;AACtC,MAAI,eAAe,QAAQ,GAAG;AAC5B,QAAI,OAAO;AACX,QAAI;AACF,aAAO,MAAM,SAAS,KAAK;AAC3B,aAAO,KAAK,MAAM,IAAI;AAAA,IACxB,SAAS,KAAK;AACZ,aAAO;AAAA,IACT;AAAA,EACF,WAAW,SAAS,KAAK,WAAW,OAAO,OAAK,cAAS,WAAW,YAApB,mBAA6B,mBAAkB,SAAS;AACtG,WAAO,SAAS,KAAK,EAAE,MAAM,MAAM,EAAE;AAAA,EACvC,OAAO;AACL,WAAO,SAAS,YAAY,EAAE,MAAM,MAAM,IAAI,YAAY,CAAC,CAAC;AAAA,EAC9D;AACF;AACA,SAAS,eAAe,UAAU;AAChC,SAAO,SAAS,SAAS,sBAAsB,SAAS,SAAS;AACnE;AACA,SAAS,eAAe,MAAM;AAC5B,MAAI,OAAO,SAAS,UAAU;AAC5B,WAAO;AAAA,EACT;AACA,MAAI,gBAAgB,aAAa;AAC/B,WAAO;AAAA,EACT;AACA,MAAI,aAAa,MAAM;AACrB,UAAM,SAAS,uBAAuB,OAAO,MAAM,KAAK,iBAAiB,KAAK;AAC9E,WAAO,MAAM,QAAQ,KAAK,MAAM,IAAI,GAAG,KAAK,OAAO,KAAK,KAAK,OAAO,IAAI,CAAC,MAAM,KAAK,UAAU,CAAC,CAAC,EAAE,KAAK,IAAI,CAAC,GAAG,MAAM,KAAK,GAAG,KAAK,OAAO,GAAG,MAAM;AAAA,EACpJ;AACA,SAAO,kBAAkB,KAAK,UAAU,IAAI,CAAC;AAC/C;AAGA,SAASC,cAAa,aAAa,aAAa;AAC9C,QAAM,YAAY,YAAY,SAAS,WAAW;AAClD,QAAM,SAAS,SAAS,OAAO,YAAY;AACzC,UAAM,kBAAkB,UAAU,MAAM,OAAO,UAAU;AACzD,QAAI,CAAC,gBAAgB,WAAW,CAAC,gBAAgB,QAAQ,MAAM;AAC7D,aAAO,aAAa,UAAU,MAAM,eAAe,CAAC;AAAA,IACtD;AACA,UAAM,WAAW,CAAC,QAAQ,gBAAgB;AACxC,aAAO;AAAA,QACL,UAAU,MAAM,UAAU,MAAM,QAAQ,WAAW,CAAC;AAAA,MACtD;AAAA,IACF;AACA,WAAO,OAAO,UAAU;AAAA,MACtB,UAAU;AAAA,MACV,UAAUA,cAAa,KAAK,MAAM,SAAS;AAAA,IAC7C,CAAC;AACD,WAAO,gBAAgB,QAAQ,KAAK,UAAU,eAAe;AAAA,EAC/D;AACA,SAAO,OAAO,OAAO,QAAQ;AAAA,IAC3B,UAAU;AAAA,IACV,UAAUA,cAAa,KAAK,MAAM,SAAS;AAAA,EAC7C,CAAC;AACH;AAGA,IAAI,UAAUA,cAAa,UAAU,gBAAgB;;;AE3LrD,IAAIC,WAAU;AASd,SAAS,+BAA+B,MAAM;AAC5C,SAAO;AAAA,IACL,KAAK,OAAO,IAAI,CAAC,MAAM,MAAM,EAAE,OAAO,EAAE,EAAE,KAAK,IAAI;AACvD;AACA,IAAI,uBAAuB,cAAc,MAAM;AAAA,EAC7C,YAAY,UAAU,SAAS,UAAU;AACvC,UAAM,+BAA+B,QAAQ,CAAC;AAUhD,gCAAO;AACP;AACA;AAXE,SAAK,UAAU;AACf,SAAK,UAAU;AACf,SAAK,WAAW;AAChB,SAAK,SAAS,SAAS;AACvB,SAAK,OAAO,SAAS;AACrB,QAAI,MAAM,mBAAmB;AAC3B,YAAM,kBAAkB,MAAM,KAAK,WAAW;AAAA,IAChD;AAAA,EACF;AAIF;AAGA,IAAI,uBAAuB;AAAA,EACzB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AACA,IAAI,6BAA6B,CAAC,SAAS,UAAU,KAAK;AAC1D,IAAI,uBAAuB;AAC3B,SAAS,QAAQ,UAAU,OAAO,SAAS;AACzC,MAAI,SAAS;AACX,QAAI,OAAO,UAAU,YAAY,WAAW,SAAS;AACnD,aAAO,QAAQ;AAAA,QACb,IAAI,MAAM,4DAA4D;AAAA,MACxE;AAAA,IACF;AACA,eAAW,OAAO,SAAS;AACzB,UAAI,CAAC,2BAA2B,SAAS,GAAG,EAAG;AAC/C,aAAO,QAAQ;AAAA,QACb,IAAI;AAAA,UACF,uBAAuB,GAAG;AAAA,QAC5B;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACA,QAAM,gBAAgB,OAAO,UAAU,WAAW,OAAO,OAAO,EAAE,MAAM,GAAG,OAAO,IAAI;AACtF,QAAM,iBAAiB,OAAO;AAAA,IAC5B;AAAA,EACF,EAAE,OAAO,CAAC,QAAQ,QAAQ;AACxB,QAAI,qBAAqB,SAAS,GAAG,GAAG;AACtC,aAAO,GAAG,IAAI,cAAc,GAAG;AAC/B,aAAO;AAAA,IACT;AACA,QAAI,CAAC,OAAO,WAAW;AACrB,aAAO,YAAY,CAAC;AAAA,IACtB;AACA,WAAO,UAAU,GAAG,IAAI,cAAc,GAAG;AACzC,WAAO;AAAA,EACT,GAAG,CAAC,CAAC;AACL,QAAM,UAAU,cAAc,WAAW,SAAS,SAAS,SAAS;AACpE,MAAI,qBAAqB,KAAK,OAAO,GAAG;AACtC,mBAAe,MAAM,QAAQ,QAAQ,sBAAsB,cAAc;AAAA,EAC3E;AACA,SAAO,SAAS,cAAc,EAAE,KAAK,CAAC,aAAa;AACjD,QAAI,SAAS,KAAK,QAAQ;AACxB,YAAM,UAAU,CAAC;AACjB,iBAAW,OAAO,OAAO,KAAK,SAAS,OAAO,GAAG;AAC/C,gBAAQ,GAAG,IAAI,SAAS,QAAQ,GAAG;AAAA,MACrC;AACA,YAAM,IAAI;AAAA,QACR;AAAA,QACA;AAAA,QACA,SAAS;AAAA,MACX;AAAA,IACF;AACA,WAAO,SAAS,KAAK;AAAA,EACvB,CAAC;AACH;AAGA,SAASC,cAAa,UAAU,aAAa;AAC3C,QAAM,aAAa,SAAS,SAAS,WAAW;AAChD,QAAM,SAAS,CAAC,OAAO,YAAY;AACjC,WAAO,QAAQ,YAAY,OAAO,OAAO;AAAA,EAC3C;AACA,SAAO,OAAO,OAAO,QAAQ;AAAA,IAC3B,UAAUA,cAAa,KAAK,MAAM,UAAU;AAAA,IAC5C,UAAU,WAAW;AAAA,EACvB,CAAC;AACH;AAGA,IAAI,WAAWA,cAAa,SAAS;AAAA,EACnC,SAAS;AAAA,IACP,cAAc,sBAAsBD,QAAO,IAAI,aAAa,CAAC;AAAA,EAC/D;AAAA,EACA,QAAQ;AAAA,EACR,KAAK;AACP,CAAC;AACD,SAAS,kBAAkB,eAAe;AACxC,SAAOC,cAAa,eAAe;AAAA,IACjC,QAAQ;AAAA,IACR,KAAK;AAAA,EACP,CAAC;AACH;;;AC1HA,IAAI,SAAS;AACb,IAAI,MAAM;AACV,IAAI,QAAQ,IAAI,OAAO,IAAI,MAAM,GAAG,GAAG,GAAG,MAAM,GAAG,GAAG,GAAG,MAAM,GAAG;AAClE,IAAI,QAAQ,MAAM,KAAK,KAAK,KAAK;AAGjC,eAAe,KAAK,OAAO;AACzB,QAAM,QAAQ,MAAM,KAAK;AACzB,QAAM,iBAAiB,MAAM,WAAW,KAAK,KAAK,MAAM,WAAW,MAAM;AACzE,QAAM,iBAAiB,MAAM,WAAW,MAAM;AAC9C,QAAM,YAAY,QAAQ,QAAQ,iBAAiB,iBAAiB,iBAAiB,mBAAmB;AACxG,SAAO;AAAA,IACL,MAAM;AAAA,IACN;AAAA,IACA;AAAA,EACF;AACF;AAGA,SAAS,wBAAwB,OAAO;AACtC,MAAI,MAAM,MAAM,IAAI,EAAE,WAAW,GAAG;AAClC,WAAO,UAAU,KAAK;AAAA,EACxB;AACA,SAAO,SAAS,KAAK;AACvB;AAGA,eAAe,KAAK,OAAOC,UAAS,OAAO,YAAY;AACrD,QAAMC,YAAWD,SAAQ,SAAS;AAAA,IAChC;AAAA,IACA;AAAA,EACF;AACA,EAAAC,UAAS,QAAQ,gBAAgB,wBAAwB,KAAK;AAC9D,SAAOD,SAAQC,SAAQ;AACzB;AAGA,IAAI,kBAAkB,SAAS,iBAAiB,OAAO;AACrD,MAAI,CAAC,OAAO;AACV,UAAM,IAAI,MAAM,0DAA0D;AAAA,EAC5E;AACA,MAAI,OAAO,UAAU,UAAU;AAC7B,UAAM,IAAI;AAAA,MACR;AAAA,IACF;AAAA,EACF;AACA,UAAQ,MAAM,QAAQ,sBAAsB,EAAE;AAC9C,SAAO,OAAO,OAAO,KAAK,KAAK,MAAM,KAAK,GAAG;AAAA,IAC3C,MAAM,KAAK,KAAK,MAAM,KAAK;AAAA,EAC7B,CAAC;AACH;;;ACnDA,IAAMC,WAAU;;;ACMhB,IAAM,OAAO,MAAM;AACnB;AACA,IAAM,cAAc,QAAQ,KAAK,KAAK,OAAO;AAC7C,IAAM,eAAe,QAAQ,MAAM,KAAK,OAAO;AAC/C,SAAS,aAAa,SAAS,CAAC,GAAG;AACjC,MAAI,OAAO,OAAO,UAAU,YAAY;AACtC,WAAO,QAAQ;AAAA,EACjB;AACA,MAAI,OAAO,OAAO,SAAS,YAAY;AACrC,WAAO,OAAO;AAAA,EAChB;AACA,MAAI,OAAO,OAAO,SAAS,YAAY;AACrC,WAAO,OAAO;AAAA,EAChB;AACA,MAAI,OAAO,OAAO,UAAU,YAAY;AACtC,WAAO,QAAQ;AAAA,EACjB;AACA,SAAO;AACT;AACA,IAAM,iBAAiB,mBAAmBC,QAAO,IAAI,aAAa,CAAC;AACnE,IAAM,UAAN,MAAc;AAAA,EAwCZ,YAAY,UAAU,CAAC,GAAG;AAiE1B;AAAA;AACA;AACA;AACA;AAEA;AAAA;AArEE,UAAMC,QAAO,IAAI,0BAAK,WAAW;AACjC,UAAM,kBAAkB;AAAA,MACtB,SAAS,QAAQ,SAAS,SAAS;AAAA,MACnC,SAAS,CAAC;AAAA,MACV,SAAS,OAAO,OAAO,CAAC,GAAG,QAAQ,SAAS;AAAA;AAAA,QAE1C,MAAMA,MAAK,KAAK,MAAM,SAAS;AAAA,MACjC,CAAC;AAAA,MACD,WAAW;AAAA,QACT,UAAU,CAAC;AAAA,QACX,QAAQ;AAAA,MACV;AAAA,IACF;AACA,oBAAgB,QAAQ,YAAY,IAAI,QAAQ,YAAY,GAAG,QAAQ,SAAS,IAAI,cAAc,KAAK;AACvG,QAAI,QAAQ,SAAS;AACnB,sBAAgB,UAAU,QAAQ;AAAA,IACpC;AACA,QAAI,QAAQ,UAAU;AACpB,sBAAgB,UAAU,WAAW,QAAQ;AAAA,IAC/C;AACA,QAAI,QAAQ,UAAU;AACpB,sBAAgB,QAAQ,WAAW,IAAI,QAAQ;AAAA,IACjD;AACA,SAAK,UAAU,QAAQ,SAAS,eAAe;AAC/C,SAAK,UAAU,kBAAkB,KAAK,OAAO,EAAE,SAAS,eAAe;AACvE,SAAK,MAAM,aAAa,QAAQ,GAAG;AACnC,SAAK,OAAOA;AACZ,QAAI,CAAC,QAAQ,cAAc;AACzB,UAAI,CAAC,QAAQ,MAAM;AACjB,aAAK,OAAO,aAAa;AAAA,UACvB,MAAM;AAAA,QACR;AAAA,MACF,OAAO;AACL,cAAMC,QAAO,gBAAgB,QAAQ,IAAI;AACzC,QAAAD,MAAK,KAAK,WAAWC,MAAK,IAAI;AAC9B,aAAK,OAAOA;AAAA,MACd;AAAA,IACF,OAAO;AACL,YAAM,EAAE,cAAc,GAAG,aAAa,IAAI;AAC1C,YAAMA,QAAO;AAAA,QACX,OAAO;AAAA,UACL;AAAA,YACE,SAAS,KAAK;AAAA,YACd,KAAK,KAAK;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,YAMV,SAAS;AAAA,YACT,gBAAgB;AAAA,UAClB;AAAA,UACA,QAAQ;AAAA,QACV;AAAA,MACF;AACA,MAAAD,MAAK,KAAK,WAAWC,MAAK,IAAI;AAC9B,WAAK,OAAOA;AAAA,IACd;AACA,UAAM,mBAAmB,KAAK;AAC9B,aAAS,IAAI,GAAG,IAAI,iBAAiB,QAAQ,QAAQ,EAAE,GAAG;AACxD,aAAO,OAAO,MAAM,iBAAiB,QAAQ,CAAC,EAAE,MAAM,OAAO,CAAC;AAAA,IAChE;AAAA,EACF;AAAA,EArGA,OAAO,SAAS,UAAU;AACxB,UAAM,sBAAsB,cAAc,KAAK;AAAA,MAC7C,eAAe,MAAM;AACnB,cAAM,UAAU,KAAK,CAAC,KAAK,CAAC;AAC5B,YAAI,OAAO,aAAa,YAAY;AAClC,gBAAM,SAAS,OAAO,CAAC;AACvB;AAAA,QACF;AACA;AAAA,UACE,OAAO;AAAA,YACL,CAAC;AAAA,YACD;AAAA,YACA;AAAA,YACA,QAAQ,aAAa,SAAS,YAAY;AAAA,cACxC,WAAW,GAAG,QAAQ,SAAS,IAAI,SAAS,SAAS;AAAA,YACvD,IAAI;AAAA,UACN;AAAA,QACF;AAAA,MACF;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,OAAO,UAAU,YAAY;AAzD/B;AA0DI,UAAM,iBAAiB,KAAK;AAC5B,UAAM,cAAa,mBAAc,KAAK;AAAA,IAItC,GAHE,cADiB,IACV,WAAU,eAAe;AAAA,MAC9B,WAAW,OAAO,CAAC,WAAW,CAAC,eAAe,SAAS,MAAM,CAAC;AAAA,IAChE,IAHiB;AAKnB,WAAO;AAAA,EACT;AAwEF;AA9GE,cADI,SACG,WAAUF;AAuBjB,cAxBI,SAwBG,WAAU,CAAC;;;AClDpB,IAAMG,WAAU;;;ACChB,SAAS,WAAW,SAAS;AAC3B,UAAQ,KAAK,KAAK,WAAW,CAACC,UAAS,YAAY;AACjD,YAAQ,IAAI,MAAM,WAAW,OAAO;AACpC,UAAM,QAAQ,KAAK,IAAI;AACvB,UAAM,iBAAiB,QAAQ,QAAQ,SAAS,MAAM,OAAO;AAC7D,UAAM,OAAO,eAAe,IAAI,QAAQ,QAAQ,SAAS,EAAE;AAC3D,WAAOA,SAAQ,OAAO,EAAE,KAAK,CAAC,aAAa;AACzC,YAAM,YAAY,SAAS,QAAQ,qBAAqB;AACxD,cAAQ,IAAI;AAAA,QACV,GAAG,eAAe,MAAM,IAAI,IAAI,MAAM,SAAS,MAAM,YAAY,SAAS,OAAO,KAAK,IAAI,IAAI,KAAK;AAAA,MACrG;AACA,aAAO;AAAA,IACT,CAAC,EAAE,MAAM,CAAC,UAAU;AAbxB;AAcM,YAAM,cAAY,WAAM,aAAN,mBAAgB,QAAQ,2BAA0B;AACpE,cAAQ,IAAI;AAAA,QACV,GAAG,eAAe,MAAM,IAAI,IAAI,MAAM,MAAM,MAAM,YAAY,SAAS,OAAO,KAAK,IAAI,IAAI,KAAK;AAAA,MAClG;AACA,YAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH;AACA,WAAW,UAAUC;;;ACrBrB,IAAIC,WAAU;AAGd,SAAS,+BAA+B,UAAU;AAChD,MAAI,CAAC,SAAS,MAAM;AAClB,WAAO;AAAA,MACL,GAAG;AAAA,MACH,MAAM,CAAC;AAAA,IACT;AAAA,EACF;AACA,QAAM,8BAA8B,iBAAiB,SAAS,QAAQ,mBAAmB,SAAS,SAAS,EAAE,SAAS,SAAS;AAC/H,MAAI,CAAC,2BAA4B,QAAO;AACxC,QAAM,oBAAoB,SAAS,KAAK;AACxC,QAAM,sBAAsB,SAAS,KAAK;AAC1C,QAAM,aAAa,SAAS,KAAK;AACjC,QAAM,eAAe,SAAS,KAAK;AACnC,SAAO,SAAS,KAAK;AACrB,SAAO,SAAS,KAAK;AACrB,SAAO,SAAS,KAAK;AACrB,SAAO,SAAS,KAAK;AACrB,QAAM,eAAe,OAAO,KAAK,SAAS,IAAI,EAAE,CAAC;AACjD,QAAM,OAAO,SAAS,KAAK,YAAY;AACvC,WAAS,OAAO;AAChB,MAAI,OAAO,sBAAsB,aAAa;AAC5C,aAAS,KAAK,qBAAqB;AAAA,EACrC;AACA,MAAI,OAAO,wBAAwB,aAAa;AAC9C,aAAS,KAAK,uBAAuB;AAAA,EACvC;AACA,WAAS,KAAK,cAAc;AAC5B,WAAS,KAAK,gBAAgB;AAC9B,SAAO;AACT;AAGA,SAAS,SAAS,SAAS,OAAO,YAAY;AAC5C,QAAM,UAAU,OAAO,UAAU,aAAa,MAAM,SAAS,UAAU,IAAI,QAAQ,QAAQ,SAAS,OAAO,UAAU;AACrH,QAAM,gBAAgB,OAAO,UAAU,aAAa,QAAQ,QAAQ;AACpE,QAAM,SAAS,QAAQ;AACvB,QAAM,UAAU,QAAQ;AACxB,MAAI,MAAM,QAAQ;AAClB,SAAO;AAAA,IACL,CAAC,OAAO,aAAa,GAAG,OAAO;AAAA,MAC7B,MAAM,OAAO;AACX,YAAI,CAAC,IAAK,QAAO,EAAE,MAAM,KAAK;AAC9B,YAAI;AACF,gBAAM,WAAW,MAAM,cAAc,EAAE,QAAQ,KAAK,QAAQ,CAAC;AAC7D,gBAAM,qBAAqB,+BAA+B,QAAQ;AAClE,kBAAQ,mBAAmB,QAAQ,QAAQ,IAAI;AAAA,YAC7C;AAAA,UACF,KAAK,CAAC,GAAG,CAAC;AACV,cAAI,CAAC,OAAO,mBAAmB,mBAAmB,MAAM;AACtD,kBAAM,YAAY,IAAI,IAAI,mBAAmB,GAAG;AAChD,kBAAM,SAAS,UAAU;AACzB,kBAAM,OAAO,SAAS,OAAO,IAAI,MAAM,KAAK,KAAK,EAAE;AACnD,kBAAM,WAAW,SAAS,OAAO,IAAI,UAAU,KAAK,OAAO,EAAE;AAC7D,gBAAI,OAAO,WAAW,mBAAmB,KAAK,eAAe;AAC3D,qBAAO,IAAI,QAAQ,OAAO,OAAO,CAAC,CAAC;AACnC,oBAAM,UAAU,SAAS;AAAA,YAC3B;AAAA,UACF;AACA,iBAAO,EAAE,OAAO,mBAAmB;AAAA,QACrC,SAAS,OAAO;AACd,cAAI,MAAM,WAAW,IAAK,OAAM;AAChC,gBAAM;AACN,iBAAO;AAAA,YACL,OAAO;AAAA,cACL,QAAQ;AAAA,cACR,SAAS,CAAC;AAAA,cACV,MAAM,CAAC;AAAA,YACT;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;AAGA,SAAS,SAAS,SAAS,OAAO,YAAY,OAAO;AACnD,MAAI,OAAO,eAAe,YAAY;AACpC,YAAQ;AACR,iBAAa;AAAA,EACf;AACA,SAAO;AAAA,IACL;AAAA,IACA,CAAC;AAAA,IACD,SAAS,SAAS,OAAO,UAAU,EAAE,OAAO,aAAa,EAAE;AAAA,IAC3D;AAAA,EACF;AACF;AACA,SAAS,OAAO,SAAS,SAAS,WAAW,OAAO;AAClD,SAAO,UAAU,KAAK,EAAE,KAAK,CAAC,WAAW;AACvC,QAAI,OAAO,MAAM;AACf,aAAO;AAAA,IACT;AACA,QAAI,YAAY;AAChB,aAAS,OAAO;AACd,kBAAY;AAAA,IACd;AACA,cAAU,QAAQ;AAAA,MAChB,QAAQ,MAAM,OAAO,OAAO,IAAI,IAAI,OAAO,MAAM;AAAA,IACnD;AACA,QAAI,WAAW;AACb,aAAO;AAAA,IACT;AACA,WAAO,OAAO,SAAS,SAAS,WAAW,KAAK;AAAA,EAClD,CAAC;AACH;AAGA,IAAI,sBAAsB,OAAO,OAAO,UAAU;AAAA,EAChD;AACF,CAAC;AAmRD,SAAS,aAAa,SAAS;AAC7B,SAAO;AAAA,IACL,UAAU,OAAO,OAAO,SAAS,KAAK,MAAM,OAAO,GAAG;AAAA,MACpD,UAAU,SAAS,KAAK,MAAM,OAAO;AAAA,IACvC,CAAC;AAAA,EACH;AACF;AACA,aAAa,UAAUC;;;AC5YhB,IAAMC,WAAU;;;ACCvB,IAAM,YAA6C;EACjD,SAAS;IACP,yCAAyC;MACvC;IACF;IACA,0CAA0C;MACxC;IACF;IACA,2CAA2C;MACzC;IACF;IACA,4BAA4B;MAC1B;IACF;IACA,8BAA8B;MAC5B;IACF;IACA,oBAAoB;MAClB;IACF;IACA,mBAAmB;MACjB;IACF;IACA,2BAA2B;MACzB;IACF;IACA,0BAA0B,CAAC,yCAAyC;IACpE,iCAAiC;MAC/B;IACF;IACA,yBAAyB,CAAC,+CAA+C;IACzE,0BAA0B;MACxB;IACF;IACA,mBAAmB,CAAC,oCAAoC;IACxD,+BAA+B;MAC7B;IACF;IACA,gCAAgC;MAC9B;IACF;IACA,yBAAyB,CAAC,+CAA+C;IACzE,0BAA0B;MACxB;IACF;IACA,oBAAoB,CAAC,8CAA8C;IACnE,wBAAwB;MACtB;IACF;IACA,wBAAwB;MACtB;IACF;IACA,yBAAyB;MACvB;IACF;IACA,gBAAgB;MACd;IACF;IACA,yBAAyB;MACvB;IACF;IACA,2BAA2B;MACzB;IACF;IACA,0BAA0B;MACxB;IACF;IACA,iBAAiB,CAAC,kDAAkD;IACpE,mBAAmB,CAAC,6CAA6C;IACjE,kBAAkB;MAChB;IACF;IACA,oBAAoB;MAClB;IACF;IACA,+BAA+B;MAC7B;IACF;IACA,gCAAgC;MAC9B;IACF;IACA,mBAAmB,CAAC,oDAAoD;IACxE,uBAAuB;MACrB;IACF;IACA,oDAAoD;MAClD;IACF;IACA,iBAAiB;MACf;IACF;IACA,kBAAkB;MAChB;IACF;IACA,+BAA+B;MAC7B;IACF;IACA,gCAAgC;MAC9B;IACF;IACA,yBAAyB;MACvB;IACF;IACA,mDAAmD;MACjD;IACF;IACA,gBAAgB;MACd;IACF;IACA,wBAAwB;MACtB;IACF;IACA,+BAA+B;MAC7B;IACF;IACA,gCAAgC;MAC9B;IACF;IACA,qBAAqB,CAAC,0CAA0C;IAChE,sBAAsB,CAAC,+CAA+C;IACtE,kCAAkC;MAChC;IACF;IACA,4BAA4B,CAAC,qCAAqC;IAClE,+BAA+B;MAC7B;IACF;IACA,6BAA6B;MAC3B;IACF;IACA,aAAa,CAAC,2DAA2D;IACzE,8BAA8B;MAC5B;IACF;IACA,yBAAyB;MACvB;IACF;IACA,sBAAsB;MACpB;IACF;IACA,wBAAwB;MACtB;IACF;IACA,wDAAwD;MACtD;IACF;IACA,sDAAsD;MACpD;IACF;IACA,yCAAyC;MACvC;IACF;IACA,uCAAuC;MACrC;IACF;IACA,uBAAuB;MACrB;IACF;IACA,yCAAyC;MACvC;IACF;IACA,8BAA8B;MAC5B;IACF;IACA,oCAAoC;MAClC;IACF;IACA,qCAAqC;MACnC;IACF;IACA,iCAAiC;MAC/B;IACF;IACA,sBAAsB,CAAC,iDAAiD;IACxE,iBAAiB,CAAC,4CAA4C;IAC9D,cAAc,CAAC,+CAA+C;IAC9D,gBAAgB,CAAC,0CAA0C;IAC3D,6BAA6B;MAC3B;IACF;IACA,oBAAoB;MAClB;MACA,CAAC;MACD,EAAE,SAAS,CAAC,WAAW,uCAAuC,EAAE;IAClE;IACA,kBAAkB,CAAC,sDAAsD;IACzE,eAAe,CAAC,yDAAyD;IACzE,iBAAiB,CAAC,oDAAoD;IACtE,kBAAkB;MAChB;IACF;IACA,2BAA2B,CAAC,6CAA6C;IACzE,4BAA4B;MAC1B;IACF;IACA,aAAa,CAAC,2DAA2D;IACzE,+BAA+B;MAC7B;IACF;IACA,gBAAgB,CAAC,iDAAiD;IAClE,uBAAuB;MACrB;IACF;IACA,qBAAqB;MACnB;IACF;IACA,kBAAkB;MAChB;IACF;IACA,sBAAsB,CAAC,6CAA6C;IACpE,wBAAwB;MACtB;IACF;IACA,0BAA0B;MACxB;IACF;IACA,sCAAsC;MACpC;IACF;IACA,yBAAyB,CAAC,wCAAwC;IAClE,wBAAwB;MACtB;IACF;IACA,+BAA+B;MAC7B;IACF;IACA,qCAAqC;MACnC;IACF;IACA,sCAAsC;MACpC;IACF;IACA,gBAAgB,CAAC,iCAAiC;IAClD,kBAAkB,CAAC,mCAAmC;IACtD,6BAA6B;MAC3B;IACF;IACA,+BAA+B;MAC7B;IACF;IACA,iBAAiB,CAAC,2CAA2C;IAC7D,mBAAmB,CAAC,6CAA6C;IACjE,mBAAmB,CAAC,6CAA6C;IACjE,8BAA8B,CAAC,2CAA2C;IAC1E,+BAA+B;MAC7B;IACF;IACA,+BAA+B;MAC7B;IACF;IACA,iCAAiC;MAC/B;IACF;IACA,0DAA0D;MACxD;IACF;IACA,6BAA6B,CAAC,iCAAiC;IAC/D,8BAA8B,CAAC,2CAA2C;IAC1E,0BAA0B;MACxB;IACF;IACA,kBAAkB;MAChB;IACF;IACA,yBAAyB,CAAC,wCAAwC;IAClE,wBAAwB;MACtB;IACF;IACA,eAAe,CAAC,wDAAwD;IACxE,yBAAyB;MACvB;IACF;IACA,iDAAiD;MAC/C;IACF;IACA,kDAAkD;MAChD;IACF;IACA,6CAA6C;MAC3C;IACF;IACA,8CAA8C;MAC5C;IACF;IACA,iCAAiC;MAC/B;IACF;IACA,mCAAmC;MACjC;IACF;IACA,yBAAyB;MACvB;IACF;IACA,gCAAgC;MAC9B;IACF;IACA,+BAA+B;MAC7B;IACF;IACA,6BAA6B;MAC3B;IACF;IACA,0CAA0C;MACxC;IACF;IACA,2CAA2C;MACzC;IACF;IACA,8BAA8B;MAC5B;IACF;IACA,wDAAwD;MACtD;IACF;IACA,sDAAsD;MACpD;IACF;IACA,yCAAyC;MACvC;IACF;IACA,uCAAuC;MACrC;IACF;IACA,8BAA8B;MAC5B;IACF;IACA,gCAAgC;MAC9B;IACF;IACA,yDAAyD;MACvD;IACF;IACA,+BAA+B;MAC7B;IACF;IACA,2BAA2B;MACzB;IACF;IACA,0BAA0B;MACxB;IACF;IACA,mBAAmB,CAAC,4CAA4C;IAChE,oBAAoB;MAClB;IACF;EACF;EACA,UAAU;IACR,uCAAuC,CAAC,kCAAkC;IAC1E,wBAAwB,CAAC,2CAA2C;IACpE,0BAA0B;MACxB;IACF;IACA,UAAU,CAAC,YAAY;IACvB,qBAAqB,CAAC,wCAAwC;IAC9D,WAAW,CAAC,wCAAwC;IACpD,2CAA2C;MACzC;IACF;IACA,gCAAgC,CAAC,8BAA8B;IAC/D,uCAAuC,CAAC,oBAAoB;IAC5D,mCAAmC;MACjC;IACF;IACA,kBAAkB,CAAC,aAAa;IAChC,gCAAgC,CAAC,qCAAqC;IACtE,yBAAyB,CAAC,qCAAqC;IAC/D,qBAAqB,CAAC,wBAAwB;IAC9C,2BAA2B,CAAC,uCAAuC;IACnE,iCAAiC;MAC/B;IACF;IACA,gBAAgB,CAAC,kCAAkC;IACnD,2CAA2C;MACzC;IACF;IACA,qCAAqC,CAAC,mBAAmB;IACzD,wBAAwB,CAAC,+BAA+B;IACxD,wBAAwB,CAAC,qCAAqC;IAC9D,uBAAuB,CAAC,sCAAsC;IAC9D,sCAAsC,CAAC,yBAAyB;IAChE,qBAAqB,CAAC,uCAAuC;IAC7D,yBAAyB,CAAC,oBAAoB;IAC9C,6BAA6B,CAAC,yCAAyC;IACvE,kBAAkB,CAAC,2CAA2C;IAC9D,kBAAkB,CAAC,0CAA0C;IAC7D,qBAAqB,CAAC,wCAAwC;IAC9D,uBAAuB;MACrB;IACF;IACA,8BAA8B,CAAC,kCAAkC;IACjE,gCAAgC,CAAC,qCAAqC;EACxE;EACA,MAAM;IACJ,uBAAuB;MACrB;MACA,CAAC;MACD,EAAE,SAAS,CAAC,QAAQ,2CAA2C,EAAE;IACnE;IACA,2CAA2C;MACzC;IACF;IACA,YAAY,CAAC,sCAAsC;IACnD,oBAAoB,CAAC,wCAAwC;IAC7D,+BAA+B;MAC7B;IACF;IACA,qBAAqB,CAAC,wCAAwC;IAC9D,oBAAoB,CAAC,6CAA6C;IAClE,aAAa,CAAC,wCAAwC;IACtD,kBAAkB,CAAC,UAAU;IAC7B,WAAW,CAAC,sBAAsB;IAClC,iBAAiB,CAAC,0CAA0C;IAC5D,oBAAoB,CAAC,8BAA8B;IACnD,qBAAqB,CAAC,wCAAwC;IAC9D,+BAA+B;MAC7B;IACF;IACA,sCAAsC;MACpC;IACF;IACA,qBAAqB,CAAC,oCAAoC;IAC1D,wBAAwB,CAAC,sBAAsB;IAC/C,oBAAoB,CAAC,wCAAwC;IAC7D,qBAAqB,CAAC,mDAAmD;IACzE,4BAA4B;MAC1B;IACF;IACA,2CAA2C;MACzC;IACF;IACA,6CAA6C;MAC3C;IACF;IACA,mBAAmB,CAAC,wBAAwB;IAC5C,uCAAuC,CAAC,yBAAyB;IACjE,WAAW,CAAC,gCAAgC;IAC5C,kBAAkB,CAAC,wCAAwC;IAC3D,mCAAmC,CAAC,gCAAgC;IACpE,uCAAuC,CAAC,iCAAiC;IACzE,8CAA8C;MAC5C;IACF;IACA,uBAAuB,CAAC,0BAA0B;IAClD,0BAA0B;MACxB;IACF;IACA,4BAA4B;MAC1B;MACA,CAAC;MACD,EAAE,SAAS,CAAC,QAAQ,gDAAgD,EAAE;IACxE;IACA,gDAAgD;MAC9C;IACF;IACA,YAAY,CAAC,uCAAuC;IACpD,+BAA+B,CAAC,4BAA4B;IAC5D,YAAY,CAAC,6CAA6C;IAC1D,qBAAqB,CAAC,oDAAoD;IAC1E,uBAAuB;MACrB;IACF;IACA,2BAA2B,CAAC,wBAAwB;EACtD;EACA,SAAS;IACP,4BAA4B,CAAC,0CAA0C;IACvE,6BAA6B;MAC3B;IACF;IACA,gCAAgC;MAC9B;IACF;IACA,iCAAiC;MAC/B;IACF;IACA,6BAA6B,CAAC,2CAA2C;IACzE,8BAA8B;MAC5B;IACF;IACA,4BAA4B;MAC1B;IACF;IACA,6BAA6B;MAC3B;IACF;EACF;EACA,WAAW;IACT,gBAAgB,CAAC,4BAA4B;IAC7C,gBAAgB,CAAC,gDAAgD;IACjE,oBAAoB,CAAC,6CAA6C;IAClE,kBAAkB,CAAC,2BAA2B;IAC9C,gBAAgB,CAAC,+CAA+C;EAClE;EACA,QAAQ;IACN,QAAQ,CAAC,uCAAuC;IAChD,aAAa,CAAC,yCAAyC;IACvD,KAAK,CAAC,qDAAqD;IAC3D,UAAU,CAAC,yDAAyD;IACpE,iBAAiB;MACf;IACF;IACA,YAAY,CAAC,oDAAoD;IACjE,cAAc;MACZ;IACF;IACA,kBAAkB,CAAC,sDAAsD;IACzE,cAAc;MACZ;IACF;IACA,gBAAgB;MACd;IACF;IACA,sBAAsB;MACpB;IACF;IACA,QAAQ,CAAC,uDAAuD;EAClE;EACA,cAAc;IACZ,eAAe;MACb;IACF;IACA,eAAe;MACb;IACF;IACA,uBAAuB;MACrB;IACF;IACA,gBAAgB;MACd;IACF;IACA,sBAAsB;MACpB;IACF;IACA,UAAU;MACR;MACA,CAAC;MACD,EAAE,mBAAmB,EAAE,UAAU,eAAe,EAAE;IACpD;IACA,aAAa;MACX;IACF;IACA,YAAY;MACV;IACF;IACA,mBAAmB;MACjB;IACF;IACA,iBAAiB,CAAC,uDAAuD;IACzE,UAAU,CAAC,2DAA2D;IACtE,oBAAoB;MAClB;IACF;IACA,4BAA4B;MAC1B;IACF;IACA,oBAAoB;MAClB;IACF;IACA,kBAAkB,CAAC,sCAAsC;IACzD,mBAAmB,CAAC,gDAAgD;IACpE,qBAAqB;MACnB;MACA,CAAC;MACD,EAAE,SAAS,CAAC,gBAAgB,oBAAoB,EAAE;IACpD;IACA,qBAAqB;MACnB;IACF;IACA,oBAAoB,CAAC,kDAAkD;IACvE,aAAa;MACX;IACF;IACA,oBAAoB;MAClB;IACF;IACA,aAAa,CAAC,iDAAiD;EACjE;EACA,cAAc;IACZ,qBAAqB;MACnB;IACF;IACA,+BAA+B;MAC7B;IACF;IACA,qBAAqB,CAAC,+CAA+C;IACrE,kCAAkC;MAChC;IACF;IACA,qBAAqB;MACnB;IACF;IACA,kCAAkC;MAChC;IACF;IACA,qBAAqB;MACnB;IACF;IACA,kBAAkB;MAChB;IACF;IACA,+BAA+B;MAC7B;IACF;IACA,gCAAgC;MAC9B;IACF;IACA,yBAAyB,CAAC,8CAA8C;IACxE,0BAA0B;MACxB;IACF;IACA,uCAAuC;MACrC;IACF;IACA,iCAAiC;MAC/B;IACF;IACA,2CAA2C;MACzC;IACF;IACA,qCAAqC;MACnC;IACF;IACA,2BAA2B;MACzB;IACF;IACA,wCAAwC;MACtC;IACF;IACA,qBAAqB;MACnB;IACF;IACA,+BAA+B;MAC7B;IACF;EACF;EACA,gBAAgB;IACd,sBAAsB,CAAC,uBAAuB;IAC9C,gBAAgB,CAAC,6BAA6B;EAChD;EACA,YAAY;IACV,4CAA4C;MAC1C;IACF;IACA,4BAA4B;MAC1B;IACF;IACA,iCAAiC;MAC/B;IACF;IACA,uCAAuC;MACrC;IACF;IACA,4BAA4B,CAAC,uBAAuB;IACpD,yBAAyB;MACvB;IACF;IACA,0BAA0B;MACxB;IACF;IACA,0CAA0C;MACxC;IACF;IACA,kCAAkC;MAChC;IACF;IACA,oCAAoC;MAClC;IACF;IACA,4BAA4B,CAAC,0CAA0C;IACvE,wBAAwB;MACtB;IACF;IACA,iBAAiB,CAAC,qDAAqD;IACvE,kBAAkB;MAChB;IACF;IACA,kCAAkC;MAChC;IACF;IACA,4BAA4B;MAC1B;IACF;IACA,2BAA2B;MACzB;IACF;IACA,sCAAsC;MACpC;IACF;IACA,yBAAyB,CAAC,uCAAuC;IACjE,iBAAiB,CAAC,+CAA+C;IACjE,cAAc,CAAC,kDAAkD;IACjE,kCAAkC;MAChC;IACF;IACA,kBAAkB;MAChB;IACF;IACA,eAAe;MACb;IACF;IACA,+BAA+B;MAC7B;IACF;IACA,mDAAmD;MACjD;IACF;IACA,0BAA0B,CAAC,sBAAsB;IACjD,oBAAoB;MAClB;MACA,CAAC;MACD,EAAE,mBAAmB,EAAE,QAAQ,MAAM,EAAE;IACzC;IACA,sCAAsC;MACpC;IACF;IACA,gBAAgB,CAAC,oCAAoC;IACrD,iBAAiB,CAAC,8CAA8C;IAChE,+CAA+C;MAC7C;IACF;IACA,iCAAiC,CAAC,8BAA8B;IAChE,+BAA+B;MAC7B;IACF;IACA,uCAAuC;MACrC;IACF;IACA,6BAA6B;MAC3B;IACF;IACA,+CAA+C;MAC7C;IACF;IACA,iCAAiC;MAC/B;IACF;IACA,kCAAkC;MAChC;IACF;IACA,8CAA8C;MAC5C;IACF;IACA,8BAA8B;MAC5B;IACF;IACA,2BAA2B,CAAC,8CAA8C;IAC1E,0BAA0B,CAAC,6CAA6C;IACxE,oBAAoB;MAClB;IACF;IACA,4BAA4B,CAAC,yCAAyC;EACxE;EACA,SAAS;IACP,yBAAyB;MACvB;IACF;IACA,yBAAyB;MACvB;IACF;IACA,qCAAqC;MACnC;IACF;IACA,qCAAqC;MACnC;IACF;IACA,+BAA+B,CAAC,iCAAiC;IACjE,uBAAuB,CAAC,kDAAkD;IAC1E,+BAA+B,CAAC,iCAAiC;IACjE,8BAA8B;MAC5B;IACF;IACA,kBAAkB,CAAC,uCAAuC;EAC5D;EACA,aAAa,EAAE,QAAQ,CAAC,0BAA0B,EAAE;EACpD,YAAY;IACV,4BAA4B;MAC1B;IACF;IACA,yBAAyB;MACvB;IACF;IACA,0BAA0B;MACxB;IACF;IACA,iBAAiB,CAAC,qDAAqD;IACvE,kBAAkB;MAChB;IACF;IACA,UAAU,CAAC,4DAA4D;IACvE,iBAAiB,CAAC,+CAA+C;IACjE,cAAc,CAAC,kDAAkD;IACjE,kBAAkB;MAChB;IACF;IACA,eAAe;MACb;IACF;IACA,yBAAyB;MACvB;IACF;IACA,kBAAkB,CAAC,mCAAmC;IACtD,mBAAmB,CAAC,6CAA6C;IACjE,gBAAgB,CAAC,oCAAoC;IACrD,iBAAiB,CAAC,8CAA8C;IAChE,+BAA+B;MAC7B;IACF;IACA,iCAAiC;MAC/B;IACF;IACA,8BAA8B;MAC5B;IACF;IACA,aAAa;MACX;IACF;EACF;EACA,iBAAiB;IACf,0BAA0B;MACxB;IACF;IACA,WAAW;MACT;IACF;IACA,YAAY,CAAC,iDAAiD;EAChE;EACA,QAAQ,EAAE,KAAK,CAAC,aAAa,EAAE;EAC/B,OAAO;IACL,gBAAgB,CAAC,2BAA2B;IAC5C,QAAQ,CAAC,aAAa;IACtB,eAAe,CAAC,gCAAgC;IAChD,QAAQ,CAAC,yBAAyB;IAClC,eAAe,CAAC,+CAA+C;IAC/D,MAAM,CAAC,6BAA6B;IACpC,KAAK,CAAC,sBAAsB;IAC5B,YAAY,CAAC,4CAA4C;IACzD,aAAa,CAAC,4BAA4B;IAC1C,MAAM,CAAC,YAAY;IACnB,cAAc,CAAC,+BAA+B;IAC9C,aAAa,CAAC,8BAA8B;IAC5C,aAAa,CAAC,6BAA6B;IAC3C,WAAW,CAAC,4BAA4B;IACxC,YAAY,CAAC,mBAAmB;IAChC,aAAa,CAAC,oBAAoB;IAClC,MAAM,CAAC,2BAA2B;IAClC,QAAQ,CAAC,8BAA8B;IACvC,QAAQ,CAAC,wBAAwB;IACjC,eAAe,CAAC,8CAA8C;EAChE;EACA,KAAK;IACH,YAAY,CAAC,sCAAsC;IACnD,cAAc,CAAC,wCAAwC;IACvD,WAAW,CAAC,qCAAqC;IACjD,WAAW,CAAC,qCAAqC;IACjD,YAAY,CAAC,sCAAsC;IACnD,WAAW,CAAC,6CAA6C;IACzD,SAAS,CAAC,gDAAgD;IAC1D,WAAW,CAAC,oDAAoD;IAChE,QAAQ,CAAC,yCAAyC;IAClD,QAAQ,CAAC,8CAA8C;IACvD,SAAS,CAAC,gDAAgD;IAC1D,kBAAkB,CAAC,mDAAmD;IACtE,WAAW,CAAC,4CAA4C;EAC1D;EACA,WAAW;IACT,iBAAiB,CAAC,0BAA0B;IAC5C,aAAa,CAAC,iCAAiC;EACjD;EACA,eAAe;IACb,kCAAkC;MAChC;IACF;IACA,mCAAmC;MACjC;IACF;IACA,+BAA+B;MAC7B;IACF;IACA,0BAA0B;MACxB;IACF;IACA,iCAAiC;MAC/B;IACF;IACA,kCAAkC;MAChC;IACF;EACF;EACA,cAAc;IACZ,qCAAqC,CAAC,8BAA8B;IACpE,uBAAuB,CAAC,oCAAoC;IAC5D,wBAAwB,CAAC,8CAA8C;IACvE,mCAAmC;MACjC;MACA,CAAC;MACD,EAAE,SAAS,CAAC,gBAAgB,qCAAqC,EAAE;IACrE;IACA,wCAAwC,CAAC,iCAAiC;IAC1E,0BAA0B,CAAC,uCAAuC;IAClE,2BAA2B;MACzB;IACF;IACA,sCAAsC;MACpC;MACA,CAAC;MACD,EAAE,SAAS,CAAC,gBAAgB,wCAAwC,EAAE;IACxE;IACA,qCAAqC,CAAC,8BAA8B;IACpE,uBAAuB,CAAC,oCAAoC;IAC5D,wBAAwB,CAAC,8CAA8C;IACvE,mCAAmC;MACjC;MACA,CAAC;MACD,EAAE,SAAS,CAAC,gBAAgB,qCAAqC,EAAE;IACrE;EACF;EACA,QAAQ;IACN,cAAc;MACZ;IACF;IACA,WAAW,CAAC,yDAAyD;IACrE,aAAa;MACX;IACF;IACA,wBAAwB,CAAC,gDAAgD;IACzE,+BAA+B;MAC7B;IACF;IACA,QAAQ,CAAC,mCAAmC;IAC5C,eAAe;MACb;IACF;IACA,aAAa,CAAC,mCAAmC;IACjD,iBAAiB,CAAC,uCAAuC;IACzD,eAAe;MACb;IACF;IACA,aAAa,CAAC,4CAA4C;IAC1D,iBAAiB;MACf;IACF;IACA,KAAK,CAAC,iDAAiD;IACvD,YAAY,CAAC,wDAAwD;IACrE,UAAU,CAAC,oDAAoD;IAC/D,UAAU,CAAC,yCAAyC;IACpD,cAAc,CAAC,yDAAyD;IACxE,MAAM,CAAC,aAAa;IACpB,eAAe,CAAC,qCAAqC;IACrD,cAAc,CAAC,0DAA0D;IACzE,qBAAqB,CAAC,2CAA2C;IACjE,YAAY,CAAC,wDAAwD;IACrE,mBAAmB,CAAC,yCAAyC;IAC7D,uBAAuB;MACrB;IACF;IACA,0BAA0B,CAAC,kBAAkB;IAC7C,YAAY,CAAC,wBAAwB;IACrC,aAAa,CAAC,kCAAkC;IAChD,wBAAwB;MACtB;IACF;IACA,mBAAmB,CAAC,kCAAkC;IACtD,mBAAmB;MACjB;IACF;IACA,gBAAgB,CAAC,sCAAsC;IACvD,eAAe;MACb;IACF;IACA,MAAM,CAAC,sDAAsD;IAC7D,iBAAiB;MACf;IACF;IACA,iBAAiB;MACf;IACF;IACA,aAAa;MACX;IACF;IACA,gBAAgB;MACd;IACF;IACA,sBAAsB;MACpB;IACF;IACA,WAAW,CAAC,wDAAwD;IACpE,QAAQ,CAAC,yDAAyD;IAClE,QAAQ,CAAC,mDAAmD;IAC5D,eAAe,CAAC,0DAA0D;IAC1E,aAAa,CAAC,2CAA2C;IACzD,iBAAiB;MACf;IACF;EACF;EACA,UAAU;IACR,KAAK,CAAC,yBAAyB;IAC/B,oBAAoB,CAAC,eAAe;IACpC,YAAY,CAAC,mCAAmC;EAClD;EACA,UAAU;IACR,QAAQ,CAAC,gBAAgB;IACzB,WAAW;MACT;MACA,EAAE,SAAS,EAAE,gBAAgB,4BAA4B,EAAE;IAC7D;EACF;EACA,MAAM;IACJ,KAAK,CAAC,WAAW;IACjB,gBAAgB,CAAC,eAAe;IAChC,YAAY,CAAC,cAAc;IAC3B,QAAQ,CAAC,UAAU;IACnB,MAAM,CAAC,OAAO;EAChB;EACA,YAAY;IACV,mCAAmC;MACjC;IACF;IACA,qBAAqB;MACnB;IACF;IACA,uBAAuB;MACrB;IACF;IACA,gCAAgC;MAC9B;IACF;IACA,+BAA+B,CAAC,qCAAqC;IACrE,iBAAiB,CAAC,2CAA2C;IAC7D,0BAA0B,CAAC,sBAAsB;IACjD,YAAY,CAAC,4BAA4B;IACzC,+BAA+B;MAC7B;IACF;IACA,iBAAiB,CAAC,wDAAwD;IAC1E,kBAAkB;MAChB;MACA,CAAC;MACD,EAAE,SAAS,CAAC,cAAc,+BAA+B,EAAE;IAC7D;IACA,2BAA2B,CAAC,uBAAuB;IACnD,aAAa,CAAC,6BAA6B;IAC3C,gCAAgC;MAC9B;IACF;IACA,kBAAkB;MAChB;IACF;EACF;EACA,MAAM;IACJ,gCAAgC;MAC9B;IACF;IACA,mCAAmC;MACjC;IACF;EACF;EACA,MAAM;IACJ,wBAAwB;MACtB;MACA,CAAC;MACD;QACE,YACE;MACJ;IACF;IACA,qBAAqB;MACnB;IACF;IACA,qBAAqB;MACnB;IACF;IACA,WAAW,CAAC,mCAAmC;IAC/C,kBAAkB,CAAC,gDAAgD;IACnE,kBAAkB,CAAC,mCAAmC;IACtD,wBAAwB,CAAC,oCAAoC;IAC7D,8BAA8B,CAAC,2CAA2C;IAC1E,oCAAoC;MAClC;IACF;IACA,kBAAkB,CAAC,8BAA8B;IACjD,iBAAiB,CAAC,8BAA8B;IAChD,gCAAgC,CAAC,qCAAqC;IACtE,8CAA8C;MAC5C;IACF;IACA,8BAA8B;MAC5B;IACF;IACA,eAAe,CAAC,wBAAwB;IACxC,QAAQ,CAAC,oBAAoB;IAC7B,iBAAiB,CAAC,gDAAgD;IAClE,eAAe,CAAC,oCAAoC;IACpD,6CAA6C;MAC3C;MACA,CAAC;MACD;QACE,YACE;MACJ;IACF;IACA,KAAK,CAAC,iBAAiB;IACvB,wBAAwB,CAAC,mCAAmC;IAC5D,mBAAmB;MACjB;IACF;IACA,mCAAmC,CAAC,kCAAkC;IACtE,sBAAsB,CAAC,wCAAwC;IAC/D,YAAY,CAAC,8CAA8C;IAC3D,sBAAsB,CAAC,+CAA+C;IACtE,sBAAsB;MACpB;IACF;IACA,YAAY,CAAC,iCAAiC;IAC9C,wBAAwB,CAAC,wCAAwC;IACjE,oBAAoB;MAClB;IACF;IACA,MAAM,CAAC,oBAAoB;IAC3B,sBAAsB,CAAC,+BAA+B;IACtD,kBAAkB,CAAC,+CAA+C;IAClE,kBAAkB,CAAC,wBAAwB;IAC3C,oCAAoC,CAAC,mCAAmC;IACxE,uBAAuB,CAAC,oCAAoC;IAC5D,0BAA0B,CAAC,gBAAgB;IAC3C,aAAa,CAAC,4BAA4B;IAC1C,qBAAqB,CAAC,mDAAmD;IACzE,gBAAgB,CAAC,6BAA6B;IAC9C,aAAa,CAAC,yBAAyB;IACvC,qCAAqC,CAAC,4BAA4B;IAClE,kBAAkB,CAAC,oDAAoD;IACvE,kBAAkB,CAAC,oDAAoD;IACvE,cAAc,CAAC,oCAAoC;IACnD,wCAAwC;MACtC;IACF;IACA,0BAA0B,CAAC,uCAAuC;IAClE,0BAA0B;MACxB;IACF;IACA,iCAAiC;MAC/B;IACF;IACA,sBAAsB,CAAC,gDAAgD;IACvE,eAAe,CAAC,wCAAwC;IACxD,wBAAwB,CAAC,6BAA6B;IACtD,mBAAmB,CAAC,gCAAgC;IACpD,0BAA0B;MACxB;MACA,CAAC;MACD;QACE,YACE;MACJ;IACF;IACA,uBAAuB,CAAC,4CAA4C;IACpE,cAAc,CAAC,uBAAuB;IACtC,aAAa,CAAC,wCAAwC;IACtD,0BAA0B;MACxB;IACF;IACA,sBAAsB;MACpB;IACF;IACA,cAAc,CAAC,uCAAuC;IACtD,yBAAyB,CAAC,2CAA2C;IACrE,2BAA2B;MACzB;IACF;IACA,4CAA4C;MAC1C;IACF;IACA,2BAA2B;MACzB;MACA,CAAC;MACD;QACE,YACE;MACJ;IACF;IACA,uBAAuB;MACrB;IACF;IACA,8BAA8B;MAC5B;IACF;IACA,uBAAuB;MACrB;IACF;IACA,uBAAuB;MACrB;IACF;IACA,mBAAmB;MACjB;IACF;IACA,mBAAmB;MACjB;IACF;IACA,sBAAsB,CAAC,wCAAwC;IAC/D,yCAAyC;MACvC;IACF;IACA,aAAa,CAAC,sCAAsC;IACpD,QAAQ,CAAC,mBAAmB;IAC5B,iBAAiB,CAAC,6CAA6C;IAC/D,sCAAsC;MACpC;IACF;IACA,iBAAiB,CAAC,kDAAkD;IACpE,mBAAmB,CAAC,yCAAyC;IAC7D,eAAe,CAAC,mCAAmC;IACnD,2BAA2B,CAAC,0CAA0C;EACxE;EACA,UAAU;IACR,mCAAmC;MACjC;IACF;IACA,qBAAqB;MACnB;IACF;IACA,sBAAsB;MACpB;IACF;IACA,0CAA0C;MACxC;IACF;IACA,4BAA4B;MAC1B;IACF;IACA,6BAA6B;MAC3B;IACF;IACA,8CAA8C;MAC5C;MACA,CAAC;MACD,EAAE,SAAS,CAAC,YAAY,2CAA2C,EAAE;IACvE;IACA,6DAA6D;MAC3D;MACA,CAAC;MACD;QACE,SAAS;UACP;UACA;QACF;MACF;IACF;IACA,yDAAyD;MACvD;IACF;IACA,2CAA2C;MACzC;IACF;IACA,4CAA4C;MAC1C;IACF;IACA,gCAAgC;MAC9B;IACF;IACA,2BAA2B;MACzB;IACF;IACA,mBAAmB;MACjB;IACF;IACA,uCAAuC;MACrC;IACF;IACA,kCAAkC;MAChC;IACF;IACA,0BAA0B;MACxB;IACF;IACA,4DAA4D;MAC1D;IACF;IACA,uDAAuD;MACrD;IACF;IACA,+CAA+C;MAC7C;IACF;IACA,kCAAkC,CAAC,oBAAoB;IACvD,6BAA6B,CAAC,0BAA0B;IACxD,qBAAqB,CAAC,gCAAgC;IACtD,oCAAoC;MAClC;IACF;IACA,sBAAsB;MACpB;IACF;IACA,uBAAuB;MACrB;IACF;IACA,2CAA2C;MACzC;IACF;IACA,6BAA6B;MAC3B;IACF;IACA,8BAA8B;MAC5B;IACF;EACF;EACA,mBAAmB;IACjB,0BAA0B,CAAC,qCAAqC;IAChE,0BAA0B;MACxB;IACF;IACA,uBAAuB,CAAC,kDAAkD;IAC1E,iBAAiB,CAAC,+CAA+C;IACjE,0BAA0B,CAAC,oCAAoC;IAC/D,0BAA0B;MACxB;IACF;EACF;EACA,OAAO;IACL,eAAe,CAAC,qDAAqD;IACrE,QAAQ,CAAC,kCAAkC;IAC3C,6BAA6B;MAC3B;IACF;IACA,cAAc,CAAC,wDAAwD;IACvE,qBAAqB;MACnB;IACF;IACA,qBAAqB;MACnB;IACF;IACA,qBAAqB;MACnB;IACF;IACA,eAAe;MACb;IACF;IACA,KAAK,CAAC,+CAA+C;IACrD,WAAW;MACT;IACF;IACA,kBAAkB,CAAC,uDAAuD;IAC1E,MAAM,CAAC,iCAAiC;IACxC,uBAAuB;MACrB;IACF;IACA,aAAa,CAAC,uDAAuD;IACrE,WAAW,CAAC,qDAAqD;IACjE,wBAAwB;MACtB;IACF;IACA,oBAAoB;MAClB;IACF;IACA,2BAA2B,CAAC,0CAA0C;IACtE,aAAa,CAAC,uDAAuD;IACrE,OAAO,CAAC,qDAAqD;IAC7D,0BAA0B;MACxB;IACF;IACA,kBAAkB;MAChB;IACF;IACA,cAAc;MACZ;IACF;IACA,QAAQ,CAAC,iDAAiD;IAC1D,cAAc;MACZ;IACF;IACA,cAAc;MACZ;IACF;IACA,qBAAqB;MACnB;IACF;EACF;EACA,WAAW,EAAE,KAAK,CAAC,iBAAiB,EAAE;EACtC,WAAW;IACT,wBAAwB;MACtB;IACF;IACA,gBAAgB;MACd;IACF;IACA,uBAAuB;MACrB;IACF;IACA,mCAAmC;MACjC;IACF;IACA,kBAAkB;MAChB;IACF;IACA,qCAAqC;MACnC;IACF;IACA,8BAA8B;MAC5B;IACF;IACA,wBAAwB;MACtB;IACF;IACA,gBAAgB;MACd;IACF;IACA,uBAAuB;MACrB;IACF;IACA,6BAA6B;MAC3B;IACF;IACA,kBAAkB;MAChB;IACF;IACA,yBAAyB;MACvB;IACF;IACA,gCAAgC;MAC9B;IACF;IACA,sBAAsB;MACpB;IACF;IACA,cAAc,CAAC,2DAA2D;IAC1E,qBAAqB;MACnB;IACF;IACA,iCAAiC;MAC/B;IACF;IACA,gBAAgB;MACd;IACF;IACA,mCAAmC;MACjC;IACF;IACA,4BAA4B;MAC1B;IACF;EACF;EACA,OAAO;IACL,kBAAkB;MAChB;MACA,CAAC;MACD,EAAE,SAAS,CAAC,SAAS,sCAAsC,EAAE;IAC/D;IACA,sCAAsC;MACpC;IACF;IACA,0BAA0B;MACxB;MACA,CAAC;MACD,EAAE,WAAW,OAAO;IACtB;IACA,iBAAiB,CAAC,oDAAoD;IACtE,wBAAwB;MACtB;MACA,CAAC;MACD,EAAE,WAAW,WAAW;IAC1B;IACA,2BAA2B;MACzB;MACA,CAAC;MACD,EAAE,WAAW,QAAQ;IACvB;IACA,2BAA2B;MACzB;MACA,CAAC;MACD,EAAE,WAAW,QAAQ;IACvB;IACA,uBAAuB;MACrB;IACF;IACA,6BAA6B;MAC3B;IACF;IACA,mBAAmB,CAAC,oDAAoD;IACxE,oCAAoC;MAClC;IACF;IACA,0BAA0B;MACxB;IACF;IACA,kBAAkB,CAAC,6CAA6C;IAChE,gBAAgB,CAAC,mDAAmD;IACpE,4BAA4B;MAC1B;IACF;IACA,mBAAmB,CAAC,yCAAyC;IAC7D,gBAAgB,CAAC,sCAAsC;IACvD,qBAAqB;MACnB;IACF;IACA,iCAAiC;MAC/B;IACF;IACA,oBAAoB,CAAC,2CAA2C;IAChE,iBAAiB,CAAC,iCAAiC;IACnD,kBAAkB,CAAC,wCAAwC;IAC3D,8BAA8B;MAC5B;IACF;IACA,gCAAgC;MAC9B;IACF;IACA,wBAAwB;MACtB;IACF;IACA,qBAAqB,CAAC,uCAAuC;IAC7D,4BAA4B,CAAC,kBAAkB;IAC/C,YAAY,CAAC,kCAAkC;IAC/C,aAAa,CAAC,wBAAwB;IACtC,sCAAsC;MACpC;IACF;IACA,2BAA2B;MACzB;IACF;IACA,4BAA4B,CAAC,2CAA2C;IACxE,kBAAkB,CAAC,2BAA2B;IAC9C,uBAAuB,CAAC,8CAA8C;IACtE,iBAAiB,CAAC,kCAAkC;IACpD,eAAe,CAAC,qCAAqC;IACrD,mBAAmB,CAAC,qCAAqC;IACzD,qBAAqB;MACnB;IACF;IACA,eAAe,CAAC,kCAAkC;IAClD,mBAAmB;MACjB;MACA,CAAC;MACD,EAAE,SAAS,CAAC,SAAS,uCAAuC,EAAE;IAChE;IACA,uCAAuC;MACrC;IACF;IACA,QAAQ,CAAC,8BAA8B;IACvC,0BAA0B;MACxB;IACF;IACA,6BAA6B;MAC3B;IACF;IACA,qBAAqB;MACnB;IACF;IACA,gBAAgB,CAAC,sDAAsD;IACvE,wBAAwB;MACtB;IACF;IACA,qBAAqB,CAAC,oDAAoD;IAC1E,iCAAiC;MAC/B;IACF;IACA,iBAAiB,CAAC,4CAA4C;IAC9D,kBAAkB;MAChB;IACF;IACA,8BAA8B;MAC5B;IACF;IACA,YAAY,CAAC,8CAA8C;IAC3D,kBAAkB;MAChB;IACF;IACA,kBAAkB,CAAC,0CAA0C;IAC7D,iBAAiB,CAAC,oCAAoC;IACtD,mCAAmC;MACjC;IACF;IACA,eAAe,CAAC,oDAAoD;IACpE,oBAAoB;MAClB;IACF;IACA,mBAAmB,CAAC,oDAAoD;IACxE,eAAe,CAAC,8CAA8C;IAC9D,+BAA+B;MAC7B;IACF;IACA,iCAAiC;MAC/B;IACF;IACA,sCAAsC;MACpC;IACF;IACA,4BAA4B;MAC1B;IACF;IACA,iBAAiB;MACf;MACA,CAAC;MACD,EAAE,SAAS,CAAC,SAAS,wBAAwB,EAAE;IACjD;IACA,wBAAwB,CAAC,yCAAyC;IAClE,wBAAwB,CAAC,yCAAyC;IAClE,8BAA8B;MAC5B;IACF;IACA,qCAAqC;MACnC;IACF;IACA,2BAA2B;MACzB;IACF;IACA,sBAAsB;MACpB;IACF;IACA,KAAK,CAAC,2BAA2B;IACjC,uBAAuB;MACrB;IACF;IACA,0BAA0B;MACxB;IACF;IACA,iCAAiC;MAC/B;IACF;IACA,oBAAoB,CAAC,wCAAwC;IAC7D,2BAA2B;MACzB;IACF;IACA,cAAc,CAAC,kCAAkC;IACjD,oCAAoC;MAClC;IACF;IACA,aAAa,CAAC,mDAAmD;IACjE,WAAW,CAAC,6CAA6C;IACzD,qBAAqB;MACnB;IACF;IACA,gBAAgB,CAAC,mDAAmD;IACpE,WAAW,CAAC,0CAA0C;IACtD,uBAAuB,CAAC,gDAAgD;IACxE,gCAAgC;MAC9B;IACF;IACA,yBAAyB,CAAC,gDAAgD;IAC1E,WAAW,CAAC,yCAAyC;IACrD,wBAAwB,CAAC,iDAAiD;IAC1E,kBAAkB,CAAC,iDAAiD;IACpE,8BAA8B;MAC5B;IACF;IACA,4BAA4B,CAAC,6CAA6C;IAC1E,YAAY,CAAC,2CAA2C;IACxD,sBAAsB,CAAC,8CAA8C;IACrE,mCAAmC;MACjC;IACF;IACA,2BAA2B,CAAC,6CAA6C;IACzE,cAAc,CAAC,yCAAyC;IACxD,eAAe,CAAC,uDAAuD;IACvE,2BAA2B;MACzB;IACF;IACA,qBAAqB;MACnB;IACF;IACA,gBAAgB;MACd;IACF;IACA,qBAAqB,CAAC,+CAA+C;IACrE,kBAAkB,CAAC,2CAA2C;IAC9D,iBAAiB,CAAC,sDAAsD;IACxE,kBAAkB,CAAC,sCAAsC;IACzD,eAAe,CAAC,uCAAuC;IACvD,gBAAgB,CAAC,0BAA0B;IAC3C,UAAU,CAAC,iCAAiC;IAC5C,eAAe,CAAC,mDAAmD;IACnE,oBAAoB;MAClB;IACF;IACA,qBAAqB,CAAC,wCAAwC;IAC9D,uBAAuB,CAAC,+CAA+C;IACvE,gCAAgC;MAC9B;IACF;IACA,mBAAmB,CAAC,4CAA4C;IAChE,WAAW,CAAC,kCAAkC;IAC9C,sBAAsB,CAAC,wCAAwC;IAC/D,YAAY,CAAC,iDAAiD;IAC9D,iBAAiB,CAAC,sDAAsD;IACxE,iBAAiB,CAAC,+CAA+C;IACjE,kBAAkB;MAChB;IACF;IACA,mBAAmB,CAAC,gDAAgD;IACpE,gBAAgB,CAAC,iDAAiD;IAClE,uBAAuB;MACrB;IACF;IACA,uBAAuB;MACrB;IACF;IACA,iBAAiB,CAAC,oCAAoC;IACtD,2BAA2B;MACzB;IACF;IACA,qCAAqC;MACnC;IACF;IACA,aAAa,CAAC,iDAAiD;IAC/D,iBAAiB,CAAC,qDAAqD;IACvE,qCAAqC;MACnC;IACF;IACA,UAAU,CAAC,yCAAyC;IACpD,YAAY,CAAC,2CAA2C;IACxD,yBAAyB;MACvB;IACF;IACA,oBAAoB;MAClB;IACF;IACA,gBAAgB,CAAC,oCAAoC;IACrD,kBAAkB;MAChB;IACF;IACA,eAAe,CAAC,qCAAqC;IACrD,cAAc,CAAC,oCAAoC;IACnD,2BAA2B;MACzB;IACF;IACA,mBAAmB,CAAC,yCAAyC;IAC7D,uBAAuB;MACrB;IACF;IACA,2BAA2B,CAAC,oCAAoC;IAChE,0BAA0B;MACxB;IACF;IACA,aAAa,CAAC,mCAAmC;IACjD,kBAAkB,CAAC,wCAAwC;IAC3D,sCAAsC;MACpC;IACF;IACA,gBAAgB,CAAC,gCAAgC;IACjD,8BAA8B;MAC5B;IACF;IACA,wBAAwB;MACtB;IACF;IACA,iBAAiB,CAAC,uCAAuC;IACzD,0BAA0B,CAAC,iBAAiB;IAC5C,YAAY,CAAC,uBAAuB;IACpC,aAAa,CAAC,6BAA6B;IAC3C,WAAW,CAAC,iCAAiC;IAC7C,iBAAiB,CAAC,uCAAuC;IACzD,qCAAqC,CAAC,kCAAkC;IACxE,eAAe,CAAC,qCAAqC;IACrD,iBAAiB,CAAC,wCAAwC;IAC1D,YAAY,CAAC,mBAAmB;IAChC,sCAAsC;MACpC;IACF;IACA,mBAAmB;MACjB;IACF;IACA,cAAc,CAAC,oCAAoC;IACnD,UAAU,CAAC,gCAAgC;IAC3C,WAAW,CAAC,iCAAiC;IAC7C,uBAAuB;MACrB;IACF;IACA,cAAc,CAAC,iCAAiC;IAChD,OAAO,CAAC,mCAAmC;IAC3C,eAAe,CAAC,2CAA2C;IAC3D,aAAa,CAAC,kDAAkD;IAChE,0BAA0B;MACxB;IACF;IACA,6BAA6B;MAC3B;MACA,CAAC;MACD,EAAE,WAAW,OAAO;IACtB;IACA,oBAAoB;MAClB;IACF;IACA,2BAA2B;MACzB;MACA,CAAC;MACD,EAAE,WAAW,WAAW;IAC1B;IACA,6BAA6B;MAC3B;IACF;IACA,8BAA8B;MAC5B;MACA,CAAC;MACD,EAAE,WAAW,QAAQ;IACvB;IACA,8BAA8B;MAC5B;MACA,CAAC;MACD,EAAE,WAAW,QAAQ;IACvB;IACA,cAAc,CAAC,qDAAqD;IACpE,kBAAkB,CAAC,kCAAkC;IACrD,mBAAmB,CAAC,yCAAyC;IAC7D,0BAA0B;MACxB;IACF;IACA,0BAA0B;MACxB;MACA,CAAC;MACD,EAAE,WAAW,OAAO;IACtB;IACA,wBAAwB;MACtB;MACA,CAAC;MACD,EAAE,WAAW,WAAW;IAC1B;IACA,2BAA2B;MACzB;MACA,CAAC;MACD,EAAE,WAAW,QAAQ;IACvB;IACA,2BAA2B;MACzB;MACA,CAAC;MACD,EAAE,WAAW,QAAQ;IACvB;IACA,iBAAiB,CAAC,kDAAkD;IACpE,UAAU,CAAC,qCAAqC;IAChD,QAAQ,CAAC,6BAA6B;IACtC,wBAAwB;MACtB;IACF;IACA,qBAAqB,CAAC,mDAAmD;IACzE,8BAA8B;MAC5B;IACF;IACA,iCAAiC,CAAC,iCAAiC;IACnE,kBAAkB;MAChB;IACF;IACA,kBAAkB,CAAC,uCAAuC;IAC1D,mCAAmC;MACjC;IACF;IACA,eAAe,CAAC,mDAAmD;IACnE,oBAAoB;MAClB;IACF;IACA,mBAAmB,CAAC,iDAAiD;IACrE,4BAA4B;MAC1B;MACA,CAAC;MACD,EAAE,SAAS,CAAC,SAAS,6BAA6B,EAAE;IACtD;IACA,6BAA6B;MAC3B;IACF;IACA,eAAe,CAAC,6CAA6C;IAC7D,4BAA4B;MAC1B;IACF;IACA,oBAAoB;MAClB;MACA,EAAE,SAAS,6BAA6B;IAC1C;EACF;EACA,QAAQ;IACN,MAAM,CAAC,kBAAkB;IACzB,SAAS,CAAC,qBAAqB;IAC/B,uBAAuB;MACrB;MACA,CAAC;MACD;QACE,YACE;MACJ;IACF;IACA,QAAQ,CAAC,oBAAoB;IAC7B,OAAO,CAAC,0BAA0B;IAClC,QAAQ,CAAC,oBAAoB;IAC7B,OAAO,CAAC,mBAAmB;EAC7B;EACA,gBAAgB;IACd,4BAA4B;MAC1B;IACF;IACA,UAAU;MACR;IACF;IACA,gBAAgB,CAAC,wDAAwD;IACzE,yBAAyB;MACvB;IACF;IACA,kBAAkB,CAAC,wCAAwC;IAC3D,mBAAmB,CAAC,kDAAkD;IACtE,uBAAuB;MACrB;IACF;IACA,aAAa;MACX;IACF;EACF;EACA,oBAAoB;IAClB,YAAY;MACV;IACF;IACA,kCAAkC;MAChC;IACF;IACA,0BAA0B;MACxB;IACF;IACA,oCAAoC;MAClC;IACF;IACA,mBAAmB,CAAC,2BAA2B;IAC/C,uBAAuB;MACrB;IACF;IACA,sBAAsB,CAAC,iBAAiB;IACxC,6BAA6B,CAAC,qCAAqC;IACnE,0BAA0B,CAAC,+CAA+C;IAC1E,0BAA0B;MACxB;IACF;EACF;EACA,OAAO;IACL,mCAAmC;MACjC;IACF;IACA,iCAAiC;MAC/B;IACF;IACA,8BAA8B;MAC5B;IACF;IACA,QAAQ,CAAC,wBAAwB;IACjC,8BAA8B;MAC5B;IACF;IACA,uBAAuB,CAAC,gDAAgD;IACxE,8BAA8B;MAC5B;IACF;IACA,uBAAuB;MACrB;IACF;IACA,aAAa,CAAC,sCAAsC;IACpD,WAAW,CAAC,mCAAmC;IAC/C,2BAA2B;MACzB;IACF;IACA,oBAAoB;MAClB;IACF;IACA,2BAA2B;MACzB;IACF;IACA,MAAM,CAAC,uBAAuB;IAC9B,gBAAgB,CAAC,yCAAyC;IAC1D,6BAA6B;MAC3B;IACF;IACA,sBAAsB,CAAC,+CAA+C;IACtE,0BAA0B,CAAC,iBAAiB;IAC5C,kBAAkB,CAAC,2CAA2C;IAC9D,6BAA6B;MAC3B;IACF;IACA,gBAAgB,CAAC,yCAAyC;IAC1D,8BAA8B;MAC5B;IACF;IACA,iBAAiB;MACf;IACF;IACA,8BAA8B;MAC5B;IACF;IACA,uBAAuB;MACrB;IACF;IACA,aAAa,CAAC,qCAAqC;EACrD;EACA,OAAO;IACL,0BAA0B;MACxB;MACA,CAAC;MACD,EAAE,SAAS,CAAC,SAAS,8BAA8B,EAAE;IACvD;IACA,8BAA8B,CAAC,mBAAmB;IAClD,sCAAsC,CAAC,4BAA4B;IACnE,OAAO,CAAC,6BAA6B;IACrC,cAAc,CAAC,6BAA6B;IAC5C,uBAAuB,CAAC,+CAA+C;IACvE,sCAAsC,CAAC,gCAAgC;IACvE,8BAA8B;MAC5B;MACA,CAAC;MACD,EAAE,SAAS,CAAC,SAAS,kCAAkC,EAAE;IAC3D;IACA,kCAAkC,CAAC,qBAAqB;IACxD,oCAAoC;MAClC;MACA,CAAC;MACD,EAAE,SAAS,CAAC,SAAS,wCAAwC,EAAE;IACjE;IACA,wCAAwC,CAAC,iBAAiB;IAC1D,yCAAyC,CAAC,6BAA6B;IACvE,6BAA6B;MAC3B;MACA,CAAC;MACD,EAAE,SAAS,CAAC,SAAS,iCAAiC,EAAE;IAC1D;IACA,iCAAiC,CAAC,qBAAqB;IACvD,8BAA8B;MAC5B;MACA,CAAC;MACD,EAAE,SAAS,CAAC,SAAS,kCAAkC,EAAE;IAC3D;IACA,kCAAkC,CAAC,oCAAoC;IACvE,oCAAoC;MAClC;MACA,CAAC;MACD,EAAE,SAAS,CAAC,SAAS,wCAAwC,EAAE;IACjE;IACA,wCAAwC,CAAC,4BAA4B;IACrE,yCAAyC,CAAC,8BAA8B;IACxE,yCAAyC;MACvC;IACF;IACA,QAAQ,CAAC,gCAAgC;IACzC,kBAAkB,CAAC,WAAW;IAC9B,SAAS,CAAC,wBAAwB;IAClC,eAAe,CAAC,uBAAuB;IACvC,mBAAmB,CAAC,iCAAiC;IACrD,2BAA2B;MACzB;MACA,CAAC;MACD,EAAE,SAAS,CAAC,SAAS,+BAA+B,EAAE;IACxD;IACA,+BAA+B,CAAC,iCAAiC;IACjE,iCAAiC;MAC/B;MACA,CAAC;MACD,EAAE,SAAS,CAAC,SAAS,qCAAqC,EAAE;IAC9D;IACA,qCAAqC,CAAC,yBAAyB;IAC/D,sCAAsC;MACpC;IACF;IACA,MAAM,CAAC,YAAY;IACnB,kBAAkB,CAAC,qDAAqD;IACxE,4BAA4B;MAC1B;MACA,CAAC;MACD,EAAE,SAAS,CAAC,SAAS,gCAAgC,EAAE;IACzD;IACA,gCAAgC,CAAC,kBAAkB;IACnD,4BAA4B;MAC1B;MACA,CAAC;MACD,EAAE,SAAS,CAAC,SAAS,gCAAgC,EAAE;IACzD;IACA,gCAAgC,CAAC,kBAAkB;IACnD,6BAA6B;MAC3B;MACA,CAAC;MACD,EAAE,SAAS,CAAC,SAAS,iCAAiC,EAAE;IAC1D;IACA,iCAAiC,CAAC,qBAAqB;IACvD,mCAAmC,CAAC,qBAAqB;IACzD,sBAAsB,CAAC,iCAAiC;IACxD,sBAAsB,CAAC,iCAAiC;IACxD,6BAA6B;MAC3B;MACA,CAAC;MACD,EAAE,SAAS,CAAC,SAAS,iCAAiC,EAAE;IAC1D;IACA,iCAAiC,CAAC,oBAAoB;IACtD,oBAAoB,CAAC,gCAAgC;IACrD,kCAAkC;MAChC;MACA,CAAC;MACD,EAAE,SAAS,CAAC,SAAS,sCAAsC,EAAE;IAC/D;IACA,sCAAsC,CAAC,yBAAyB;IAChE,uBAAuB,CAAC,4BAA4B;IACpD,mCAAmC;MACjC;MACA,CAAC;MACD,EAAE,SAAS,CAAC,SAAS,uCAAuC,EAAE;IAChE;IACA,uCAAuC,CAAC,gBAAgB;IACxD,wCAAwC,CAAC,2BAA2B;IACpE,2BAA2B,CAAC,uCAAuC;IACnE,wCAAwC,CAAC,4BAA4B;IACrE,2BAA2B,CAAC,wCAAwC;IACpE,2CAA2C;MACzC;MACA,CAAC;MACD,EAAE,SAAS,CAAC,SAAS,+CAA+C,EAAE;IACxE;IACA,+CAA+C;MAC7C;IACF;IACA,SAAS,CAAC,gCAAgC;IAC1C,UAAU,CAAC,mCAAmC;IAC9C,qBAAqB,CAAC,aAAa;EACrC;AACF;AAEA,IAAO,oBAAQ;;;AC5iEf,IAAM,qBAAqB,oBAAI,IAAI;AACnC,WAAW,CAAC,OAAO,SAAS,KAAK,OAAO,QAAQ,iBAAS,GAAG;AAC1D,aAAW,CAAC,YAAYC,SAAQ,KAAK,OAAO,QAAQ,SAAS,GAAG;AAC9D,UAAM,CAAC,OAAO,UAAU,WAAW,IAAIA;AACvC,UAAM,CAAC,QAAQ,GAAG,IAAI,MAAM,MAAM,GAAG;AACrC,UAAM,mBAAmB,OAAO;MAC9B;QACE;QACA;MACF;MACA;IACF;AAEA,QAAI,CAAC,mBAAmB,IAAI,KAAK,GAAG;AAClC,yBAAmB,IAAI,OAAO,oBAAI,IAAI,CAAC;IACzC;AAEA,uBAAmB,IAAI,KAAK,EAAE,IAAI,YAAY;MAC5C;MACA;MACA;MACA;IACF,CAAC;EACH;AACF;AAQA,IAAM,UAAU;EACd,IAAI,EAAE,MAAM,GAAgB,YAAoB;AAC9C,WAAO,mBAAmB,IAAI,KAAK,EAAE,IAAI,UAAU;EACrD;EACA,yBAAyB,QAAqB,YAAoB;AAChE,WAAO;MACL,OAAO,KAAK,IAAI,QAAQ,UAAU;;MAClC,cAAc;MACd,UAAU;MACV,YAAY;IACd;EACF;EACA,eACE,QACA,YACA,YACA;AACA,WAAO,eAAe,OAAO,OAAO,YAAY,UAAU;AAC1D,WAAO;EACT;EACA,eAAe,QAAqB,YAAoB;AACtD,WAAO,OAAO,MAAM,UAAU;AAC9B,WAAO;EACT;EACA,QAAQ,EAAE,MAAM,GAAgB;AAC9B,WAAO,CAAC,GAAG,mBAAmB,IAAI,KAAK,EAAE,KAAK,CAAC;EACjD;EACA,IAAI,QAAqB,YAAoB,OAAY;AACvD,WAAQ,OAAO,MAAM,UAAU,IAAI;EACrC;EACA,IAAI,EAAE,SAAS,OAAO,MAAM,GAAgB,YAAoB;AAC9D,QAAI,MAAM,UAAU,GAAG;AACrB,aAAO,MAAM,UAAU;IACzB;AAEA,UAAM,SAAS,mBAAmB,IAAI,KAAK,EAAE,IAAI,UAAU;AAC3D,QAAI,CAAC,QAAQ;AACX,aAAO;IACT;AAEA,UAAM,EAAE,kBAAkB,YAAY,IAAI;AAE1C,QAAI,aAAa;AACf,YAAM,UAAU,IAAI;QAClB;QACA;QACA;QACA;QACA;MACF;IACF,OAAO;AACL,YAAM,UAAU,IAAI,QAAQ,QAAQ,SAAS,gBAAgB;IAC/D;AAEA,WAAO,MAAM,UAAU;EACzB;AACF;AAEO,SAAS,mBAAmB,SAAuC;AACxE,QAAM,aAAa,CAAC;AAEpB,aAAW,SAAS,mBAAmB,KAAK,GAAG;AAC7C,eAAW,KAAK,IAAI,IAAI,MAAM,EAAE,SAAS,OAAO,OAAO,CAAC,EAAE,GAAG,OAAO;EACtE;AAEA,SAAO;AACT;AAEA,SAAS,SACP,SACA,OACA,YACA,UACA,aACA;AACA,QAAM,sBAAsB,QAAQ,QAAQ,SAAS,QAAQ;AAG7D,WAAS,mBACJ,MACH;AAEA,QAAI,UAAU,oBAAoB,SAAS,MAAM,GAAG,IAAI;AAGxD,QAAI,YAAY,WAAW;AACzB,gBAAU,OAAO,OAAO,CAAC,GAAG,SAAS;QACnC,MAAM,QAAQ,YAAY,SAAS;QACnC,CAAC,YAAY,SAAS,GAAG;MAC3B,CAAC;AACD,aAAO,oBAAoB,OAAO;IACpC;AAEA,QAAI,YAAY,SAAS;AACvB,YAAM,CAAC,UAAU,aAAa,IAAI,YAAY;AAC9C,cAAQ,IAAI;QACV,WAAW,KAAK,IAAI,UAAU,kCAAkC,QAAQ,IAAI,aAAa;MAC3F;IACF;AACA,QAAI,YAAY,YAAY;AAC1B,cAAQ,IAAI,KAAK,YAAY,UAAU;IACzC;AAEA,QAAI,YAAY,mBAAmB;AAEjC,YAAMC,WAAU,oBAAoB,SAAS,MAAM,GAAG,IAAI;AAE1D,iBAAW,CAAC,MAAM,KAAK,KAAK,OAAO;QACjC,YAAY;MACd,GAAG;AACD,YAAI,QAAQA,UAAS;AACnB,kBAAQ,IAAI;YACV,IAAI,IAAI,0CAA0C,KAAK,IAAI,UAAU,aAAa,KAAK;UACzF;AACA,cAAI,EAAE,SAASA,WAAU;AACvBA,qBAAQ,KAAK,IAAIA,SAAQ,IAAI;UAC/B;AACA,iBAAOA,SAAQ,IAAI;QACrB;MACF;AACA,aAAO,oBAAoBA,QAAO;IACpC;AAGA,WAAO,oBAAoB,GAAG,IAAI;EACpC;AACA,SAAO,OAAO,OAAO,iBAAiB,mBAAmB;AAC3D;;;ACrKO,SAAS,oBAAoB,SAAuB;AACzD,QAAM,MAAM,mBAAmB,OAAO;AACtC,SAAO;IACL,MAAM;EACR;AACF;AACA,oBAAoB,UAAUC;AAEvB,SAAS,0BAA0B,SAAqC;AAC7E,QAAM,MAAM,mBAAmB,OAAO;AACtC,SAAO;IACL,GAAG;IACH,MAAM;EACR;AACF;AACA,0BAA0B,UAAUA;;;AC1BpC,IAAMC,WAAU;;;ACOhB,IAAMC,WAAU,QAAK,OAAO,YAAY,2BAA2B,YAAY,EAAE;AAAA,EAC/E;AAAA,IACE,WAAW,mBAAmBC,QAAO;AAAA,EACvC;AACF;", "names": ["NullObject", "parse", "safeParse", "name", "method", "hook", "hook", "VERSION", "isPlainObject", "with<PERSON><PERSON><PERSON><PERSON>", "VERSION", "with<PERSON><PERSON><PERSON><PERSON>", "request", "endpoint", "VERSION", "VERSION", "hook", "auth", "VERSION", "request", "VERSION", "VERSION", "VERSION", "VERSION", "endpoint", "options", "VERSION", "VERSION", "Octokit", "VERSION"]}