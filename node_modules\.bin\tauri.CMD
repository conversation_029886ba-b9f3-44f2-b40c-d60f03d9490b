@SETLOCAL
@IF NOT DEFINED NODE_PATH (
  @SET "NODE_PATH=D:\ssh\learn\project-graph-origin\node_modules\.pnpm\@tauri-apps+cli@2.7.1\node_modules\@tauri-apps\cli\node_modules;D:\ssh\learn\project-graph-origin\node_modules\.pnpm\@tauri-apps+cli@2.7.1\node_modules\@tauri-apps\node_modules;D:\ssh\learn\project-graph-origin\node_modules\.pnpm\@tauri-apps+cli@2.7.1\node_modules;D:\ssh\learn\project-graph-origin\node_modules\.pnpm\node_modules"
) ELSE (
  @SET "NODE_PATH=D:\ssh\learn\project-graph-origin\node_modules\.pnpm\@tauri-apps+cli@2.7.1\node_modules\@tauri-apps\cli\node_modules;D:\ssh\learn\project-graph-origin\node_modules\.pnpm\@tauri-apps+cli@2.7.1\node_modules\@tauri-apps\node_modules;D:\ssh\learn\project-graph-origin\node_modules\.pnpm\@tauri-apps+cli@2.7.1\node_modules;D:\ssh\learn\project-graph-origin\node_modules\.pnpm\node_modules;%NODE_PATH%"
)
@IF EXIST "%~dp0\node.exe" (
  "%~dp0\node.exe"  "%~dp0\..\@tauri-apps\cli\tauri.js" %*
) ELSE (
  @SET PATHEXT=%PATHEXT:;.JS;=;%
  node  "%~dp0\..\@tauri-apps\cli\tauri.js" %*
)
