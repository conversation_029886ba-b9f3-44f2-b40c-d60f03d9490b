<!DOCTYPE html><html class="default" lang="en" data-base=".."><head><meta charset="utf-8"/><meta http-equiv="x-ua-compatible" content="IE=edge"/><title>GenerationConfig | @google/genai</title><meta name="description" content="Documentation for @google/genai"/><meta name="viewport" content="width=device-width, initial-scale=1"/><link rel="stylesheet" href="../assets/style.css"/><link rel="stylesheet" href="../assets/highlight.css"/><script defer src="../assets/main.js"></script><script async src="../assets/icons.js" id="tsd-icons-script"></script><script async src="../assets/search.js" id="tsd-search-script"></script><script async src="../assets/navigation.js" id="tsd-nav-script"></script></head><body><script>document.documentElement.dataset.theme = localStorage.getItem("tsd-theme") || "os";document.body.style.display="none";setTimeout(() => app?app.showPage():document.body.style.removeProperty("display"),500)</script><header class="tsd-page-toolbar"><div class="tsd-toolbar-contents container"><div class="table-cell" id="tsd-search"><div class="field"><label for="tsd-search-field" class="tsd-widget tsd-toolbar-icon search no-caption"><svg width="16" height="16" viewBox="0 0 16 16" fill="none" aria-hidden="true"><use href="../assets/icons.svg#icon-search"></use></svg></label><input type="text" id="tsd-search-field" aria-label="Search"/></div><div class="field"><div id="tsd-toolbar-links"></div></div><ul class="results"><li class="state loading">Preparing search index...</li><li class="state failure">The search index is not available</li></ul><a href="../index.html" class="title">@google/genai</a></div><div class="table-cell" id="tsd-widgets"><a href="#" class="tsd-widget tsd-toolbar-icon menu no-caption" data-toggle="menu" aria-label="Menu"><svg width="16" height="16" viewBox="0 0 16 16" fill="none" aria-hidden="true"><use href="../assets/icons.svg#icon-menu"></use></svg></a></div></div></header><div class="container container-main"><div class="col-content"><div class="tsd-page-title"><ul class="tsd-breadcrumb"><li><a href="../modules.html">@google/genai</a></li><li><a href="../modules/types.html">types</a></li><li><a href="types.GenerationConfig.html">GenerationConfig</a></li></ul><h1>Interface GenerationConfig</h1></div><section class="tsd-panel tsd-comment"><div class="tsd-comment tsd-typography"><p>Generation config.</p>
</div><div class="tsd-comment tsd-typography"></div></section><div class="tsd-signature"><span class="tsd-signature-keyword">interface</span> <span class="tsd-kind-interface">GenerationConfig</span> <span class="tsd-signature-symbol">{</span><br/>    <a class="tsd-kind-property" href="types.GenerationConfig.html#audiotimestamp">audioTimestamp</a><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">;</span><br/>    <a class="tsd-kind-property" href="types.GenerationConfig.html#candidatecount">candidateCount</a><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">;</span><br/>    <a class="tsd-kind-property" href="types.GenerationConfig.html#enableaffectivedialog">enableAffectiveDialog</a><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">;</span><br/>    <a class="tsd-kind-property" href="types.GenerationConfig.html#frequencypenalty">frequencyPenalty</a><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">;</span><br/>    <a class="tsd-kind-property" href="types.GenerationConfig.html#logprobs">logprobs</a><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">;</span><br/>    <a class="tsd-kind-property" href="types.GenerationConfig.html#maxoutputtokens">maxOutputTokens</a><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">;</span><br/>    <a class="tsd-kind-property" href="types.GenerationConfig.html#mediaresolution">mediaResolution</a><span class="tsd-signature-symbol">?:</span> <a href="../enums/types.MediaResolution.html" class="tsd-signature-type tsd-kind-enum">MediaResolution</a><span class="tsd-signature-symbol">;</span><br/>    <a class="tsd-kind-property" href="types.GenerationConfig.html#modelselectionconfig">modelSelectionConfig</a><span class="tsd-signature-symbol">?:</span> <a href="types.ModelSelectionConfig.html" class="tsd-signature-type tsd-kind-interface">ModelSelectionConfig</a><span class="tsd-signature-symbol">;</span><br/>    <a class="tsd-kind-property" href="types.GenerationConfig.html#presencepenalty">presencePenalty</a><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">;</span><br/>    <a class="tsd-kind-property" href="types.GenerationConfig.html#responsejsonschema">responseJsonSchema</a><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">unknown</span><span class="tsd-signature-symbol">;</span><br/>    <a class="tsd-kind-property" href="types.GenerationConfig.html#responselogprobs">responseLogprobs</a><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">;</span><br/>    <a class="tsd-kind-property" href="types.GenerationConfig.html#responsemimetype">responseMimeType</a><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">;</span><br/>    <a class="tsd-kind-property" href="types.GenerationConfig.html#responsemodalities">responseModalities</a><span class="tsd-signature-symbol">?:</span> <a href="../enums/types.Modality.html" class="tsd-signature-type tsd-kind-enum">Modality</a><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">;</span><br/>    <a class="tsd-kind-property" href="types.GenerationConfig.html#responseschema">responseSchema</a><span class="tsd-signature-symbol">?:</span> <a href="types.Schema.html" class="tsd-signature-type tsd-kind-interface">Schema</a><span class="tsd-signature-symbol">;</span><br/>    <a class="tsd-kind-property" href="types.GenerationConfig.html#routingconfig">routingConfig</a><span class="tsd-signature-symbol">?:</span> <a href="types.GenerationConfigRoutingConfig.html" class="tsd-signature-type tsd-kind-interface">GenerationConfigRoutingConfig</a><span class="tsd-signature-symbol">;</span><br/>    <a class="tsd-kind-property" href="types.GenerationConfig.html#seed">seed</a><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">;</span><br/>    <a class="tsd-kind-property" href="types.GenerationConfig.html#speechconfig">speechConfig</a><span class="tsd-signature-symbol">?:</span> <a href="types.SpeechConfig.html" class="tsd-signature-type tsd-kind-interface">SpeechConfig</a><span class="tsd-signature-symbol">;</span><br/>    <a class="tsd-kind-property" href="types.GenerationConfig.html#stopsequences">stopSequences</a><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">;</span><br/>    <a class="tsd-kind-property" href="types.GenerationConfig.html#temperature">temperature</a><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">;</span><br/>    <a class="tsd-kind-property" href="types.GenerationConfig.html#thinkingconfig">thinkingConfig</a><span class="tsd-signature-symbol">?:</span> <a href="types.GenerationConfigThinkingConfig.html" class="tsd-signature-type tsd-kind-interface">GenerationConfigThinkingConfig</a><span class="tsd-signature-symbol">;</span><br/>    <a class="tsd-kind-property" href="types.GenerationConfig.html#topk">topK</a><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">;</span><br/>    <a class="tsd-kind-property" href="types.GenerationConfig.html#topp">topP</a><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">;</span><br/><span class="tsd-signature-symbol">}</span></div><aside class="tsd-sources"><ul><li>Defined in src/types.ts:2968</li></ul></aside><section class="tsd-panel-group tsd-index-group"><section class="tsd-panel tsd-index-panel"><details class="tsd-index-content tsd-accordion" open><summary class="tsd-accordion-summary tsd-index-summary"><h5 class="tsd-index-heading uppercase" role="button" aria-expanded="false" tabIndex="0"><svg width="16" height="16" viewBox="0 0 16 16" fill="none" aria-hidden="true"><use href="../assets/icons.svg#icon-chevronSmall"></use></svg> Index</h5></summary><div class="tsd-accordion-details"><section class="tsd-index-section"><h3 class="tsd-index-heading">Properties</h3><div class="tsd-index-list"><a href="types.GenerationConfig.html#audiotimestamp" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>audio<wbr/>Timestamp?</span></a>
<a href="types.GenerationConfig.html#candidatecount" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>candidate<wbr/>Count?</span></a>
<a href="types.GenerationConfig.html#enableaffectivedialog" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>enable<wbr/>Affective<wbr/>Dialog?</span></a>
<a href="types.GenerationConfig.html#frequencypenalty" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>frequency<wbr/>Penalty?</span></a>
<a href="types.GenerationConfig.html#logprobs" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>logprobs?</span></a>
<a href="types.GenerationConfig.html#maxoutputtokens" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>max<wbr/>Output<wbr/>Tokens?</span></a>
<a href="types.GenerationConfig.html#mediaresolution" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>media<wbr/>Resolution?</span></a>
<a href="types.GenerationConfig.html#modelselectionconfig" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>model<wbr/>Selection<wbr/>Config?</span></a>
<a href="types.GenerationConfig.html#presencepenalty" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>presence<wbr/>Penalty?</span></a>
<a href="types.GenerationConfig.html#responsejsonschema" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>response<wbr/>Json<wbr/>Schema?</span></a>
<a href="types.GenerationConfig.html#responselogprobs" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>response<wbr/>Logprobs?</span></a>
<a href="types.GenerationConfig.html#responsemimetype" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>response<wbr/>Mime<wbr/>Type?</span></a>
<a href="types.GenerationConfig.html#responsemodalities" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>response<wbr/>Modalities?</span></a>
<a href="types.GenerationConfig.html#responseschema" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>response<wbr/>Schema?</span></a>
<a href="types.GenerationConfig.html#routingconfig" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>routing<wbr/>Config?</span></a>
<a href="types.GenerationConfig.html#seed" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>seed?</span></a>
<a href="types.GenerationConfig.html#speechconfig" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>speech<wbr/>Config?</span></a>
<a href="types.GenerationConfig.html#stopsequences" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>stop<wbr/>Sequences?</span></a>
<a href="types.GenerationConfig.html#temperature" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>temperature?</span></a>
<a href="types.GenerationConfig.html#thinkingconfig" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>thinking<wbr/>Config?</span></a>
<a href="types.GenerationConfig.html#topk" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>topK?</span></a>
<a href="types.GenerationConfig.html#topp" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>topP?</span></a>
</div></section></div></details></section></section><details class="tsd-panel-group tsd-member-group tsd-accordion" open><summary class="tsd-accordion-summary" data-key="section-Properties"><h2><svg width="20" height="20" viewBox="0 0 24 24" fill="none" aria-hidden="true"><use href="../assets/icons.svg#icon-chevronDown"></use></svg> Properties</h2></summary><section><section class="tsd-panel tsd-member"><a id="audiotimestamp" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><code class="tsd-tag">Optional</code><span>audio<wbr/>Timestamp</span><a href="#audiotimestamp" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-property">audioTimestamp</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">boolean</span></div><div class="tsd-comment tsd-typography"><p>Optional. If enabled, audio timestamp will be included in the request to the model.</p>
</div><div class="tsd-comment tsd-typography"></div><aside class="tsd-sources"><ul><li>Defined in src/types.ts:2972</li></ul></aside></section><section class="tsd-panel tsd-member"><a id="candidatecount" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><code class="tsd-tag">Optional</code><span>candidate<wbr/>Count</span><a href="#candidatecount" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-property">candidateCount</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">number</span></div><div class="tsd-comment tsd-typography"><p>Optional. Number of candidates to generate.</p>
</div><div class="tsd-comment tsd-typography"></div><aside class="tsd-sources"><ul><li>Defined in src/types.ts:2974</li></ul></aside></section><section class="tsd-panel tsd-member"><a id="enableaffectivedialog" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><code class="tsd-tag">Optional</code><span>enable<wbr/>Affective<wbr/>Dialog</span><a href="#enableaffectivedialog" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-property">enableAffectiveDialog</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">boolean</span></div><div class="tsd-comment tsd-typography"><p>Optional. If enabled, the model will detect emotions and adapt its responses accordingly.</p>
</div><div class="tsd-comment tsd-typography"></div><aside class="tsd-sources"><ul><li>Defined in src/types.ts:2976</li></ul></aside></section><section class="tsd-panel tsd-member"><a id="frequencypenalty" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><code class="tsd-tag">Optional</code><span>frequency<wbr/>Penalty</span><a href="#frequencypenalty" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-property">frequencyPenalty</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">number</span></div><div class="tsd-comment tsd-typography"><p>Optional. Frequency penalties.</p>
</div><div class="tsd-comment tsd-typography"></div><aside class="tsd-sources"><ul><li>Defined in src/types.ts:2978</li></ul></aside></section><section class="tsd-panel tsd-member"><a id="logprobs" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><code class="tsd-tag">Optional</code><span>logprobs</span><a href="#logprobs" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-property">logprobs</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">number</span></div><div class="tsd-comment tsd-typography"><p>Optional. Logit probabilities.</p>
</div><div class="tsd-comment tsd-typography"></div><aside class="tsd-sources"><ul><li>Defined in src/types.ts:2980</li></ul></aside></section><section class="tsd-panel tsd-member"><a id="maxoutputtokens" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><code class="tsd-tag">Optional</code><span>max<wbr/>Output<wbr/>Tokens</span><a href="#maxoutputtokens" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-property">maxOutputTokens</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">number</span></div><div class="tsd-comment tsd-typography"><p>Optional. The maximum number of output tokens to generate per message.</p>
</div><div class="tsd-comment tsd-typography"></div><aside class="tsd-sources"><ul><li>Defined in src/types.ts:2982</li></ul></aside></section><section class="tsd-panel tsd-member"><a id="mediaresolution" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><code class="tsd-tag">Optional</code><span>media<wbr/>Resolution</span><a href="#mediaresolution" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-property">mediaResolution</span><span class="tsd-signature-symbol">?:</span> <a href="../enums/types.MediaResolution.html" class="tsd-signature-type tsd-kind-enum">MediaResolution</a></div><div class="tsd-comment tsd-typography"><p>Optional. If specified, the media resolution specified will be used.</p>
</div><div class="tsd-comment tsd-typography"></div><aside class="tsd-sources"><ul><li>Defined in src/types.ts:2984</li></ul></aside></section><section class="tsd-panel tsd-member"><a id="modelselectionconfig" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><code class="tsd-tag">Optional</code><span>model<wbr/>Selection<wbr/>Config</span><a href="#modelselectionconfig" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-property">modelSelectionConfig</span><span class="tsd-signature-symbol">?:</span> <a href="types.ModelSelectionConfig.html" class="tsd-signature-type tsd-kind-interface">ModelSelectionConfig</a></div><div class="tsd-comment tsd-typography"><p>Optional. Config for model selection.</p>
</div><div class="tsd-comment tsd-typography"></div><aside class="tsd-sources"><ul><li>Defined in src/types.ts:2970</li></ul></aside></section><section class="tsd-panel tsd-member"><a id="presencepenalty" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><code class="tsd-tag">Optional</code><span>presence<wbr/>Penalty</span><a href="#presencepenalty" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-property">presencePenalty</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">number</span></div><div class="tsd-comment tsd-typography"><p>Optional. Positive penalties.</p>
</div><div class="tsd-comment tsd-typography"></div><aside class="tsd-sources"><ul><li>Defined in src/types.ts:2986</li></ul></aside></section><section class="tsd-panel tsd-member"><a id="responsejsonschema" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><code class="tsd-tag">Optional</code><span>response<wbr/>Json<wbr/>Schema</span><a href="#responsejsonschema" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-property">responseJsonSchema</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">unknown</span></div><div class="tsd-comment tsd-typography"><p>Optional. Output schema of the generated response. This is an alternative to <code>response_schema</code> that accepts <a href="https://json-schema.org/">JSON Schema</a>. If set, <code>response_schema</code> must be omitted, but <code>response_mime_type</code> is required. While the full JSON Schema may be sent, not all features are supported. Specifically, only the following properties are supported: - <code>$id</code> - <code>$defs</code> - <code>$ref</code> - <code>$anchor</code> - <code>type</code> - <code>format</code> - <code>title</code> - <code>description</code> - <code>enum</code> (for strings and numbers) - <code>items</code> - <code>prefixItems</code> - <code>minItems</code> - <code>maxItems</code> - <code>minimum</code> - <code>maximum</code> - <code>anyOf</code> - <code>oneOf</code> (interpreted the same as <code>anyOf</code>) - <code>properties</code> - <code>additionalProperties</code> - <code>required</code> The non-standard <code>propertyOrdering</code> property may also be set. Cyclic references are unrolled to a limited degree and, as such, may only be used within non-required properties. (Nullable properties are not sufficient.) If <code>$ref</code> is set on a sub-schema, no other properties, except for than those starting as a <code>$</code>, may be set.</p>
</div><div class="tsd-comment tsd-typography"></div><aside class="tsd-sources"><ul><li>Defined in src/types.ts:2988</li></ul></aside></section><section class="tsd-panel tsd-member"><a id="responselogprobs" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><code class="tsd-tag">Optional</code><span>response<wbr/>Logprobs</span><a href="#responselogprobs" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-property">responseLogprobs</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">boolean</span></div><div class="tsd-comment tsd-typography"><p>Optional. If true, export the logprobs results in response.</p>
</div><div class="tsd-comment tsd-typography"></div><aside class="tsd-sources"><ul><li>Defined in src/types.ts:2990</li></ul></aside></section><section class="tsd-panel tsd-member"><a id="responsemimetype" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><code class="tsd-tag">Optional</code><span>response<wbr/>Mime<wbr/>Type</span><a href="#responsemimetype" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-property">responseMimeType</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">string</span></div><div class="tsd-comment tsd-typography"><p>Optional. Output response mimetype of the generated candidate text. Supported mimetype: - <code>text/plain</code>: (default) Text output. - <code>application/json</code>: JSON response in the candidates. The model needs to be prompted to output the appropriate response type, otherwise the behavior is undefined. This is a preview feature.</p>
</div><div class="tsd-comment tsd-typography"></div><aside class="tsd-sources"><ul><li>Defined in src/types.ts:2992</li></ul></aside></section><section class="tsd-panel tsd-member"><a id="responsemodalities" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><code class="tsd-tag">Optional</code><span>response<wbr/>Modalities</span><a href="#responsemodalities" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-property">responseModalities</span><span class="tsd-signature-symbol">?:</span> <a href="../enums/types.Modality.html" class="tsd-signature-type tsd-kind-enum">Modality</a><span class="tsd-signature-symbol">[]</span></div><div class="tsd-comment tsd-typography"><p>Optional. The modalities of the response.</p>
</div><div class="tsd-comment tsd-typography"></div><aside class="tsd-sources"><ul><li>Defined in src/types.ts:2994</li></ul></aside></section><section class="tsd-panel tsd-member"><a id="responseschema" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><code class="tsd-tag">Optional</code><span>response<wbr/>Schema</span><a href="#responseschema" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-property">responseSchema</span><span class="tsd-signature-symbol">?:</span> <a href="types.Schema.html" class="tsd-signature-type tsd-kind-interface">Schema</a></div><div class="tsd-comment tsd-typography"><p>Optional. The <code>Schema</code> object allows the definition of input and output data types. These types can be objects, but also primitives and arrays. Represents a select subset of an <a href="https://spec.openapis.org/oas/v3.0.3#schema">OpenAPI 3.0 schema object</a>. If set, a compatible response_mime_type must also be set. Compatible mimetypes: <code>application/json</code>: Schema for JSON response.</p>
</div><div class="tsd-comment tsd-typography"></div><aside class="tsd-sources"><ul><li>Defined in src/types.ts:2996</li></ul></aside></section><section class="tsd-panel tsd-member"><a id="routingconfig" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><code class="tsd-tag">Optional</code><span>routing<wbr/>Config</span><a href="#routingconfig" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-property">routingConfig</span><span class="tsd-signature-symbol">?:</span> <a href="types.GenerationConfigRoutingConfig.html" class="tsd-signature-type tsd-kind-interface">GenerationConfigRoutingConfig</a></div><div class="tsd-comment tsd-typography"><p>Optional. Routing configuration.</p>
</div><div class="tsd-comment tsd-typography"></div><aside class="tsd-sources"><ul><li>Defined in src/types.ts:2998</li></ul></aside></section><section class="tsd-panel tsd-member"><a id="seed" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><code class="tsd-tag">Optional</code><span>seed</span><a href="#seed" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-property">seed</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">number</span></div><div class="tsd-comment tsd-typography"><p>Optional. Seed.</p>
</div><div class="tsd-comment tsd-typography"></div><aside class="tsd-sources"><ul><li>Defined in src/types.ts:3000</li></ul></aside></section><section class="tsd-panel tsd-member"><a id="speechconfig" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><code class="tsd-tag">Optional</code><span>speech<wbr/>Config</span><a href="#speechconfig" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-property">speechConfig</span><span class="tsd-signature-symbol">?:</span> <a href="types.SpeechConfig.html" class="tsd-signature-type tsd-kind-interface">SpeechConfig</a></div><div class="tsd-comment tsd-typography"><p>Optional. The speech generation config.</p>
</div><div class="tsd-comment tsd-typography"></div><aside class="tsd-sources"><ul><li>Defined in src/types.ts:3002</li></ul></aside></section><section class="tsd-panel tsd-member"><a id="stopsequences" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><code class="tsd-tag">Optional</code><span>stop<wbr/>Sequences</span><a href="#stopsequences" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-property">stopSequences</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">[]</span></div><div class="tsd-comment tsd-typography"><p>Optional. Stop sequences.</p>
</div><div class="tsd-comment tsd-typography"></div><aside class="tsd-sources"><ul><li>Defined in src/types.ts:3004</li></ul></aside></section><section class="tsd-panel tsd-member"><a id="temperature" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><code class="tsd-tag">Optional</code><span>temperature</span><a href="#temperature" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-property">temperature</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">number</span></div><div class="tsd-comment tsd-typography"><p>Optional. Controls the randomness of predictions.</p>
</div><div class="tsd-comment tsd-typography"></div><aside class="tsd-sources"><ul><li>Defined in src/types.ts:3006</li></ul></aside></section><section class="tsd-panel tsd-member"><a id="thinkingconfig" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><code class="tsd-tag">Optional</code><span>thinking<wbr/>Config</span><a href="#thinkingconfig" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-property">thinkingConfig</span><span class="tsd-signature-symbol">?:</span> <a href="types.GenerationConfigThinkingConfig.html" class="tsd-signature-type tsd-kind-interface">GenerationConfigThinkingConfig</a></div><div class="tsd-comment tsd-typography"><p>Optional. Config for thinking features. An error will be returned if this field is set for models that don't support thinking.</p>
</div><div class="tsd-comment tsd-typography"></div><aside class="tsd-sources"><ul><li>Defined in src/types.ts:3008</li></ul></aside></section><section class="tsd-panel tsd-member"><a id="topk" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><code class="tsd-tag">Optional</code><span>topK</span><a href="#topk" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-property">topK</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">number</span></div><div class="tsd-comment tsd-typography"><p>Optional. If specified, top-k sampling will be used.</p>
</div><div class="tsd-comment tsd-typography"></div><aside class="tsd-sources"><ul><li>Defined in src/types.ts:3010</li></ul></aside></section><section class="tsd-panel tsd-member"><a id="topp" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><code class="tsd-tag">Optional</code><span>topP</span><a href="#topp" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-property">topP</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">number</span></div><div class="tsd-comment tsd-typography"><p>Optional. If specified, nucleus sampling will be used.</p>
</div><div class="tsd-comment tsd-typography"></div><aside class="tsd-sources"><ul><li>Defined in src/types.ts:3012</li></ul></aside></section></section></details></div><div class="col-sidebar"><div class="page-menu"><div class="tsd-navigation settings"><details class="tsd-accordion"><summary class="tsd-accordion-summary"><h3><svg width="20" height="20" viewBox="0 0 24 24" fill="none" aria-hidden="true"><use href="../assets/icons.svg#icon-chevronDown"></use></svg>Settings</h3></summary><div class="tsd-accordion-details"><div class="tsd-theme-toggle"><label class="settings-label" for="tsd-theme">Theme</label><select id="tsd-theme"><option value="os">OS</option><option value="light">Light</option><option value="dark">Dark</option></select></div></div></details></div><details open class="tsd-accordion tsd-page-navigation"><summary class="tsd-accordion-summary"><h3><svg width="20" height="20" viewBox="0 0 24 24" fill="none" aria-hidden="true"><use href="../assets/icons.svg#icon-chevronDown"></use></svg>On This Page</h3></summary><div class="tsd-accordion-details"><details open class="tsd-accordion tsd-page-navigation-section"><summary class="tsd-accordion-summary" data-key="section-Properties"><svg width="20" height="20" viewBox="0 0 24 24" fill="none" aria-hidden="true"><use href="../assets/icons.svg#icon-chevronDown"></use></svg>Properties</summary><div><a href="#audiotimestamp" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>audio<wbr/>Timestamp</span></a><a href="#candidatecount" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>candidate<wbr/>Count</span></a><a href="#enableaffectivedialog" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>enable<wbr/>Affective<wbr/>Dialog</span></a><a href="#frequencypenalty" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>frequency<wbr/>Penalty</span></a><a href="#logprobs" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>logprobs</span></a><a href="#maxoutputtokens" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>max<wbr/>Output<wbr/>Tokens</span></a><a href="#mediaresolution" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>media<wbr/>Resolution</span></a><a href="#modelselectionconfig" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>model<wbr/>Selection<wbr/>Config</span></a><a href="#presencepenalty" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>presence<wbr/>Penalty</span></a><a href="#responsejsonschema" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>response<wbr/>Json<wbr/>Schema</span></a><a href="#responselogprobs" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>response<wbr/>Logprobs</span></a><a href="#responsemimetype" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>response<wbr/>Mime<wbr/>Type</span></a><a href="#responsemodalities" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>response<wbr/>Modalities</span></a><a href="#responseschema" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>response<wbr/>Schema</span></a><a href="#routingconfig" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>routing<wbr/>Config</span></a><a href="#seed" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>seed</span></a><a href="#speechconfig" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>speech<wbr/>Config</span></a><a href="#stopsequences" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>stop<wbr/>Sequences</span></a><a href="#temperature" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>temperature</span></a><a href="#thinkingconfig" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>thinking<wbr/>Config</span></a><a href="#topk" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>topK</span></a><a href="#topp" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>topP</span></a></div></details></div></details></div><div class="site-menu"><nav class="tsd-navigation"><a href="../modules.html">@google/genai</a><ul class="tsd-small-nested-navigation" id="tsd-nav-container"><li>Loading...</li></ul></nav></div></div></div><footer><p class="tsd-generator">Generated using <a href="https://typedoc.org/" target="_blank">TypeDoc</a></p></footer><div class="overlay"></div></body></html>
