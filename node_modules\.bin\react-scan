#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/d/ssh/learn/project-graph-origin/node_modules/.pnpm/react-scan@0.4.3_@types+rea_e51f6fb62478d2c0db6596a7d6587c59/node_modules/react-scan/bin/node_modules:/mnt/d/ssh/learn/project-graph-origin/node_modules/.pnpm/react-scan@0.4.3_@types+rea_e51f6fb62478d2c0db6596a7d6587c59/node_modules/react-scan/node_modules:/mnt/d/ssh/learn/project-graph-origin/node_modules/.pnpm/react-scan@0.4.3_@types+rea_e51f6fb62478d2c0db6596a7d6587c59/node_modules:/mnt/d/ssh/learn/project-graph-origin/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/d/ssh/learn/project-graph-origin/node_modules/.pnpm/react-scan@0.4.3_@types+rea_e51f6fb62478d2c0db6596a7d6587c59/node_modules/react-scan/bin/node_modules:/mnt/d/ssh/learn/project-graph-origin/node_modules/.pnpm/react-scan@0.4.3_@types+rea_e51f6fb62478d2c0db6596a7d6587c59/node_modules/react-scan/node_modules:/mnt/d/ssh/learn/project-graph-origin/node_modules/.pnpm/react-scan@0.4.3_@types+rea_e51f6fb62478d2c0db6596a7d6587c59/node_modules:/mnt/d/ssh/learn/project-graph-origin/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../react-scan/bin/cli.js" "$@"
else
  exec node  "$basedir/../react-scan/bin/cli.js" "$@"
fi
