@SETLOCAL
@IF NOT DEFINED NODE_PATH (
  @SET "NODE_PATH=D:\ssh\learn\project-graph-origin\node_modules\.pnpm\vitest@3.2.4_@types+debug@4_172ece3346e986592dc412fa0eadbf14\node_modules\vitest\node_modules;D:\ssh\learn\project-graph-origin\node_modules\.pnpm\vitest@3.2.4_@types+debug@4_172ece3346e986592dc412fa0eadbf14\node_modules;D:\ssh\learn\project-graph-origin\node_modules\.pnpm\node_modules"
) ELSE (
  @SET "NODE_PATH=D:\ssh\learn\project-graph-origin\node_modules\.pnpm\vitest@3.2.4_@types+debug@4_172ece3346e986592dc412fa0eadbf14\node_modules\vitest\node_modules;D:\ssh\learn\project-graph-origin\node_modules\.pnpm\vitest@3.2.4_@types+debug@4_172ece3346e986592dc412fa0eadbf14\node_modules;D:\ssh\learn\project-graph-origin\node_modules\.pnpm\node_modules;%NODE_PATH%"
)
@IF EXIST "%~dp0\node.exe" (
  "%~dp0\node.exe"  "%~dp0\..\vitest\vitest.mjs" %*
) ELSE (
  @SET PATHEXT=%PATHEXT:;.JS;=;%
  node  "%~dp0\..\vitest\vitest.mjs" %*
)
