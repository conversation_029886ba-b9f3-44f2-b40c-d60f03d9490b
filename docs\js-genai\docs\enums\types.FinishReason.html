<!DOCTYPE html><html class="default" lang="en" data-base=".."><head><meta charset="utf-8"/><meta http-equiv="x-ua-compatible" content="IE=edge"/><title>FinishReason | @google/genai</title><meta name="description" content="Documentation for @google/genai"/><meta name="viewport" content="width=device-width, initial-scale=1"/><link rel="stylesheet" href="../assets/style.css"/><link rel="stylesheet" href="../assets/highlight.css"/><script defer src="../assets/main.js"></script><script async src="../assets/icons.js" id="tsd-icons-script"></script><script async src="../assets/search.js" id="tsd-search-script"></script><script async src="../assets/navigation.js" id="tsd-nav-script"></script></head><body><script>document.documentElement.dataset.theme = localStorage.getItem("tsd-theme") || "os";document.body.style.display="none";setTimeout(() => app?app.showPage():document.body.style.removeProperty("display"),500)</script><header class="tsd-page-toolbar"><div class="tsd-toolbar-contents container"><div class="table-cell" id="tsd-search"><div class="field"><label for="tsd-search-field" class="tsd-widget tsd-toolbar-icon search no-caption"><svg width="16" height="16" viewBox="0 0 16 16" fill="none" aria-hidden="true"><use href="../assets/icons.svg#icon-search"></use></svg></label><input type="text" id="tsd-search-field" aria-label="Search"/></div><div class="field"><div id="tsd-toolbar-links"></div></div><ul class="results"><li class="state loading">Preparing search index...</li><li class="state failure">The search index is not available</li></ul><a href="../index.html" class="title">@google/genai</a></div><div class="table-cell" id="tsd-widgets"><a href="#" class="tsd-widget tsd-toolbar-icon menu no-caption" data-toggle="menu" aria-label="Menu"><svg width="16" height="16" viewBox="0 0 16 16" fill="none" aria-hidden="true"><use href="../assets/icons.svg#icon-menu"></use></svg></a></div></div></header><div class="container container-main"><div class="col-content"><div class="tsd-page-title"><ul class="tsd-breadcrumb"><li><a href="../modules.html">@google/genai</a></li><li><a href="../modules/types.html">types</a></li><li><a href="types.FinishReason.html">FinishReason</a></li></ul><h1>Enumeration FinishReason</h1></div><section class="tsd-panel tsd-comment"><div class="tsd-comment tsd-typography"><p>Output only. The reason why the model stopped generating tokens.</p>
<p>If empty, the model has not stopped generating the tokens.</p>
</div><div class="tsd-comment tsd-typography"></div></section><aside class="tsd-sources"><ul><li>Defined in src/types.ts:256</li></ul></aside><section class="tsd-panel-group tsd-index-group"><section class="tsd-panel tsd-index-panel"><details class="tsd-index-content tsd-accordion" open><summary class="tsd-accordion-summary tsd-index-summary"><h5 class="tsd-index-heading uppercase" role="button" aria-expanded="false" tabIndex="0"><svg width="16" height="16" viewBox="0 0 16 16" fill="none" aria-hidden="true"><use href="../assets/icons.svg#icon-chevronSmall"></use></svg> Index</h5></summary><div class="tsd-accordion-details"><section class="tsd-index-section"><h3 class="tsd-index-heading">Enumeration Members</h3><div class="tsd-index-list"><a href="types.FinishReason.html#blocklist" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Enumeration Member"><use href="../assets/icons.svg#icon-16"></use></svg><span>BLOCKLIST</span></a>
<a href="types.FinishReason.html#finish_reason_unspecified" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Enumeration Member"><use href="../assets/icons.svg#icon-16"></use></svg><span>FINISH_<wbr/>REASON_<wbr/>UNSPECIFIED</span></a>
<a href="types.FinishReason.html#image_safety" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Enumeration Member"><use href="../assets/icons.svg#icon-16"></use></svg><span>IMAGE_<wbr/>SAFETY</span></a>
<a href="types.FinishReason.html#language" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Enumeration Member"><use href="../assets/icons.svg#icon-16"></use></svg><span>LANGUAGE</span></a>
<a href="types.FinishReason.html#malformed_function_call" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Enumeration Member"><use href="../assets/icons.svg#icon-16"></use></svg><span>MALFORMED_<wbr/>FUNCTION_<wbr/>CALL</span></a>
<a href="types.FinishReason.html#max_tokens" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Enumeration Member"><use href="../assets/icons.svg#icon-16"></use></svg><span>MAX_<wbr/>TOKENS</span></a>
<a href="types.FinishReason.html#other" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Enumeration Member"><use href="../assets/icons.svg#icon-16"></use></svg><span>OTHER</span></a>
<a href="types.FinishReason.html#prohibited_content" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Enumeration Member"><use href="../assets/icons.svg#icon-16"></use></svg><span>PROHIBITED_<wbr/>CONTENT</span></a>
<a href="types.FinishReason.html#recitation" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Enumeration Member"><use href="../assets/icons.svg#icon-16"></use></svg><span>RECITATION</span></a>
<a href="types.FinishReason.html#safety" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Enumeration Member"><use href="../assets/icons.svg#icon-16"></use></svg><span>SAFETY</span></a>
<a href="types.FinishReason.html#spii" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Enumeration Member"><use href="../assets/icons.svg#icon-16"></use></svg><span>SPII</span></a>
<a href="types.FinishReason.html#stop" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Enumeration Member"><use href="../assets/icons.svg#icon-16"></use></svg><span>STOP</span></a>
<a href="types.FinishReason.html#unexpected_tool_call" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Enumeration Member"><use href="../assets/icons.svg#icon-16"></use></svg><span>UNEXPECTED_<wbr/>TOOL_<wbr/>CALL</span></a>
</div></section></div></details></section></section><details class="tsd-panel-group tsd-member-group tsd-accordion" open><summary class="tsd-accordion-summary" data-key="section-Enumeration Members"><h2><svg width="20" height="20" viewBox="0 0 24 24" fill="none" aria-hidden="true"><use href="../assets/icons.svg#icon-chevronDown"></use></svg> Enumeration Members</h2></summary><section><section class="tsd-panel tsd-member"><a id="blocklist" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><span>BLOCKLIST</span><a href="#blocklist" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-enum-member">BLOCKLIST</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">&quot;BLOCKLIST&quot;</span></div><div class="tsd-comment tsd-typography"><p>Token generation stopped because the content contains forbidden terms.</p>
</div><div class="tsd-comment tsd-typography"></div><aside class="tsd-sources"><ul><li>Defined in src/types.ts:288</li></ul></aside></section><section class="tsd-panel tsd-member"><a id="finish_reason_unspecified" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><span>FINISH_<wbr/>REASON_<wbr/>UNSPECIFIED</span><a href="#finish_reason_unspecified" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-enum-member">FINISH_REASON_UNSPECIFIED</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">&quot;FINISH_REASON_UNSPECIFIED&quot;</span></div><div class="tsd-comment tsd-typography"><p>The finish reason is unspecified.</p>
</div><div class="tsd-comment tsd-typography"></div><aside class="tsd-sources"><ul><li>Defined in src/types.ts:260</li></ul></aside></section><section class="tsd-panel tsd-member"><a id="image_safety" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><span>IMAGE_<wbr/>SAFETY</span><a href="#image_safety" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-enum-member">IMAGE_SAFETY</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">&quot;IMAGE_SAFETY&quot;</span></div><div class="tsd-comment tsd-typography"><p>Token generation stopped because generated images have safety violations.</p>
</div><div class="tsd-comment tsd-typography"></div><aside class="tsd-sources"><ul><li>Defined in src/types.ts:304</li></ul></aside></section><section class="tsd-panel tsd-member"><a id="language" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><span>LANGUAGE</span><a href="#language" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-enum-member">LANGUAGE</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">&quot;LANGUAGE&quot;</span></div><div class="tsd-comment tsd-typography"><p>The token generation stopped because of using an unsupported language.</p>
</div><div class="tsd-comment tsd-typography"></div><aside class="tsd-sources"><ul><li>Defined in src/types.ts:280</li></ul></aside></section><section class="tsd-panel tsd-member"><a id="malformed_function_call" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><span>MALFORMED_<wbr/>FUNCTION_<wbr/>CALL</span><a href="#malformed_function_call" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-enum-member">MALFORMED_FUNCTION_CALL</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">&quot;MALFORMED_FUNCTION_CALL&quot;</span></div><div class="tsd-comment tsd-typography"><p>The function call generated by the model is invalid.</p>
</div><div class="tsd-comment tsd-typography"></div><aside class="tsd-sources"><ul><li>Defined in src/types.ts:300</li></ul></aside></section><section class="tsd-panel tsd-member"><a id="max_tokens" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><span>MAX_<wbr/>TOKENS</span><a href="#max_tokens" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-enum-member">MAX_TOKENS</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">&quot;MAX_TOKENS&quot;</span></div><div class="tsd-comment tsd-typography"><p>Token generation reached the configured maximum output tokens.</p>
</div><div class="tsd-comment tsd-typography"></div><aside class="tsd-sources"><ul><li>Defined in src/types.ts:268</li></ul></aside></section><section class="tsd-panel tsd-member"><a id="other" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><span>OTHER</span><a href="#other" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-enum-member">OTHER</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">&quot;OTHER&quot;</span></div><div class="tsd-comment tsd-typography"><p>All other reasons that stopped the token generation.</p>
</div><div class="tsd-comment tsd-typography"></div><aside class="tsd-sources"><ul><li>Defined in src/types.ts:284</li></ul></aside></section><section class="tsd-panel tsd-member"><a id="prohibited_content" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><span>PROHIBITED_<wbr/>CONTENT</span><a href="#prohibited_content" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-enum-member">PROHIBITED_CONTENT</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">&quot;PROHIBITED_CONTENT&quot;</span></div><div class="tsd-comment tsd-typography"><p>Token generation stopped for potentially containing prohibited content.</p>
</div><div class="tsd-comment tsd-typography"></div><aside class="tsd-sources"><ul><li>Defined in src/types.ts:292</li></ul></aside></section><section class="tsd-panel tsd-member"><a id="recitation" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><span>RECITATION</span><a href="#recitation" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-enum-member">RECITATION</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">&quot;RECITATION&quot;</span></div><div class="tsd-comment tsd-typography"><p>The token generation stopped because of potential recitation.</p>
</div><div class="tsd-comment tsd-typography"></div><aside class="tsd-sources"><ul><li>Defined in src/types.ts:276</li></ul></aside></section><section class="tsd-panel tsd-member"><a id="safety" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><span>SAFETY</span><a href="#safety" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-enum-member">SAFETY</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">&quot;SAFETY&quot;</span></div><div class="tsd-comment tsd-typography"><p>Token generation stopped because the content potentially contains safety violations. NOTE: When streaming, [content][] is empty if content filters blocks the output.</p>
</div><div class="tsd-comment tsd-typography"></div><aside class="tsd-sources"><ul><li>Defined in src/types.ts:272</li></ul></aside></section><section class="tsd-panel tsd-member"><a id="spii" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><span>SPII</span><a href="#spii" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-enum-member">SPII</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">&quot;SPII&quot;</span></div><div class="tsd-comment tsd-typography"><p>Token generation stopped because the content potentially contains Sensitive Personally Identifiable Information (SPII).</p>
</div><div class="tsd-comment tsd-typography"></div><aside class="tsd-sources"><ul><li>Defined in src/types.ts:296</li></ul></aside></section><section class="tsd-panel tsd-member"><a id="stop" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><span>STOP</span><a href="#stop" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-enum-member">STOP</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">&quot;STOP&quot;</span></div><div class="tsd-comment tsd-typography"><p>Token generation reached a natural stopping point or a configured stop sequence.</p>
</div><div class="tsd-comment tsd-typography"></div><aside class="tsd-sources"><ul><li>Defined in src/types.ts:264</li></ul></aside></section><section class="tsd-panel tsd-member"><a id="unexpected_tool_call" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><span>UNEXPECTED_<wbr/>TOOL_<wbr/>CALL</span><a href="#unexpected_tool_call" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-enum-member">UNEXPECTED_TOOL_CALL</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">&quot;UNEXPECTED_TOOL_CALL&quot;</span></div><div class="tsd-comment tsd-typography"><p>The tool call generated by the model is invalid.</p>
</div><div class="tsd-comment tsd-typography"></div><aside class="tsd-sources"><ul><li>Defined in src/types.ts:308</li></ul></aside></section></section></details></div><div class="col-sidebar"><div class="page-menu"><div class="tsd-navigation settings"><details class="tsd-accordion"><summary class="tsd-accordion-summary"><h3><svg width="20" height="20" viewBox="0 0 24 24" fill="none" aria-hidden="true"><use href="../assets/icons.svg#icon-chevronDown"></use></svg>Settings</h3></summary><div class="tsd-accordion-details"><div class="tsd-theme-toggle"><label class="settings-label" for="tsd-theme">Theme</label><select id="tsd-theme"><option value="os">OS</option><option value="light">Light</option><option value="dark">Dark</option></select></div></div></details></div><details open class="tsd-accordion tsd-page-navigation"><summary class="tsd-accordion-summary"><h3><svg width="20" height="20" viewBox="0 0 24 24" fill="none" aria-hidden="true"><use href="../assets/icons.svg#icon-chevronDown"></use></svg>On This Page</h3></summary><div class="tsd-accordion-details"><details open class="tsd-accordion tsd-page-navigation-section"><summary class="tsd-accordion-summary" data-key="section-Enumeration Members"><svg width="20" height="20" viewBox="0 0 24 24" fill="none" aria-hidden="true"><use href="../assets/icons.svg#icon-chevronDown"></use></svg>Enumeration Members</summary><div><a href="#blocklist" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Enumeration Member"><use href="../assets/icons.svg#icon-16"></use></svg><span>BLOCKLIST</span></a><a href="#finish_reason_unspecified" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Enumeration Member"><use href="../assets/icons.svg#icon-16"></use></svg><span>FINISH_<wbr/>REASON_<wbr/>UNSPECIFIED</span></a><a href="#image_safety" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Enumeration Member"><use href="../assets/icons.svg#icon-16"></use></svg><span>IMAGE_<wbr/>SAFETY</span></a><a href="#language" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Enumeration Member"><use href="../assets/icons.svg#icon-16"></use></svg><span>LANGUAGE</span></a><a href="#malformed_function_call" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Enumeration Member"><use href="../assets/icons.svg#icon-16"></use></svg><span>MALFORMED_<wbr/>FUNCTION_<wbr/>CALL</span></a><a href="#max_tokens" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Enumeration Member"><use href="../assets/icons.svg#icon-16"></use></svg><span>MAX_<wbr/>TOKENS</span></a><a href="#other" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Enumeration Member"><use href="../assets/icons.svg#icon-16"></use></svg><span>OTHER</span></a><a href="#prohibited_content" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Enumeration Member"><use href="../assets/icons.svg#icon-16"></use></svg><span>PROHIBITED_<wbr/>CONTENT</span></a><a href="#recitation" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Enumeration Member"><use href="../assets/icons.svg#icon-16"></use></svg><span>RECITATION</span></a><a href="#safety" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Enumeration Member"><use href="../assets/icons.svg#icon-16"></use></svg><span>SAFETY</span></a><a href="#spii" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Enumeration Member"><use href="../assets/icons.svg#icon-16"></use></svg><span>SPII</span></a><a href="#stop" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Enumeration Member"><use href="../assets/icons.svg#icon-16"></use></svg><span>STOP</span></a><a href="#unexpected_tool_call" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Enumeration Member"><use href="../assets/icons.svg#icon-16"></use></svg><span>UNEXPECTED_<wbr/>TOOL_<wbr/>CALL</span></a></div></details></div></details></div><div class="site-menu"><nav class="tsd-navigation"><a href="../modules.html">@google/genai</a><ul class="tsd-small-nested-navigation" id="tsd-nav-container"><li>Loading...</li></ul></nav></div></div></div><footer><p class="tsd-generator">Generated using <a href="https://typedoc.org/" target="_blank">TypeDoc</a></p></footer><div class="overlay"></div></body></html>
