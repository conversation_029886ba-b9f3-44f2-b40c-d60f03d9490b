{"version": 3, "sources": ["../../../../node_modules/.pnpm/@tauri-apps+plugin-updater@2.9.0/node_modules/@tauri-apps/plugin-updater/dist-js/index.js"], "sourcesContent": ["import { Resource, Channel, invoke } from '@tauri-apps/api/core';\n\n// Copyright 2019-2023 Tauri Programme within The Commons Conservancy\n// SPDX-License-Identifier: Apache-2.0\n// SPDX-License-Identifier: MIT\nclass Update extends Resource {\n    constructor(metadata) {\n        super(metadata.rid);\n        this.available = true;\n        this.currentVersion = metadata.currentVersion;\n        this.version = metadata.version;\n        this.date = metadata.date;\n        this.body = metadata.body;\n        this.rawJson = metadata.rawJson;\n    }\n    /** Download the updater package */\n    async download(onEvent, options) {\n        convertToRustHeaders(options);\n        const channel = new Channel();\n        if (onEvent) {\n            channel.onmessage = onEvent;\n        }\n        const downloadedBytesRid = await invoke('plugin:updater|download', {\n            onEvent: channel,\n            rid: this.rid,\n            ...options\n        });\n        this.downloadedBytes = new Resource(downloadedBytesRid);\n    }\n    /** Install downloaded updater package */\n    async install() {\n        if (!this.downloadedBytes) {\n            throw new Error('Update.install called before Update.download');\n        }\n        await invoke('plugin:updater|install', {\n            updateRid: this.rid,\n            bytesRid: this.downloadedBytes.rid\n        });\n        // Don't need to call close, we did it in rust side already\n        this.downloadedBytes = undefined;\n    }\n    /** Downloads the updater package and installs it */\n    async downloadAndInstall(onEvent, options) {\n        convertToRustHeaders(options);\n        const channel = new Channel();\n        if (onEvent) {\n            channel.onmessage = onEvent;\n        }\n        await invoke('plugin:updater|download_and_install', {\n            onEvent: channel,\n            rid: this.rid,\n            ...options\n        });\n    }\n    async close() {\n        await this.downloadedBytes?.close();\n        await super.close();\n    }\n}\n/** Check for updates, resolves to `null` if no updates are available */\nasync function check(options) {\n    convertToRustHeaders(options);\n    const metadata = await invoke('plugin:updater|check', {\n        ...options\n    });\n    return metadata ? new Update(metadata) : null;\n}\n/**\n * Converts the headers in options to be an {@linkcode Array<[string, string]>} which is what the Rust side expects\n */\nfunction convertToRustHeaders(options) {\n    if (options?.headers) {\n        options.headers = Array.from(new Headers(options.headers).entries());\n    }\n}\n\nexport { Update, check };\n"], "mappings": ";;;;;;;;AAKA,IAAM,SAAN,cAAqB,SAAS;AAAA,EAC1B,YAAY,UAAU;AAClB,UAAM,SAAS,GAAG;AAClB,SAAK,YAAY;AACjB,SAAK,iBAAiB,SAAS;AAC/B,SAAK,UAAU,SAAS;AACxB,SAAK,OAAO,SAAS;AACrB,SAAK,OAAO,SAAS;AACrB,SAAK,UAAU,SAAS;AAAA,EAC5B;AAAA;AAAA,EAEA,MAAM,SAAS,SAAS,SAAS;AAC7B,yBAAqB,OAAO;AAC5B,UAAM,UAAU,IAAI,QAAQ;AAC5B,QAAI,SAAS;AACT,cAAQ,YAAY;AAAA,IACxB;AACA,UAAM,qBAAqB,MAAM,OAAO,2BAA2B;AAAA,MAC/D,SAAS;AAAA,MACT,KAAK,KAAK;AAAA,MACV,GAAG;AAAA,IACP,CAAC;AACD,SAAK,kBAAkB,IAAI,SAAS,kBAAkB;AAAA,EAC1D;AAAA;AAAA,EAEA,MAAM,UAAU;AACZ,QAAI,CAAC,KAAK,iBAAiB;AACvB,YAAM,IAAI,MAAM,8CAA8C;AAAA,IAClE;AACA,UAAM,OAAO,0BAA0B;AAAA,MACnC,WAAW,KAAK;AAAA,MAChB,UAAU,KAAK,gBAAgB;AAAA,IACnC,CAAC;AAED,SAAK,kBAAkB;AAAA,EAC3B;AAAA;AAAA,EAEA,MAAM,mBAAmB,SAAS,SAAS;AACvC,yBAAqB,OAAO;AAC5B,UAAM,UAAU,IAAI,QAAQ;AAC5B,QAAI,SAAS;AACT,cAAQ,YAAY;AAAA,IACxB;AACA,UAAM,OAAO,uCAAuC;AAAA,MAChD,SAAS;AAAA,MACT,KAAK,KAAK;AAAA,MACV,GAAG;AAAA,IACP,CAAC;AAAA,EACL;AAAA,EACA,MAAM,QAAQ;AAtDlB;AAuDQ,YAAM,UAAK,oBAAL,mBAAsB;AAC5B,UAAM,MAAM,MAAM;AAAA,EACtB;AACJ;AAEA,eAAe,MAAM,SAAS;AAC1B,uBAAqB,OAAO;AAC5B,QAAM,WAAW,MAAM,OAAO,wBAAwB;AAAA,IAClD,GAAG;AAAA,EACP,CAAC;AACD,SAAO,WAAW,IAAI,OAAO,QAAQ,IAAI;AAC7C;AAIA,SAAS,qBAAqB,SAAS;AACnC,MAAI,mCAAS,SAAS;AAClB,YAAQ,UAAU,MAAM,KAAK,IAAI,QAAQ,QAAQ,OAAO,EAAE,QAAQ,CAAC;AAAA,EACvE;AACJ;", "names": []}