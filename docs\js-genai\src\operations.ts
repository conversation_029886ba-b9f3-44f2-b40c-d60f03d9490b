/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */

// Code generated by the Google Gen AI SDK generator DO NOT EDIT.

import {ApiClient} from './_api_client.js';
import * as common from './_common.js';
import {BaseModule} from './_common.js';
import * as converters from './converters/_operations_converters.js';
import * as types from './types.js';

export class Operations extends BaseModule {
  constructor(private readonly apiClient: ApiClient) {
    super();
  }

  /**
   * Gets the status of a long-running operation.
   *
   * @param parameters The parameters for the get operation request.
   * @return The updated Operation object, with the latest status or result.
   */
  async getVideosOperation(
    parameters: types.OperationGetParameters<
      types.GenerateVideosResponse,
      types.GenerateVideosOperation
    >,
  ): Promise<types.GenerateVideosOperation> {
    const operation = parameters.operation;
    const config = parameters.config;

    if (operation.name === undefined || operation.name === '') {
      throw new Error('Operation name is required.');
    }

    if (this.apiClient.isVertexAI()) {
      const resourceName = operation.name.split('/operations/')[0];
      let httpOptions: types.HttpOptions | undefined = undefined;

      if (config && 'httpOptions' in config) {
        httpOptions = config.httpOptions;
      }

      const rawOperation = await this.fetchPredictVideosOperationInternal({
        operationName: operation.name,
        resourceName: resourceName,
        config: {httpOptions: httpOptions},
      });

      return operation._fromAPIResponse({
        apiResponse: rawOperation,
        isVertexAI: true,
      });
    } else {
      const rawOperation = await this.getVideosOperationInternal({
        operationName: operation.name,
        config: config,
      });
      return operation._fromAPIResponse({
        apiResponse: rawOperation,
        isVertexAI: false,
      });
    }
  }

  /**
   * Gets the status of a long-running operation.
   *
   * @param parameters The parameters for the get operation request.
   * @return The updated Operation object, with the latest status or result.
   */
  async get<T, U extends types.Operation<T>>(
    parameters: types.OperationGetParameters<T, U>,
  ): Promise<types.Operation<T>> {
    const operation = parameters.operation;
    const config = parameters.config;

    if (operation.name === undefined || operation.name === '') {
      throw new Error('Operation name is required.');
    }

    if (this.apiClient.isVertexAI()) {
      const resourceName = operation.name.split('/operations/')[0];
      let httpOptions: types.HttpOptions | undefined = undefined;

      if (config && 'httpOptions' in config) {
        httpOptions = config.httpOptions;
      }

      const rawOperation = await this.fetchPredictVideosOperationInternal({
        operationName: operation.name,
        resourceName: resourceName,
        config: {httpOptions: httpOptions},
      });

      return operation._fromAPIResponse({
        apiResponse: rawOperation,
        isVertexAI: true,
      });
    } else {
      const rawOperation = await this.getVideosOperationInternal({
        operationName: operation.name,
        config: config,
      });
      return operation._fromAPIResponse({
        apiResponse: rawOperation,
        isVertexAI: false,
      });
    }
  }

  private async getVideosOperationInternal(
    params: types.GetOperationParameters,
  ): Promise<Record<string, unknown>> {
    let response: Promise<Record<string, unknown>>;

    let path: string = '';
    let queryParams: Record<string, string> = {};
    if (this.apiClient.isVertexAI()) {
      const body = converters.getOperationParametersToVertex(params);
      path = common.formatMap(
        '{operationName}',
        body['_url'] as Record<string, unknown>,
      );
      queryParams = body['_query'] as Record<string, string>;
      delete body['config'];
      delete body['_url'];
      delete body['_query'];

      response = this.apiClient
        .request({
          path: path,
          queryParams: queryParams,
          body: JSON.stringify(body),
          httpMethod: 'GET',
          httpOptions: params.config?.httpOptions,
          abortSignal: params.config?.abortSignal,
        })
        .then((httpResponse) => {
          return httpResponse.json();
        }) as Promise<Record<string, unknown>>;

      return response;
    } else {
      const body = converters.getOperationParametersToMldev(params);
      path = common.formatMap(
        '{operationName}',
        body['_url'] as Record<string, unknown>,
      );
      queryParams = body['_query'] as Record<string, string>;
      delete body['config'];
      delete body['_url'];
      delete body['_query'];

      response = this.apiClient
        .request({
          path: path,
          queryParams: queryParams,
          body: JSON.stringify(body),
          httpMethod: 'GET',
          httpOptions: params.config?.httpOptions,
          abortSignal: params.config?.abortSignal,
        })
        .then((httpResponse) => {
          return httpResponse.json();
        }) as Promise<Record<string, unknown>>;

      return response;
    }
  }

  private async fetchPredictVideosOperationInternal(
    params: types.FetchPredictOperationParameters,
  ): Promise<Record<string, unknown>> {
    let response: Promise<Record<string, unknown>>;

    let path: string = '';
    let queryParams: Record<string, string> = {};
    if (this.apiClient.isVertexAI()) {
      const body = converters.fetchPredictOperationParametersToVertex(params);
      path = common.formatMap(
        '{resourceName}:fetchPredictOperation',
        body['_url'] as Record<string, unknown>,
      );
      queryParams = body['_query'] as Record<string, string>;
      delete body['config'];
      delete body['_url'];
      delete body['_query'];

      response = this.apiClient
        .request({
          path: path,
          queryParams: queryParams,
          body: JSON.stringify(body),
          httpMethod: 'POST',
          httpOptions: params.config?.httpOptions,
          abortSignal: params.config?.abortSignal,
        })
        .then((httpResponse) => {
          return httpResponse.json();
        }) as Promise<Record<string, unknown>>;

      return response;
    } else {
      throw new Error('This method is only supported by the Vertex AI.');
    }
  }
}
