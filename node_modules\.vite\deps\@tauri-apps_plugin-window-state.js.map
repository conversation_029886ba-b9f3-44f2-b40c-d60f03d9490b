{"version": 3, "sources": ["../../../../node_modules/.pnpm/@tauri-apps+plugin-window-state@2.4.0/node_modules/@tauri-apps/plugin-window-state/dist-js/index.js"], "sourcesContent": ["import { invoke } from '@tauri-apps/api/core';\nimport { getCurrentWindow } from '@tauri-apps/api/window';\n\n// Copyright 2019-2023 Tauri Programme within The Commons Conservancy\n// SPDX-License-Identifier: Apache-2.0\n// SPDX-License-Identifier: MIT\nvar StateFlags;\n(function (StateFlags) {\n    StateFlags[StateFlags[\"SIZE\"] = 1] = \"SIZE\";\n    StateFlags[StateFlags[\"POSITION\"] = 2] = \"POSITION\";\n    StateFlags[StateFlags[\"MAXIMIZED\"] = 4] = \"MAXIMIZED\";\n    StateFlags[StateFlags[\"VISIBLE\"] = 8] = \"VISIBLE\";\n    StateFlags[StateFlags[\"DECORATIONS\"] = 16] = \"DECORATIONS\";\n    StateFlags[StateFlags[\"FULLSCREEN\"] = 32] = \"FULLSCREEN\";\n    StateFlags[StateFlags[\"ALL\"] = 63] = \"ALL\";\n})(StateFlags || (StateFlags = {}));\n/**\n *  Save the state of all open windows to disk.\n */\nasync function saveWindowState(flags) {\n    await invoke('plugin:window-state|save_window_state', { flags });\n}\n/**\n *  Restore the state for the specified window from disk.\n */\nasync function restoreState(label, flags) {\n    await invoke('plugin:window-state|restore_state', { label, flags });\n}\n/**\n *  Restore the state for the current window from disk.\n */\nasync function restoreStateCurrent(flags) {\n    await restoreState(getCurrentWindow().label, flags);\n}\n/**\n *  Get the name of the file used to store window state.\n */\nasync function filename() {\n    return await invoke('plugin:window-state|filename');\n}\n\nexport { StateFlags, filename, restoreState, restoreStateCurrent, saveWindowState };\n"], "mappings": ";;;;;;;;;;;AAMA,IAAI;AAAA,CACH,SAAUA,aAAY;AACnB,EAAAA,YAAWA,YAAW,MAAM,IAAI,CAAC,IAAI;AACrC,EAAAA,YAAWA,YAAW,UAAU,IAAI,CAAC,IAAI;AACzC,EAAAA,YAAWA,YAAW,WAAW,IAAI,CAAC,IAAI;AAC1C,EAAAA,YAAWA,YAAW,SAAS,IAAI,CAAC,IAAI;AACxC,EAAAA,YAAWA,YAAW,aAAa,IAAI,EAAE,IAAI;AAC7C,EAAAA,YAAWA,YAAW,YAAY,IAAI,EAAE,IAAI;AAC5C,EAAAA,YAAWA,YAAW,KAAK,IAAI,EAAE,IAAI;AACzC,GAAG,eAAe,aAAa,CAAC,EAAE;AAIlC,eAAe,gBAAgB,OAAO;AAClC,QAAM,OAAO,yCAAyC,EAAE,MAAM,CAAC;AACnE;AAIA,eAAe,aAAa,OAAO,OAAO;AACtC,QAAM,OAAO,qCAAqC,EAAE,OAAO,MAAM,CAAC;AACtE;AAIA,eAAe,oBAAoB,OAAO;AACtC,QAAM,aAAa,iBAAiB,EAAE,OAAO,KAAK;AACtD;AAIA,eAAe,WAAW;AACtB,SAAO,MAAM,OAAO,8BAA8B;AACtD;", "names": ["StateFlags"]}