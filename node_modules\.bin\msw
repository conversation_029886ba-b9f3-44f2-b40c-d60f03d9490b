#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/d/ssh/learn/project-graph-origin/node_modules/.pnpm/msw@2.7.3_@types+node@24.1.0_typescript@5.8.3/node_modules/msw/cli/node_modules:/mnt/d/ssh/learn/project-graph-origin/node_modules/.pnpm/msw@2.7.3_@types+node@24.1.0_typescript@5.8.3/node_modules/msw/node_modules:/mnt/d/ssh/learn/project-graph-origin/node_modules/.pnpm/msw@2.7.3_@types+node@24.1.0_typescript@5.8.3/node_modules:/mnt/d/ssh/learn/project-graph-origin/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/d/ssh/learn/project-graph-origin/node_modules/.pnpm/msw@2.7.3_@types+node@24.1.0_typescript@5.8.3/node_modules/msw/cli/node_modules:/mnt/d/ssh/learn/project-graph-origin/node_modules/.pnpm/msw@2.7.3_@types+node@24.1.0_typescript@5.8.3/node_modules/msw/node_modules:/mnt/d/ssh/learn/project-graph-origin/node_modules/.pnpm/msw@2.7.3_@types+node@24.1.0_typescript@5.8.3/node_modules:/mnt/d/ssh/learn/project-graph-origin/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../../node_modules/.pnpm/msw@2.7.3_@types+node@24.1.0_typescript@5.8.3/node_modules/msw/cli/index.js" "$@"
else
  exec node  "$basedir/../../../node_modules/.pnpm/msw@2.7.3_@types+node@24.1.0_typescript@5.8.3/node_modules/msw/cli/index.js" "$@"
fi
