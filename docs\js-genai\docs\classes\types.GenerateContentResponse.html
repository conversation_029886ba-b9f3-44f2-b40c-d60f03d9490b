<!DOCTYPE html><html class="default" lang="en" data-base=".."><head><meta charset="utf-8"/><meta http-equiv="x-ua-compatible" content="IE=edge"/><title>GenerateContentResponse | @google/genai</title><meta name="description" content="Documentation for @google/genai"/><meta name="viewport" content="width=device-width, initial-scale=1"/><link rel="stylesheet" href="../assets/style.css"/><link rel="stylesheet" href="../assets/highlight.css"/><script defer src="../assets/main.js"></script><script async src="../assets/icons.js" id="tsd-icons-script"></script><script async src="../assets/search.js" id="tsd-search-script"></script><script async src="../assets/navigation.js" id="tsd-nav-script"></script></head><body><script>document.documentElement.dataset.theme = localStorage.getItem("tsd-theme") || "os";document.body.style.display="none";setTimeout(() => app?app.showPage():document.body.style.removeProperty("display"),500)</script><header class="tsd-page-toolbar"><div class="tsd-toolbar-contents container"><div class="table-cell" id="tsd-search"><div class="field"><label for="tsd-search-field" class="tsd-widget tsd-toolbar-icon search no-caption"><svg width="16" height="16" viewBox="0 0 16 16" fill="none" aria-hidden="true"><use href="../assets/icons.svg#icon-search"></use></svg></label><input type="text" id="tsd-search-field" aria-label="Search"/></div><div class="field"><div id="tsd-toolbar-links"></div></div><ul class="results"><li class="state loading">Preparing search index...</li><li class="state failure">The search index is not available</li></ul><a href="../index.html" class="title">@google/genai</a></div><div class="table-cell" id="tsd-widgets"><a href="#" class="tsd-widget tsd-toolbar-icon menu no-caption" data-toggle="menu" aria-label="Menu"><svg width="16" height="16" viewBox="0 0 16 16" fill="none" aria-hidden="true"><use href="../assets/icons.svg#icon-menu"></use></svg></a></div></div></header><div class="container container-main"><div class="col-content"><div class="tsd-page-title"><ul class="tsd-breadcrumb"><li><a href="../modules.html">@google/genai</a></li><li><a href="../modules/types.html">types</a></li><li><a href="types.GenerateContentResponse.html">GenerateContentResponse</a></li></ul><h1>Class GenerateContentResponse</h1></div><section class="tsd-panel tsd-comment"><div class="tsd-comment tsd-typography"><p>Response message for PredictionService.GenerateContent.</p>
</div><div class="tsd-comment tsd-typography"></div></section><aside class="tsd-sources"><ul><li>Defined in src/types.ts:2170</li></ul></aside><section class="tsd-panel-group tsd-index-group"><section class="tsd-panel tsd-index-panel"><details class="tsd-index-content tsd-accordion" open><summary class="tsd-accordion-summary tsd-index-summary"><h5 class="tsd-index-heading uppercase" role="button" aria-expanded="false" tabIndex="0"><svg width="16" height="16" viewBox="0 0 16 16" fill="none" aria-hidden="true"><use href="../assets/icons.svg#icon-chevronSmall"></use></svg> Index</h5></summary><div class="tsd-accordion-details"><section class="tsd-index-section"><h3 class="tsd-index-heading">Constructors</h3><div class="tsd-index-list"><a href="types.GenerateContentResponse.html#constructor" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Constructor"><use href="../assets/icons.svg#icon-512"></use></svg><span>constructor</span></a>
</div></section><section class="tsd-index-section"><h3 class="tsd-index-heading">Properties</h3><div class="tsd-index-list"><a href="types.GenerateContentResponse.html#automaticfunctioncallinghistory" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>automatic<wbr/>Function<wbr/>Calling<wbr/>History?</span></a>
<a href="types.GenerateContentResponse.html#candidates" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>candidates?</span></a>
<a href="types.GenerateContentResponse.html#createtime" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>create<wbr/>Time?</span></a>
<a href="types.GenerateContentResponse.html#modelversion" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>model<wbr/>Version?</span></a>
<a href="types.GenerateContentResponse.html#promptfeedback" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>prompt<wbr/>Feedback?</span></a>
<a href="types.GenerateContentResponse.html#responseid" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>response<wbr/>Id?</span></a>
<a href="types.GenerateContentResponse.html#usagemetadata" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>usage<wbr/>Metadata?</span></a>
</div></section><section class="tsd-index-section"><h3 class="tsd-index-heading">Accessors</h3><div class="tsd-index-list"><a href="types.GenerateContentResponse.html#codeexecutionresult" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Accessor"><use href="../assets/icons.svg#icon-262144"></use></svg><span>code<wbr/>Execution<wbr/>Result</span></a>
<a href="types.GenerateContentResponse.html#data" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Accessor"><use href="../assets/icons.svg#icon-262144"></use></svg><span>data</span></a>
<a href="types.GenerateContentResponse.html#executablecode" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Accessor"><use href="../assets/icons.svg#icon-262144"></use></svg><span>executable<wbr/>Code</span></a>
<a href="types.GenerateContentResponse.html#functioncalls" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Accessor"><use href="../assets/icons.svg#icon-262144"></use></svg><span>function<wbr/>Calls</span></a>
<a href="types.GenerateContentResponse.html#text" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Accessor"><use href="../assets/icons.svg#icon-262144"></use></svg><span>text</span></a>
</div></section></div></details></section></section><details class="tsd-panel-group tsd-member-group tsd-accordion" open><summary class="tsd-accordion-summary" data-key="section-Constructors"><h2><svg width="20" height="20" viewBox="0 0 24 24" fill="none" aria-hidden="true"><use href="../assets/icons.svg#icon-chevronDown"></use></svg> Constructors</h2></summary><section><section class="tsd-panel tsd-member"><a id="constructor" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><span>constructor</span><a href="#constructor" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><ul class="tsd-signatures"><li class=""><div class="tsd-signature tsd-anchor-link"><a id="constructorgeneratecontentresponse" class="tsd-anchor"></a><span class="tsd-signature-keyword">new</span> <span class="tsd-kind-constructor-signature">GenerateContentResponse</span><span class="tsd-signature-symbol">()</span><span class="tsd-signature-symbol">:</span> <a href="types.GenerateContentResponse.html" class="tsd-signature-type tsd-kind-class">GenerateContentResponse</a><a href="#constructorgeneratecontentresponse" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></div><div class="tsd-description"><h4 class="tsd-returns-title">Returns <a href="types.GenerateContentResponse.html" class="tsd-signature-type tsd-kind-class">GenerateContentResponse</a></h4></div></li></ul></section></section></details><details class="tsd-panel-group tsd-member-group tsd-accordion" open><summary class="tsd-accordion-summary" data-key="section-Properties"><h2><svg width="20" height="20" viewBox="0 0 24 24" fill="none" aria-hidden="true"><use href="../assets/icons.svg#icon-chevronDown"></use></svg> Properties</h2></summary><section><section class="tsd-panel tsd-member"><a id="automaticfunctioncallinghistory" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><code class="tsd-tag">Optional</code><span>automatic<wbr/>Function<wbr/>Calling<wbr/>History</span><a href="#automaticfunctioncallinghistory" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-property">automaticFunctionCallingHistory</span><span class="tsd-signature-symbol">?:</span> <a href="../interfaces/types.Content.html" class="tsd-signature-type tsd-kind-interface">Content</a><span class="tsd-signature-symbol">[]</span></div><div class="tsd-comment tsd-typography"><p>The history of automatic function calling.</p>
</div><div class="tsd-comment tsd-typography"></div><aside class="tsd-sources"><ul><li>Defined in src/types.ts:2182</li></ul></aside></section><section class="tsd-panel tsd-member"><a id="candidates" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><code class="tsd-tag">Optional</code><span>candidates</span><a href="#candidates" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-property">candidates</span><span class="tsd-signature-symbol">?:</span> <a href="../interfaces/types.Candidate.html" class="tsd-signature-type tsd-kind-interface">Candidate</a><span class="tsd-signature-symbol">[]</span></div><div class="tsd-comment tsd-typography"><p>Response variations returned by the model.</p>
</div><div class="tsd-comment tsd-typography"></div><aside class="tsd-sources"><ul><li>Defined in src/types.ts:2173</li></ul></aside></section><section class="tsd-panel tsd-member"><a id="createtime" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><code class="tsd-tag">Optional</code><span>create<wbr/>Time</span><a href="#createtime" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-property">createTime</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">string</span></div><div class="tsd-comment tsd-typography"><p>Timestamp when the request is made to the server.</p>
</div><div class="tsd-comment tsd-typography"></div><aside class="tsd-sources"><ul><li>Defined in src/types.ts:2176</li></ul></aside></section><section class="tsd-panel tsd-member"><a id="modelversion" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><code class="tsd-tag">Optional</code><span>model<wbr/>Version</span><a href="#modelversion" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-property">modelVersion</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">string</span></div><div class="tsd-comment tsd-typography"><p>Output only. The model version used to generate the response.</p>
</div><div class="tsd-comment tsd-typography"></div><aside class="tsd-sources"><ul><li>Defined in src/types.ts:2184</li></ul></aside></section><section class="tsd-panel tsd-member"><a id="promptfeedback" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><code class="tsd-tag">Optional</code><span>prompt<wbr/>Feedback</span><a href="#promptfeedback" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-property">promptFeedback</span><span class="tsd-signature-symbol">?:</span> <a href="types.GenerateContentResponsePromptFeedback.html" class="tsd-signature-type tsd-kind-class">GenerateContentResponsePromptFeedback</a></div><div class="tsd-comment tsd-typography"><p>Output only. Content filter results for a prompt sent in the request. Note: Sent only in the first stream chunk. Only happens when no candidates were generated due to content violations.</p>
</div><div class="tsd-comment tsd-typography"></div><aside class="tsd-sources"><ul><li>Defined in src/types.ts:2186</li></ul></aside></section><section class="tsd-panel tsd-member"><a id="responseid" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><code class="tsd-tag">Optional</code><span>response<wbr/>Id</span><a href="#responseid" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-property">responseId</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">string</span></div><div class="tsd-comment tsd-typography"><p>Identifier for each response.</p>
</div><div class="tsd-comment tsd-typography"></div><aside class="tsd-sources"><ul><li>Defined in src/types.ts:2179</li></ul></aside></section><section class="tsd-panel tsd-member"><a id="usagemetadata" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><code class="tsd-tag">Optional</code><span>usage<wbr/>Metadata</span><a href="#usagemetadata" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-property">usageMetadata</span><span class="tsd-signature-symbol">?:</span> <a href="types.GenerateContentResponseUsageMetadata.html" class="tsd-signature-type tsd-kind-class">GenerateContentResponseUsageMetadata</a></div><div class="tsd-comment tsd-typography"><p>Usage metadata about the response(s).</p>
</div><div class="tsd-comment tsd-typography"></div><aside class="tsd-sources"><ul><li>Defined in src/types.ts:2188</li></ul></aside></section></section></details><details class="tsd-panel-group tsd-member-group tsd-accordion" open><summary class="tsd-accordion-summary" data-key="section-Accessors"><h2><svg width="20" height="20" viewBox="0 0 24 24" fill="none" aria-hidden="true"><use href="../assets/icons.svg#icon-chevronDown"></use></svg> Accessors</h2></summary><section><section class="tsd-panel tsd-member"><a id="codeexecutionresult" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><span>code<wbr/>Execution<wbr/>Result</span><a href="#codeexecutionresult" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><ul class="tsd-signatures"><li class=""><div class="tsd-signature" id="codeexecutionresult-1"><span class="tsd-signature-keyword">get</span> <span class="tsd-kind-get-signature">codeExecutionResult</span><span class="tsd-signature-symbol">()</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">undefined</span> <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">string</span></div><div class="tsd-description"><div class="tsd-comment tsd-typography"><p>Returns the first code execution result from the first candidate in the response.</p>
</div><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">undefined</span> <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">string</span></h4><div class="tsd-comment tsd-typography"><div class="tsd-tag-remarks"><h4 class="tsd-anchor-link"><a id="remarks" class="tsd-anchor"></a>Remarks<a href="#remarks" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h4><p>If there are multiple candidates in the response, the code execution result from
the first one will be returned.
If there are no code execution result in the response, undefined will be returned.</p>
</div><div class="tsd-tag-example"><h4 class="tsd-anchor-link"><a id="example" class="tsd-anchor"></a>Example<a href="#example" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h4><pre><code class="ts"><span class="hl-5">const</span><span class="hl-1"> </span><span class="hl-6">response</span><span class="hl-1"> = </span><span class="hl-3">await</span><span class="hl-1"> </span><span class="hl-4">ai</span><span class="hl-1">.</span><span class="hl-4">models</span><span class="hl-1">.</span><span class="hl-0">generateContent</span><span class="hl-1">({</span><br/><span class="hl-1">  </span><span class="hl-4">model:</span><span class="hl-1"> </span><span class="hl-2">&#39;gemini-2.0-flash&#39;</span><span class="hl-1">,</span><br/><span class="hl-1">  </span><span class="hl-4">contents:</span><br/><span class="hl-1">    </span><span class="hl-2">&#39;What is the sum of the first 50 prime numbers? Generate and run code for the calculation, and make sure you get all 50.&#39;</span><br/><span class="hl-1">  </span><span class="hl-4">config</span><span class="hl-1">: {</span><br/><span class="hl-1">    </span><span class="hl-4">tools:</span><span class="hl-1"> [{</span><span class="hl-4">codeExecution:</span><span class="hl-1"> {}}],</span><br/><span class="hl-1">  },</span><br/><span class="hl-1">});</span><br/><br/><span class="hl-4">console</span><span class="hl-1">.</span><span class="hl-0">debug</span><span class="hl-1">(</span><span class="hl-4">response</span><span class="hl-1">.</span><span class="hl-4">codeExecutionResult</span><span class="hl-1">);</span>
</code><button type="button">Copy</button></pre>

</div></div><aside class="tsd-sources"><ul><li>Defined in src/types.ts:2425</li></ul></aside></div></li></ul></section><section class="tsd-panel tsd-member"><a id="data" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><span>data</span><a href="#data" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><ul class="tsd-signatures"><li class=""><div class="tsd-signature" id="data-1"><span class="tsd-signature-keyword">get</span> <span class="tsd-kind-get-signature">data</span><span class="tsd-signature-symbol">()</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">undefined</span> <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">string</span></div><div class="tsd-description"><div class="tsd-comment tsd-typography"><p>Returns the concatenation of all inline data parts from the first candidate
in the response.</p>
</div><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">undefined</span> <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">string</span></h4><div class="tsd-comment tsd-typography"><div class="tsd-tag-remarks"><h4 class="tsd-anchor-link"><a id="remarks-1" class="tsd-anchor"></a>Remarks<a href="#remarks-1" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h4><p>If there are multiple candidates in the response, the inline data from the
first one will be returned. If there are non-inline data parts in the
response, the concatenation of all inline data parts will be returned, and
a warning will be logged.</p>
</div></div><aside class="tsd-sources"><ul><li>Defined in src/types.ts:2260</li></ul></aside></div></li></ul></section><section class="tsd-panel tsd-member"><a id="executablecode" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><span>executable<wbr/>Code</span><a href="#executablecode" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><ul class="tsd-signatures"><li class=""><div class="tsd-signature" id="executablecode-1"><span class="tsd-signature-keyword">get</span> <span class="tsd-kind-get-signature">executableCode</span><span class="tsd-signature-symbol">()</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">undefined</span> <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">string</span></div><div class="tsd-description"><div class="tsd-comment tsd-typography"><p>Returns the first executable code from the first candidate in the response.</p>
</div><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">undefined</span> <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">string</span></h4><div class="tsd-comment tsd-typography"><div class="tsd-tag-remarks"><h4 class="tsd-anchor-link"><a id="remarks-2" class="tsd-anchor"></a>Remarks<a href="#remarks-2" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h4><p>If there are multiple candidates in the response, the executable code from
the first one will be returned.
If there are no executable code in the response, undefined will be
returned.</p>
</div><div class="tsd-tag-example"><h4 class="tsd-anchor-link"><a id="example-1" class="tsd-anchor"></a>Example<a href="#example-1" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h4><pre><code class="ts"><span class="hl-5">const</span><span class="hl-1"> </span><span class="hl-6">response</span><span class="hl-1"> = </span><span class="hl-3">await</span><span class="hl-1"> </span><span class="hl-4">ai</span><span class="hl-1">.</span><span class="hl-4">models</span><span class="hl-1">.</span><span class="hl-0">generateContent</span><span class="hl-1">({</span><br/><span class="hl-1">  </span><span class="hl-4">model:</span><span class="hl-1"> </span><span class="hl-2">&#39;gemini-2.0-flash&#39;</span><span class="hl-1">,</span><br/><span class="hl-1">  </span><span class="hl-4">contents:</span><br/><span class="hl-1">    </span><span class="hl-2">&#39;What is the sum of the first 50 prime numbers? Generate and run code for the calculation, and make sure you get all 50.&#39;</span><br/><span class="hl-1">  </span><span class="hl-4">config</span><span class="hl-1">: {</span><br/><span class="hl-1">    </span><span class="hl-4">tools:</span><span class="hl-1"> [{</span><span class="hl-4">codeExecution:</span><span class="hl-1"> {}}],</span><br/><span class="hl-1">  },</span><br/><span class="hl-1">});</span><br/><br/><span class="hl-4">console</span><span class="hl-1">.</span><span class="hl-0">debug</span><span class="hl-1">(</span><span class="hl-4">response</span><span class="hl-1">.</span><span class="hl-4">executableCode</span><span class="hl-1">);</span>
</code><button type="button">Copy</button></pre>

</div></div><aside class="tsd-sources"><ul><li>Defined in src/types.ts:2381</li></ul></aside></div></li></ul></section><section class="tsd-panel tsd-member"><a id="functioncalls" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><span>function<wbr/>Calls</span><a href="#functioncalls" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><ul class="tsd-signatures"><li class=""><div class="tsd-signature" id="functioncalls-1"><span class="tsd-signature-keyword">get</span> <span class="tsd-kind-get-signature">functionCalls</span><span class="tsd-signature-symbol">()</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">undefined</span> <span class="tsd-signature-symbol">|</span> <a href="../interfaces/types.FunctionCall.html" class="tsd-signature-type tsd-kind-interface">FunctionCall</a><span class="tsd-signature-symbol">[]</span></div><div class="tsd-description"><div class="tsd-comment tsd-typography"><p>Returns the function calls from the first candidate in the response.</p>
</div><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">undefined</span> <span class="tsd-signature-symbol">|</span> <a href="../interfaces/types.FunctionCall.html" class="tsd-signature-type tsd-kind-interface">FunctionCall</a><span class="tsd-signature-symbol">[]</span></h4><div class="tsd-comment tsd-typography"><div class="tsd-tag-remarks"><h4 class="tsd-anchor-link"><a id="remarks-3" class="tsd-anchor"></a>Remarks<a href="#remarks-3" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h4><p>If there are multiple candidates in the response, the function calls from
the first one will be returned.
If there are no function calls in the response, undefined will be returned.</p>
</div><div class="tsd-tag-example"><h4 class="tsd-anchor-link"><a id="example-2" class="tsd-anchor"></a>Example<a href="#example-2" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h4><pre><code class="ts"><span class="hl-5">const</span><span class="hl-1"> </span><span class="hl-6">controlLightFunctionDeclaration</span><span class="hl-1">: </span><span class="hl-7">FunctionDeclaration</span><span class="hl-1"> = {</span><br/><span class="hl-1">  </span><span class="hl-4">name:</span><span class="hl-1"> </span><span class="hl-2">&#39;controlLight&#39;</span><span class="hl-1">,</span><br/><span class="hl-1">  </span><span class="hl-4">parameters:</span><span class="hl-1"> {</span><br/><span class="hl-1">  </span><span class="hl-4">type:</span><span class="hl-1"> </span><span class="hl-4">Type</span><span class="hl-1">.</span><span class="hl-6">OBJECT</span><span class="hl-1">,</span><br/><span class="hl-1">  </span><span class="hl-4">description:</span><span class="hl-1"> </span><span class="hl-2">&#39;Set the brightness and color temperature of a room light.&#39;</span><span class="hl-1">,</span><br/><span class="hl-1">  </span><span class="hl-4">properties:</span><span class="hl-1"> {</span><br/><span class="hl-1">    </span><span class="hl-4">brightness:</span><span class="hl-1"> {</span><br/><span class="hl-1">      </span><span class="hl-4">type:</span><span class="hl-1"> </span><span class="hl-4">Type</span><span class="hl-1">.</span><span class="hl-6">NUMBER</span><span class="hl-1">,</span><br/><span class="hl-1">      </span><span class="hl-4">description:</span><br/><span class="hl-1">        </span><span class="hl-2">&#39;Light level from 0 to 100. Zero is off and 100 is full brightness.&#39;</span><span class="hl-1">,</span><br/><span class="hl-1">    },</span><br/><span class="hl-1">    </span><span class="hl-4">colorTemperature:</span><span class="hl-1"> {</span><br/><span class="hl-1">      </span><span class="hl-4">type:</span><span class="hl-1"> </span><span class="hl-4">Type</span><span class="hl-1">.</span><span class="hl-6">STRING</span><span class="hl-1">,</span><br/><span class="hl-1">      </span><span class="hl-4">description:</span><br/><span class="hl-1">        </span><span class="hl-2">&#39;Color temperature of the light fixture which can be `daylight`, `cool` or `warm`.&#39;</span><span class="hl-1">,</span><br/><span class="hl-1">    },</span><br/><span class="hl-1">  },</span><br/><span class="hl-1">  </span><span class="hl-4">required:</span><span class="hl-1"> [</span><span class="hl-2">&#39;brightness&#39;</span><span class="hl-1">, </span><span class="hl-2">&#39;colorTemperature&#39;</span><span class="hl-1">],</span><br/><span class="hl-1"> };</span><br/><span class="hl-1"> </span><span class="hl-4">const</span><span class="hl-1"> </span><span class="hl-4">response</span><span class="hl-1"> = </span><span class="hl-3">await</span><span class="hl-1"> </span><span class="hl-4">ai</span><span class="hl-1">.</span><span class="hl-4">models</span><span class="hl-1">.</span><span class="hl-0">generateContent</span><span class="hl-1">({</span><br/><span class="hl-1">    </span><span class="hl-4">model:</span><span class="hl-1"> </span><span class="hl-2">&#39;gemini-2.0-flash&#39;</span><span class="hl-1">,</span><br/><span class="hl-1">    </span><span class="hl-4">contents:</span><span class="hl-1"> </span><span class="hl-2">&#39;Dim the lights so the room feels cozy and warm.&#39;</span><span class="hl-1">,</span><br/><span class="hl-1">    </span><span class="hl-4">config:</span><span class="hl-1"> {</span><br/><span class="hl-1">      </span><span class="hl-4">tools:</span><span class="hl-1"> [{</span><span class="hl-4">functionDeclarations:</span><span class="hl-1"> [</span><span class="hl-4">controlLightFunctionDeclaration</span><span class="hl-1">]}],</span><br/><span class="hl-1">      </span><span class="hl-4">toolConfig:</span><span class="hl-1"> {</span><br/><span class="hl-1">        </span><span class="hl-4">functionCallingConfig:</span><span class="hl-1"> {</span><br/><span class="hl-1">          </span><span class="hl-4">mode:</span><span class="hl-1"> </span><span class="hl-4">FunctionCallingConfigMode</span><span class="hl-1">.</span><span class="hl-6">ANY</span><span class="hl-1">,</span><br/><span class="hl-1">          </span><span class="hl-4">allowedFunctionNames:</span><span class="hl-1"> [</span><span class="hl-2">&#39;controlLight&#39;</span><span class="hl-1">],</span><br/><span class="hl-1">        },</span><br/><span class="hl-1">      },</span><br/><span class="hl-1">    },</span><br/><span class="hl-1">  });</span><br/><span class="hl-1"> </span><span class="hl-4">console</span><span class="hl-1">.</span><span class="hl-0">debug</span><span class="hl-1">(</span><span class="hl-6">JSON</span><span class="hl-1">.</span><span class="hl-0">stringify</span><span class="hl-1">(</span><span class="hl-4">response</span><span class="hl-1">.</span><span class="hl-4">functionCalls</span><span class="hl-1">));</span>
</code><button type="button">Copy</button></pre>

</div></div><aside class="tsd-sources"><ul><li>Defined in src/types.ts:2337</li></ul></aside></div></li></ul></section><section class="tsd-panel tsd-member"><a id="text" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><span>text</span><a href="#text" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><ul class="tsd-signatures"><li class=""><div class="tsd-signature" id="text-1"><span class="tsd-signature-keyword">get</span> <span class="tsd-kind-get-signature">text</span><span class="tsd-signature-symbol">()</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">undefined</span> <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">string</span></div><div class="tsd-description"><div class="tsd-comment tsd-typography"><p>Returns the concatenation of all text parts from the first candidate in the response.</p>
</div><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">undefined</span> <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">string</span></h4><div class="tsd-comment tsd-typography"><div class="tsd-tag-remarks"><h4 class="tsd-anchor-link"><a id="remarks-4" class="tsd-anchor"></a>Remarks<a href="#remarks-4" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h4><p>If there are multiple candidates in the response, the text from the first
one will be returned.
If there are non-text parts in the response, the concatenation of all text
parts will be returned, and a warning will be logged.
If there are thought parts in the response, the concatenation of all text
parts excluding the thought parts will be returned.</p>
</div><div class="tsd-tag-example"><h4 class="tsd-anchor-link"><a id="example-3" class="tsd-anchor"></a>Example<a href="#example-3" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h4><pre><code class="ts"><span class="hl-5">const</span><span class="hl-1"> </span><span class="hl-6">response</span><span class="hl-1"> = </span><span class="hl-3">await</span><span class="hl-1"> </span><span class="hl-4">ai</span><span class="hl-1">.</span><span class="hl-4">models</span><span class="hl-1">.</span><span class="hl-0">generateContent</span><span class="hl-1">({</span><br/><span class="hl-1">  </span><span class="hl-4">model:</span><span class="hl-1"> </span><span class="hl-2">&#39;gemini-2.0-flash&#39;</span><span class="hl-1">,</span><br/><span class="hl-1">  </span><span class="hl-4">contents:</span><br/><span class="hl-1">    </span><span class="hl-2">&#39;Why is the sky blue?&#39;</span><span class="hl-1">,</span><br/><span class="hl-1">});</span><br/><br/><span class="hl-4">console</span><span class="hl-1">.</span><span class="hl-0">debug</span><span class="hl-1">(</span><span class="hl-4">response</span><span class="hl-1">.</span><span class="hl-4">text</span><span class="hl-1">);</span>
</code><button type="button">Copy</button></pre>

</div></div><aside class="tsd-sources"><ul><li>Defined in src/types.ts:2211</li></ul></aside></div></li></ul></section></section></details></div><div class="col-sidebar"><div class="page-menu"><div class="tsd-navigation settings"><details class="tsd-accordion"><summary class="tsd-accordion-summary"><h3><svg width="20" height="20" viewBox="0 0 24 24" fill="none" aria-hidden="true"><use href="../assets/icons.svg#icon-chevronDown"></use></svg>Settings</h3></summary><div class="tsd-accordion-details"><div class="tsd-theme-toggle"><label class="settings-label" for="tsd-theme">Theme</label><select id="tsd-theme"><option value="os">OS</option><option value="light">Light</option><option value="dark">Dark</option></select></div></div></details></div><details open class="tsd-accordion tsd-page-navigation"><summary class="tsd-accordion-summary"><h3><svg width="20" height="20" viewBox="0 0 24 24" fill="none" aria-hidden="true"><use href="../assets/icons.svg#icon-chevronDown"></use></svg>On This Page</h3></summary><div class="tsd-accordion-details"><details open class="tsd-accordion tsd-page-navigation-section"><summary class="tsd-accordion-summary" data-key="section-Constructors"><svg width="20" height="20" viewBox="0 0 24 24" fill="none" aria-hidden="true"><use href="../assets/icons.svg#icon-chevronDown"></use></svg>Constructors</summary><div><a href="#constructor" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Constructor"><use href="../assets/icons.svg#icon-512"></use></svg><span>constructor</span></a></div></details><details open class="tsd-accordion tsd-page-navigation-section"><summary class="tsd-accordion-summary" data-key="section-Properties"><svg width="20" height="20" viewBox="0 0 24 24" fill="none" aria-hidden="true"><use href="../assets/icons.svg#icon-chevronDown"></use></svg>Properties</summary><div><a href="#automaticfunctioncallinghistory" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>automatic<wbr/>Function<wbr/>Calling<wbr/>History</span></a><a href="#candidates" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>candidates</span></a><a href="#createtime" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>create<wbr/>Time</span></a><a href="#modelversion" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>model<wbr/>Version</span></a><a href="#promptfeedback" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>prompt<wbr/>Feedback</span></a><a href="#responseid" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>response<wbr/>Id</span></a><a href="#usagemetadata" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>usage<wbr/>Metadata</span></a></div></details><details open class="tsd-accordion tsd-page-navigation-section"><summary class="tsd-accordion-summary" data-key="section-Accessors"><svg width="20" height="20" viewBox="0 0 24 24" fill="none" aria-hidden="true"><use href="../assets/icons.svg#icon-chevronDown"></use></svg>Accessors</summary><div><a href="#codeexecutionresult" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Accessor"><use href="../assets/icons.svg#icon-262144"></use></svg><span>code<wbr/>Execution<wbr/>Result</span></a><a href="#data" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Accessor"><use href="../assets/icons.svg#icon-262144"></use></svg><span>data</span></a><a href="#executablecode" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Accessor"><use href="../assets/icons.svg#icon-262144"></use></svg><span>executable<wbr/>Code</span></a><a href="#functioncalls" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Accessor"><use href="../assets/icons.svg#icon-262144"></use></svg><span>function<wbr/>Calls</span></a><a href="#text" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Accessor"><use href="../assets/icons.svg#icon-262144"></use></svg><span>text</span></a></div></details></div></details></div><div class="site-menu"><nav class="tsd-navigation"><a href="../modules.html">@google/genai</a><ul class="tsd-small-nested-navigation" id="tsd-nav-container"><li>Loading...</li></ul></nav></div></div></div><footer><p class="tsd-generator">Generated using <a href="https://typedoc.org/" target="_blank">TypeDoc</a></p></footer><div class="overlay"></div></body></html>
