<!DOCTYPE html><html class="default" lang="en" data-base=".."><head><meta charset="utf-8"/><meta http-equiv="x-ua-compatible" content="IE=edge"/><title>LiveServerContent | @google/genai</title><meta name="description" content="Documentation for @google/genai"/><meta name="viewport" content="width=device-width, initial-scale=1"/><link rel="stylesheet" href="../assets/style.css"/><link rel="stylesheet" href="../assets/highlight.css"/><script defer src="../assets/main.js"></script><script async src="../assets/icons.js" id="tsd-icons-script"></script><script async src="../assets/search.js" id="tsd-search-script"></script><script async src="../assets/navigation.js" id="tsd-nav-script"></script></head><body><script>document.documentElement.dataset.theme = localStorage.getItem("tsd-theme") || "os";document.body.style.display="none";setTimeout(() => app?app.showPage():document.body.style.removeProperty("display"),500)</script><header class="tsd-page-toolbar"><div class="tsd-toolbar-contents container"><div class="table-cell" id="tsd-search"><div class="field"><label for="tsd-search-field" class="tsd-widget tsd-toolbar-icon search no-caption"><svg width="16" height="16" viewBox="0 0 16 16" fill="none" aria-hidden="true"><use href="../assets/icons.svg#icon-search"></use></svg></label><input type="text" id="tsd-search-field" aria-label="Search"/></div><div class="field"><div id="tsd-toolbar-links"></div></div><ul class="results"><li class="state loading">Preparing search index...</li><li class="state failure">The search index is not available</li></ul><a href="../index.html" class="title">@google/genai</a></div><div class="table-cell" id="tsd-widgets"><a href="#" class="tsd-widget tsd-toolbar-icon menu no-caption" data-toggle="menu" aria-label="Menu"><svg width="16" height="16" viewBox="0 0 16 16" fill="none" aria-hidden="true"><use href="../assets/icons.svg#icon-menu"></use></svg></a></div></div></header><div class="container container-main"><div class="col-content"><div class="tsd-page-title"><ul class="tsd-breadcrumb"><li><a href="../modules.html">@google/genai</a></li><li><a href="../modules/types.html">types</a></li><li><a href="types.LiveServerContent.html">LiveServerContent</a></li></ul><h1>Interface LiveServerContent</h1></div><section class="tsd-panel tsd-comment"><div class="tsd-comment tsd-typography"><p>Incremental server update generated by the model in response to client messages.</p>
<p>Content is generated as quickly as possible, and not in real time. Clients
may choose to buffer and play it out in real time.</p>
</div><div class="tsd-comment tsd-typography"></div></section><div class="tsd-signature"><span class="tsd-signature-keyword">interface</span> <span class="tsd-kind-interface">LiveServerContent</span> <span class="tsd-signature-symbol">{</span><br/>    <a class="tsd-kind-property" href="types.LiveServerContent.html#generationcomplete">generationComplete</a><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">;</span><br/>    <a class="tsd-kind-property" href="types.LiveServerContent.html#groundingmetadata">groundingMetadata</a><span class="tsd-signature-symbol">?:</span> <a href="types.GroundingMetadata.html" class="tsd-signature-type tsd-kind-interface">GroundingMetadata</a><span class="tsd-signature-symbol">;</span><br/>    <a class="tsd-kind-property" href="types.LiveServerContent.html#inputtranscription">inputTranscription</a><span class="tsd-signature-symbol">?:</span> <a href="types.Transcription.html" class="tsd-signature-type tsd-kind-interface">Transcription</a><span class="tsd-signature-symbol">;</span><br/>    <a class="tsd-kind-property" href="types.LiveServerContent.html#interrupted">interrupted</a><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">;</span><br/>    <a class="tsd-kind-property" href="types.LiveServerContent.html#modelturn">modelTurn</a><span class="tsd-signature-symbol">?:</span> <a href="types.Content.html" class="tsd-signature-type tsd-kind-interface">Content</a><span class="tsd-signature-symbol">;</span><br/>    <a class="tsd-kind-property" href="types.LiveServerContent.html#outputtranscription">outputTranscription</a><span class="tsd-signature-symbol">?:</span> <a href="types.Transcription.html" class="tsd-signature-type tsd-kind-interface">Transcription</a><span class="tsd-signature-symbol">;</span><br/>    <a class="tsd-kind-property" href="types.LiveServerContent.html#turncomplete">turnComplete</a><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">;</span><br/>    <a class="tsd-kind-property" href="types.LiveServerContent.html#urlcontextmetadata">urlContextMetadata</a><span class="tsd-signature-symbol">?:</span> <a href="types.UrlContextMetadata.html" class="tsd-signature-type tsd-kind-interface">UrlContextMetadata</a><span class="tsd-signature-symbol">;</span><br/><span class="tsd-signature-symbol">}</span></div><aside class="tsd-sources"><ul><li>Defined in src/types.ts:4540</li></ul></aside><section class="tsd-panel-group tsd-index-group"><section class="tsd-panel tsd-index-panel"><details class="tsd-index-content tsd-accordion" open><summary class="tsd-accordion-summary tsd-index-summary"><h5 class="tsd-index-heading uppercase" role="button" aria-expanded="false" tabIndex="0"><svg width="16" height="16" viewBox="0 0 16 16" fill="none" aria-hidden="true"><use href="../assets/icons.svg#icon-chevronSmall"></use></svg> Index</h5></summary><div class="tsd-accordion-details"><section class="tsd-index-section"><h3 class="tsd-index-heading">Properties</h3><div class="tsd-index-list"><a href="types.LiveServerContent.html#generationcomplete" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>generation<wbr/>Complete?</span></a>
<a href="types.LiveServerContent.html#groundingmetadata" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>grounding<wbr/>Metadata?</span></a>
<a href="types.LiveServerContent.html#inputtranscription" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>input<wbr/>Transcription?</span></a>
<a href="types.LiveServerContent.html#interrupted" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>interrupted?</span></a>
<a href="types.LiveServerContent.html#modelturn" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>model<wbr/>Turn?</span></a>
<a href="types.LiveServerContent.html#outputtranscription" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>output<wbr/>Transcription?</span></a>
<a href="types.LiveServerContent.html#turncomplete" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>turn<wbr/>Complete?</span></a>
<a href="types.LiveServerContent.html#urlcontextmetadata" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>url<wbr/>Context<wbr/>Metadata?</span></a>
</div></section></div></details></section></section><details class="tsd-panel-group tsd-member-group tsd-accordion" open><summary class="tsd-accordion-summary" data-key="section-Properties"><h2><svg width="20" height="20" viewBox="0 0 24 24" fill="none" aria-hidden="true"><use href="../assets/icons.svg#icon-chevronDown"></use></svg> Properties</h2></summary><section><section class="tsd-panel tsd-member"><a id="generationcomplete" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><code class="tsd-tag">Optional</code><span>generation<wbr/>Complete</span><a href="#generationcomplete" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-property">generationComplete</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">boolean</span></div><div class="tsd-comment tsd-typography"><p>If true, indicates that the model is done generating. When model is
interrupted while generating there will be no generation_complete message
in interrupted turn, it will go through interrupted &gt; turn_complete.
When model assumes realtime playback there will be delay between
generation_complete and turn_complete that is caused by model
waiting for playback to finish. If true, indicates that the model
has finished generating all content. This is a signal to the client
that it can stop sending messages.</p>
</div><div class="tsd-comment tsd-typography"></div><aside class="tsd-sources"><ul><li>Defined in src/types.ts:4557</li></ul></aside></section><section class="tsd-panel tsd-member"><a id="groundingmetadata" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><code class="tsd-tag">Optional</code><span>grounding<wbr/>Metadata</span><a href="#groundingmetadata" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-property">groundingMetadata</span><span class="tsd-signature-symbol">?:</span> <a href="types.GroundingMetadata.html" class="tsd-signature-type tsd-kind-interface">GroundingMetadata</a></div><div class="tsd-comment tsd-typography"><p>Metadata returned to client when grounding is enabled.</p>
</div><div class="tsd-comment tsd-typography"></div><aside class="tsd-sources"><ul><li>Defined in src/types.ts:4548</li></ul></aside></section><section class="tsd-panel tsd-member"><a id="inputtranscription" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><code class="tsd-tag">Optional</code><span>input<wbr/>Transcription</span><a href="#inputtranscription" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-property">inputTranscription</span><span class="tsd-signature-symbol">?:</span> <a href="types.Transcription.html" class="tsd-signature-type tsd-kind-interface">Transcription</a></div><div class="tsd-comment tsd-typography"><p>Input transcription. The transcription is independent to the model
turn which means it doesn’t imply any ordering between transcription and
model turn.</p>
</div><div class="tsd-comment tsd-typography"></div><aside class="tsd-sources"><ul><li>Defined in src/types.ts:4561</li></ul></aside></section><section class="tsd-panel tsd-member"><a id="interrupted" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><code class="tsd-tag">Optional</code><span>interrupted</span><a href="#interrupted" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-property">interrupted</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">boolean</span></div><div class="tsd-comment tsd-typography"><p>If true, indicates that a client message has interrupted current model generation. If the client is playing out the content in realtime, this is a good signal to stop and empty the current queue.</p>
</div><div class="tsd-comment tsd-typography"></div><aside class="tsd-sources"><ul><li>Defined in src/types.ts:4546</li></ul></aside></section><section class="tsd-panel tsd-member"><a id="modelturn" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><code class="tsd-tag">Optional</code><span>model<wbr/>Turn</span><a href="#modelturn" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-property">modelTurn</span><span class="tsd-signature-symbol">?:</span> <a href="types.Content.html" class="tsd-signature-type tsd-kind-interface">Content</a></div><div class="tsd-comment tsd-typography"><p>The content that the model has generated as part of the current conversation with the user.</p>
</div><div class="tsd-comment tsd-typography"></div><aside class="tsd-sources"><ul><li>Defined in src/types.ts:4542</li></ul></aside></section><section class="tsd-panel tsd-member"><a id="outputtranscription" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><code class="tsd-tag">Optional</code><span>output<wbr/>Transcription</span><a href="#outputtranscription" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-property">outputTranscription</span><span class="tsd-signature-symbol">?:</span> <a href="types.Transcription.html" class="tsd-signature-type tsd-kind-interface">Transcription</a></div><div class="tsd-comment tsd-typography"><p>Output transcription. The transcription is independent to the model
turn which means it doesn’t imply any ordering between transcription and
model turn.</p>
</div><div class="tsd-comment tsd-typography"></div><aside class="tsd-sources"><ul><li>Defined in src/types.ts:4566</li></ul></aside></section><section class="tsd-panel tsd-member"><a id="turncomplete" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><code class="tsd-tag">Optional</code><span>turn<wbr/>Complete</span><a href="#turncomplete" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-property">turnComplete</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">boolean</span></div><div class="tsd-comment tsd-typography"><p>If true, indicates that the model is done generating. Generation will only start in response to additional client messages. Can be set alongside <code>content</code>, indicating that the <code>content</code> is the last in the turn.</p>
</div><div class="tsd-comment tsd-typography"></div><aside class="tsd-sources"><ul><li>Defined in src/types.ts:4544</li></ul></aside></section><section class="tsd-panel tsd-member"><a id="urlcontextmetadata" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><code class="tsd-tag">Optional</code><span>url<wbr/>Context<wbr/>Metadata</span><a href="#urlcontextmetadata" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-property">urlContextMetadata</span><span class="tsd-signature-symbol">?:</span> <a href="types.UrlContextMetadata.html" class="tsd-signature-type tsd-kind-interface">UrlContextMetadata</a></div><div class="tsd-comment tsd-typography"><p>Metadata related to url context retrieval tool.</p>
</div><div class="tsd-comment tsd-typography"></div><aside class="tsd-sources"><ul><li>Defined in src/types.ts:4568</li></ul></aside></section></section></details></div><div class="col-sidebar"><div class="page-menu"><div class="tsd-navigation settings"><details class="tsd-accordion"><summary class="tsd-accordion-summary"><h3><svg width="20" height="20" viewBox="0 0 24 24" fill="none" aria-hidden="true"><use href="../assets/icons.svg#icon-chevronDown"></use></svg>Settings</h3></summary><div class="tsd-accordion-details"><div class="tsd-theme-toggle"><label class="settings-label" for="tsd-theme">Theme</label><select id="tsd-theme"><option value="os">OS</option><option value="light">Light</option><option value="dark">Dark</option></select></div></div></details></div><details open class="tsd-accordion tsd-page-navigation"><summary class="tsd-accordion-summary"><h3><svg width="20" height="20" viewBox="0 0 24 24" fill="none" aria-hidden="true"><use href="../assets/icons.svg#icon-chevronDown"></use></svg>On This Page</h3></summary><div class="tsd-accordion-details"><details open class="tsd-accordion tsd-page-navigation-section"><summary class="tsd-accordion-summary" data-key="section-Properties"><svg width="20" height="20" viewBox="0 0 24 24" fill="none" aria-hidden="true"><use href="../assets/icons.svg#icon-chevronDown"></use></svg>Properties</summary><div><a href="#generationcomplete" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>generation<wbr/>Complete</span></a><a href="#groundingmetadata" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>grounding<wbr/>Metadata</span></a><a href="#inputtranscription" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>input<wbr/>Transcription</span></a><a href="#interrupted" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>interrupted</span></a><a href="#modelturn" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>model<wbr/>Turn</span></a><a href="#outputtranscription" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>output<wbr/>Transcription</span></a><a href="#turncomplete" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>turn<wbr/>Complete</span></a><a href="#urlcontextmetadata" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>url<wbr/>Context<wbr/>Metadata</span></a></div></details></div></details></div><div class="site-menu"><nav class="tsd-navigation"><a href="../modules.html">@google/genai</a><ul class="tsd-small-nested-navigation" id="tsd-nav-container"><li>Loading...</li></ul></nav></div></div></div><footer><p class="tsd-generator">Generated using <a href="https://typedoc.org/" target="_blank">TypeDoc</a></p></footer><div class="overlay"></div></body></html>
