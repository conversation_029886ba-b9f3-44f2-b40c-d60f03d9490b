var Ed=Object.defineProperty;var Td=(e,t,r)=>t in e?Ed(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r;var Nn=(e,t,r)=>Td(e,typeof t!="symbol"?t+"":t,r);import{u as Dd,S as wt}from"./index-dHdkUThP.js";function j(e,t,r,i,n){if(typeof t=="function"?e!==t||!0:!t.has(e))throw new TypeError("Cannot write private member to an object whose class did not declare it");return t.set(e,r),r}function f(e,t,r,i){if(r==="a"&&!i)throw new TypeError("Private accessor was defined without a getter");if(typeof t=="function"?e!==t||!i:!t.has(e))throw new TypeError("Cannot read private member from an object whose class did not declare it");return r==="m"?i:r==="a"?i.call(e):i?i.value:t.get(e)}let Go=function(){const{crypto:e}=globalThis;if(e!=null&&e.randomUUID)return Go=e.randomUUID.bind(e),e.randomUUID();const t=new Uint8Array(1),r=e?()=>e.getRandomValues(t)[0]:()=>Math.random()*255&255;return"10000000-1000-4000-8000-100000000000".replace(/[018]/g,i=>(+i^r()&15>>+i/4).toString(16))};function Zn(e){return typeof e=="object"&&e!==null&&("name"in e&&e.name==="AbortError"||"message"in e&&String(e.message).includes("FetchRequestCanceledException"))}const Ln=e=>{if(e instanceof Error)return e;if(typeof e=="object"&&e!==null){try{if(Object.prototype.toString.call(e)==="[object Error]"){const t=new Error(e.message,e.cause?{cause:e.cause}:{});return e.stack&&(t.stack=e.stack),e.cause&&!t.cause&&(t.cause=e.cause),e.name&&(t.name=e.name),t}}catch{}try{return new Error(JSON.stringify(e))}catch{}}return new Error(e)};class U extends Error{}class Y extends U{constructor(t,r,i,n){super(`${Y.makeMessage(t,r,i)}`),this.status=t,this.headers=n,this.requestID=n==null?void 0:n.get("x-request-id"),this.error=r;const o=r;this.code=o==null?void 0:o.code,this.param=o==null?void 0:o.param,this.type=o==null?void 0:o.type}static makeMessage(t,r,i){const n=r!=null&&r.message?typeof r.message=="string"?r.message:JSON.stringify(r.message):r?JSON.stringify(r):i;return t&&n?`${t} ${n}`:t?`${t} status code (no body)`:n||"(no status code or body)"}static generate(t,r,i,n){if(!t||!n)return new on({message:i,cause:Ln(r)});const o=r==null?void 0:r.error;return t===400?new Ho(t,o,i,n):t===401?new Yo(t,o,i,n):t===403?new Qo(t,o,i,n):t===404?new ea(t,o,i,n):t===409?new ta(t,o,i,n):t===422?new na(t,o,i,n):t===429?new ra(t,o,i,n):t>=500?new ia(t,o,i,n):new Y(t,o,i,n)}}class ae extends Y{constructor({message:t}={}){super(void 0,void 0,t||"Request was aborted.",void 0)}}class on extends Y{constructor({message:t,cause:r}){super(void 0,void 0,t||"Connection error.",void 0),r&&(this.cause=r)}}class or extends on{constructor({message:t}={}){super({message:t??"Request timed out."})}}class Ho extends Y{}class Yo extends Y{}class Qo extends Y{}class ea extends Y{}class ta extends Y{}class na extends Y{}class ra extends Y{}class ia extends Y{}class oa extends U{constructor(){super("Could not parse response content as the length limit was reached")}}class aa extends U{constructor(){super("Could not parse response content as the request was rejected by the content filter")}}class Ke extends Error{constructor(t){super(t)}}const Rd=/^[a-z][a-z0-9+.-]*:/i,Cd=e=>Rd.test(e);let te=e=>(te=Array.isArray,te(e)),so=te;function Zd(e){return typeof e!="object"?{}:e??{}}function Ld(e){if(!e)return!0;for(const t in e)return!1;return!0}function Md(e,t){return Object.prototype.hasOwnProperty.call(e,t)}function jn(e){return e!=null&&typeof e=="object"&&!Array.isArray(e)}const Fd=(e,t)=>{if(typeof t!="number"||!Number.isInteger(t))throw new U(`${e} must be an integer`);if(t<0)throw new U(`${e} must be a positive integer`);return t},Bd=e=>{try{return JSON.parse(e)}catch{return}},ht=e=>new Promise(t=>setTimeout(t,e)),Te="5.10.2",Jd=()=>typeof window<"u"&&typeof window.document<"u"&&typeof navigator<"u";function Wd(){return typeof Deno<"u"&&Deno.build!=null?"deno":typeof EdgeRuntime<"u"?"edge":Object.prototype.toString.call(typeof globalThis.process<"u"?globalThis.process:0)==="[object process]"?"node":"unknown"}const qd=()=>{var r;const e=Wd();if(e==="deno")return{"X-Stainless-Lang":"js","X-Stainless-Package-Version":Te,"X-Stainless-OS":co(Deno.build.os),"X-Stainless-Arch":uo(Deno.build.arch),"X-Stainless-Runtime":"deno","X-Stainless-Runtime-Version":typeof Deno.version=="string"?Deno.version:((r=Deno.version)==null?void 0:r.deno)??"unknown"};if(typeof EdgeRuntime<"u")return{"X-Stainless-Lang":"js","X-Stainless-Package-Version":Te,"X-Stainless-OS":"Unknown","X-Stainless-Arch":`other:${EdgeRuntime}`,"X-Stainless-Runtime":"edge","X-Stainless-Runtime-Version":globalThis.process.version};if(e==="node")return{"X-Stainless-Lang":"js","X-Stainless-Package-Version":Te,"X-Stainless-OS":co(globalThis.process.platform??"unknown"),"X-Stainless-Arch":uo(globalThis.process.arch??"unknown"),"X-Stainless-Runtime":"node","X-Stainless-Runtime-Version":globalThis.process.version??"unknown"};const t=Vd();return t?{"X-Stainless-Lang":"js","X-Stainless-Package-Version":Te,"X-Stainless-OS":"Unknown","X-Stainless-Arch":"unknown","X-Stainless-Runtime":`browser:${t.browser}`,"X-Stainless-Runtime-Version":t.version}:{"X-Stainless-Lang":"js","X-Stainless-Package-Version":Te,"X-Stainless-OS":"Unknown","X-Stainless-Arch":"unknown","X-Stainless-Runtime":"unknown","X-Stainless-Runtime-Version":"unknown"}};function Vd(){if(typeof navigator>"u"||!navigator)return null;const e=[{key:"edge",pattern:/Edge(?:\W+(\d+)\.(\d+)(?:\.(\d+))?)?/},{key:"ie",pattern:/MSIE(?:\W+(\d+)\.(\d+)(?:\.(\d+))?)?/},{key:"ie",pattern:/Trident(?:.*rv\:(\d+)\.(\d+)(?:\.(\d+))?)?/},{key:"chrome",pattern:/Chrome(?:\W+(\d+)\.(\d+)(?:\.(\d+))?)?/},{key:"firefox",pattern:/Firefox(?:\W+(\d+)\.(\d+)(?:\.(\d+))?)?/},{key:"safari",pattern:/(?:Version\W+(\d+)\.(\d+)(?:\.(\d+))?)?(?:\W+Mobile\S*)?\W+Safari/}];for(const{key:t,pattern:r}of e){const i=r.exec(navigator.userAgent);if(i){const n=i[1]||0,o=i[2]||0,a=i[3]||0;return{browser:t,version:`${n}.${o}.${a}`}}}return null}const uo=e=>e==="x32"?"x32":e==="x86_64"||e==="x64"?"x64":e==="arm"?"arm":e==="aarch64"||e==="arm64"?"arm64":e?`other:${e}`:"unknown",co=e=>(e=e.toLowerCase(),e.includes("ios")?"iOS":e==="android"?"Android":e==="darwin"?"MacOS":e==="win32"?"Windows":e==="freebsd"?"FreeBSD":e==="openbsd"?"OpenBSD":e==="linux"?"Linux":e?`Other:${e}`:"Unknown");let lo;const Xd=()=>lo??(lo=qd());function Kd(){if(typeof fetch<"u")return fetch;throw new Error("`fetch` is not defined as a global; Either pass `fetch` to the client, `new OpenAI({ fetch })` or polyfill the global, `globalThis.fetch = fetch`")}function sa(...e){const t=globalThis.ReadableStream;if(typeof t>"u")throw new Error("`ReadableStream` is not defined as a global; You will need to polyfill it, `globalThis.ReadableStream = ReadableStream`");return new t(...e)}function ua(e){let t=Symbol.asyncIterator in e?e[Symbol.asyncIterator]():e[Symbol.iterator]();return sa({start(){},async pull(r){const{done:i,value:n}=await t.next();i?r.close():r.enqueue(n)},async cancel(){var r;await((r=t.return)==null?void 0:r.call(t))}})}function ca(e){if(e[Symbol.asyncIterator])return e;const t=e.getReader();return{async next(){try{const r=await t.read();return r!=null&&r.done&&t.releaseLock(),r}catch(r){throw t.releaseLock(),r}},async return(){const r=t.cancel();return t.releaseLock(),await r,{done:!0,value:void 0}},[Symbol.asyncIterator](){return this}}}async function Gd(e){var i,n;if(e===null||typeof e!="object")return;if(e[Symbol.asyncIterator]){await((n=(i=e[Symbol.asyncIterator]()).return)==null?void 0:n.call(i));return}const t=e.getReader(),r=t.cancel();t.releaseLock(),await r}const Hd=({headers:e,body:t})=>({bodyHeaders:{"content-type":"application/json"},body:JSON.stringify(t)}),la="RFC3986",da=e=>String(e),fo={RFC1738:e=>String(e).replace(/%20/g,"+"),RFC3986:da},Yd="RFC1738";let Mn=(e,t)=>(Mn=Object.hasOwn??Function.prototype.call.bind(Object.prototype.hasOwnProperty),Mn(e,t));const de=(()=>{const e=[];for(let t=0;t<256;++t)e.push("%"+((t<16?"0":"")+t.toString(16)).toUpperCase());return e})(),Un=1024,Qd=(e,t,r,i,n)=>{if(e.length===0)return e;let o=e;if(typeof e=="symbol"?o=Symbol.prototype.toString.call(e):typeof e!="string"&&(o=String(e)),r==="iso-8859-1")return escape(o).replace(/%u[0-9a-f]{4}/gi,function(s){return"%26%23"+parseInt(s.slice(2),16)+"%3B"});let a="";for(let s=0;s<o.length;s+=Un){const u=o.length>=Un?o.slice(s,s+Un):o,c=[];for(let p=0;p<u.length;++p){let d=u.charCodeAt(p);if(d===45||d===46||d===95||d===126||d>=48&&d<=57||d>=65&&d<=90||d>=97&&d<=122||n===Yd&&(d===40||d===41)){c[c.length]=u.charAt(p);continue}if(d<128){c[c.length]=de[d];continue}if(d<2048){c[c.length]=de[192|d>>6]+de[128|d&63];continue}if(d<55296||d>=57344){c[c.length]=de[224|d>>12]+de[128|d>>6&63]+de[128|d&63];continue}p+=1,d=65536+((d&1023)<<10|u.charCodeAt(p)&1023),c[c.length]=de[240|d>>18]+de[128|d>>12&63]+de[128|d>>6&63]+de[128|d&63]}a+=c.join("")}return a};function ef(e){return!e||typeof e!="object"?!1:!!(e.constructor&&e.constructor.isBuffer&&e.constructor.isBuffer(e))}function mo(e,t){if(te(e)){const r=[];for(let i=0;i<e.length;i+=1)r.push(t(e[i]));return r}return t(e)}const fa={brackets(e){return String(e)+"[]"},comma:"comma",indices(e,t){return String(e)+"["+t+"]"},repeat(e){return String(e)}},ma=function(e,t){Array.prototype.push.apply(e,te(t)?t:[t])};let ho;const X={addQueryPrefix:!1,allowDots:!1,allowEmptyArrays:!1,arrayFormat:"indices",charset:"utf-8",charsetSentinel:!1,delimiter:"&",encode:!0,encodeDotInKeys:!1,encoder:Qd,encodeValuesOnly:!1,format:la,formatter:da,indices:!1,serializeDate(e){return(ho??(ho=Function.prototype.call.bind(Date.prototype.toISOString)))(e)},skipNulls:!1,strictNullHandling:!1};function tf(e){return typeof e=="string"||typeof e=="number"||typeof e=="boolean"||typeof e=="symbol"||typeof e=="bigint"}const Pn={};function ha(e,t,r,i,n,o,a,s,u,c,p,d,y,v,_,S,b,m){let h=e,g=m,w=0,x=!1;for(;(g=g.get(Pn))!==void 0&&!x;){const B=g.get(e);if(w+=1,typeof B<"u"){if(B===w)throw new RangeError("Cyclic object value");x=!0}typeof g.get(Pn)>"u"&&(w=0)}if(typeof c=="function"?h=c(t,h):h instanceof Date?h=y==null?void 0:y(h):r==="comma"&&te(h)&&(h=mo(h,function(B){return B instanceof Date?y==null?void 0:y(B):B})),h===null){if(o)return u&&!S?u(t,X.encoder,b,"key",v):t;h=""}if(tf(h)||ef(h)){if(u){const B=S?t:u(t,X.encoder,b,"key",v);return[(_==null?void 0:_(B))+"="+(_==null?void 0:_(u(h,X.encoder,b,"value",v)))]}return[(_==null?void 0:_(t))+"="+(_==null?void 0:_(String(h)))]}const z=[];if(typeof h>"u")return z;let N;if(r==="comma"&&te(h))S&&u&&(h=mo(h,u)),N=[{value:h.length>0?h.join(",")||null:void 0}];else if(te(c))N=c;else{const B=Object.keys(h);N=p?B.sort(p):B}const F=s?String(t).replace(/\./g,"%2E"):String(t),C=i&&te(h)&&h.length===1?F+"[]":F;if(n&&te(h)&&h.length===0)return C+"[]";for(let B=0;B<N.length;++B){const Z=N[B],Ue=typeof Z=="object"&&typeof Z.value<"u"?Z.value:h[Z];if(a&&Ue===null)continue;const On=d&&s?Z.replace(/\./g,"%2E"):Z,Ad=te(h)?typeof r=="function"?r(C,On):C:C+(d?"."+On:"["+On+"]");m.set(e,w);const ao=new WeakMap;ao.set(Pn,m),ma(z,ha(Ue,Ad,r,i,n,o,a,s,r==="comma"&&S&&te(h)?null:u,c,p,d,y,v,_,S,b,ao))}return z}function nf(e=X){if(typeof e.allowEmptyArrays<"u"&&typeof e.allowEmptyArrays!="boolean")throw new TypeError("`allowEmptyArrays` option can only be `true` or `false`, when provided");if(typeof e.encodeDotInKeys<"u"&&typeof e.encodeDotInKeys!="boolean")throw new TypeError("`encodeDotInKeys` option can only be `true` or `false`, when provided");if(e.encoder!==null&&typeof e.encoder<"u"&&typeof e.encoder!="function")throw new TypeError("Encoder has to be a function.");const t=e.charset||X.charset;if(typeof e.charset<"u"&&e.charset!=="utf-8"&&e.charset!=="iso-8859-1")throw new TypeError("The charset option must be either utf-8, iso-8859-1, or undefined");let r=la;if(typeof e.format<"u"){if(!Mn(fo,e.format))throw new TypeError("Unknown format option provided.");r=e.format}const i=fo[r];let n=X.filter;(typeof e.filter=="function"||te(e.filter))&&(n=e.filter);let o;if(e.arrayFormat&&e.arrayFormat in fa?o=e.arrayFormat:"indices"in e?o=e.indices?"indices":"repeat":o=X.arrayFormat,"commaRoundTrip"in e&&typeof e.commaRoundTrip!="boolean")throw new TypeError("`commaRoundTrip` must be a boolean, or absent");const a=typeof e.allowDots>"u"?e.encodeDotInKeys?!0:X.allowDots:!!e.allowDots;return{addQueryPrefix:typeof e.addQueryPrefix=="boolean"?e.addQueryPrefix:X.addQueryPrefix,allowDots:a,allowEmptyArrays:typeof e.allowEmptyArrays=="boolean"?!!e.allowEmptyArrays:X.allowEmptyArrays,arrayFormat:o,charset:t,charsetSentinel:typeof e.charsetSentinel=="boolean"?e.charsetSentinel:X.charsetSentinel,commaRoundTrip:!!e.commaRoundTrip,delimiter:typeof e.delimiter>"u"?X.delimiter:e.delimiter,encode:typeof e.encode=="boolean"?e.encode:X.encode,encodeDotInKeys:typeof e.encodeDotInKeys=="boolean"?e.encodeDotInKeys:X.encodeDotInKeys,encoder:typeof e.encoder=="function"?e.encoder:X.encoder,encodeValuesOnly:typeof e.encodeValuesOnly=="boolean"?e.encodeValuesOnly:X.encodeValuesOnly,filter:n,format:r,formatter:i,serializeDate:typeof e.serializeDate=="function"?e.serializeDate:X.serializeDate,skipNulls:typeof e.skipNulls=="boolean"?e.skipNulls:X.skipNulls,sort:typeof e.sort=="function"?e.sort:null,strictNullHandling:typeof e.strictNullHandling=="boolean"?e.strictNullHandling:X.strictNullHandling}}function rf(e,t={}){let r=e;const i=nf(t);let n,o;typeof i.filter=="function"?(o=i.filter,r=o("",r)):te(i.filter)&&(o=i.filter,n=o);const a=[];if(typeof r!="object"||r===null)return"";const s=fa[i.arrayFormat],u=s==="comma"&&i.commaRoundTrip;n||(n=Object.keys(r)),i.sort&&n.sort(i.sort);const c=new WeakMap;for(let y=0;y<n.length;++y){const v=n[y];i.skipNulls&&r[v]===null||ma(a,ha(r[v],v,s,u,i.allowEmptyArrays,i.strictNullHandling,i.skipNulls,i.encodeDotInKeys,i.encode?i.encoder:null,i.filter,i.sort,i.allowDots,i.serializeDate,i.format,i.formatter,i.encodeValuesOnly,i.charset,c))}const p=a.join(i.delimiter);let d=i.addQueryPrefix===!0?"?":"";return i.charsetSentinel&&(i.charset==="iso-8859-1"?d+="utf8=%26%2310003%3B&":d+="utf8=%E2%9C%93&"),p.length>0?d+p:""}function of(e){let t=0;for(const n of e)t+=n.length;const r=new Uint8Array(t);let i=0;for(const n of e)r.set(n,i),i+=n.length;return r}let po;function ar(e){let t;return(po??(t=new globalThis.TextEncoder,po=t.encode.bind(t)))(e)}let go;function vo(e){let t;return(go??(t=new globalThis.TextDecoder,go=t.decode.bind(t)))(e)}var ne,re;class an{constructor(){ne.set(this,void 0),re.set(this,void 0),j(this,ne,new Uint8Array),j(this,re,null)}decode(t){if(t==null)return[];const r=t instanceof ArrayBuffer?new Uint8Array(t):typeof t=="string"?ar(t):t;j(this,ne,of([f(this,ne,"f"),r]));const i=[];let n;for(;(n=af(f(this,ne,"f"),f(this,re,"f")))!=null;){if(n.carriage&&f(this,re,"f")==null){j(this,re,n.index);continue}if(f(this,re,"f")!=null&&(n.index!==f(this,re,"f")+1||n.carriage)){i.push(vo(f(this,ne,"f").subarray(0,f(this,re,"f")-1))),j(this,ne,f(this,ne,"f").subarray(f(this,re,"f"))),j(this,re,null);continue}const o=f(this,re,"f")!==null?n.preceding-1:n.preceding,a=vo(f(this,ne,"f").subarray(0,o));i.push(a),j(this,ne,f(this,ne,"f").subarray(n.index)),j(this,re,null)}return i}flush(){return f(this,ne,"f").length?this.decode(`
`):[]}}ne=new WeakMap,re=new WeakMap;an.NEWLINE_CHARS=new Set([`
`,"\r"]);an.NEWLINE_REGEXP=/\r\n|[\n\r]/g;function af(e,t){for(let n=t??0;n<e.length;n++){if(e[n]===10)return{preceding:n,index:n+1,carriage:!1};if(e[n]===13)return{preceding:n,index:n+1,carriage:!0}}return null}function sf(e){for(let i=0;i<e.length-1;i++){if(e[i]===10&&e[i+1]===10||e[i]===13&&e[i+1]===13)return i+2;if(e[i]===13&&e[i+1]===10&&i+3<e.length&&e[i+2]===13&&e[i+3]===10)return i+4}return-1}const Mt={off:0,error:200,warn:300,info:400,debug:500},_o=(e,t,r)=>{if(e){if(Md(Mt,e))return e;G(r).warn(`${t} was set to ${JSON.stringify(e)}, expected one of ${JSON.stringify(Object.keys(Mt))}`)}};function Ge(){}function kt(e,t,r){return!t||Mt[e]>Mt[r]?Ge:t[e].bind(t)}const uf={error:Ge,warn:Ge,info:Ge,debug:Ge};let bo=new WeakMap;function G(e){const t=e.logger,r=e.logLevel??"off";if(!t)return uf;const i=bo.get(t);if(i&&i[0]===r)return i[1];const n={error:kt("error",t,r),warn:kt("warn",t,r),info:kt("info",t,r),debug:kt("debug",t,r)};return bo.set(t,[r,n]),n}const ye=e=>(e.options&&(e.options={...e.options},delete e.options.headers),e.headers&&(e.headers=Object.fromEntries((e.headers instanceof Headers?[...e.headers]:Object.entries(e.headers)).map(([t,r])=>[t,t.toLowerCase()==="authorization"||t.toLowerCase()==="cookie"||t.toLowerCase()==="set-cookie"?"***":r]))),"retryOfRequestLogID"in e&&(e.retryOfRequestLogID&&(e.retryOf=e.retryOfRequestLogID),delete e.retryOfRequestLogID),e);var Xe;class me{constructor(t,r,i){this.iterator=t,Xe.set(this,void 0),this.controller=r,j(this,Xe,i)}static fromSSEResponse(t,r,i){let n=!1;const o=i?G(i):console;async function*a(){if(n)throw new U("Cannot iterate over a consumed stream, use `.tee()` to split the stream.");n=!0;let s=!1;try{for await(const u of cf(t,r))if(!s){if(u.data.startsWith("[DONE]")){s=!0;continue}if(u.event===null||u.event.startsWith("response.")||u.event.startsWith("image_edit.")||u.event.startsWith("image_generation.")||u.event.startsWith("transcript.")){let c;try{c=JSON.parse(u.data)}catch(p){throw o.error("Could not parse message into JSON:",u.data),o.error("From chunk:",u.raw),p}if(c&&c.error)throw new Y(void 0,c.error,void 0,t.headers);yield c}else{let c;try{c=JSON.parse(u.data)}catch(p){throw console.error("Could not parse message into JSON:",u.data),console.error("From chunk:",u.raw),p}if(u.event=="error")throw new Y(void 0,c.error,c.message,void 0);yield{event:u.event,data:c}}}s=!0}catch(u){if(Zn(u))return;throw u}finally{s||r.abort()}}return new me(a,r,i)}static fromReadableStream(t,r,i){let n=!1;async function*o(){const s=new an,u=ca(t);for await(const c of u)for(const p of s.decode(c))yield p;for(const c of s.flush())yield c}async function*a(){if(n)throw new U("Cannot iterate over a consumed stream, use `.tee()` to split the stream.");n=!0;let s=!1;try{for await(const u of o())s||u&&(yield JSON.parse(u));s=!0}catch(u){if(Zn(u))return;throw u}finally{s||r.abort()}}return new me(a,r,i)}[(Xe=new WeakMap,Symbol.asyncIterator)](){return this.iterator()}tee(){const t=[],r=[],i=this.iterator(),n=o=>({next:()=>{if(o.length===0){const a=i.next();t.push(a),r.push(a)}return o.shift()}});return[new me(()=>n(t),this.controller,f(this,Xe,"f")),new me(()=>n(r),this.controller,f(this,Xe,"f"))]}toReadableStream(){const t=this;let r;return sa({async start(){r=t[Symbol.asyncIterator]()},async pull(i){try{const{value:n,done:o}=await r.next();if(o)return i.close();const a=ar(JSON.stringify(n)+`
`);i.enqueue(a)}catch(n){i.error(n)}},async cancel(){var i;await((i=r.return)==null?void 0:i.call(r))}})}}async function*cf(e,t){if(!e.body)throw t.abort(),typeof globalThis.navigator<"u"&&globalThis.navigator.product==="ReactNative"?new U("The default react-native fetch implementation does not support streaming. Please use expo/fetch: https://docs.expo.dev/versions/latest/sdk/expo/#expofetch-api"):new U("Attempted to iterate over a response with no body");const r=new df,i=new an,n=ca(e.body);for await(const o of lf(n))for(const a of i.decode(o)){const s=r.decode(a);s&&(yield s)}for(const o of i.flush()){const a=r.decode(o);a&&(yield a)}}async function*lf(e){let t=new Uint8Array;for await(const r of e){if(r==null)continue;const i=r instanceof ArrayBuffer?new Uint8Array(r):typeof r=="string"?ar(r):r;let n=new Uint8Array(t.length+i.length);n.set(t),n.set(i,t.length),t=n;let o;for(;(o=sf(t))!==-1;)yield t.slice(0,o),t=t.slice(o)}t.length>0&&(yield t)}class df{constructor(){this.event=null,this.data=[],this.chunks=[]}decode(t){if(t.endsWith("\r")&&(t=t.substring(0,t.length-1)),!t){if(!this.event&&!this.data.length)return null;const o={event:this.event,data:this.data.join(`
`),raw:this.chunks};return this.event=null,this.data=[],this.chunks=[],o}if(this.chunks.push(t),t.startsWith(":"))return null;let[r,i,n]=ff(t,":");return n.startsWith(" ")&&(n=n.substring(1)),r==="event"?this.event=n:r==="data"&&this.data.push(n),null}}function ff(e,t){const r=e.indexOf(t);return r!==-1?[e.substring(0,r),t,e.substring(r+t.length)]:[e,"",""]}async function pa(e,t){const{response:r,requestLogID:i,retryOfRequestLogID:n,startTime:o}=t,a=await(async()=>{var d;if(t.options.stream)return G(e).debug("response",r.status,r.url,r.headers,r.body),t.options.__streamClass?t.options.__streamClass.fromSSEResponse(r,t.controller,e):me.fromSSEResponse(r,t.controller,e);if(r.status===204)return null;if(t.options.__binaryResponse)return r;const s=r.headers.get("content-type"),u=(d=s==null?void 0:s.split(";")[0])==null?void 0:d.trim();if((u==null?void 0:u.includes("application/json"))||(u==null?void 0:u.endsWith("+json"))){const y=await r.json();return ga(y,r)}return await r.text()})();return G(e).debug(`[${i}] response parsed`,ye({retryOfRequestLogID:n,url:r.url,status:r.status,body:a,durationMs:Date.now()-o})),a}function ga(e,t){return!e||typeof e!="object"||Array.isArray(e)?e:Object.defineProperty(e,"_request_id",{value:t.headers.get("x-request-id"),enumerable:!1})}var He;class sn extends Promise{constructor(t,r,i=pa){super(n=>{n(null)}),this.responsePromise=r,this.parseResponse=i,He.set(this,void 0),j(this,He,t)}_thenUnwrap(t){return new sn(f(this,He,"f"),this.responsePromise,async(r,i)=>ga(t(await this.parseResponse(r,i),i),i.response))}asResponse(){return this.responsePromise.then(t=>t.response)}async withResponse(){const[t,r]=await Promise.all([this.parse(),this.asResponse()]);return{data:t,response:r,request_id:r.headers.get("x-request-id")}}parse(){return this.parsedPromise||(this.parsedPromise=this.responsePromise.then(t=>this.parseResponse(f(this,He,"f"),t))),this.parsedPromise}then(t,r){return this.parse().then(t,r)}catch(t){return this.parse().catch(t)}finally(t){return this.parse().finally(t)}}He=new WeakMap;var St;class va{constructor(t,r,i,n){St.set(this,void 0),j(this,St,t),this.options=n,this.response=r,this.body=i}hasNextPage(){return this.getPaginatedItems().length?this.nextPageRequestOptions()!=null:!1}async getNextPage(){const t=this.nextPageRequestOptions();if(!t)throw new U("No next page expected; please check `.hasNextPage()` before calling `.getNextPage()`.");return await f(this,St,"f").requestAPIList(this.constructor,t)}async*iterPages(){let t=this;for(yield t;t.hasNextPage();)t=await t.getNextPage(),yield t}async*[(St=new WeakMap,Symbol.asyncIterator)](){for await(const t of this.iterPages())for(const r of t.getPaginatedItems())yield r}}class mf extends sn{constructor(t,r,i){super(t,r,async(n,o)=>new i(n,o.response,await pa(n,o),o.options))}async*[Symbol.asyncIterator](){const t=await this;for await(const r of t)yield r}}class un extends va{constructor(t,r,i,n){super(t,r,i,n),this.data=i.data||[],this.object=i.object}getPaginatedItems(){return this.data??[]}nextPageRequestOptions(){return null}}class W extends va{constructor(t,r,i,n){super(t,r,i,n),this.data=i.data||[],this.has_more=i.has_more||!1}getPaginatedItems(){return this.data??[]}hasNextPage(){return this.has_more===!1?!1:super.hasNextPage()}nextPageRequestOptions(){var i;const t=this.getPaginatedItems(),r=(i=t[t.length-1])==null?void 0:i.id;return r?{...this.options,query:{...Zd(this.options.query),after:r}}:null}}const _a=()=>{var e;if(typeof File>"u"){const{process:t}=globalThis,r=typeof((e=t==null?void 0:t.versions)==null?void 0:e.node)=="string"&&parseInt(t.versions.node.split("."))<20;throw new Error("`File` is not defined as a global, which is required for file uploads."+(r?" Update to Node 20 LTS or newer, or set `globalThis.File` to `import('node:buffer').File`.":""))}};function ot(e,t,r){return _a(),new File(e,t??"unknown_file",r)}function Pt(e){return(typeof e=="object"&&e!==null&&("name"in e&&e.name&&String(e.name)||"url"in e&&e.url&&String(e.url)||"filename"in e&&e.filename&&String(e.filename)||"path"in e&&e.path&&String(e.path))||"").split(/[\\/]/).pop()||void 0}const ba=e=>e!=null&&typeof e=="object"&&typeof e[Symbol.asyncIterator]=="function",Ie=async(e,t)=>({...e,body:await pf(e.body,t)}),yo=new WeakMap;function hf(e){const t=typeof e=="function"?e:e.fetch,r=yo.get(t);if(r)return r;const i=(async()=>{try{const n="Response"in t?t.Response:(await t("data:,")).constructor,o=new FormData;return o.toString()!==await new n(o).text()}catch{return!0}})();return yo.set(t,i),i}const pf=async(e,t)=>{if(!await hf(t))throw new TypeError("The provided fetch function does not support file uploads with the current global FormData class.");const r=new FormData;return await Promise.all(Object.entries(e||{}).map(([i,n])=>Fn(r,i,n))),r},gf=e=>e instanceof Blob&&"name"in e,Fn=async(e,t,r)=>{if(r!==void 0){if(r==null)throw new TypeError(`Received null for "${t}"; to pass null in FormData, you must use the string 'null'`);if(typeof r=="string"||typeof r=="number"||typeof r=="boolean")e.append(t,String(r));else if(r instanceof Response)e.append(t,ot([await r.blob()],Pt(r)));else if(ba(r))e.append(t,ot([await new Response(ua(r)).blob()],Pt(r)));else if(gf(r))e.append(t,r,Pt(r));else if(Array.isArray(r))await Promise.all(r.map(i=>Fn(e,t+"[]",i)));else if(typeof r=="object")await Promise.all(Object.entries(r).map(([i,n])=>Fn(e,`${t}[${i}]`,n)));else throw new TypeError(`Invalid value given to form, expected a string, number, boolean, object, Array, File or Blob but got ${r} instead`)}},ya=e=>e!=null&&typeof e=="object"&&typeof e.size=="number"&&typeof e.type=="string"&&typeof e.text=="function"&&typeof e.slice=="function"&&typeof e.arrayBuffer=="function",vf=e=>e!=null&&typeof e=="object"&&typeof e.name=="string"&&typeof e.lastModified=="number"&&ya(e),_f=e=>e!=null&&typeof e=="object"&&typeof e.url=="string"&&typeof e.blob=="function";async function bf(e,t,r){if(_a(),e=await e,vf(e))return e instanceof File?e:ot([await e.arrayBuffer()],e.name);if(_f(e)){const n=await e.blob();return t||(t=new URL(e.url).pathname.split(/[\\/]/).pop()),ot(await Bn(n),t,r)}const i=await Bn(e);if(t||(t=Pt(e)),!(r!=null&&r.type)){const n=i.find(o=>typeof o=="object"&&"type"in o&&o.type);typeof n=="string"&&(r={...r,type:n})}return ot(i,t,r)}async function Bn(e){var r;let t=[];if(typeof e=="string"||ArrayBuffer.isView(e)||e instanceof ArrayBuffer)t.push(e);else if(ya(e))t.push(e instanceof Blob?e:await e.arrayBuffer());else if(ba(e))for await(const i of e)t.push(...await Bn(i));else{const i=(r=e==null?void 0:e.constructor)==null?void 0:r.name;throw new Error(`Unexpected data type: ${typeof e}${i?`; constructor: ${i}`:""}${yf(e)}`)}return t}function yf(e){return typeof e!="object"||e===null?"":`; props: [${Object.getOwnPropertyNames(e).map(r=>`"${r}"`).join(", ")}]`}class P{constructor(t){this._client=t}}function $a(e){return e.replace(/[^A-Za-z0-9\-._~!$&'()*+,;=:@]+/g,encodeURIComponent)}const $o=Object.freeze(Object.create(null)),$f=(e=$a)=>function(r,...i){if(r.length===1)return r[0];let n=!1;const o=[],a=r.reduce((p,d,y)=>{var S;/[?#]/.test(d)&&(n=!0);const v=i[y];let _=(n?encodeURIComponent:e)(""+v);return y!==i.length&&(v==null||typeof v=="object"&&v.toString===((S=Object.getPrototypeOf(Object.getPrototypeOf(v.hasOwnProperty??$o)??$o))==null?void 0:S.toString))&&(_=v+"",o.push({start:p.length+d.length,length:_.length,error:`Value of type ${Object.prototype.toString.call(v).slice(8,-1)} is not a valid path parameter`})),p+d+(y===i.length?"":_)},""),s=a.split(/[?#]/,1)[0],u=new RegExp("(?<=^|\\/)(?:\\.|%2e){1,2}(?=\\/|$)","gi");let c;for(;(c=u.exec(s))!==null;)o.push({start:c.index,length:c[0].length,error:`Value "${c[0]}" can't be safely passed as a path parameter`});if(o.sort((p,d)=>p.start-d.start),o.length>0){let p=0;const d=o.reduce((y,v)=>{const _=" ".repeat(v.start-p),S="^".repeat(v.length);return p=v.start+v.length,y+_+S},"");throw new U(`Path parameters result in path with invalid segments:
${o.map(y=>y.error).join(`
`)}
${a}
${d}`)}return a},I=$f($a);let wa=class extends P{list(t,r={},i){return this._client.getAPIList(I`/chat/completions/${t}/messages`,W,{query:r,...i})}};function wf(e){return typeof e.parse=="function"}const Ft=e=>(e==null?void 0:e.role)==="assistant",ka=e=>(e==null?void 0:e.role)==="tool";var Jn,At,Et,Ye,Qe,Tt,et,ge,tt,Bt,Jt,De,Sa;class sr{constructor(){Jn.add(this),this.controller=new AbortController,At.set(this,void 0),Et.set(this,()=>{}),Ye.set(this,()=>{}),Qe.set(this,void 0),Tt.set(this,()=>{}),et.set(this,()=>{}),ge.set(this,{}),tt.set(this,!1),Bt.set(this,!1),Jt.set(this,!1),De.set(this,!1),j(this,At,new Promise((t,r)=>{j(this,Et,t,"f"),j(this,Ye,r,"f")})),j(this,Qe,new Promise((t,r)=>{j(this,Tt,t,"f"),j(this,et,r,"f")})),f(this,At,"f").catch(()=>{}),f(this,Qe,"f").catch(()=>{})}_run(t){setTimeout(()=>{t().then(()=>{this._emitFinal(),this._emit("end")},f(this,Jn,"m",Sa).bind(this))},0)}_connected(){this.ended||(f(this,Et,"f").call(this),this._emit("connect"))}get ended(){return f(this,tt,"f")}get errored(){return f(this,Bt,"f")}get aborted(){return f(this,Jt,"f")}abort(){this.controller.abort()}on(t,r){return(f(this,ge,"f")[t]||(f(this,ge,"f")[t]=[])).push({listener:r}),this}off(t,r){const i=f(this,ge,"f")[t];if(!i)return this;const n=i.findIndex(o=>o.listener===r);return n>=0&&i.splice(n,1),this}once(t,r){return(f(this,ge,"f")[t]||(f(this,ge,"f")[t]=[])).push({listener:r,once:!0}),this}emitted(t){return new Promise((r,i)=>{j(this,De,!0),t!=="error"&&this.once("error",i),this.once(t,r)})}async done(){j(this,De,!0),await f(this,Qe,"f")}_emit(t,...r){if(f(this,tt,"f"))return;t==="end"&&(j(this,tt,!0),f(this,Tt,"f").call(this));const i=f(this,ge,"f")[t];if(i&&(f(this,ge,"f")[t]=i.filter(n=>!n.once),i.forEach(({listener:n})=>n(...r))),t==="abort"){const n=r[0];!f(this,De,"f")&&!(i!=null&&i.length)&&Promise.reject(n),f(this,Ye,"f").call(this,n),f(this,et,"f").call(this,n),this._emit("end");return}if(t==="error"){const n=r[0];!f(this,De,"f")&&!(i!=null&&i.length)&&Promise.reject(n),f(this,Ye,"f").call(this,n),f(this,et,"f").call(this,n),this._emit("end")}}_emitFinal(){}}At=new WeakMap,Et=new WeakMap,Ye=new WeakMap,Qe=new WeakMap,Tt=new WeakMap,et=new WeakMap,ge=new WeakMap,tt=new WeakMap,Bt=new WeakMap,Jt=new WeakMap,De=new WeakMap,Jn=new WeakSet,Sa=function(t){if(j(this,Bt,!0),t instanceof Error&&t.name==="AbortError"&&(t=new ae),t instanceof ae)return j(this,Jt,!0),this._emit("abort",t);if(t instanceof U)return this._emit("error",t);if(t instanceof Error){const r=new U(t.message);return r.cause=t,this._emit("error",r)}return this._emit("error",new U(String(t)))};function ur(e){return(e==null?void 0:e.$brand)==="auto-parseable-response-format"}function pt(e){return(e==null?void 0:e.$brand)==="auto-parseable-tool"}function kf(e,t){return!t||!Ia(t)?{...e,choices:e.choices.map(r=>({...r,message:{...r.message,parsed:null,...r.message.tool_calls?{tool_calls:r.message.tool_calls}:void 0}}))}:cr(e,t)}function cr(e,t){const r=e.choices.map(i=>{var n;if(i.finish_reason==="length")throw new oa;if(i.finish_reason==="content_filter")throw new aa;return{...i,message:{...i.message,...i.message.tool_calls?{tool_calls:((n=i.message.tool_calls)==null?void 0:n.map(o=>If(t,o)))??void 0}:void 0,parsed:i.message.content&&!i.message.refusal?Sf(t,i.message.content):null}}});return{...e,choices:r}}function Sf(e,t){var r,i;return((r=e.response_format)==null?void 0:r.type)!=="json_schema"?null:((i=e.response_format)==null?void 0:i.type)==="json_schema"?"$parseRaw"in e.response_format?e.response_format.$parseRaw(t):JSON.parse(t):null}function If(e,t){var i;const r=(i=e.tools)==null?void 0:i.find(n=>{var o;return((o=n.function)==null?void 0:o.name)===t.function.name});return{...t,function:{...t.function,parsed_arguments:pt(r)?r.$parseRaw(t.function.arguments):r!=null&&r.function.strict?JSON.parse(t.function.arguments):null}}}function xf(e,t){var i;if(!e)return!1;const r=(i=e.tools)==null?void 0:i.find(n=>{var o;return((o=n.function)==null?void 0:o.name)===t.function.name});return pt(r)||(r==null?void 0:r.function.strict)||!1}function Ia(e){var t;return ur(e.response_format)?!0:((t=e.tools)==null?void 0:t.some(r=>pt(r)||r.type==="function"&&r.function.strict===!0))??!1}function zf(e){for(const t of e??[]){if(t.type!=="function")throw new U(`Currently only \`function\` tool types support auto-parsing; Received \`${t.type}\``);if(t.function.strict!==!0)throw new U(`The \`${t.function.name}\` tool is not marked with \`strict: true\`. Only strict function tools can be auto-parsed`)}}var Q,Wn,Wt,qn,Vn,Xn,xa,za;const Of=10;class Oa extends sr{constructor(){super(...arguments),Q.add(this),this._chatCompletions=[],this.messages=[]}_addChatCompletion(t){var i;this._chatCompletions.push(t),this._emit("chatCompletion",t);const r=(i=t.choices[0])==null?void 0:i.message;return r&&this._addMessage(r),t}_addMessage(t,r=!0){if("content"in t||(t.content=null),this.messages.push(t),r){if(this._emit("message",t),ka(t)&&t.content)this._emit("functionToolCallResult",t.content);else if(Ft(t)&&t.tool_calls)for(const i of t.tool_calls)i.type==="function"&&this._emit("functionToolCall",i.function)}}async finalChatCompletion(){await this.done();const t=this._chatCompletions[this._chatCompletions.length-1];if(!t)throw new U("stream ended without producing a ChatCompletion");return t}async finalContent(){return await this.done(),f(this,Q,"m",Wn).call(this)}async finalMessage(){return await this.done(),f(this,Q,"m",Wt).call(this)}async finalFunctionToolCall(){return await this.done(),f(this,Q,"m",qn).call(this)}async finalFunctionToolCallResult(){return await this.done(),f(this,Q,"m",Vn).call(this)}async totalUsage(){return await this.done(),f(this,Q,"m",Xn).call(this)}allChatCompletions(){return[...this._chatCompletions]}_emitFinal(){const t=this._chatCompletions[this._chatCompletions.length-1];t&&this._emit("finalChatCompletion",t);const r=f(this,Q,"m",Wt).call(this);r&&this._emit("finalMessage",r);const i=f(this,Q,"m",Wn).call(this);i&&this._emit("finalContent",i);const n=f(this,Q,"m",qn).call(this);n&&this._emit("finalFunctionToolCall",n);const o=f(this,Q,"m",Vn).call(this);o!=null&&this._emit("finalFunctionToolCallResult",o),this._chatCompletions.some(a=>a.usage)&&this._emit("totalUsage",f(this,Q,"m",Xn).call(this))}async _createChatCompletion(t,r,i){const n=i==null?void 0:i.signal;n&&(n.aborted&&this.controller.abort(),n.addEventListener("abort",()=>this.controller.abort())),f(this,Q,"m",xa).call(this,r);const o=await t.chat.completions.create({...r,stream:!1},{...i,signal:this.controller.signal});return this._connected(),this._addChatCompletion(cr(o,r))}async _runChatCompletion(t,r,i){for(const n of r.messages)this._addMessage(n,!1);return await this._createChatCompletion(t,r,i)}async _runTools(t,r,i){var v,_,S;const n="tool",{tool_choice:o="auto",stream:a,...s}=r,u=typeof o!="string"&&((v=o==null?void 0:o.function)==null?void 0:v.name),{maxChatCompletions:c=Of}=i||{},p=r.tools.map(b=>{if(pt(b)){if(!b.$callback)throw new U("Tool given to `.runTools()` that does not have an associated function");return{type:"function",function:{function:b.$callback,name:b.function.name,description:b.function.description||"",parameters:b.function.parameters,parse:b.$parseRaw,strict:!0}}}return b}),d={};for(const b of p)b.type==="function"&&(d[b.function.name||b.function.function.name]=b.function);const y="tools"in r?p.map(b=>b.type==="function"?{type:"function",function:{name:b.function.name||b.function.function.name,parameters:b.function.parameters,description:b.function.description,strict:b.function.strict}}:b):void 0;for(const b of r.messages)this._addMessage(b,!1);for(let b=0;b<c;++b){const h=(_=(await this._createChatCompletion(t,{...s,tool_choice:o,tools:y,messages:[...this.messages]},i)).choices[0])==null?void 0:_.message;if(!h)throw new U("missing message in ChatCompletion response");if(!((S=h.tool_calls)!=null&&S.length))return;for(const g of h.tool_calls){if(g.type!=="function")continue;const w=g.id,{name:x,arguments:z}=g.function,N=d[x];if(N){if(u&&u!==x){const Z=`Invalid tool_call: ${JSON.stringify(x)}. ${JSON.stringify(u)} requested. Please try again`;this._addMessage({role:n,tool_call_id:w,content:Z});continue}}else{const Z=`Invalid tool_call: ${JSON.stringify(x)}. Available options are: ${Object.keys(d).map(Ue=>JSON.stringify(Ue)).join(", ")}. Please try again`;this._addMessage({role:n,tool_call_id:w,content:Z});continue}let F;try{F=wf(N)?await N.parse(z):z}catch(Z){const Ue=Z instanceof Error?Z.message:String(Z);this._addMessage({role:n,tool_call_id:w,content:Ue});continue}const C=await N.function(F,this),B=f(this,Q,"m",za).call(this,C);if(this._addMessage({role:n,tool_call_id:w,content:B}),u)return}}}}Q=new WeakSet,Wn=function(){return f(this,Q,"m",Wt).call(this).content??null},Wt=function(){let t=this.messages.length;for(;t-- >0;){const r=this.messages[t];if(Ft(r))return{...r,content:r.content??null,refusal:r.refusal??null}}throw new U("stream ended without producing a ChatCompletionMessage with role=assistant")},qn=function(){var t,r;for(let i=this.messages.length-1;i>=0;i--){const n=this.messages[i];if(Ft(n)&&((t=n==null?void 0:n.tool_calls)!=null&&t.length))return(r=n.tool_calls.at(-1))==null?void 0:r.function}},Vn=function(){for(let t=this.messages.length-1;t>=0;t--){const r=this.messages[t];if(ka(r)&&r.content!=null&&typeof r.content=="string"&&this.messages.some(i=>{var n;return i.role==="assistant"&&((n=i.tool_calls)==null?void 0:n.some(o=>o.type==="function"&&o.id===r.tool_call_id))}))return r.content}},Xn=function(){const t={completion_tokens:0,prompt_tokens:0,total_tokens:0};for(const{usage:r}of this._chatCompletions)r&&(t.completion_tokens+=r.completion_tokens,t.prompt_tokens+=r.prompt_tokens,t.total_tokens+=r.total_tokens);return t},xa=function(t){if(t.n!=null&&t.n>1)throw new U("ChatCompletion convenience helpers only support n=1 at this time. To use n>1, please use chat.completions.create() directly.")},za=function(t){return typeof t=="string"?t:t===void 0?"undefined":JSON.stringify(t)};class lr extends Oa{static runTools(t,r,i){const n=new lr,o={...i,headers:{...i==null?void 0:i.headers,"X-Stainless-Helper-Method":"runTools"}};return n._run(()=>n._runTools(t,r,o)),n}_addMessage(t,r=!0){super._addMessage(t,r),Ft(t)&&t.content&&this._emit("content",t.content)}}const Na=1,ja=2,Ua=4,Pa=8,Aa=16,Ea=32,Ta=64,Da=128,Ra=256,Ca=Da|Ra,Za=Aa|Ea|Ca|Ta,La=Na|ja|Za,Ma=Ua|Pa,Nf=La|Ma,K={STR:Na,NUM:ja,ARR:Ua,OBJ:Pa,NULL:Aa,BOOL:Ea,NAN:Ta,INFINITY:Da,MINUS_INFINITY:Ra,INF:Ca,SPECIAL:Za,ATOM:La,COLLECTION:Ma,ALL:Nf};class jf extends Error{}class Uf extends Error{}function Pf(e,t=K.ALL){if(typeof e!="string")throw new TypeError(`expecting str, got ${typeof e}`);if(!e.trim())throw new Error(`${e} is empty`);return Af(e.trim(),t)}const Af=(e,t)=>{const r=e.length;let i=0;const n=y=>{throw new jf(`${y} at position ${i}`)},o=y=>{throw new Uf(`${y} at position ${i}`)},a=()=>(d(),i>=r&&n("Unexpected end of input"),e[i]==='"'?s():e[i]==="{"?u():e[i]==="["?c():e.substring(i,i+4)==="null"||K.NULL&t&&r-i<4&&"null".startsWith(e.substring(i))?(i+=4,null):e.substring(i,i+4)==="true"||K.BOOL&t&&r-i<4&&"true".startsWith(e.substring(i))?(i+=4,!0):e.substring(i,i+5)==="false"||K.BOOL&t&&r-i<5&&"false".startsWith(e.substring(i))?(i+=5,!1):e.substring(i,i+8)==="Infinity"||K.INFINITY&t&&r-i<8&&"Infinity".startsWith(e.substring(i))?(i+=8,1/0):e.substring(i,i+9)==="-Infinity"||K.MINUS_INFINITY&t&&1<r-i&&r-i<9&&"-Infinity".startsWith(e.substring(i))?(i+=9,-1/0):e.substring(i,i+3)==="NaN"||K.NAN&t&&r-i<3&&"NaN".startsWith(e.substring(i))?(i+=3,NaN):p()),s=()=>{const y=i;let v=!1;for(i++;i<r&&(e[i]!=='"'||v&&e[i-1]==="\\");)v=e[i]==="\\"?!v:!1,i++;if(e.charAt(i)=='"')try{return JSON.parse(e.substring(y,++i-Number(v)))}catch(_){o(String(_))}else if(K.STR&t)try{return JSON.parse(e.substring(y,i-Number(v))+'"')}catch{return JSON.parse(e.substring(y,e.lastIndexOf("\\"))+'"')}n("Unterminated string literal")},u=()=>{i++,d();const y={};try{for(;e[i]!=="}";){if(d(),i>=r&&K.OBJ&t)return y;const v=s();d(),i++;try{const _=a();Object.defineProperty(y,v,{value:_,writable:!0,enumerable:!0,configurable:!0})}catch(_){if(K.OBJ&t)return y;throw _}d(),e[i]===","&&i++}}catch{if(K.OBJ&t)return y;n("Expected '}' at end of object")}return i++,y},c=()=>{i++;const y=[];try{for(;e[i]!=="]";)y.push(a()),d(),e[i]===","&&i++}catch{if(K.ARR&t)return y;n("Expected ']' at end of array")}return i++,y},p=()=>{if(i===0){e==="-"&&K.NUM&t&&n("Not sure what '-' is");try{return JSON.parse(e)}catch(v){if(K.NUM&t)try{return e[e.length-1]==="."?JSON.parse(e.substring(0,e.lastIndexOf("."))):JSON.parse(e.substring(0,e.lastIndexOf("e")))}catch{}o(String(v))}}const y=i;for(e[i]==="-"&&i++;e[i]&&!",]}".includes(e[i]);)i++;i==r&&!(K.NUM&t)&&n("Unterminated number literal");try{return JSON.parse(e.substring(y,i))}catch{e.substring(y,i)==="-"&&K.NUM&t&&n("Not sure what '-' is");try{return JSON.parse(e.substring(y,e.lastIndexOf("e")))}catch(_){o(String(_))}}},d=()=>{for(;i<r&&` 
\r	`.includes(e[i]);)i++};return a()},wo=e=>Pf(e,K.ALL^K.NUM);var q,pe,Pe,_e,An,It,En,Tn,Dn,xt,Rn,ko;class ut extends Oa{constructor(t){super(),q.add(this),pe.set(this,void 0),Pe.set(this,void 0),_e.set(this,void 0),j(this,pe,t),j(this,Pe,[])}get currentChatCompletionSnapshot(){return f(this,_e,"f")}static fromReadableStream(t){const r=new ut(null);return r._run(()=>r._fromReadableStream(t)),r}static createChatCompletion(t,r,i){const n=new ut(r);return n._run(()=>n._runChatCompletion(t,{...r,stream:!0},{...i,headers:{...i==null?void 0:i.headers,"X-Stainless-Helper-Method":"stream"}})),n}async _createChatCompletion(t,r,i){var a;super._createChatCompletion;const n=i==null?void 0:i.signal;n&&(n.aborted&&this.controller.abort(),n.addEventListener("abort",()=>this.controller.abort())),f(this,q,"m",An).call(this);const o=await t.chat.completions.create({...r,stream:!0},{...i,signal:this.controller.signal});this._connected();for await(const s of o)f(this,q,"m",En).call(this,s);if((a=o.controller.signal)!=null&&a.aborted)throw new ae;return this._addChatCompletion(f(this,q,"m",xt).call(this))}async _fromReadableStream(t,r){var a;const i=r==null?void 0:r.signal;i&&(i.aborted&&this.controller.abort(),i.addEventListener("abort",()=>this.controller.abort())),f(this,q,"m",An).call(this),this._connected();const n=me.fromReadableStream(t,this.controller);let o;for await(const s of n)o&&o!==s.id&&this._addChatCompletion(f(this,q,"m",xt).call(this)),f(this,q,"m",En).call(this,s),o=s.id;if((a=n.controller.signal)!=null&&a.aborted)throw new ae;return this._addChatCompletion(f(this,q,"m",xt).call(this))}[(pe=new WeakMap,Pe=new WeakMap,_e=new WeakMap,q=new WeakSet,An=function(){this.ended||j(this,_e,void 0)},It=function(r){let i=f(this,Pe,"f")[r.index];return i||(i={content_done:!1,refusal_done:!1,logprobs_content_done:!1,logprobs_refusal_done:!1,done_tool_calls:new Set,current_tool_call_index:null},f(this,Pe,"f")[r.index]=i,i)},En=function(r){var n,o,a,s,u,c,p,d,y,v,_,S,b,m,h;if(this.ended)return;const i=f(this,q,"m",ko).call(this,r);this._emit("chunk",r,i);for(const g of r.choices){const w=i.choices[g.index];g.delta.content!=null&&((n=w.message)==null?void 0:n.role)==="assistant"&&((o=w.message)!=null&&o.content)&&(this._emit("content",g.delta.content,w.message.content),this._emit("content.delta",{delta:g.delta.content,snapshot:w.message.content,parsed:w.message.parsed})),g.delta.refusal!=null&&((a=w.message)==null?void 0:a.role)==="assistant"&&((s=w.message)!=null&&s.refusal)&&this._emit("refusal.delta",{delta:g.delta.refusal,snapshot:w.message.refusal}),((u=g.logprobs)==null?void 0:u.content)!=null&&((c=w.message)==null?void 0:c.role)==="assistant"&&this._emit("logprobs.content.delta",{content:(p=g.logprobs)==null?void 0:p.content,snapshot:((d=w.logprobs)==null?void 0:d.content)??[]}),((y=g.logprobs)==null?void 0:y.refusal)!=null&&((v=w.message)==null?void 0:v.role)==="assistant"&&this._emit("logprobs.refusal.delta",{refusal:(_=g.logprobs)==null?void 0:_.refusal,snapshot:((S=w.logprobs)==null?void 0:S.refusal)??[]});const x=f(this,q,"m",It).call(this,w);w.finish_reason&&(f(this,q,"m",Dn).call(this,w),x.current_tool_call_index!=null&&f(this,q,"m",Tn).call(this,w,x.current_tool_call_index));for(const z of g.delta.tool_calls??[])x.current_tool_call_index!==z.index&&(f(this,q,"m",Dn).call(this,w),x.current_tool_call_index!=null&&f(this,q,"m",Tn).call(this,w,x.current_tool_call_index)),x.current_tool_call_index=z.index;for(const z of g.delta.tool_calls??[]){const N=(b=w.message.tool_calls)==null?void 0:b[z.index];N!=null&&N.type&&((N==null?void 0:N.type)==="function"?this._emit("tool_calls.function.arguments.delta",{name:(m=N.function)==null?void 0:m.name,index:z.index,arguments:N.function.arguments,parsed_arguments:N.function.parsed_arguments,arguments_delta:((h=z.function)==null?void 0:h.arguments)??""}):(N==null||N.type,void 0))}}},Tn=function(r,i){var a,s,u;if(f(this,q,"m",It).call(this,r).done_tool_calls.has(i))return;const o=(a=r.message.tool_calls)==null?void 0:a[i];if(!o)throw new Error("no tool call snapshot");if(!o.type)throw new Error("tool call snapshot missing `type`");if(o.type==="function"){const c=(u=(s=f(this,pe,"f"))==null?void 0:s.tools)==null?void 0:u.find(p=>p.type==="function"&&p.function.name===o.function.name);this._emit("tool_calls.function.arguments.done",{name:o.function.name,index:i,arguments:o.function.arguments,parsed_arguments:pt(c)?c.$parseRaw(o.function.arguments):c!=null&&c.function.strict?JSON.parse(o.function.arguments):null})}else o.type},Dn=function(r){var n,o;const i=f(this,q,"m",It).call(this,r);if(r.message.content&&!i.content_done){i.content_done=!0;const a=f(this,q,"m",Rn).call(this);this._emit("content.done",{content:r.message.content,parsed:a?a.$parseRaw(r.message.content):null})}r.message.refusal&&!i.refusal_done&&(i.refusal_done=!0,this._emit("refusal.done",{refusal:r.message.refusal})),(n=r.logprobs)!=null&&n.content&&!i.logprobs_content_done&&(i.logprobs_content_done=!0,this._emit("logprobs.content.done",{content:r.logprobs.content})),(o=r.logprobs)!=null&&o.refusal&&!i.logprobs_refusal_done&&(i.logprobs_refusal_done=!0,this._emit("logprobs.refusal.done",{refusal:r.logprobs.refusal}))},xt=function(){if(this.ended)throw new U("stream has ended, this shouldn't happen");const r=f(this,_e,"f");if(!r)throw new U("request ended without sending any chunks");return j(this,_e,void 0),j(this,Pe,[]),Ef(r,f(this,pe,"f"))},Rn=function(){var i;const r=(i=f(this,pe,"f"))==null?void 0:i.response_format;return ur(r)?r:null},ko=function(r){var i,n,o,a;let s=f(this,_e,"f");const{choices:u,...c}=r;s?Object.assign(s,c):s=j(this,_e,{...c,choices:[]});for(const{delta:p,finish_reason:d,index:y,logprobs:v=null,..._}of r.choices){let S=s.choices[y];if(S||(S=s.choices[y]={finish_reason:d,index:y,message:{},logprobs:v,..._}),v)if(!S.logprobs)S.logprobs=Object.assign({},v);else{const{content:z,refusal:N,...F}=v;Object.assign(S.logprobs,F),z&&((i=S.logprobs).content??(i.content=[]),S.logprobs.content.push(...z)),N&&((n=S.logprobs).refusal??(n.refusal=[]),S.logprobs.refusal.push(...N))}if(d&&(S.finish_reason=d,f(this,pe,"f")&&Ia(f(this,pe,"f")))){if(d==="length")throw new oa;if(d==="content_filter")throw new aa}if(Object.assign(S,_),!p)continue;const{content:b,refusal:m,function_call:h,role:g,tool_calls:w,...x}=p;if(Object.assign(S.message,x),m&&(S.message.refusal=(S.message.refusal||"")+m),g&&(S.message.role=g),h&&(S.message.function_call?(h.name&&(S.message.function_call.name=h.name),h.arguments&&((o=S.message.function_call).arguments??(o.arguments=""),S.message.function_call.arguments+=h.arguments)):S.message.function_call=h),b&&(S.message.content=(S.message.content||"")+b,!S.message.refusal&&f(this,q,"m",Rn).call(this)&&(S.message.parsed=wo(S.message.content))),w){S.message.tool_calls||(S.message.tool_calls=[]);for(const{index:z,id:N,type:F,function:C,...B}of w){const Z=(a=S.message.tool_calls)[z]??(a[z]={});Object.assign(Z,B),N&&(Z.id=N),F&&(Z.type=F),C&&(Z.function??(Z.function={name:C.name??"",arguments:""})),C!=null&&C.name&&(Z.function.name=C.name),C!=null&&C.arguments&&(Z.function.arguments+=C.arguments,xf(f(this,pe,"f"),Z)&&(Z.function.parsed_arguments=wo(Z.function.arguments)))}}}return s},Symbol.asyncIterator)](){const t=[],r=[];let i=!1;return this.on("chunk",n=>{const o=r.shift();o?o.resolve(n):t.push(n)}),this.on("end",()=>{i=!0;for(const n of r)n.resolve(void 0);r.length=0}),this.on("abort",n=>{i=!0;for(const o of r)o.reject(n);r.length=0}),this.on("error",n=>{i=!0;for(const o of r)o.reject(n);r.length=0}),{next:async()=>t.length?{value:t.shift(),done:!1}:i?{value:void 0,done:!0}:new Promise((o,a)=>r.push({resolve:o,reject:a})).then(o=>o?{value:o,done:!1}:{value:void 0,done:!0}),return:async()=>(this.abort(),{value:void 0,done:!0})}}toReadableStream(){return new me(this[Symbol.asyncIterator].bind(this),this.controller).toReadableStream()}}function Ef(e,t){const{id:r,choices:i,created:n,model:o,system_fingerprint:a,...s}=e,u={...s,id:r,choices:i.map(({message:c,finish_reason:p,index:d,logprobs:y,...v})=>{if(!p)throw new U(`missing finish_reason for choice ${d}`);const{content:_=null,function_call:S,tool_calls:b,...m}=c,h=c.role;if(!h)throw new U(`missing role for choice ${d}`);if(S){const{arguments:g,name:w}=S;if(g==null)throw new U(`missing function_call.arguments for choice ${d}`);if(!w)throw new U(`missing function_call.name for choice ${d}`);return{...v,message:{content:_,function_call:{arguments:g,name:w},role:h,refusal:c.refusal??null},finish_reason:p,index:d,logprobs:y}}return b?{...v,index:d,finish_reason:p,logprobs:y,message:{...m,role:h,content:_,refusal:c.refusal??null,tool_calls:b.map((g,w)=>{const{function:x,type:z,id:N,...F}=g,{arguments:C,name:B,...Z}=x||{};if(N==null)throw new U(`missing choices[${d}].tool_calls[${w}].id
${zt(e)}`);if(z==null)throw new U(`missing choices[${d}].tool_calls[${w}].type
${zt(e)}`);if(B==null)throw new U(`missing choices[${d}].tool_calls[${w}].function.name
${zt(e)}`);if(C==null)throw new U(`missing choices[${d}].tool_calls[${w}].function.arguments
${zt(e)}`);return{...F,id:N,type:z,function:{...Z,name:B,arguments:C}}})}}:{...v,message:{...m,content:_,role:h,refusal:c.refusal??null},finish_reason:p,index:d,logprobs:y}}),created:n,model:o,object:"chat.completion",...a?{system_fingerprint:a}:{}};return kf(u,t)}function zt(e){return JSON.stringify(e)}class qt extends ut{static fromReadableStream(t){const r=new qt(null);return r._run(()=>r._fromReadableStream(t)),r}static runTools(t,r,i){const n=new qt(r),o={...i,headers:{...i==null?void 0:i.headers,"X-Stainless-Helper-Method":"runTools"}};return n._run(()=>n._runTools(t,r,o)),n}}let dr=class extends P{constructor(){super(...arguments),this.messages=new wa(this._client)}create(t,r){return this._client.post("/chat/completions",{body:t,...r,stream:t.stream??!1})}retrieve(t,r){return this._client.get(I`/chat/completions/${t}`,r)}update(t,r,i){return this._client.post(I`/chat/completions/${t}`,{body:r,...i})}list(t={},r){return this._client.getAPIList("/chat/completions",W,{query:t,...r})}delete(t,r){return this._client.delete(I`/chat/completions/${t}`,r)}parse(t,r){return zf(t.tools),this._client.chat.completions.create(t,{...r,headers:{...r==null?void 0:r.headers,"X-Stainless-Helper-Method":"chat.completions.parse"}})._thenUnwrap(i=>cr(i,t))}runTools(t,r){return t.stream?qt.runTools(this._client,t,r):lr.runTools(this._client,t,r)}stream(t,r){return ut.createChatCompletion(this._client,t,r)}};dr.Messages=wa;class fr extends P{constructor(){super(...arguments),this.completions=new dr(this._client)}}fr.Completions=dr;const Fa=Symbol("brand.privateNullableHeaders");function*Tf(e){if(!e)return;if(Fa in e){const{values:i,nulls:n}=e;yield*i.entries();for(const o of n)yield[o,null];return}let t=!1,r;e instanceof Headers?r=e.entries():so(e)?r=e:(t=!0,r=Object.entries(e??{}));for(let i of r){const n=i[0];if(typeof n!="string")throw new TypeError("expected header name to be a string");const o=so(i[1])?i[1]:[i[1]];let a=!1;for(const s of o)s!==void 0&&(t&&!a&&(a=!0,yield[n,null]),yield[n,s])}}const O=e=>{const t=new Headers,r=new Set;for(const i of e){const n=new Set;for(const[o,a]of Tf(i)){const s=o.toLowerCase();n.has(s)||(t.delete(o),n.add(s)),a===null?(t.delete(o),r.add(s)):(t.append(o,a),r.delete(s))}}return{[Fa]:!0,values:t,nulls:r}};class Ba extends P{create(t,r){return this._client.post("/audio/speech",{body:t,...r,headers:O([{Accept:"application/octet-stream"},r==null?void 0:r.headers]),__binaryResponse:!0})}}class Ja extends P{create(t,r){return this._client.post("/audio/transcriptions",Ie({body:t,...r,stream:t.stream??!1,__metadata:{model:t.model}},this._client))}}class Wa extends P{create(t,r){return this._client.post("/audio/translations",Ie({body:t,...r,__metadata:{model:t.model}},this._client))}}class gt extends P{constructor(){super(...arguments),this.transcriptions=new Ja(this._client),this.translations=new Wa(this._client),this.speech=new Ba(this._client)}}gt.Transcriptions=Ja;gt.Translations=Wa;gt.Speech=Ba;class qa extends P{create(t,r){return this._client.post("/batches",{body:t,...r})}retrieve(t,r){return this._client.get(I`/batches/${t}`,r)}list(t={},r){return this._client.getAPIList("/batches",W,{query:t,...r})}cancel(t,r){return this._client.post(I`/batches/${t}/cancel`,r)}}class Va extends P{create(t,r){return this._client.post("/assistants",{body:t,...r,headers:O([{"OpenAI-Beta":"assistants=v2"},r==null?void 0:r.headers])})}retrieve(t,r){return this._client.get(I`/assistants/${t}`,{...r,headers:O([{"OpenAI-Beta":"assistants=v2"},r==null?void 0:r.headers])})}update(t,r,i){return this._client.post(I`/assistants/${t}`,{body:r,...i,headers:O([{"OpenAI-Beta":"assistants=v2"},i==null?void 0:i.headers])})}list(t={},r){return this._client.getAPIList("/assistants",W,{query:t,...r,headers:O([{"OpenAI-Beta":"assistants=v2"},r==null?void 0:r.headers])})}delete(t,r){return this._client.delete(I`/assistants/${t}`,{...r,headers:O([{"OpenAI-Beta":"assistants=v2"},r==null?void 0:r.headers])})}}class Xa extends P{create(t,r){return this._client.post("/realtime/sessions",{body:t,...r,headers:O([{"OpenAI-Beta":"assistants=v2"},r==null?void 0:r.headers])})}}class Ka extends P{create(t,r){return this._client.post("/realtime/transcription_sessions",{body:t,...r,headers:O([{"OpenAI-Beta":"assistants=v2"},r==null?void 0:r.headers])})}}class cn extends P{constructor(){super(...arguments),this.sessions=new Xa(this._client),this.transcriptionSessions=new Ka(this._client)}}cn.Sessions=Xa;cn.TranscriptionSessions=Ka;class Ga extends P{create(t,r,i){return this._client.post(I`/threads/${t}/messages`,{body:r,...i,headers:O([{"OpenAI-Beta":"assistants=v2"},i==null?void 0:i.headers])})}retrieve(t,r,i){const{thread_id:n}=r;return this._client.get(I`/threads/${n}/messages/${t}`,{...i,headers:O([{"OpenAI-Beta":"assistants=v2"},i==null?void 0:i.headers])})}update(t,r,i){const{thread_id:n,...o}=r;return this._client.post(I`/threads/${n}/messages/${t}`,{body:o,...i,headers:O([{"OpenAI-Beta":"assistants=v2"},i==null?void 0:i.headers])})}list(t,r={},i){return this._client.getAPIList(I`/threads/${t}/messages`,W,{query:r,...i,headers:O([{"OpenAI-Beta":"assistants=v2"},i==null?void 0:i.headers])})}delete(t,r,i){const{thread_id:n}=r;return this._client.delete(I`/threads/${n}/messages/${t}`,{...i,headers:O([{"OpenAI-Beta":"assistants=v2"},i==null?void 0:i.headers])})}}class Ha extends P{retrieve(t,r,i){const{thread_id:n,run_id:o,...a}=r;return this._client.get(I`/threads/${n}/runs/${o}/steps/${t}`,{query:a,...i,headers:O([{"OpenAI-Beta":"assistants=v2"},i==null?void 0:i.headers])})}list(t,r,i){const{thread_id:n,...o}=r;return this._client.getAPIList(I`/threads/${n}/runs/${t}/steps`,W,{query:o,...i,headers:O([{"OpenAI-Beta":"assistants=v2"},i==null?void 0:i.headers])})}}const Df=e=>{if(typeof Buffer<"u"){const t=Buffer.from(e,"base64");return Array.from(new Float32Array(t.buffer,t.byteOffset,t.length/Float32Array.BYTES_PER_ELEMENT))}else{const t=atob(e),r=t.length,i=new Uint8Array(r);for(let n=0;n<r;n++)i[n]=t.charCodeAt(n);return Array.from(new Float32Array(i.buffer))}};var Cn={};const Ae=e=>{var t,r,i,n;if(typeof globalThis.process<"u")return((t=Cn==null?void 0:Cn[e])==null?void 0:t.trim())??void 0;if(typeof globalThis.Deno<"u")return(n=(i=(r=globalThis.Deno.env)==null?void 0:r.get)==null?void 0:i.call(r,e))==null?void 0:n.trim()};var H,we,Kn,fe,Dt,ue,ke,Ze,$e,Vt,ie,Rt,Ct,at,nt,rt,So,Io,xo,zo,Oo,No,jo;class st extends sr{constructor(){super(...arguments),H.add(this),Kn.set(this,[]),fe.set(this,{}),Dt.set(this,{}),ue.set(this,void 0),ke.set(this,void 0),Ze.set(this,void 0),$e.set(this,void 0),Vt.set(this,void 0),ie.set(this,void 0),Rt.set(this,void 0),Ct.set(this,void 0),at.set(this,void 0)}[(Kn=new WeakMap,fe=new WeakMap,Dt=new WeakMap,ue=new WeakMap,ke=new WeakMap,Ze=new WeakMap,$e=new WeakMap,Vt=new WeakMap,ie=new WeakMap,Rt=new WeakMap,Ct=new WeakMap,at=new WeakMap,H=new WeakSet,Symbol.asyncIterator)](){const t=[],r=[];let i=!1;return this.on("event",n=>{const o=r.shift();o?o.resolve(n):t.push(n)}),this.on("end",()=>{i=!0;for(const n of r)n.resolve(void 0);r.length=0}),this.on("abort",n=>{i=!0;for(const o of r)o.reject(n);r.length=0}),this.on("error",n=>{i=!0;for(const o of r)o.reject(n);r.length=0}),{next:async()=>t.length?{value:t.shift(),done:!1}:i?{value:void 0,done:!0}:new Promise((o,a)=>r.push({resolve:o,reject:a})).then(o=>o?{value:o,done:!1}:{value:void 0,done:!0}),return:async()=>(this.abort(),{value:void 0,done:!0})}}static fromReadableStream(t){const r=new we;return r._run(()=>r._fromReadableStream(t)),r}async _fromReadableStream(t,r){var o;const i=r==null?void 0:r.signal;i&&(i.aborted&&this.controller.abort(),i.addEventListener("abort",()=>this.controller.abort())),this._connected();const n=me.fromReadableStream(t,this.controller);for await(const a of n)f(this,H,"m",nt).call(this,a);if((o=n.controller.signal)!=null&&o.aborted)throw new ae;return this._addRun(f(this,H,"m",rt).call(this))}toReadableStream(){return new me(this[Symbol.asyncIterator].bind(this),this.controller).toReadableStream()}static createToolAssistantStream(t,r,i,n){const o=new we;return o._run(()=>o._runToolAssistantStream(t,r,i,{...n,headers:{...n==null?void 0:n.headers,"X-Stainless-Helper-Method":"stream"}})),o}async _createToolAssistantStream(t,r,i,n){var u;const o=n==null?void 0:n.signal;o&&(o.aborted&&this.controller.abort(),o.addEventListener("abort",()=>this.controller.abort()));const a={...i,stream:!0},s=await t.submitToolOutputs(r,a,{...n,signal:this.controller.signal});this._connected();for await(const c of s)f(this,H,"m",nt).call(this,c);if((u=s.controller.signal)!=null&&u.aborted)throw new ae;return this._addRun(f(this,H,"m",rt).call(this))}static createThreadAssistantStream(t,r,i){const n=new we;return n._run(()=>n._threadAssistantStream(t,r,{...i,headers:{...i==null?void 0:i.headers,"X-Stainless-Helper-Method":"stream"}})),n}static createAssistantStream(t,r,i,n){const o=new we;return o._run(()=>o._runAssistantStream(t,r,i,{...n,headers:{...n==null?void 0:n.headers,"X-Stainless-Helper-Method":"stream"}})),o}currentEvent(){return f(this,Rt,"f")}currentRun(){return f(this,Ct,"f")}currentMessageSnapshot(){return f(this,ue,"f")}currentRunStepSnapshot(){return f(this,at,"f")}async finalRunSteps(){return await this.done(),Object.values(f(this,fe,"f"))}async finalMessages(){return await this.done(),Object.values(f(this,Dt,"f"))}async finalRun(){if(await this.done(),!f(this,ke,"f"))throw Error("Final run was not received.");return f(this,ke,"f")}async _createThreadAssistantStream(t,r,i){var s;const n=i==null?void 0:i.signal;n&&(n.aborted&&this.controller.abort(),n.addEventListener("abort",()=>this.controller.abort()));const o={...r,stream:!0},a=await t.createAndRun(o,{...i,signal:this.controller.signal});this._connected();for await(const u of a)f(this,H,"m",nt).call(this,u);if((s=a.controller.signal)!=null&&s.aborted)throw new ae;return this._addRun(f(this,H,"m",rt).call(this))}async _createAssistantStream(t,r,i,n){var u;const o=n==null?void 0:n.signal;o&&(o.aborted&&this.controller.abort(),o.addEventListener("abort",()=>this.controller.abort()));const a={...i,stream:!0},s=await t.create(r,a,{...n,signal:this.controller.signal});this._connected();for await(const c of s)f(this,H,"m",nt).call(this,c);if((u=s.controller.signal)!=null&&u.aborted)throw new ae;return this._addRun(f(this,H,"m",rt).call(this))}static accumulateDelta(t,r){for(const[i,n]of Object.entries(r)){if(!t.hasOwnProperty(i)){t[i]=n;continue}let o=t[i];if(o==null){t[i]=n;continue}if(i==="index"||i==="type"){t[i]=n;continue}if(typeof o=="string"&&typeof n=="string")o+=n;else if(typeof o=="number"&&typeof n=="number")o+=n;else if(jn(o)&&jn(n))o=this.accumulateDelta(o,n);else if(Array.isArray(o)&&Array.isArray(n)){if(o.every(a=>typeof a=="string"||typeof a=="number")){o.push(...n);continue}for(const a of n){if(!jn(a))throw new Error(`Expected array delta entry to be an object but got: ${a}`);const s=a.index;if(s==null)throw console.error(a),new Error("Expected array delta entry to have an `index` property");if(typeof s!="number")throw new Error(`Expected array delta entry \`index\` property to be a number but got ${s}`);const u=o[s];u==null?o.push(a):o[s]=this.accumulateDelta(u,a)}continue}else throw Error(`Unhandled record type: ${i}, deltaValue: ${n}, accValue: ${o}`);t[i]=o}return t}_addRun(t){return t}async _threadAssistantStream(t,r,i){return await this._createThreadAssistantStream(r,t,i)}async _runAssistantStream(t,r,i,n){return await this._createAssistantStream(r,t,i,n)}async _runToolAssistantStream(t,r,i,n){return await this._createToolAssistantStream(r,t,i,n)}}we=st,nt=function(t){if(!this.ended)switch(j(this,Rt,t),f(this,H,"m",xo).call(this,t),t.event){case"thread.created":break;case"thread.run.created":case"thread.run.queued":case"thread.run.in_progress":case"thread.run.requires_action":case"thread.run.completed":case"thread.run.incomplete":case"thread.run.failed":case"thread.run.cancelling":case"thread.run.cancelled":case"thread.run.expired":f(this,H,"m",jo).call(this,t);break;case"thread.run.step.created":case"thread.run.step.in_progress":case"thread.run.step.delta":case"thread.run.step.completed":case"thread.run.step.failed":case"thread.run.step.cancelled":case"thread.run.step.expired":f(this,H,"m",Io).call(this,t);break;case"thread.message.created":case"thread.message.in_progress":case"thread.message.delta":case"thread.message.completed":case"thread.message.incomplete":f(this,H,"m",So).call(this,t);break;case"error":throw new Error("Encountered an error event in event processing - errors should be processed earlier")}},rt=function(){if(this.ended)throw new U("stream has ended, this shouldn't happen");if(!f(this,ke,"f"))throw Error("Final run has not been received");return f(this,ke,"f")},So=function(t){const[r,i]=f(this,H,"m",Oo).call(this,t,f(this,ue,"f"));j(this,ue,r),f(this,Dt,"f")[r.id]=r;for(const n of i){const o=r.content[n.index];(o==null?void 0:o.type)=="text"&&this._emit("textCreated",o.text)}switch(t.event){case"thread.message.created":this._emit("messageCreated",t.data);break;case"thread.message.in_progress":break;case"thread.message.delta":if(this._emit("messageDelta",t.data.delta,r),t.data.delta.content)for(const n of t.data.delta.content){if(n.type=="text"&&n.text){let o=n.text,a=r.content[n.index];if(a&&a.type=="text")this._emit("textDelta",o,a.text);else throw Error("The snapshot associated with this text delta is not text or missing")}if(n.index!=f(this,Ze,"f")){if(f(this,$e,"f"))switch(f(this,$e,"f").type){case"text":this._emit("textDone",f(this,$e,"f").text,f(this,ue,"f"));break;case"image_file":this._emit("imageFileDone",f(this,$e,"f").image_file,f(this,ue,"f"));break}j(this,Ze,n.index)}j(this,$e,r.content[n.index])}break;case"thread.message.completed":case"thread.message.incomplete":if(f(this,Ze,"f")!==void 0){const n=t.data.content[f(this,Ze,"f")];if(n)switch(n.type){case"image_file":this._emit("imageFileDone",n.image_file,f(this,ue,"f"));break;case"text":this._emit("textDone",n.text,f(this,ue,"f"));break}}f(this,ue,"f")&&this._emit("messageDone",t.data),j(this,ue,void 0)}},Io=function(t){const r=f(this,H,"m",zo).call(this,t);switch(j(this,at,r),t.event){case"thread.run.step.created":this._emit("runStepCreated",t.data);break;case"thread.run.step.delta":const i=t.data.delta;if(i.step_details&&i.step_details.type=="tool_calls"&&i.step_details.tool_calls&&r.step_details.type=="tool_calls")for(const o of i.step_details.tool_calls)o.index==f(this,Vt,"f")?this._emit("toolCallDelta",o,r.step_details.tool_calls[o.index]):(f(this,ie,"f")&&this._emit("toolCallDone",f(this,ie,"f")),j(this,Vt,o.index),j(this,ie,r.step_details.tool_calls[o.index]),f(this,ie,"f")&&this._emit("toolCallCreated",f(this,ie,"f")));this._emit("runStepDelta",t.data.delta,r);break;case"thread.run.step.completed":case"thread.run.step.failed":case"thread.run.step.cancelled":case"thread.run.step.expired":j(this,at,void 0),t.data.step_details.type=="tool_calls"&&f(this,ie,"f")&&(this._emit("toolCallDone",f(this,ie,"f")),j(this,ie,void 0)),this._emit("runStepDone",t.data,r);break}},xo=function(t){f(this,Kn,"f").push(t),this._emit("event",t)},zo=function(t){switch(t.event){case"thread.run.step.created":return f(this,fe,"f")[t.data.id]=t.data,t.data;case"thread.run.step.delta":let r=f(this,fe,"f")[t.data.id];if(!r)throw Error("Received a RunStepDelta before creation of a snapshot");let i=t.data;if(i.delta){const n=we.accumulateDelta(r,i.delta);f(this,fe,"f")[t.data.id]=n}return f(this,fe,"f")[t.data.id];case"thread.run.step.completed":case"thread.run.step.failed":case"thread.run.step.cancelled":case"thread.run.step.expired":case"thread.run.step.in_progress":f(this,fe,"f")[t.data.id]=t.data;break}if(f(this,fe,"f")[t.data.id])return f(this,fe,"f")[t.data.id];throw new Error("No snapshot available")},Oo=function(t,r){let i=[];switch(t.event){case"thread.message.created":return[t.data,i];case"thread.message.delta":if(!r)throw Error("Received a delta with no existing snapshot (there should be one from message creation)");let n=t.data;if(n.delta.content)for(const o of n.delta.content)if(o.index in r.content){let a=r.content[o.index];r.content[o.index]=f(this,H,"m",No).call(this,o,a)}else r.content[o.index]=o,i.push(o);return[r,i];case"thread.message.in_progress":case"thread.message.completed":case"thread.message.incomplete":if(r)return[r,i];throw Error("Received thread message event with no existing snapshot")}throw Error("Tried to accumulate a non-message event")},No=function(t,r){return we.accumulateDelta(r,t)},jo=function(t){switch(j(this,Ct,t.data),t.event){case"thread.run.created":break;case"thread.run.queued":break;case"thread.run.in_progress":break;case"thread.run.requires_action":case"thread.run.cancelled":case"thread.run.failed":case"thread.run.completed":case"thread.run.expired":case"thread.run.incomplete":j(this,ke,t.data),f(this,ie,"f")&&(this._emit("toolCallDone",f(this,ie,"f")),j(this,ie,void 0));break}};let mr=class extends P{constructor(){super(...arguments),this.steps=new Ha(this._client)}create(t,r,i){const{include:n,...o}=r;return this._client.post(I`/threads/${t}/runs`,{query:{include:n},body:o,...i,headers:O([{"OpenAI-Beta":"assistants=v2"},i==null?void 0:i.headers]),stream:r.stream??!1})}retrieve(t,r,i){const{thread_id:n}=r;return this._client.get(I`/threads/${n}/runs/${t}`,{...i,headers:O([{"OpenAI-Beta":"assistants=v2"},i==null?void 0:i.headers])})}update(t,r,i){const{thread_id:n,...o}=r;return this._client.post(I`/threads/${n}/runs/${t}`,{body:o,...i,headers:O([{"OpenAI-Beta":"assistants=v2"},i==null?void 0:i.headers])})}list(t,r={},i){return this._client.getAPIList(I`/threads/${t}/runs`,W,{query:r,...i,headers:O([{"OpenAI-Beta":"assistants=v2"},i==null?void 0:i.headers])})}cancel(t,r,i){const{thread_id:n}=r;return this._client.post(I`/threads/${n}/runs/${t}/cancel`,{...i,headers:O([{"OpenAI-Beta":"assistants=v2"},i==null?void 0:i.headers])})}async createAndPoll(t,r,i){const n=await this.create(t,r,i);return await this.poll(n.id,{thread_id:t},i)}createAndStream(t,r,i){return st.createAssistantStream(t,this._client.beta.threads.runs,r,i)}async poll(t,r,i){var o;const n=O([i==null?void 0:i.headers,{"X-Stainless-Poll-Helper":"true","X-Stainless-Custom-Poll-Interval":((o=i==null?void 0:i.pollIntervalMs)==null?void 0:o.toString())??void 0}]);for(;;){const{data:a,response:s}=await this.retrieve(t,r,{...i,headers:{...i==null?void 0:i.headers,...n}}).withResponse();switch(a.status){case"queued":case"in_progress":case"cancelling":let u=5e3;if(i!=null&&i.pollIntervalMs)u=i.pollIntervalMs;else{const c=s.headers.get("openai-poll-after-ms");if(c){const p=parseInt(c);isNaN(p)||(u=p)}}await ht(u);break;case"requires_action":case"incomplete":case"cancelled":case"completed":case"failed":case"expired":return a}}}stream(t,r,i){return st.createAssistantStream(t,this._client.beta.threads.runs,r,i)}submitToolOutputs(t,r,i){const{thread_id:n,...o}=r;return this._client.post(I`/threads/${n}/runs/${t}/submit_tool_outputs`,{body:o,...i,headers:O([{"OpenAI-Beta":"assistants=v2"},i==null?void 0:i.headers]),stream:r.stream??!1})}async submitToolOutputsAndPoll(t,r,i){const n=await this.submitToolOutputs(t,r,i);return await this.poll(n.id,r,i)}submitToolOutputsStream(t,r,i){return st.createToolAssistantStream(t,this._client.beta.threads.runs,r,i)}};mr.Steps=Ha;class ln extends P{constructor(){super(...arguments),this.runs=new mr(this._client),this.messages=new Ga(this._client)}create(t={},r){return this._client.post("/threads",{body:t,...r,headers:O([{"OpenAI-Beta":"assistants=v2"},r==null?void 0:r.headers])})}retrieve(t,r){return this._client.get(I`/threads/${t}`,{...r,headers:O([{"OpenAI-Beta":"assistants=v2"},r==null?void 0:r.headers])})}update(t,r,i){return this._client.post(I`/threads/${t}`,{body:r,...i,headers:O([{"OpenAI-Beta":"assistants=v2"},i==null?void 0:i.headers])})}delete(t,r){return this._client.delete(I`/threads/${t}`,{...r,headers:O([{"OpenAI-Beta":"assistants=v2"},r==null?void 0:r.headers])})}createAndRun(t,r){return this._client.post("/threads/runs",{body:t,...r,headers:O([{"OpenAI-Beta":"assistants=v2"},r==null?void 0:r.headers]),stream:t.stream??!1})}async createAndRunPoll(t,r){const i=await this.createAndRun(t,r);return await this.runs.poll(i.id,{thread_id:i.thread_id},r)}createAndRunStream(t,r){return st.createThreadAssistantStream(t,this._client.beta.threads,r)}}ln.Runs=mr;ln.Messages=Ga;class vt extends P{constructor(){super(...arguments),this.realtime=new cn(this._client),this.assistants=new Va(this._client),this.threads=new ln(this._client)}}vt.Realtime=cn;vt.Assistants=Va;vt.Threads=ln;class Ya extends P{create(t,r){return this._client.post("/completions",{body:t,...r,stream:t.stream??!1})}}class Qa extends P{retrieve(t,r,i){const{container_id:n}=r;return this._client.get(I`/containers/${n}/files/${t}/content`,{...i,headers:O([{Accept:"application/binary"},i==null?void 0:i.headers]),__binaryResponse:!0})}}let hr=class extends P{constructor(){super(...arguments),this.content=new Qa(this._client)}create(t,r,i){return this._client.post(I`/containers/${t}/files`,Ie({body:r,...i},this._client))}retrieve(t,r,i){const{container_id:n}=r;return this._client.get(I`/containers/${n}/files/${t}`,i)}list(t,r={},i){return this._client.getAPIList(I`/containers/${t}/files`,W,{query:r,...i})}delete(t,r,i){const{container_id:n}=r;return this._client.delete(I`/containers/${n}/files/${t}`,{...i,headers:O([{Accept:"*/*"},i==null?void 0:i.headers])})}};hr.Content=Qa;class pr extends P{constructor(){super(...arguments),this.files=new hr(this._client)}create(t,r){return this._client.post("/containers",{body:t,...r})}retrieve(t,r){return this._client.get(I`/containers/${t}`,r)}list(t={},r){return this._client.getAPIList("/containers",W,{query:t,...r})}delete(t,r){return this._client.delete(I`/containers/${t}`,{...r,headers:O([{Accept:"*/*"},r==null?void 0:r.headers])})}}pr.Files=hr;class es extends P{create(t,r){const i=!!t.encoding_format;let n=i?t.encoding_format:"base64";i&&G(this._client).debug("embeddings/user defined encoding_format:",t.encoding_format);const o=this._client.post("/embeddings",{body:{...t,encoding_format:n},...r});return i?o:(G(this._client).debug("embeddings/decoding base64 embeddings from base64"),o._thenUnwrap(a=>(a&&a.data&&a.data.forEach(s=>{const u=s.embedding;s.embedding=Df(u)}),a)))}}class ts extends P{retrieve(t,r,i){const{eval_id:n,run_id:o}=r;return this._client.get(I`/evals/${n}/runs/${o}/output_items/${t}`,i)}list(t,r,i){const{eval_id:n,...o}=r;return this._client.getAPIList(I`/evals/${n}/runs/${t}/output_items`,W,{query:o,...i})}}class gr extends P{constructor(){super(...arguments),this.outputItems=new ts(this._client)}create(t,r,i){return this._client.post(I`/evals/${t}/runs`,{body:r,...i})}retrieve(t,r,i){const{eval_id:n}=r;return this._client.get(I`/evals/${n}/runs/${t}`,i)}list(t,r={},i){return this._client.getAPIList(I`/evals/${t}/runs`,W,{query:r,...i})}delete(t,r,i){const{eval_id:n}=r;return this._client.delete(I`/evals/${n}/runs/${t}`,i)}cancel(t,r,i){const{eval_id:n}=r;return this._client.post(I`/evals/${n}/runs/${t}`,i)}}gr.OutputItems=ts;class vr extends P{constructor(){super(...arguments),this.runs=new gr(this._client)}create(t,r){return this._client.post("/evals",{body:t,...r})}retrieve(t,r){return this._client.get(I`/evals/${t}`,r)}update(t,r,i){return this._client.post(I`/evals/${t}`,{body:r,...i})}list(t={},r){return this._client.getAPIList("/evals",W,{query:t,...r})}delete(t,r){return this._client.delete(I`/evals/${t}`,r)}}vr.Runs=gr;let ns=class extends P{create(t,r){return this._client.post("/files",Ie({body:t,...r},this._client))}retrieve(t,r){return this._client.get(I`/files/${t}`,r)}list(t={},r){return this._client.getAPIList("/files",W,{query:t,...r})}delete(t,r){return this._client.delete(I`/files/${t}`,r)}content(t,r){return this._client.get(I`/files/${t}/content`,{...r,headers:O([{Accept:"application/binary"},r==null?void 0:r.headers]),__binaryResponse:!0})}async waitForProcessing(t,{pollInterval:r=5e3,maxWait:i=30*60*1e3}={}){const n=new Set(["processed","error","deleted"]),o=Date.now();let a=await this.retrieve(t);for(;!a.status||!n.has(a.status);)if(await ht(r),a=await this.retrieve(t),Date.now()-o>i)throw new or({message:`Giving up on waiting for file ${t} to finish processing after ${i} milliseconds.`});return a}};class rs extends P{}let is=class extends P{run(t,r){return this._client.post("/fine_tuning/alpha/graders/run",{body:t,...r})}validate(t,r){return this._client.post("/fine_tuning/alpha/graders/validate",{body:t,...r})}};class _r extends P{constructor(){super(...arguments),this.graders=new is(this._client)}}_r.Graders=is;class os extends P{create(t,r,i){return this._client.getAPIList(I`/fine_tuning/checkpoints/${t}/permissions`,un,{body:r,method:"post",...i})}retrieve(t,r={},i){return this._client.get(I`/fine_tuning/checkpoints/${t}/permissions`,{query:r,...i})}delete(t,r,i){const{fine_tuned_model_checkpoint:n}=r;return this._client.delete(I`/fine_tuning/checkpoints/${n}/permissions/${t}`,i)}}let br=class extends P{constructor(){super(...arguments),this.permissions=new os(this._client)}};br.Permissions=os;class as extends P{list(t,r={},i){return this._client.getAPIList(I`/fine_tuning/jobs/${t}/checkpoints`,W,{query:r,...i})}}class yr extends P{constructor(){super(...arguments),this.checkpoints=new as(this._client)}create(t,r){return this._client.post("/fine_tuning/jobs",{body:t,...r})}retrieve(t,r){return this._client.get(I`/fine_tuning/jobs/${t}`,r)}list(t={},r){return this._client.getAPIList("/fine_tuning/jobs",W,{query:t,...r})}cancel(t,r){return this._client.post(I`/fine_tuning/jobs/${t}/cancel`,r)}listEvents(t,r={},i){return this._client.getAPIList(I`/fine_tuning/jobs/${t}/events`,W,{query:r,...i})}pause(t,r){return this._client.post(I`/fine_tuning/jobs/${t}/pause`,r)}resume(t,r){return this._client.post(I`/fine_tuning/jobs/${t}/resume`,r)}}yr.Checkpoints=as;class We extends P{constructor(){super(...arguments),this.methods=new rs(this._client),this.jobs=new yr(this._client),this.checkpoints=new br(this._client),this.alpha=new _r(this._client)}}We.Methods=rs;We.Jobs=yr;We.Checkpoints=br;We.Alpha=_r;class ss extends P{}class $r extends P{constructor(){super(...arguments),this.graderModels=new ss(this._client)}}$r.GraderModels=ss;class us extends P{createVariation(t,r){return this._client.post("/images/variations",Ie({body:t,...r},this._client))}edit(t,r){return this._client.post("/images/edits",Ie({body:t,...r,stream:t.stream??!1},this._client))}generate(t,r){return this._client.post("/images/generations",{body:t,...r,stream:t.stream??!1})}}class cs extends P{retrieve(t,r){return this._client.get(I`/models/${t}`,r)}list(t){return this._client.getAPIList("/models",un,t)}delete(t,r){return this._client.delete(I`/models/${t}`,r)}}class ls extends P{create(t,r){return this._client.post("/moderations",{body:t,...r})}}function Rf(e,t){return!t||!Zf(t)?{...e,output_parsed:null,output:e.output.map(r=>r.type==="function_call"?{...r,parsed_arguments:null}:r.type==="message"?{...r,content:r.content.map(i=>({...i,parsed:null}))}:r)}:ds(e,t)}function ds(e,t){const r=e.output.map(n=>{if(n.type==="function_call")return{...n,parsed_arguments:Ff(t,n)};if(n.type==="message"){const o=n.content.map(a=>a.type==="output_text"?{...a,parsed:Cf(t,a.text)}:a);return{...n,content:o}}return n}),i=Object.assign({},e,{output:r});return Object.getOwnPropertyDescriptor(e,"output_text")||Gn(i),Object.defineProperty(i,"output_parsed",{enumerable:!0,get(){for(const n of i.output)if(n.type==="message"){for(const o of n.content)if(o.type==="output_text"&&o.parsed!==null)return o.parsed}return null}}),i}function Cf(e,t){var r,i,n,o;return((i=(r=e.text)==null?void 0:r.format)==null?void 0:i.type)!=="json_schema"?null:"$parseRaw"in((n=e.text)==null?void 0:n.format)?((o=e.text)==null?void 0:o.format).$parseRaw(t):JSON.parse(t)}function Zf(e){var t;return!!ur((t=e.text)==null?void 0:t.format)}function Lf(e){return(e==null?void 0:e.$brand)==="auto-parseable-tool"}function Mf(e,t){return e.find(r=>r.type==="function"&&r.name===t)}function Ff(e,t){const r=Mf(e.tools??[],t.name);return{...t,...t,parsed_arguments:Lf(r)?r.$parseRaw(t.arguments):r!=null&&r.strict?JSON.parse(t.arguments):null}}function Gn(e){const t=[];for(const r of e.output)if(r.type==="message")for(const i of r.content)i.type==="output_text"&&t.push(i.text);e.output_text=t.join("")}var Ee,Ot,be,Nt,Uo,Po,Ao,Eo;class wr extends sr{constructor(t){super(),Ee.add(this),Ot.set(this,void 0),be.set(this,void 0),Nt.set(this,void 0),j(this,Ot,t)}static createResponse(t,r,i){const n=new wr(r);return n._run(()=>n._createOrRetrieveResponse(t,r,{...i,headers:{...i==null?void 0:i.headers,"X-Stainless-Helper-Method":"stream"}})),n}async _createOrRetrieveResponse(t,r,i){var s;const n=i==null?void 0:i.signal;n&&(n.aborted&&this.controller.abort(),n.addEventListener("abort",()=>this.controller.abort())),f(this,Ee,"m",Uo).call(this);let o,a=null;"response_id"in r?(o=await t.responses.retrieve(r.response_id,{stream:!0},{...i,signal:this.controller.signal,stream:!0}),a=r.starting_after??null):o=await t.responses.create({...r,stream:!0},{...i,signal:this.controller.signal}),this._connected();for await(const u of o)f(this,Ee,"m",Po).call(this,u,a);if((s=o.controller.signal)!=null&&s.aborted)throw new ae;return f(this,Ee,"m",Ao).call(this)}[(Ot=new WeakMap,be=new WeakMap,Nt=new WeakMap,Ee=new WeakSet,Uo=function(){this.ended||j(this,be,void 0)},Po=function(r,i){if(this.ended)return;const n=(a,s)=>{(i==null||s.sequence_number>i)&&this._emit(a,s)},o=f(this,Ee,"m",Eo).call(this,r);switch(n("event",r),r.type){case"response.output_text.delta":{const a=o.output[r.output_index];if(!a)throw new U(`missing output at index ${r.output_index}`);if(a.type==="message"){const s=a.content[r.content_index];if(!s)throw new U(`missing content at index ${r.content_index}`);if(s.type!=="output_text")throw new U(`expected content to be 'output_text', got ${s.type}`);n("response.output_text.delta",{...r,snapshot:s.text})}break}case"response.function_call_arguments.delta":{const a=o.output[r.output_index];if(!a)throw new U(`missing output at index ${r.output_index}`);a.type==="function_call"&&n("response.function_call_arguments.delta",{...r,snapshot:a.arguments});break}default:n(r.type,r);break}},Ao=function(){if(this.ended)throw new U("stream has ended, this shouldn't happen");const r=f(this,be,"f");if(!r)throw new U("request ended without sending any events");j(this,be,void 0);const i=Bf(r,f(this,Ot,"f"));return j(this,Nt,i),i},Eo=function(r){let i=f(this,be,"f");if(!i){if(r.type!=="response.created")throw new U(`When snapshot hasn't been set yet, expected 'response.created' event, got ${r.type}`);return i=j(this,be,r.response),i}switch(r.type){case"response.output_item.added":{i.output.push(r.item);break}case"response.content_part.added":{const n=i.output[r.output_index];if(!n)throw new U(`missing output at index ${r.output_index}`);n.type==="message"&&n.content.push(r.part);break}case"response.output_text.delta":{const n=i.output[r.output_index];if(!n)throw new U(`missing output at index ${r.output_index}`);if(n.type==="message"){const o=n.content[r.content_index];if(!o)throw new U(`missing content at index ${r.content_index}`);if(o.type!=="output_text")throw new U(`expected content to be 'output_text', got ${o.type}`);o.text+=r.delta}break}case"response.function_call_arguments.delta":{const n=i.output[r.output_index];if(!n)throw new U(`missing output at index ${r.output_index}`);n.type==="function_call"&&(n.arguments+=r.delta);break}case"response.completed":{j(this,be,r.response);break}}return i},Symbol.asyncIterator)](){const t=[],r=[];let i=!1;return this.on("event",n=>{const o=r.shift();o?o.resolve(n):t.push(n)}),this.on("end",()=>{i=!0;for(const n of r)n.resolve(void 0);r.length=0}),this.on("abort",n=>{i=!0;for(const o of r)o.reject(n);r.length=0}),this.on("error",n=>{i=!0;for(const o of r)o.reject(n);r.length=0}),{next:async()=>t.length?{value:t.shift(),done:!1}:i?{value:void 0,done:!0}:new Promise((o,a)=>r.push({resolve:o,reject:a})).then(o=>o?{value:o,done:!1}:{value:void 0,done:!0}),return:async()=>(this.abort(),{value:void 0,done:!0})}}async finalResponse(){await this.done();const t=f(this,Nt,"f");if(!t)throw new U("stream ended without producing a ChatCompletion");return t}}function Bf(e,t){return Rf(e,t)}class fs extends P{list(t,r={},i){return this._client.getAPIList(I`/responses/${t}/input_items`,W,{query:r,...i})}}class kr extends P{constructor(){super(...arguments),this.inputItems=new fs(this._client)}create(t,r){return this._client.post("/responses",{body:t,...r,stream:t.stream??!1})._thenUnwrap(i=>("object"in i&&i.object==="response"&&Gn(i),i))}retrieve(t,r={},i){return this._client.get(I`/responses/${t}`,{query:r,...i,stream:(r==null?void 0:r.stream)??!1})._thenUnwrap(n=>("object"in n&&n.object==="response"&&Gn(n),n))}delete(t,r){return this._client.delete(I`/responses/${t}`,{...r,headers:O([{Accept:"*/*"},r==null?void 0:r.headers])})}parse(t,r){return this._client.responses.create(t,r)._thenUnwrap(i=>ds(i,t))}stream(t,r){return wr.createResponse(this._client,t,r)}cancel(t,r){return this._client.post(I`/responses/${t}/cancel`,r)}}kr.InputItems=fs;class ms extends P{create(t,r,i){return this._client.post(I`/uploads/${t}/parts`,Ie({body:r,...i},this._client))}}class Sr extends P{constructor(){super(...arguments),this.parts=new ms(this._client)}create(t,r){return this._client.post("/uploads",{body:t,...r})}cancel(t,r){return this._client.post(I`/uploads/${t}/cancel`,r)}complete(t,r,i){return this._client.post(I`/uploads/${t}/complete`,{body:r,...i})}}Sr.Parts=ms;const Jf=async e=>{const t=await Promise.allSettled(e),r=t.filter(n=>n.status==="rejected");if(r.length){for(const n of r)console.error(n.reason);throw new Error(`${r.length} promise(s) failed - see the above errors`)}const i=[];for(const n of t)n.status==="fulfilled"&&i.push(n.value);return i};class hs extends P{create(t,r,i){return this._client.post(I`/vector_stores/${t}/file_batches`,{body:r,...i,headers:O([{"OpenAI-Beta":"assistants=v2"},i==null?void 0:i.headers])})}retrieve(t,r,i){const{vector_store_id:n}=r;return this._client.get(I`/vector_stores/${n}/file_batches/${t}`,{...i,headers:O([{"OpenAI-Beta":"assistants=v2"},i==null?void 0:i.headers])})}cancel(t,r,i){const{vector_store_id:n}=r;return this._client.post(I`/vector_stores/${n}/file_batches/${t}/cancel`,{...i,headers:O([{"OpenAI-Beta":"assistants=v2"},i==null?void 0:i.headers])})}async createAndPoll(t,r,i){const n=await this.create(t,r);return await this.poll(t,n.id,i)}listFiles(t,r,i){const{vector_store_id:n,...o}=r;return this._client.getAPIList(I`/vector_stores/${n}/file_batches/${t}/files`,W,{query:o,...i,headers:O([{"OpenAI-Beta":"assistants=v2"},i==null?void 0:i.headers])})}async poll(t,r,i){var o;const n=O([i==null?void 0:i.headers,{"X-Stainless-Poll-Helper":"true","X-Stainless-Custom-Poll-Interval":((o=i==null?void 0:i.pollIntervalMs)==null?void 0:o.toString())??void 0}]);for(;;){const{data:a,response:s}=await this.retrieve(r,{vector_store_id:t},{...i,headers:n}).withResponse();switch(a.status){case"in_progress":let u=5e3;if(i!=null&&i.pollIntervalMs)u=i.pollIntervalMs;else{const c=s.headers.get("openai-poll-after-ms");if(c){const p=parseInt(c);isNaN(p)||(u=p)}}await ht(u);break;case"failed":case"cancelled":case"completed":return a}}}async uploadAndPoll(t,{files:r,fileIds:i=[]},n){if(r==null||r.length==0)throw new Error("No `files` provided to process. If you've already uploaded files you should use `.createAndPoll()` instead");const o=(n==null?void 0:n.maxConcurrency)??5,a=Math.min(o,r.length),s=this._client,u=r.values(),c=[...i];async function p(y){for(let v of y){const _=await s.files.create({file:v,purpose:"assistants"},n);c.push(_.id)}}const d=Array(a).fill(u).map(p);return await Jf(d),await this.createAndPoll(t,{file_ids:c})}}class ps extends P{create(t,r,i){return this._client.post(I`/vector_stores/${t}/files`,{body:r,...i,headers:O([{"OpenAI-Beta":"assistants=v2"},i==null?void 0:i.headers])})}retrieve(t,r,i){const{vector_store_id:n}=r;return this._client.get(I`/vector_stores/${n}/files/${t}`,{...i,headers:O([{"OpenAI-Beta":"assistants=v2"},i==null?void 0:i.headers])})}update(t,r,i){const{vector_store_id:n,...o}=r;return this._client.post(I`/vector_stores/${n}/files/${t}`,{body:o,...i,headers:O([{"OpenAI-Beta":"assistants=v2"},i==null?void 0:i.headers])})}list(t,r={},i){return this._client.getAPIList(I`/vector_stores/${t}/files`,W,{query:r,...i,headers:O([{"OpenAI-Beta":"assistants=v2"},i==null?void 0:i.headers])})}delete(t,r,i){const{vector_store_id:n}=r;return this._client.delete(I`/vector_stores/${n}/files/${t}`,{...i,headers:O([{"OpenAI-Beta":"assistants=v2"},i==null?void 0:i.headers])})}async createAndPoll(t,r,i){const n=await this.create(t,r,i);return await this.poll(t,n.id,i)}async poll(t,r,i){var o;const n=O([i==null?void 0:i.headers,{"X-Stainless-Poll-Helper":"true","X-Stainless-Custom-Poll-Interval":((o=i==null?void 0:i.pollIntervalMs)==null?void 0:o.toString())??void 0}]);for(;;){const a=await this.retrieve(r,{vector_store_id:t},{...i,headers:n}).withResponse(),s=a.data;switch(s.status){case"in_progress":let u=5e3;if(i!=null&&i.pollIntervalMs)u=i.pollIntervalMs;else{const c=a.response.headers.get("openai-poll-after-ms");if(c){const p=parseInt(c);isNaN(p)||(u=p)}}await ht(u);break;case"failed":case"completed":return s}}}async upload(t,r,i){const n=await this._client.files.create({file:r,purpose:"assistants"},i);return this.create(t,{file_id:n.id},i)}async uploadAndPoll(t,r,i){const n=await this.upload(t,r,i);return await this.poll(t,n.id,i)}content(t,r,i){const{vector_store_id:n}=r;return this._client.getAPIList(I`/vector_stores/${n}/files/${t}/content`,un,{...i,headers:O([{"OpenAI-Beta":"assistants=v2"},i==null?void 0:i.headers])})}}class dn extends P{constructor(){super(...arguments),this.files=new ps(this._client),this.fileBatches=new hs(this._client)}create(t,r){return this._client.post("/vector_stores",{body:t,...r,headers:O([{"OpenAI-Beta":"assistants=v2"},r==null?void 0:r.headers])})}retrieve(t,r){return this._client.get(I`/vector_stores/${t}`,{...r,headers:O([{"OpenAI-Beta":"assistants=v2"},r==null?void 0:r.headers])})}update(t,r,i){return this._client.post(I`/vector_stores/${t}`,{body:r,...i,headers:O([{"OpenAI-Beta":"assistants=v2"},i==null?void 0:i.headers])})}list(t={},r){return this._client.getAPIList("/vector_stores",W,{query:t,...r,headers:O([{"OpenAI-Beta":"assistants=v2"},r==null?void 0:r.headers])})}delete(t,r){return this._client.delete(I`/vector_stores/${t}`,{...r,headers:O([{"OpenAI-Beta":"assistants=v2"},r==null?void 0:r.headers])})}search(t,r,i){return this._client.getAPIList(I`/vector_stores/${t}/search`,un,{body:r,method:"post",...i,headers:O([{"OpenAI-Beta":"assistants=v2"},i==null?void 0:i.headers])})}}dn.Files=ps;dn.FileBatches=hs;var Re,gs,Zt;class vs extends P{constructor(){super(...arguments),Re.add(this)}async unwrap(t,r,i=this._client.webhookSecret,n=300){return await this.verifySignature(t,r,i,n),JSON.parse(t)}async verifySignature(t,r,i=this._client.webhookSecret,n=300){if(typeof crypto>"u"||typeof crypto.subtle.importKey!="function"||typeof crypto.subtle.verify!="function")throw new Error("Webhook signature verification is only supported when the `crypto` global is defined");f(this,Re,"m",gs).call(this,i);const o=O([r]).values,a=f(this,Re,"m",Zt).call(this,o,"webhook-signature"),s=f(this,Re,"m",Zt).call(this,o,"webhook-timestamp"),u=f(this,Re,"m",Zt).call(this,o,"webhook-id"),c=parseInt(s,10);if(isNaN(c))throw new Ke("Invalid webhook timestamp format");const p=Math.floor(Date.now()/1e3);if(p-c>n)throw new Ke("Webhook timestamp is too old");if(c>p+n)throw new Ke("Webhook timestamp is too new");const d=a.split(" ").map(S=>S.startsWith("v1,")?S.substring(3):S),y=i.startsWith("whsec_")?Buffer.from(i.replace("whsec_",""),"base64"):Buffer.from(i,"utf-8"),v=u?`${u}.${s}.${t}`:`${s}.${t}`,_=await crypto.subtle.importKey("raw",y,{name:"HMAC",hash:"SHA-256"},!1,["verify"]);for(const S of d)try{const b=Buffer.from(S,"base64");if(await crypto.subtle.verify("HMAC",_,b,new TextEncoder().encode(v)))return}catch{continue}throw new Ke("The given webhook signature does not match the expected signature")}}Re=new WeakSet,gs=function(t){if(typeof t!="string"||t.length===0)throw new Error("The webhook secret must either be set using the env var, OPENAI_WEBHOOK_SECRET, on the client class, OpenAI({ webhookSecret: '123' }), or passed to this function")},Zt=function(t,r){if(!t)throw new Error("Headers are required");const i=t.get(r);if(i==null)throw new Error(`Missing required header: ${r}`);return i};var Hn,Ir,Lt,_s;class D{constructor({baseURL:t=Ae("OPENAI_BASE_URL"),apiKey:r=Ae("OPENAI_API_KEY"),organization:i=Ae("OPENAI_ORG_ID")??null,project:n=Ae("OPENAI_PROJECT_ID")??null,webhookSecret:o=Ae("OPENAI_WEBHOOK_SECRET")??null,...a}={}){if(Hn.add(this),Lt.set(this,void 0),this.completions=new Ya(this),this.chat=new fr(this),this.embeddings=new es(this),this.files=new ns(this),this.images=new us(this),this.audio=new gt(this),this.moderations=new ls(this),this.models=new cs(this),this.fineTuning=new We(this),this.graders=new $r(this),this.vectorStores=new dn(this),this.webhooks=new vs(this),this.beta=new vt(this),this.batches=new qa(this),this.uploads=new Sr(this),this.responses=new kr(this),this.evals=new vr(this),this.containers=new pr(this),r===void 0)throw new U("The OPENAI_API_KEY environment variable is missing or empty; either provide it, or instantiate the OpenAI client with an apiKey option, like new OpenAI({ apiKey: 'My API Key' }).");const s={apiKey:r,organization:i,project:n,webhookSecret:o,...a,baseURL:t||"https://api.openai.com/v1"};if(!s.dangerouslyAllowBrowser&&Jd())throw new U(`It looks like you're running in a browser-like environment.

This is disabled by default, as it risks exposing your secret API credentials to attackers.
If you understand the risks and have appropriate mitigations in place,
you can set the \`dangerouslyAllowBrowser\` option to \`true\`, e.g.,

new OpenAI({ apiKey, dangerouslyAllowBrowser: true });

https://help.openai.com/en/articles/5112595-best-practices-for-api-key-safety
`);this.baseURL=s.baseURL,this.timeout=s.timeout??Ir.DEFAULT_TIMEOUT,this.logger=s.logger??console;const u="warn";this.logLevel=u,this.logLevel=_o(s.logLevel,"ClientOptions.logLevel",this)??_o(Ae("OPENAI_LOG"),"process.env['OPENAI_LOG']",this)??u,this.fetchOptions=s.fetchOptions,this.maxRetries=s.maxRetries??2,this.fetch=s.fetch??Kd(),j(this,Lt,Hd),this._options=s,this.apiKey=r,this.organization=i,this.project=n,this.webhookSecret=o}withOptions(t){return new this.constructor({...this._options,baseURL:this.baseURL,maxRetries:this.maxRetries,timeout:this.timeout,logger:this.logger,logLevel:this.logLevel,fetch:this.fetch,fetchOptions:this.fetchOptions,apiKey:this.apiKey,organization:this.organization,project:this.project,webhookSecret:this.webhookSecret,...t})}defaultQuery(){return this._options.defaultQuery}validateHeaders({values:t,nulls:r}){}async authHeaders(t){return O([{Authorization:`Bearer ${this.apiKey}`}])}stringifyQuery(t){return rf(t,{arrayFormat:"brackets"})}getUserAgent(){return`${this.constructor.name}/JS ${Te}`}defaultIdempotencyKey(){return`stainless-node-retry-${Go()}`}makeStatusError(t,r,i,n){return Y.generate(t,r,i,n)}buildURL(t,r,i){const n=!f(this,Hn,"m",_s).call(this)&&i||this.baseURL,o=Cd(t)?new URL(t):new URL(n+(n.endsWith("/")&&t.startsWith("/")?t.slice(1):t)),a=this.defaultQuery();return Ld(a)||(r={...a,...r}),typeof r=="object"&&r&&!Array.isArray(r)&&(o.search=this.stringifyQuery(r)),o.toString()}async prepareOptions(t){}async prepareRequest(t,{url:r,options:i}){}get(t,r){return this.methodRequest("get",t,r)}post(t,r){return this.methodRequest("post",t,r)}patch(t,r){return this.methodRequest("patch",t,r)}put(t,r){return this.methodRequest("put",t,r)}delete(t,r){return this.methodRequest("delete",t,r)}methodRequest(t,r,i){return this.request(Promise.resolve(i).then(n=>({method:t,path:r,...n})))}request(t,r=null){return new sn(this,this.makeRequest(t,r,void 0))}async makeRequest(t,r,i){var m,h;const n=await t,o=n.maxRetries??this.maxRetries;r==null&&(r=o),await this.prepareOptions(n);const{req:a,url:s,timeout:u}=await this.buildRequest(n,{retryCount:o-r});await this.prepareRequest(a,{url:s,options:n});const c="log_"+(Math.random()*(1<<24)|0).toString(16).padStart(6,"0"),p=i===void 0?"":`, retryOf: ${i}`,d=Date.now();if(G(this).debug(`[${c}] sending request`,ye({retryOfRequestLogID:i,method:n.method,url:s,options:n,headers:a.headers})),(m=n.signal)!=null&&m.aborted)throw new ae;const y=new AbortController,v=await this.fetchWithTimeout(s,a,u,y).catch(Ln),_=Date.now();if(v instanceof Error){const g=`retrying, ${r} attempts remaining`;if((h=n.signal)!=null&&h.aborted)throw new ae;const w=Zn(v)||/timed? ?out/i.test(String(v)+("cause"in v?String(v.cause):""));if(r)return G(this).info(`[${c}] connection ${w?"timed out":"failed"} - ${g}`),G(this).debug(`[${c}] connection ${w?"timed out":"failed"} (${g})`,ye({retryOfRequestLogID:i,url:s,durationMs:_-d,message:v.message})),this.retryRequest(n,r,i??c);throw G(this).info(`[${c}] connection ${w?"timed out":"failed"} - error; no more retries left`),G(this).debug(`[${c}] connection ${w?"timed out":"failed"} (error; no more retries left)`,ye({retryOfRequestLogID:i,url:s,durationMs:_-d,message:v.message})),w?new or:new on({cause:v})}const S=[...v.headers.entries()].filter(([g])=>g==="x-request-id").map(([g,w])=>", "+g+": "+JSON.stringify(w)).join(""),b=`[${c}${p}${S}] ${a.method} ${s} ${v.ok?"succeeded":"failed"} with status ${v.status} in ${_-d}ms`;if(!v.ok){const g=await this.shouldRetry(v);if(r&&g){const C=`retrying, ${r} attempts remaining`;return await Gd(v.body),G(this).info(`${b} - ${C}`),G(this).debug(`[${c}] response error (${C})`,ye({retryOfRequestLogID:i,url:v.url,status:v.status,headers:v.headers,durationMs:_-d})),this.retryRequest(n,r,i??c,v.headers)}const w=g?"error; no more retries left":"error; not retryable";G(this).info(`${b} - ${w}`);const x=await v.text().catch(C=>Ln(C).message),z=Bd(x),N=z?void 0:x;throw G(this).debug(`[${c}] response error (${w})`,ye({retryOfRequestLogID:i,url:v.url,status:v.status,headers:v.headers,message:N,durationMs:Date.now()-d})),this.makeStatusError(v.status,z,N,v.headers)}return G(this).info(b),G(this).debug(`[${c}] response start`,ye({retryOfRequestLogID:i,url:v.url,status:v.status,headers:v.headers,durationMs:_-d})),{response:v,options:n,controller:y,requestLogID:c,retryOfRequestLogID:i,startTime:d}}getAPIList(t,r,i){return this.requestAPIList(r,{method:"get",path:t,...i})}requestAPIList(t,r){const i=this.makeRequest(r,null,void 0);return new mf(this,i,t)}async fetchWithTimeout(t,r,i,n){const{signal:o,method:a,...s}=r||{};o&&o.addEventListener("abort",()=>n.abort());const u=setTimeout(()=>n.abort(),i),c=globalThis.ReadableStream&&s.body instanceof globalThis.ReadableStream||typeof s.body=="object"&&s.body!==null&&Symbol.asyncIterator in s.body,p={signal:n.signal,...c?{duplex:"half"}:{},method:"GET",...s};a&&(p.method=a.toUpperCase());try{return await this.fetch.call(void 0,t,p)}finally{clearTimeout(u)}}async shouldRetry(t){const r=t.headers.get("x-should-retry");return r==="true"?!0:r==="false"?!1:t.status===408||t.status===409||t.status===429||t.status>=500}async retryRequest(t,r,i,n){let o;const a=n==null?void 0:n.get("retry-after-ms");if(a){const u=parseFloat(a);Number.isNaN(u)||(o=u)}const s=n==null?void 0:n.get("retry-after");if(s&&!o){const u=parseFloat(s);Number.isNaN(u)?o=Date.parse(s)-Date.now():o=u*1e3}if(!(o&&0<=o&&o<60*1e3)){const u=t.maxRetries??this.maxRetries;o=this.calculateDefaultRetryTimeoutMillis(r,u)}return await ht(o),this.makeRequest(t,r-1,i)}calculateDefaultRetryTimeoutMillis(t,r){const o=r-t,a=Math.min(.5*Math.pow(2,o),8),s=1-Math.random()*.25;return a*s*1e3}async buildRequest(t,{retryCount:r=0}={}){const i={...t},{method:n,path:o,query:a,defaultBaseURL:s}=i,u=this.buildURL(o,a,s);"timeout"in i&&Fd("timeout",i.timeout),i.timeout=i.timeout??this.timeout;const{bodyHeaders:c,body:p}=this.buildBody({options:i}),d=await this.buildHeaders({options:t,method:n,bodyHeaders:c,retryCount:r});return{req:{method:n,headers:d,...i.signal&&{signal:i.signal},...globalThis.ReadableStream&&p instanceof globalThis.ReadableStream&&{duplex:"half"},...p&&{body:p},...this.fetchOptions??{},...i.fetchOptions??{}},url:u,timeout:i.timeout}}async buildHeaders({options:t,method:r,bodyHeaders:i,retryCount:n}){let o={};this.idempotencyHeader&&r!=="get"&&(t.idempotencyKey||(t.idempotencyKey=this.defaultIdempotencyKey()),o[this.idempotencyHeader]=t.idempotencyKey);const a=O([o,{Accept:"application/json","User-Agent":this.getUserAgent(),"X-Stainless-Retry-Count":String(n),...t.timeout?{"X-Stainless-Timeout":String(Math.trunc(t.timeout/1e3))}:{},...Xd(),"OpenAI-Organization":this.organization,"OpenAI-Project":this.project},await this.authHeaders(t),this._options.defaultHeaders,i,t.headers]);return this.validateHeaders(a),a.values}buildBody({options:{body:t,headers:r}}){if(!t)return{bodyHeaders:void 0,body:void 0};const i=O([r]);return ArrayBuffer.isView(t)||t instanceof ArrayBuffer||t instanceof DataView||typeof t=="string"&&i.values.has("content-type")||t instanceof Blob||t instanceof FormData||t instanceof URLSearchParams||globalThis.ReadableStream&&t instanceof globalThis.ReadableStream?{bodyHeaders:void 0,body:t}:typeof t=="object"&&(Symbol.asyncIterator in t||Symbol.iterator in t&&"next"in t&&typeof t.next=="function")?{bodyHeaders:void 0,body:ua(t)}:f(this,Lt,"f").call(this,{body:t,headers:i})}}Ir=D,Lt=new WeakMap,Hn=new WeakSet,_s=function(){return this.baseURL!=="https://api.openai.com/v1"};D.OpenAI=Ir;D.DEFAULT_TIMEOUT=6e5;D.OpenAIError=U;D.APIError=Y;D.APIConnectionError=on;D.APIConnectionTimeoutError=or;D.APIUserAbortError=ae;D.NotFoundError=ea;D.ConflictError=ta;D.RateLimitError=ra;D.BadRequestError=Ho;D.AuthenticationError=Yo;D.InternalServerError=ia;D.PermissionDeniedError=Qo;D.UnprocessableEntityError=na;D.InvalidWebhookSignatureError=Ke;D.toFile=bf;D.Completions=Ya;D.Chat=fr;D.Embeddings=es;D.Files=ns;D.Images=us;D.Audio=gt;D.Moderations=ls;D.Models=cs;D.FineTuning=We;D.Graders=$r;D.VectorStores=dn;D.Webhooks=vs;D.Beta=vt;D.Batches=qa;D.Uploads=Sr;D.Responses=kr;D.Evals=vr;D.Containers=pr;const bs=Object.freeze({status:"aborted"});function l(e,t,r){function i(s,u){var c;Object.defineProperty(s,"_zod",{value:s._zod??{},enumerable:!1}),(c=s._zod).traits??(c.traits=new Set),s._zod.traits.add(e),t(s,u);for(const p in a.prototype)p in s||Object.defineProperty(s,p,{value:a.prototype[p].bind(s)});s._zod.constr=a,s._zod.def=u}const n=(r==null?void 0:r.Parent)??Object;class o extends n{}Object.defineProperty(o,"name",{value:e});function a(s){var u;const c=r!=null&&r.Parent?new o:this;i(c,s),(u=c._zod).deferred??(u.deferred=[]);for(const p of c._zod.deferred)p();return c}return Object.defineProperty(a,"init",{value:i}),Object.defineProperty(a,Symbol.hasInstance,{value:s=>{var u,c;return r!=null&&r.Parent&&s instanceof r.Parent?!0:(c=(u=s==null?void 0:s._zod)==null?void 0:u.traits)==null?void 0:c.has(e)}}),Object.defineProperty(a,"name",{value:e}),a}const ys=Symbol("zod_brand");class Me extends Error{constructor(){super("Encountered Promise during synchronous parse. Use .parseAsync() instead.")}}const Xt={};function ee(e){return e&&Object.assign(Xt,e),Xt}function Wf(e){return e}function qf(e){return e}function Vf(e){}function Xf(e){throw new Error}function Kf(e){}function xr(e){const t=Object.values(e).filter(i=>typeof i=="number");return Object.entries(e).filter(([i,n])=>t.indexOf(+i)===-1).map(([i,n])=>n)}function k(e,t="|"){return e.map(r=>E(r)).join(t)}function $s(e,t){return typeof t=="bigint"?t.toString():t}function fn(e){return{get value(){{const t=e();return Object.defineProperty(this,"value",{value:t}),t}}}}function Oe(e){return e==null}function mn(e){const t=e.startsWith("^")?1:0,r=e.endsWith("$")?e.length-1:e.length;return e.slice(t,r)}function ws(e,t){const r=(e.toString().split(".")[1]||"").length,i=(t.toString().split(".")[1]||"").length,n=r>i?r:i,o=Number.parseInt(e.toFixed(n).replace(".","")),a=Number.parseInt(t.toFixed(n).replace(".",""));return o%a/10**n}function R(e,t,r){Object.defineProperty(e,t,{get(){{const i=r();return e[t]=i,i}},set(i){Object.defineProperty(e,t,{value:i})},configurable:!0})}function qe(e,t,r){Object.defineProperty(e,t,{value:r,writable:!0,enumerable:!0,configurable:!0})}function Gf(e,t){return t?t.reduce((r,i)=>r==null?void 0:r[i],e):e}function Hf(e){const t=Object.keys(e),r=t.map(i=>e[i]);return Promise.all(r).then(i=>{const n={};for(let o=0;o<t.length;o++)n[t[o]]=i[o];return n})}function Yf(e=10){const t="abcdefghijklmnopqrstuvwxyz";let r="";for(let i=0;i<e;i++)r+=t[Math.floor(Math.random()*t.length)];return r}function Ce(e){return JSON.stringify(e)}const zr=Error.captureStackTrace?Error.captureStackTrace:(...e)=>{};function ct(e){return typeof e=="object"&&e!==null&&!Array.isArray(e)}const ks=fn(()=>{var e;if(typeof navigator<"u"&&((e=navigator==null?void 0:navigator.userAgent)!=null&&e.includes("Cloudflare")))return!1;try{const t=Function;return new t(""),!0}catch{return!1}});function lt(e){if(ct(e)===!1)return!1;const t=e.constructor;if(t===void 0)return!0;const r=t.prototype;return!(ct(r)===!1||Object.prototype.hasOwnProperty.call(r,"isPrototypeOf")===!1)}function Qf(e){let t=0;for(const r in e)Object.prototype.hasOwnProperty.call(e,r)&&t++;return t}const em=e=>{const t=typeof e;switch(t){case"undefined":return"undefined";case"string":return"string";case"number":return Number.isNaN(e)?"nan":"number";case"boolean":return"boolean";case"function":return"function";case"bigint":return"bigint";case"symbol":return"symbol";case"object":return Array.isArray(e)?"array":e===null?"null":e.then&&typeof e.then=="function"&&e.catch&&typeof e.catch=="function"?"promise":typeof Map<"u"&&e instanceof Map?"map":typeof Set<"u"&&e instanceof Set?"set":typeof Date<"u"&&e instanceof Date?"date":typeof File<"u"&&e instanceof File?"file":"object";default:throw new Error(`Unknown data type: ${t}`)}},Kt=new Set(["string","number","symbol"]),Ss=new Set(["string","number","bigint","boolean","symbol","undefined"]);function Ne(e){return e.replace(/[.*+?^${}()|[\]\\]/g,"\\$&")}function he(e,t,r){const i=new e._zod.constr(t??e._zod.def);return(!t||r!=null&&r.parent)&&(i._zod.parent=e),i}function $(e){const t=e;if(!t)return{};if(typeof t=="string")return{error:()=>t};if((t==null?void 0:t.message)!==void 0){if((t==null?void 0:t.error)!==void 0)throw new Error("Cannot specify both `message` and `error` params");t.error=t.message}return delete t.message,typeof t.error=="string"?{...t,error:()=>t.error}:t}function tm(e){let t;return new Proxy({},{get(r,i,n){return t??(t=e()),Reflect.get(t,i,n)},set(r,i,n,o){return t??(t=e()),Reflect.set(t,i,n,o)},has(r,i){return t??(t=e()),Reflect.has(t,i)},deleteProperty(r,i){return t??(t=e()),Reflect.deleteProperty(t,i)},ownKeys(r){return t??(t=e()),Reflect.ownKeys(t)},getOwnPropertyDescriptor(r,i){return t??(t=e()),Reflect.getOwnPropertyDescriptor(t,i)},defineProperty(r,i,n){return t??(t=e()),Reflect.defineProperty(t,i,n)}})}function E(e){return typeof e=="bigint"?e.toString()+"n":typeof e=="string"?`"${e}"`:`${e}`}function Is(e){return Object.keys(e).filter(t=>e[t]._zod.optin==="optional"&&e[t]._zod.optout==="optional")}const xs={safeint:[Number.MIN_SAFE_INTEGER,Number.MAX_SAFE_INTEGER],int32:[-2147483648,2147483647],uint32:[0,4294967295],float32:[-34028234663852886e22,34028234663852886e22],float64:[-Number.MAX_VALUE,Number.MAX_VALUE]},zs={int64:[BigInt("-9223372036854775808"),BigInt("9223372036854775807")],uint64:[BigInt(0),BigInt("18446744073709551615")]};function Os(e,t){const r={},i=e._zod.def;for(const n in t){if(!(n in i.shape))throw new Error(`Unrecognized key: "${n}"`);t[n]&&(r[n]=i.shape[n])}return he(e,{...e._zod.def,shape:r,checks:[]})}function Ns(e,t){const r={...e._zod.def.shape},i=e._zod.def;for(const n in t){if(!(n in i.shape))throw new Error(`Unrecognized key: "${n}"`);t[n]&&delete r[n]}return he(e,{...e._zod.def,shape:r,checks:[]})}function js(e,t){if(!lt(t))throw new Error("Invalid input to extend: expected a plain object");const r={...e._zod.def,get shape(){const i={...e._zod.def.shape,...t};return qe(this,"shape",i),i},checks:[]};return he(e,r)}function Us(e,t){return he(e,{...e._zod.def,get shape(){const r={...e._zod.def.shape,...t._zod.def.shape};return qe(this,"shape",r),r},catchall:t._zod.def.catchall,checks:[]})}function Ps(e,t,r){const i=t._zod.def.shape,n={...i};if(r)for(const o in r){if(!(o in i))throw new Error(`Unrecognized key: "${o}"`);r[o]&&(n[o]=e?new e({type:"optional",innerType:i[o]}):i[o])}else for(const o in i)n[o]=e?new e({type:"optional",innerType:i[o]}):i[o];return he(t,{...t._zod.def,shape:n,checks:[]})}function As(e,t,r){const i=t._zod.def.shape,n={...i};if(r)for(const o in r){if(!(o in n))throw new Error(`Unrecognized key: "${o}"`);r[o]&&(n[o]=new e({type:"nonoptional",innerType:i[o]}))}else for(const o in i)n[o]=new e({type:"nonoptional",innerType:i[o]});return he(t,{...t._zod.def,shape:n,checks:[]})}function Le(e,t=0){var r;for(let i=t;i<e.issues.length;i++)if(((r=e.issues[i])==null?void 0:r.continue)!==!0)return!0;return!1}function se(e,t){return t.map(r=>{var i;return(i=r).path??(i.path=[]),r.path.unshift(e),r})}function it(e){return typeof e=="string"?e:e==null?void 0:e.message}function le(e,t,r){var n,o,a,s,u,c;const i={...e,path:e.path??[]};if(!e.message){const p=it((a=(o=(n=e.inst)==null?void 0:n._zod.def)==null?void 0:o.error)==null?void 0:a.call(o,e))??it((s=t==null?void 0:t.error)==null?void 0:s.call(t,e))??it((u=r.customError)==null?void 0:u.call(r,e))??it((c=r.localeError)==null?void 0:c.call(r,e))??"Invalid input";i.message=p}return delete i.inst,delete i.continue,t!=null&&t.reportInput||delete i.input,i}function hn(e){return e instanceof Set?"set":e instanceof Map?"map":e instanceof File?"file":"unknown"}function pn(e){return Array.isArray(e)?"array":typeof e=="string"?"string":"unknown"}function Fe(...e){const[t,r,i]=e;return typeof t=="string"?{message:t,code:"custom",input:r,inst:i}:{...t}}function nm(e){return Object.entries(e).filter(([t,r])=>Number.isNaN(Number.parseInt(t,10))).map(t=>t[1])}class rm{constructor(...t){}}const im=Object.freeze(Object.defineProperty({__proto__:null,BIGINT_FORMAT_RANGES:zs,Class:rm,NUMBER_FORMAT_RANGES:xs,aborted:Le,allowsEval:ks,assert:Kf,assertEqual:Wf,assertIs:Vf,assertNever:Xf,assertNotEqual:qf,assignProp:qe,cached:fn,captureStackTrace:zr,cleanEnum:nm,cleanRegex:mn,clone:he,createTransparentProxy:tm,defineLazy:R,esc:Ce,escapeRegex:Ne,extend:js,finalizeIssue:le,floatSafeRemainder:ws,getElementAtPath:Gf,getEnumValues:xr,getLengthableOrigin:pn,getParsedType:em,getSizableOrigin:hn,isObject:ct,isPlainObject:lt,issue:Fe,joinValues:k,jsonStringifyReplacer:$s,merge:Us,normalizeParams:$,nullish:Oe,numKeys:Qf,omit:Ns,optionalKeys:Is,partial:Ps,pick:Os,prefixIssues:se,primitiveTypes:Ss,promiseAllObject:Hf,propertyKeyTypes:Kt,randomString:Yf,required:As,stringifyPrimitive:E,unwrapMessage:it},Symbol.toStringTag,{value:"Module"})),Es=(e,t)=>{e.name="$ZodError",Object.defineProperty(e,"_zod",{value:e._zod,enumerable:!1}),Object.defineProperty(e,"issues",{value:t,enumerable:!1}),Object.defineProperty(e,"message",{get(){return JSON.stringify(t,$s,2)},enumerable:!0}),Object.defineProperty(e,"toString",{value:()=>e.message,enumerable:!1})},Or=l("$ZodError",Es),_t=l("$ZodError",Es,{Parent:Error});function Nr(e,t=r=>r.message){const r={},i=[];for(const n of e.issues)n.path.length>0?(r[n.path[0]]=r[n.path[0]]||[],r[n.path[0]].push(t(n))):i.push(t(n));return{formErrors:i,fieldErrors:r}}function jr(e,t){const r=t||function(o){return o.message},i={_errors:[]},n=o=>{for(const a of o.issues)if(a.code==="invalid_union"&&a.errors.length)a.errors.map(s=>n({issues:s}));else if(a.code==="invalid_key")n({issues:a.issues});else if(a.code==="invalid_element")n({issues:a.issues});else if(a.path.length===0)i._errors.push(r(a));else{let s=i,u=0;for(;u<a.path.length;){const c=a.path[u];u===a.path.length-1?(s[c]=s[c]||{_errors:[]},s[c]._errors.push(r(a))):s[c]=s[c]||{_errors:[]},s=s[c],u++}}};return n(e),i}function Ts(e,t){const r=t||function(o){return o.message},i={errors:[]},n=(o,a=[])=>{var s,u;for(const c of o.issues)if(c.code==="invalid_union"&&c.errors.length)c.errors.map(p=>n({issues:p},c.path));else if(c.code==="invalid_key")n({issues:c.issues},c.path);else if(c.code==="invalid_element")n({issues:c.issues},c.path);else{const p=[...a,...c.path];if(p.length===0){i.errors.push(r(c));continue}let d=i,y=0;for(;y<p.length;){const v=p[y],_=y===p.length-1;typeof v=="string"?(d.properties??(d.properties={}),(s=d.properties)[v]??(s[v]={errors:[]}),d=d.properties[v]):(d.items??(d.items=[]),(u=d.items)[v]??(u[v]={errors:[]}),d=d.items[v]),_&&d.errors.push(r(c)),y++}}};return n(e),i}function Ds(e){const t=[];for(const r of e)typeof r=="number"?t.push(`[${r}]`):typeof r=="symbol"?t.push(`[${JSON.stringify(String(r))}]`):/[^\w$]/.test(r)?t.push(`[${JSON.stringify(r)}]`):(t.length&&t.push("."),t.push(r));return t.join("")}function Rs(e){var i;const t=[],r=[...e.issues].sort((n,o)=>n.path.length-o.path.length);for(const n of r)t.push(`✖ ${n.message}`),(i=n.path)!=null&&i.length&&t.push(`  → at ${Ds(n.path)}`);return t.join(`
`)}const Ur=e=>(t,r,i,n)=>{const o=i?Object.assign(i,{async:!1}):{async:!1},a=t._zod.run({value:r,issues:[]},o);if(a instanceof Promise)throw new Me;if(a.issues.length){const s=new((n==null?void 0:n.Err)??e)(a.issues.map(u=>le(u,o,ee())));throw zr(s,n==null?void 0:n.callee),s}return a.value},Yn=Ur(_t),Pr=e=>async(t,r,i,n)=>{const o=i?Object.assign(i,{async:!0}):{async:!0};let a=t._zod.run({value:r,issues:[]},o);if(a instanceof Promise&&(a=await a),a.issues.length){const s=new((n==null?void 0:n.Err)??e)(a.issues.map(u=>le(u,o,ee())));throw zr(s,n==null?void 0:n.callee),s}return a.value},Qn=Pr(_t),Ar=e=>(t,r,i)=>{const n=i?{...i,async:!1}:{async:!1},o=t._zod.run({value:r,issues:[]},n);if(o instanceof Promise)throw new Me;return o.issues.length?{success:!1,error:new(e??Or)(o.issues.map(a=>le(a,n,ee())))}:{success:!0,data:o.value}},Cs=Ar(_t),Er=e=>async(t,r,i)=>{const n=i?Object.assign(i,{async:!0}):{async:!0};let o=t._zod.run({value:r,issues:[]},n);return o instanceof Promise&&(o=await o),o.issues.length?{success:!1,error:new e(o.issues.map(a=>le(a,n,ee())))}:{success:!0,data:o.value}},Zs=Er(_t),Ls=/^[cC][^\s-]{8,}$/,Ms=/^[0-9a-z]+$/,Fs=/^[0-9A-HJKMNP-TV-Za-hjkmnp-tv-z]{26}$/,Bs=/^[0-9a-vA-V]{20}$/,Js=/^[A-Za-z0-9]{27}$/,Ws=/^[a-zA-Z0-9_-]{21}$/,qs=/^P(?:(\d+W)|(?!.*W)(?=\d|T\d)(\d+Y)?(\d+M)?(\d+D)?(T(?=\d)(\d+H)?(\d+M)?(\d+([.,]\d+)?S)?)?)$/,om=/^[-+]?P(?!$)(?:(?:[-+]?\d+Y)|(?:[-+]?\d+[.,]\d+Y$))?(?:(?:[-+]?\d+M)|(?:[-+]?\d+[.,]\d+M$))?(?:(?:[-+]?\d+W)|(?:[-+]?\d+[.,]\d+W$))?(?:(?:[-+]?\d+D)|(?:[-+]?\d+[.,]\d+D$))?(?:T(?=[\d+-])(?:(?:[-+]?\d+H)|(?:[-+]?\d+[.,]\d+H$))?(?:(?:[-+]?\d+M)|(?:[-+]?\d+[.,]\d+M$))?(?:[-+]?\d+(?:[.,]\d+)?S)?)??$/,Vs=/^([0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12})$/,Be=e=>e?new RegExp(`^([0-9a-fA-F]{8}-[0-9a-fA-F]{4}-${e}[0-9a-fA-F]{3}-[89abAB][0-9a-fA-F]{3}-[0-9a-fA-F]{12})$`):/^([0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[1-8][0-9a-fA-F]{3}-[89abAB][0-9a-fA-F]{3}-[0-9a-fA-F]{12}|00000000-0000-0000-0000-000000000000)$/,am=Be(4),sm=Be(6),um=Be(7),Xs=/^(?!\.)(?!.*\.\.)([A-Za-z0-9_'+\-\.]*)[A-Za-z0-9_+-]@([A-Za-z0-9][A-Za-z0-9\-]*\.)+[A-Za-z]{2,}$/,cm=/^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/,lm=/^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/,dm=/^[^\s@"]{1,64}@[^\s@]{1,255}$/u,fm=/^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/,Ks="^(\\p{Extended_Pictographic}|\\p{Emoji_Component})+$";function Gs(){return new RegExp(Ks,"u")}const Hs=/^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])$/,Ys=/^(([0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}|::|([0-9a-fA-F]{1,4})?::([0-9a-fA-F]{1,4}:?){0,6})$/,Qs=/^((25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\/([0-9]|[1-2][0-9]|3[0-2])$/,eu=/^(([0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}|::|([0-9a-fA-F]{1,4})?::([0-9a-fA-F]{1,4}:?){0,6})\/(12[0-8]|1[01][0-9]|[1-9]?[0-9])$/,tu=/^$|^(?:[0-9a-zA-Z+/]{4})*(?:(?:[0-9a-zA-Z+/]{2}==)|(?:[0-9a-zA-Z+/]{3}=))?$/,Tr=/^[A-Za-z0-9_-]*$/,nu=/^([a-zA-Z0-9-]+\.)*[a-zA-Z0-9-]+$/,mm=/^([a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?\.)+[a-zA-Z]{2,}$/,ru=/^\+(?:[0-9]){6,14}[0-9]$/,iu="(?:(?:\\d\\d[2468][048]|\\d\\d[13579][26]|\\d\\d0[48]|[02468][048]00|[13579][26]00)-02-29|\\d{4}-(?:(?:0[13578]|1[02])-(?:0[1-9]|[12]\\d|3[01])|(?:0[469]|11)-(?:0[1-9]|[12]\\d|30)|(?:02)-(?:0[1-9]|1\\d|2[0-8])))",ou=new RegExp(`^${iu}$`);function au(e){const t="(?:[01]\\d|2[0-3]):[0-5]\\d";return typeof e.precision=="number"?e.precision===-1?`${t}`:e.precision===0?`${t}:[0-5]\\d`:`${t}:[0-5]\\d\\.\\d{${e.precision}}`:`${t}(?::[0-5]\\d(?:\\.\\d+)?)?`}function su(e){return new RegExp(`^${au(e)}$`)}function uu(e){const t=au({precision:e.precision}),r=["Z"];e.local&&r.push(""),e.offset&&r.push("([+-]\\d{2}:\\d{2})");const i=`${t}(?:${r.join("|")})`;return new RegExp(`^${iu}T(?:${i})$`)}const cu=e=>{const t=e?`[\\s\\S]{${(e==null?void 0:e.minimum)??0},${(e==null?void 0:e.maximum)??""}}`:"[\\s\\S]*";return new RegExp(`^${t}$`)},lu=/^\d+n?$/,du=/^\d+$/,fu=/^-?\d+(?:\.\d+)?/i,mu=/true|false/i,hu=/null/i,pu=/undefined/i,gu=/^[^A-Z]*$/,vu=/^[^a-z]*$/,_u=Object.freeze(Object.defineProperty({__proto__:null,_emoji:Ks,base64:tu,base64url:Tr,bigint:lu,boolean:mu,browserEmail:fm,cidrv4:Qs,cidrv6:eu,cuid:Ls,cuid2:Ms,date:ou,datetime:uu,domain:mm,duration:qs,e164:ru,email:Xs,emoji:Gs,extendedDuration:om,guid:Vs,hostname:nu,html5Email:cm,integer:du,ipv4:Hs,ipv6:Ys,ksuid:Js,lowercase:gu,nanoid:Ws,null:hu,number:fu,rfc5322Email:lm,string:cu,time:su,ulid:Fs,undefined:pu,unicodeEmail:dm,uppercase:vu,uuid:Be,uuid4:am,uuid6:sm,uuid7:um,xid:Bs},Symbol.toStringTag,{value:"Module"})),J=l("$ZodCheck",(e,t)=>{var r;e._zod??(e._zod={}),e._zod.def=t,(r=e._zod).onattach??(r.onattach=[])}),bu={number:"number",bigint:"bigint",object:"date"},Dr=l("$ZodCheckLessThan",(e,t)=>{J.init(e,t);const r=bu[typeof t.value];e._zod.onattach.push(i=>{const n=i._zod.bag,o=(t.inclusive?n.maximum:n.exclusiveMaximum)??Number.POSITIVE_INFINITY;t.value<o&&(t.inclusive?n.maximum=t.value:n.exclusiveMaximum=t.value)}),e._zod.check=i=>{(t.inclusive?i.value<=t.value:i.value<t.value)||i.issues.push({origin:r,code:"too_big",maximum:t.value,input:i.value,inclusive:t.inclusive,inst:e,continue:!t.abort})}}),Rr=l("$ZodCheckGreaterThan",(e,t)=>{J.init(e,t);const r=bu[typeof t.value];e._zod.onattach.push(i=>{const n=i._zod.bag,o=(t.inclusive?n.minimum:n.exclusiveMinimum)??Number.NEGATIVE_INFINITY;t.value>o&&(t.inclusive?n.minimum=t.value:n.exclusiveMinimum=t.value)}),e._zod.check=i=>{(t.inclusive?i.value>=t.value:i.value>t.value)||i.issues.push({origin:r,code:"too_small",minimum:t.value,input:i.value,inclusive:t.inclusive,inst:e,continue:!t.abort})}}),yu=l("$ZodCheckMultipleOf",(e,t)=>{J.init(e,t),e._zod.onattach.push(r=>{var i;(i=r._zod.bag).multipleOf??(i.multipleOf=t.value)}),e._zod.check=r=>{if(typeof r.value!=typeof t.value)throw new Error("Cannot mix number and bigint in multiple_of check.");(typeof r.value=="bigint"?r.value%t.value===BigInt(0):ws(r.value,t.value)===0)||r.issues.push({origin:typeof r.value,code:"not_multiple_of",divisor:t.value,input:r.value,inst:e,continue:!t.abort})}}),$u=l("$ZodCheckNumberFormat",(e,t)=>{var a;J.init(e,t),t.format=t.format||"float64";const r=(a=t.format)==null?void 0:a.includes("int"),i=r?"int":"number",[n,o]=xs[t.format];e._zod.onattach.push(s=>{const u=s._zod.bag;u.format=t.format,u.minimum=n,u.maximum=o,r&&(u.pattern=du)}),e._zod.check=s=>{const u=s.value;if(r){if(!Number.isInteger(u)){s.issues.push({expected:i,format:t.format,code:"invalid_type",input:u,inst:e});return}if(!Number.isSafeInteger(u)){u>0?s.issues.push({input:u,code:"too_big",maximum:Number.MAX_SAFE_INTEGER,note:"Integers must be within the safe integer range.",inst:e,origin:i,continue:!t.abort}):s.issues.push({input:u,code:"too_small",minimum:Number.MIN_SAFE_INTEGER,note:"Integers must be within the safe integer range.",inst:e,origin:i,continue:!t.abort});return}}u<n&&s.issues.push({origin:"number",input:u,code:"too_small",minimum:n,inclusive:!0,inst:e,continue:!t.abort}),u>o&&s.issues.push({origin:"number",input:u,code:"too_big",maximum:o,inst:e})}}),wu=l("$ZodCheckBigIntFormat",(e,t)=>{J.init(e,t);const[r,i]=zs[t.format];e._zod.onattach.push(n=>{const o=n._zod.bag;o.format=t.format,o.minimum=r,o.maximum=i}),e._zod.check=n=>{const o=n.value;o<r&&n.issues.push({origin:"bigint",input:o,code:"too_small",minimum:r,inclusive:!0,inst:e,continue:!t.abort}),o>i&&n.issues.push({origin:"bigint",input:o,code:"too_big",maximum:i,inst:e})}}),ku=l("$ZodCheckMaxSize",(e,t)=>{var r;J.init(e,t),(r=e._zod.def).when??(r.when=i=>{const n=i.value;return!Oe(n)&&n.size!==void 0}),e._zod.onattach.push(i=>{const n=i._zod.bag.maximum??Number.POSITIVE_INFINITY;t.maximum<n&&(i._zod.bag.maximum=t.maximum)}),e._zod.check=i=>{const n=i.value;n.size<=t.maximum||i.issues.push({origin:hn(n),code:"too_big",maximum:t.maximum,input:n,inst:e,continue:!t.abort})}}),Su=l("$ZodCheckMinSize",(e,t)=>{var r;J.init(e,t),(r=e._zod.def).when??(r.when=i=>{const n=i.value;return!Oe(n)&&n.size!==void 0}),e._zod.onattach.push(i=>{const n=i._zod.bag.minimum??Number.NEGATIVE_INFINITY;t.minimum>n&&(i._zod.bag.minimum=t.minimum)}),e._zod.check=i=>{const n=i.value;n.size>=t.minimum||i.issues.push({origin:hn(n),code:"too_small",minimum:t.minimum,input:n,inst:e,continue:!t.abort})}}),Iu=l("$ZodCheckSizeEquals",(e,t)=>{var r;J.init(e,t),(r=e._zod.def).when??(r.when=i=>{const n=i.value;return!Oe(n)&&n.size!==void 0}),e._zod.onattach.push(i=>{const n=i._zod.bag;n.minimum=t.size,n.maximum=t.size,n.size=t.size}),e._zod.check=i=>{const n=i.value,o=n.size;if(o===t.size)return;const a=o>t.size;i.issues.push({origin:hn(n),...a?{code:"too_big",maximum:t.size}:{code:"too_small",minimum:t.size},inclusive:!0,exact:!0,input:i.value,inst:e,continue:!t.abort})}}),xu=l("$ZodCheckMaxLength",(e,t)=>{var r;J.init(e,t),(r=e._zod.def).when??(r.when=i=>{const n=i.value;return!Oe(n)&&n.length!==void 0}),e._zod.onattach.push(i=>{const n=i._zod.bag.maximum??Number.POSITIVE_INFINITY;t.maximum<n&&(i._zod.bag.maximum=t.maximum)}),e._zod.check=i=>{const n=i.value;if(n.length<=t.maximum)return;const a=pn(n);i.issues.push({origin:a,code:"too_big",maximum:t.maximum,inclusive:!0,input:n,inst:e,continue:!t.abort})}}),zu=l("$ZodCheckMinLength",(e,t)=>{var r;J.init(e,t),(r=e._zod.def).when??(r.when=i=>{const n=i.value;return!Oe(n)&&n.length!==void 0}),e._zod.onattach.push(i=>{const n=i._zod.bag.minimum??Number.NEGATIVE_INFINITY;t.minimum>n&&(i._zod.bag.minimum=t.minimum)}),e._zod.check=i=>{const n=i.value;if(n.length>=t.minimum)return;const a=pn(n);i.issues.push({origin:a,code:"too_small",minimum:t.minimum,inclusive:!0,input:n,inst:e,continue:!t.abort})}}),Ou=l("$ZodCheckLengthEquals",(e,t)=>{var r;J.init(e,t),(r=e._zod.def).when??(r.when=i=>{const n=i.value;return!Oe(n)&&n.length!==void 0}),e._zod.onattach.push(i=>{const n=i._zod.bag;n.minimum=t.length,n.maximum=t.length,n.length=t.length}),e._zod.check=i=>{const n=i.value,o=n.length;if(o===t.length)return;const a=pn(n),s=o>t.length;i.issues.push({origin:a,...s?{code:"too_big",maximum:t.length}:{code:"too_small",minimum:t.length},inclusive:!0,exact:!0,input:i.value,inst:e,continue:!t.abort})}}),bt=l("$ZodCheckStringFormat",(e,t)=>{var r,i;J.init(e,t),e._zod.onattach.push(n=>{const o=n._zod.bag;o.format=t.format,t.pattern&&(o.patterns??(o.patterns=new Set),o.patterns.add(t.pattern))}),t.pattern?(r=e._zod).check??(r.check=n=>{t.pattern.lastIndex=0,!t.pattern.test(n.value)&&n.issues.push({origin:"string",code:"invalid_format",format:t.format,input:n.value,...t.pattern?{pattern:t.pattern.toString()}:{},inst:e,continue:!t.abort})}):(i=e._zod).check??(i.check=()=>{})}),Nu=l("$ZodCheckRegex",(e,t)=>{bt.init(e,t),e._zod.check=r=>{t.pattern.lastIndex=0,!t.pattern.test(r.value)&&r.issues.push({origin:"string",code:"invalid_format",format:"regex",input:r.value,pattern:t.pattern.toString(),inst:e,continue:!t.abort})}}),ju=l("$ZodCheckLowerCase",(e,t)=>{t.pattern??(t.pattern=gu),bt.init(e,t)}),Uu=l("$ZodCheckUpperCase",(e,t)=>{t.pattern??(t.pattern=vu),bt.init(e,t)}),Pu=l("$ZodCheckIncludes",(e,t)=>{J.init(e,t);const r=Ne(t.includes),i=new RegExp(typeof t.position=="number"?`^.{${t.position}}${r}`:r);t.pattern=i,e._zod.onattach.push(n=>{const o=n._zod.bag;o.patterns??(o.patterns=new Set),o.patterns.add(i)}),e._zod.check=n=>{n.value.includes(t.includes,t.position)||n.issues.push({origin:"string",code:"invalid_format",format:"includes",includes:t.includes,input:n.value,inst:e,continue:!t.abort})}}),Au=l("$ZodCheckStartsWith",(e,t)=>{J.init(e,t);const r=new RegExp(`^${Ne(t.prefix)}.*`);t.pattern??(t.pattern=r),e._zod.onattach.push(i=>{const n=i._zod.bag;n.patterns??(n.patterns=new Set),n.patterns.add(r)}),e._zod.check=i=>{i.value.startsWith(t.prefix)||i.issues.push({origin:"string",code:"invalid_format",format:"starts_with",prefix:t.prefix,input:i.value,inst:e,continue:!t.abort})}}),Eu=l("$ZodCheckEndsWith",(e,t)=>{J.init(e,t);const r=new RegExp(`.*${Ne(t.suffix)}$`);t.pattern??(t.pattern=r),e._zod.onattach.push(i=>{const n=i._zod.bag;n.patterns??(n.patterns=new Set),n.patterns.add(r)}),e._zod.check=i=>{i.value.endsWith(t.suffix)||i.issues.push({origin:"string",code:"invalid_format",format:"ends_with",suffix:t.suffix,input:i.value,inst:e,continue:!t.abort})}});function To(e,t,r){e.issues.length&&t.issues.push(...se(r,e.issues))}const Tu=l("$ZodCheckProperty",(e,t)=>{J.init(e,t),e._zod.check=r=>{const i=t.schema._zod.run({value:r.value[t.property],issues:[]},{});if(i instanceof Promise)return i.then(n=>To(n,r,t.property));To(i,r,t.property)}}),Du=l("$ZodCheckMimeType",(e,t)=>{J.init(e,t);const r=new Set(t.mime);e._zod.onattach.push(i=>{i._zod.bag.mime=t.mime}),e._zod.check=i=>{r.has(i.value.type)||i.issues.push({code:"invalid_value",values:t.mime,input:i.value.type,inst:e})}}),Ru=l("$ZodCheckOverwrite",(e,t)=>{J.init(e,t),e._zod.check=r=>{r.value=t.tx(r.value)}});class Cu{constructor(t=[]){this.content=[],this.indent=0,this&&(this.args=t)}indented(t){this.indent+=1,t(this),this.indent-=1}write(t){if(typeof t=="function"){t(this,{execution:"sync"}),t(this,{execution:"async"});return}const i=t.split(`
`).filter(a=>a),n=Math.min(...i.map(a=>a.length-a.trimStart().length)),o=i.map(a=>a.slice(n)).map(a=>" ".repeat(this.indent*2)+a);for(const a of o)this.content.push(a)}compile(){const t=Function,r=this==null?void 0:this.args,n=[...((this==null?void 0:this.content)??[""]).map(o=>`  ${o}`)];return new t(...r,n.join(`
`))}}const Zu={major:4,minor:0,patch:0},A=l("$ZodType",(e,t)=>{var n;var r;e??(e={}),e._zod.def=t,e._zod.bag=e._zod.bag||{},e._zod.version=Zu;const i=[...e._zod.def.checks??[]];e._zod.traits.has("$ZodCheck")&&i.unshift(e);for(const o of i)for(const a of o._zod.onattach)a(e);if(i.length===0)(r=e._zod).deferred??(r.deferred=[]),(n=e._zod.deferred)==null||n.push(()=>{e._zod.run=e._zod.parse});else{const o=(a,s,u)=>{let c=Le(a),p;for(const d of s){if(d._zod.def.when){if(!d._zod.def.when(a))continue}else if(c)continue;const y=a.issues.length,v=d._zod.check(a);if(v instanceof Promise&&(u==null?void 0:u.async)===!1)throw new Me;if(p||v instanceof Promise)p=(p??Promise.resolve()).then(async()=>{await v,a.issues.length!==y&&(c||(c=Le(a,y)))});else{if(a.issues.length===y)continue;c||(c=Le(a,y))}}return p?p.then(()=>a):a};e._zod.run=(a,s)=>{const u=e._zod.parse(a,s);if(u instanceof Promise){if(s.async===!1)throw new Me;return u.then(c=>o(c,i,s))}return o(u,i,s)}}e["~standard"]={validate:o=>{var a;try{const s=Cs(e,o);return s.success?{value:s.data}:{issues:(a=s.error)==null?void 0:a.issues}}catch{return Zs(e,o).then(u=>{var c;return u.success?{value:u.data}:{issues:(c=u.error)==null?void 0:c.issues}})}},vendor:"zod",version:1}}),yt=l("$ZodString",(e,t)=>{var r;A.init(e,t),e._zod.pattern=[...((r=e==null?void 0:e._zod.bag)==null?void 0:r.patterns)??[]].pop()??cu(e._zod.bag),e._zod.parse=(i,n)=>{if(t.coerce)try{i.value=String(i.value)}catch{}return typeof i.value=="string"||i.issues.push({expected:"string",code:"invalid_type",input:i.value,inst:e}),i}}),L=l("$ZodStringFormat",(e,t)=>{bt.init(e,t),yt.init(e,t)}),Lu=l("$ZodGUID",(e,t)=>{t.pattern??(t.pattern=Vs),L.init(e,t)}),Mu=l("$ZodUUID",(e,t)=>{if(t.version){const i={v1:1,v2:2,v3:3,v4:4,v5:5,v6:6,v7:7,v8:8}[t.version];if(i===void 0)throw new Error(`Invalid UUID version: "${t.version}"`);t.pattern??(t.pattern=Be(i))}else t.pattern??(t.pattern=Be());L.init(e,t)}),Fu=l("$ZodEmail",(e,t)=>{t.pattern??(t.pattern=Xs),L.init(e,t)}),Bu=l("$ZodURL",(e,t)=>{L.init(e,t),e._zod.check=r=>{try{const i=r.value,n=new URL(i),o=n.href;t.hostname&&(t.hostname.lastIndex=0,t.hostname.test(n.hostname)||r.issues.push({code:"invalid_format",format:"url",note:"Invalid hostname",pattern:nu.source,input:r.value,inst:e,continue:!t.abort})),t.protocol&&(t.protocol.lastIndex=0,t.protocol.test(n.protocol.endsWith(":")?n.protocol.slice(0,-1):n.protocol)||r.issues.push({code:"invalid_format",format:"url",note:"Invalid protocol",pattern:t.protocol.source,input:r.value,inst:e,continue:!t.abort})),!i.endsWith("/")&&o.endsWith("/")?r.value=o.slice(0,-1):r.value=o;return}catch{r.issues.push({code:"invalid_format",format:"url",input:r.value,inst:e,continue:!t.abort})}}}),Ju=l("$ZodEmoji",(e,t)=>{t.pattern??(t.pattern=Gs()),L.init(e,t)}),Wu=l("$ZodNanoID",(e,t)=>{t.pattern??(t.pattern=Ws),L.init(e,t)}),qu=l("$ZodCUID",(e,t)=>{t.pattern??(t.pattern=Ls),L.init(e,t)}),Vu=l("$ZodCUID2",(e,t)=>{t.pattern??(t.pattern=Ms),L.init(e,t)}),Xu=l("$ZodULID",(e,t)=>{t.pattern??(t.pattern=Fs),L.init(e,t)}),Ku=l("$ZodXID",(e,t)=>{t.pattern??(t.pattern=Bs),L.init(e,t)}),Gu=l("$ZodKSUID",(e,t)=>{t.pattern??(t.pattern=Js),L.init(e,t)}),Hu=l("$ZodISODateTime",(e,t)=>{t.pattern??(t.pattern=uu(t)),L.init(e,t)}),Yu=l("$ZodISODate",(e,t)=>{t.pattern??(t.pattern=ou),L.init(e,t)}),Qu=l("$ZodISOTime",(e,t)=>{t.pattern??(t.pattern=su(t)),L.init(e,t)}),ec=l("$ZodISODuration",(e,t)=>{t.pattern??(t.pattern=qs),L.init(e,t)}),tc=l("$ZodIPv4",(e,t)=>{t.pattern??(t.pattern=Hs),L.init(e,t),e._zod.onattach.push(r=>{const i=r._zod.bag;i.format="ipv4"})}),nc=l("$ZodIPv6",(e,t)=>{t.pattern??(t.pattern=Ys),L.init(e,t),e._zod.onattach.push(r=>{const i=r._zod.bag;i.format="ipv6"}),e._zod.check=r=>{try{new URL(`http://[${r.value}]`)}catch{r.issues.push({code:"invalid_format",format:"ipv6",input:r.value,inst:e,continue:!t.abort})}}}),rc=l("$ZodCIDRv4",(e,t)=>{t.pattern??(t.pattern=Qs),L.init(e,t)}),ic=l("$ZodCIDRv6",(e,t)=>{t.pattern??(t.pattern=eu),L.init(e,t),e._zod.check=r=>{const[i,n]=r.value.split("/");try{if(!n)throw new Error;const o=Number(n);if(`${o}`!==n)throw new Error;if(o<0||o>128)throw new Error;new URL(`http://[${i}]`)}catch{r.issues.push({code:"invalid_format",format:"cidrv6",input:r.value,inst:e,continue:!t.abort})}}});function Cr(e){if(e==="")return!0;if(e.length%4!==0)return!1;try{return atob(e),!0}catch{return!1}}const oc=l("$ZodBase64",(e,t)=>{t.pattern??(t.pattern=tu),L.init(e,t),e._zod.onattach.push(r=>{r._zod.bag.contentEncoding="base64"}),e._zod.check=r=>{Cr(r.value)||r.issues.push({code:"invalid_format",format:"base64",input:r.value,inst:e,continue:!t.abort})}});function ac(e){if(!Tr.test(e))return!1;const t=e.replace(/[-_]/g,i=>i==="-"?"+":"/"),r=t.padEnd(Math.ceil(t.length/4)*4,"=");return Cr(r)}const sc=l("$ZodBase64URL",(e,t)=>{t.pattern??(t.pattern=Tr),L.init(e,t),e._zod.onattach.push(r=>{r._zod.bag.contentEncoding="base64url"}),e._zod.check=r=>{ac(r.value)||r.issues.push({code:"invalid_format",format:"base64url",input:r.value,inst:e,continue:!t.abort})}}),uc=l("$ZodE164",(e,t)=>{t.pattern??(t.pattern=ru),L.init(e,t)});function cc(e,t=null){try{const r=e.split(".");if(r.length!==3)return!1;const[i]=r;if(!i)return!1;const n=JSON.parse(atob(i));return!("typ"in n&&(n==null?void 0:n.typ)!=="JWT"||!n.alg||t&&(!("alg"in n)||n.alg!==t))}catch{return!1}}const lc=l("$ZodJWT",(e,t)=>{L.init(e,t),e._zod.check=r=>{cc(r.value,t.alg)||r.issues.push({code:"invalid_format",format:"jwt",input:r.value,inst:e,continue:!t.abort})}}),dc=l("$ZodCustomStringFormat",(e,t)=>{L.init(e,t),e._zod.check=r=>{t.fn(r.value)||r.issues.push({code:"invalid_format",format:t.format,input:r.value,inst:e,continue:!t.abort})}}),Zr=l("$ZodNumber",(e,t)=>{A.init(e,t),e._zod.pattern=e._zod.bag.pattern??fu,e._zod.parse=(r,i)=>{if(t.coerce)try{r.value=Number(r.value)}catch{}const n=r.value;if(typeof n=="number"&&!Number.isNaN(n)&&Number.isFinite(n))return r;const o=typeof n=="number"?Number.isNaN(n)?"NaN":Number.isFinite(n)?void 0:"Infinity":void 0;return r.issues.push({expected:"number",code:"invalid_type",input:n,inst:e,...o?{received:o}:{}}),r}}),fc=l("$ZodNumber",(e,t)=>{$u.init(e,t),Zr.init(e,t)}),Lr=l("$ZodBoolean",(e,t)=>{A.init(e,t),e._zod.pattern=mu,e._zod.parse=(r,i)=>{if(t.coerce)try{r.value=!!r.value}catch{}const n=r.value;return typeof n=="boolean"||r.issues.push({expected:"boolean",code:"invalid_type",input:n,inst:e}),r}}),Mr=l("$ZodBigInt",(e,t)=>{A.init(e,t),e._zod.pattern=lu,e._zod.parse=(r,i)=>{if(t.coerce)try{r.value=BigInt(r.value)}catch{}return typeof r.value=="bigint"||r.issues.push({expected:"bigint",code:"invalid_type",input:r.value,inst:e}),r}}),mc=l("$ZodBigInt",(e,t)=>{wu.init(e,t),Mr.init(e,t)}),hc=l("$ZodSymbol",(e,t)=>{A.init(e,t),e._zod.parse=(r,i)=>{const n=r.value;return typeof n=="symbol"||r.issues.push({expected:"symbol",code:"invalid_type",input:n,inst:e}),r}}),pc=l("$ZodUndefined",(e,t)=>{A.init(e,t),e._zod.pattern=pu,e._zod.values=new Set([void 0]),e._zod.optin="optional",e._zod.optout="optional",e._zod.parse=(r,i)=>{const n=r.value;return typeof n>"u"||r.issues.push({expected:"undefined",code:"invalid_type",input:n,inst:e}),r}}),gc=l("$ZodNull",(e,t)=>{A.init(e,t),e._zod.pattern=hu,e._zod.values=new Set([null]),e._zod.parse=(r,i)=>{const n=r.value;return n===null||r.issues.push({expected:"null",code:"invalid_type",input:n,inst:e}),r}}),vc=l("$ZodAny",(e,t)=>{A.init(e,t),e._zod.parse=r=>r}),Gt=l("$ZodUnknown",(e,t)=>{A.init(e,t),e._zod.parse=r=>r}),_c=l("$ZodNever",(e,t)=>{A.init(e,t),e._zod.parse=(r,i)=>(r.issues.push({expected:"never",code:"invalid_type",input:r.value,inst:e}),r)}),bc=l("$ZodVoid",(e,t)=>{A.init(e,t),e._zod.parse=(r,i)=>{const n=r.value;return typeof n>"u"||r.issues.push({expected:"void",code:"invalid_type",input:n,inst:e}),r}}),yc=l("$ZodDate",(e,t)=>{A.init(e,t),e._zod.parse=(r,i)=>{if(t.coerce)try{r.value=new Date(r.value)}catch{}const n=r.value,o=n instanceof Date;return o&&!Number.isNaN(n.getTime())||r.issues.push({expected:"date",code:"invalid_type",input:n,...o?{received:"Invalid Date"}:{},inst:e}),r}});function Do(e,t,r){e.issues.length&&t.issues.push(...se(r,e.issues)),t.value[r]=e.value}const Fr=l("$ZodArray",(e,t)=>{A.init(e,t),e._zod.parse=(r,i)=>{const n=r.value;if(!Array.isArray(n))return r.issues.push({expected:"array",code:"invalid_type",input:n,inst:e}),r;r.value=Array(n.length);const o=[];for(let a=0;a<n.length;a++){const s=n[a],u=t.element._zod.run({value:s,issues:[]},i);u instanceof Promise?o.push(u.then(c=>Do(c,r,a))):Do(u,r,a)}return o.length?Promise.all(o).then(()=>r):r}});function jt(e,t,r){e.issues.length&&t.issues.push(...se(r,e.issues)),t.value[r]=e.value}function Ro(e,t,r,i){e.issues.length?i[r]===void 0?r in i?t.value[r]=void 0:t.value[r]=e.value:t.issues.push(...se(r,e.issues)):e.value===void 0?r in i&&(t.value[r]=void 0):t.value[r]=e.value}const $c=l("$ZodObject",(e,t)=>{A.init(e,t);const r=fn(()=>{const d=Object.keys(t.shape);for(const v of d)if(!(t.shape[v]instanceof A))throw new Error(`Invalid element at key "${v}": expected a Zod schema`);const y=Is(t.shape);return{shape:t.shape,keys:d,keySet:new Set(d),numKeys:d.length,optionalKeys:new Set(y)}});R(e._zod,"propValues",()=>{const d=t.shape,y={};for(const v in d){const _=d[v]._zod;if(_.values){y[v]??(y[v]=new Set);for(const S of _.values)y[v].add(S)}}return y});const i=d=>{const y=new Cu(["shape","payload","ctx"]),v=r.value,_=h=>{const g=Ce(h);return`shape[${g}]._zod.run({ value: input[${g}], issues: [] }, ctx)`};y.write("const input = payload.value;");const S=Object.create(null);let b=0;for(const h of v.keys)S[h]=`key_${b++}`;y.write("const newResult = {}");for(const h of v.keys)if(v.optionalKeys.has(h)){const g=S[h];y.write(`const ${g} = ${_(h)};`);const w=Ce(h);y.write(`
        if (${g}.issues.length) {
          if (input[${w}] === undefined) {
            if (${w} in input) {
              newResult[${w}] = undefined;
            }
          } else {
            payload.issues = payload.issues.concat(
              ${g}.issues.map((iss) => ({
                ...iss,
                path: iss.path ? [${w}, ...iss.path] : [${w}],
              }))
            );
          }
        } else if (${g}.value === undefined) {
          if (${w} in input) newResult[${w}] = undefined;
        } else {
          newResult[${w}] = ${g}.value;
        }
        `)}else{const g=S[h];y.write(`const ${g} = ${_(h)};`),y.write(`
          if (${g}.issues.length) payload.issues = payload.issues.concat(${g}.issues.map(iss => ({
            ...iss,
            path: iss.path ? [${Ce(h)}, ...iss.path] : [${Ce(h)}]
          })));`),y.write(`newResult[${Ce(h)}] = ${g}.value`)}y.write("payload.value = newResult;"),y.write("return payload;");const m=y.compile();return(h,g)=>m(d,h,g)};let n;const o=ct,a=!Xt.jitless,u=a&&ks.value,c=t.catchall;let p;e._zod.parse=(d,y)=>{p??(p=r.value);const v=d.value;if(!o(v))return d.issues.push({expected:"object",code:"invalid_type",input:v,inst:e}),d;const _=[];if(a&&u&&(y==null?void 0:y.async)===!1&&y.jitless!==!0)n||(n=i(t.shape)),d=n(d,y);else{d.value={};const g=p.shape;for(const w of p.keys){const x=g[w],z=x._zod.run({value:v[w],issues:[]},y),N=x._zod.optin==="optional"&&x._zod.optout==="optional";z instanceof Promise?_.push(z.then(F=>N?Ro(F,d,w,v):jt(F,d,w))):N?Ro(z,d,w,v):jt(z,d,w)}}if(!c)return _.length?Promise.all(_).then(()=>d):d;const S=[],b=p.keySet,m=c._zod,h=m.def.type;for(const g of Object.keys(v)){if(b.has(g))continue;if(h==="never"){S.push(g);continue}const w=m.run({value:v[g],issues:[]},y);w instanceof Promise?_.push(w.then(x=>jt(x,d,g))):jt(w,d,g)}return S.length&&d.issues.push({code:"unrecognized_keys",keys:S,input:v,inst:e}),_.length?Promise.all(_).then(()=>d):d}});function Co(e,t,r,i){for(const n of e)if(n.issues.length===0)return t.value=n.value,t;return t.issues.push({code:"invalid_union",input:t.value,inst:r,errors:e.map(n=>n.issues.map(o=>le(o,i,ee())))}),t}const Br=l("$ZodUnion",(e,t)=>{A.init(e,t),R(e._zod,"optin",()=>t.options.some(r=>r._zod.optin==="optional")?"optional":void 0),R(e._zod,"optout",()=>t.options.some(r=>r._zod.optout==="optional")?"optional":void 0),R(e._zod,"values",()=>{if(t.options.every(r=>r._zod.values))return new Set(t.options.flatMap(r=>Array.from(r._zod.values)))}),R(e._zod,"pattern",()=>{if(t.options.every(r=>r._zod.pattern)){const r=t.options.map(i=>i._zod.pattern);return new RegExp(`^(${r.map(i=>mn(i.source)).join("|")})$`)}}),e._zod.parse=(r,i)=>{let n=!1;const o=[];for(const a of t.options){const s=a._zod.run({value:r.value,issues:[]},i);if(s instanceof Promise)o.push(s),n=!0;else{if(s.issues.length===0)return s;o.push(s)}}return n?Promise.all(o).then(a=>Co(a,r,e,i)):Co(o,r,e,i)}}),wc=l("$ZodDiscriminatedUnion",(e,t)=>{Br.init(e,t);const r=e._zod.parse;R(e._zod,"propValues",()=>{const n={};for(const o of t.options){const a=o._zod.propValues;if(!a||Object.keys(a).length===0)throw new Error(`Invalid discriminated union option at index "${t.options.indexOf(o)}"`);for(const[s,u]of Object.entries(a)){n[s]||(n[s]=new Set);for(const c of u)n[s].add(c)}}return n});const i=fn(()=>{const n=t.options,o=new Map;for(const a of n){const s=a._zod.propValues[t.discriminator];if(!s||s.size===0)throw new Error(`Invalid discriminated union option at index "${t.options.indexOf(a)}"`);for(const u of s){if(o.has(u))throw new Error(`Duplicate discriminator value "${String(u)}"`);o.set(u,a)}}return o});e._zod.parse=(n,o)=>{const a=n.value;if(!ct(a))return n.issues.push({code:"invalid_type",expected:"object",input:a,inst:e}),n;const s=i.value.get(a==null?void 0:a[t.discriminator]);return s?s._zod.run(n,o):t.unionFallback?r(n,o):(n.issues.push({code:"invalid_union",errors:[],note:"No matching discriminator",input:a,path:[t.discriminator],inst:e}),n)}}),kc=l("$ZodIntersection",(e,t)=>{A.init(e,t),e._zod.parse=(r,i)=>{const n=r.value,o=t.left._zod.run({value:n,issues:[]},i),a=t.right._zod.run({value:n,issues:[]},i);return o instanceof Promise||a instanceof Promise?Promise.all([o,a]).then(([u,c])=>Zo(r,u,c)):Zo(r,o,a)}});function er(e,t){if(e===t)return{valid:!0,data:e};if(e instanceof Date&&t instanceof Date&&+e==+t)return{valid:!0,data:e};if(lt(e)&&lt(t)){const r=Object.keys(t),i=Object.keys(e).filter(o=>r.indexOf(o)!==-1),n={...e,...t};for(const o of i){const a=er(e[o],t[o]);if(!a.valid)return{valid:!1,mergeErrorPath:[o,...a.mergeErrorPath]};n[o]=a.data}return{valid:!0,data:n}}if(Array.isArray(e)&&Array.isArray(t)){if(e.length!==t.length)return{valid:!1,mergeErrorPath:[]};const r=[];for(let i=0;i<e.length;i++){const n=e[i],o=t[i],a=er(n,o);if(!a.valid)return{valid:!1,mergeErrorPath:[i,...a.mergeErrorPath]};r.push(a.data)}return{valid:!0,data:r}}return{valid:!1,mergeErrorPath:[]}}function Zo(e,t,r){if(t.issues.length&&e.issues.push(...t.issues),r.issues.length&&e.issues.push(...r.issues),Le(e))return e;const i=er(t.value,r.value);if(!i.valid)throw new Error(`Unmergable intersection. Error path: ${JSON.stringify(i.mergeErrorPath)}`);return e.value=i.data,e}const gn=l("$ZodTuple",(e,t)=>{A.init(e,t);const r=t.items,i=r.length-[...r].reverse().findIndex(n=>n._zod.optin!=="optional");e._zod.parse=(n,o)=>{const a=n.value;if(!Array.isArray(a))return n.issues.push({input:a,inst:e,expected:"tuple",code:"invalid_type"}),n;n.value=[];const s=[];if(!t.rest){const c=a.length>r.length,p=a.length<i-1;if(c||p)return n.issues.push({input:a,inst:e,origin:"array",...c?{code:"too_big",maximum:r.length}:{code:"too_small",minimum:r.length}}),n}let u=-1;for(const c of r){if(u++,u>=a.length&&u>=i)continue;const p=c._zod.run({value:a[u],issues:[]},o);p instanceof Promise?s.push(p.then(d=>Ut(d,n,u))):Ut(p,n,u)}if(t.rest){const c=a.slice(r.length);for(const p of c){u++;const d=t.rest._zod.run({value:p,issues:[]},o);d instanceof Promise?s.push(d.then(y=>Ut(y,n,u))):Ut(d,n,u)}}return s.length?Promise.all(s).then(()=>n):n}});function Ut(e,t,r){e.issues.length&&t.issues.push(...se(r,e.issues)),t.value[r]=e.value}const Sc=l("$ZodRecord",(e,t)=>{A.init(e,t),e._zod.parse=(r,i)=>{const n=r.value;if(!lt(n))return r.issues.push({expected:"record",code:"invalid_type",input:n,inst:e}),r;const o=[];if(t.keyType._zod.values){const a=t.keyType._zod.values;r.value={};for(const u of a)if(typeof u=="string"||typeof u=="number"||typeof u=="symbol"){const c=t.valueType._zod.run({value:n[u],issues:[]},i);c instanceof Promise?o.push(c.then(p=>{p.issues.length&&r.issues.push(...se(u,p.issues)),r.value[u]=p.value})):(c.issues.length&&r.issues.push(...se(u,c.issues)),r.value[u]=c.value)}let s;for(const u in n)a.has(u)||(s=s??[],s.push(u));s&&s.length>0&&r.issues.push({code:"unrecognized_keys",input:n,inst:e,keys:s})}else{r.value={};for(const a of Reflect.ownKeys(n)){if(a==="__proto__")continue;const s=t.keyType._zod.run({value:a,issues:[]},i);if(s instanceof Promise)throw new Error("Async schemas not supported in object keys currently");if(s.issues.length){r.issues.push({origin:"record",code:"invalid_key",issues:s.issues.map(c=>le(c,i,ee())),input:a,path:[a],inst:e}),r.value[s.value]=s.value;continue}const u=t.valueType._zod.run({value:n[a],issues:[]},i);u instanceof Promise?o.push(u.then(c=>{c.issues.length&&r.issues.push(...se(a,c.issues)),r.value[s.value]=c.value})):(u.issues.length&&r.issues.push(...se(a,u.issues)),r.value[s.value]=u.value)}}return o.length?Promise.all(o).then(()=>r):r}}),Ic=l("$ZodMap",(e,t)=>{A.init(e,t),e._zod.parse=(r,i)=>{const n=r.value;if(!(n instanceof Map))return r.issues.push({expected:"map",code:"invalid_type",input:n,inst:e}),r;const o=[];r.value=new Map;for(const[a,s]of n){const u=t.keyType._zod.run({value:a,issues:[]},i),c=t.valueType._zod.run({value:s,issues:[]},i);u instanceof Promise||c instanceof Promise?o.push(Promise.all([u,c]).then(([p,d])=>{Lo(p,d,r,a,n,e,i)})):Lo(u,c,r,a,n,e,i)}return o.length?Promise.all(o).then(()=>r):r}});function Lo(e,t,r,i,n,o,a){e.issues.length&&(Kt.has(typeof i)?r.issues.push(...se(i,e.issues)):r.issues.push({origin:"map",code:"invalid_key",input:n,inst:o,issues:e.issues.map(s=>le(s,a,ee()))})),t.issues.length&&(Kt.has(typeof i)?r.issues.push(...se(i,t.issues)):r.issues.push({origin:"map",code:"invalid_element",input:n,inst:o,key:i,issues:t.issues.map(s=>le(s,a,ee()))})),r.value.set(e.value,t.value)}const xc=l("$ZodSet",(e,t)=>{A.init(e,t),e._zod.parse=(r,i)=>{const n=r.value;if(!(n instanceof Set))return r.issues.push({input:n,inst:e,expected:"set",code:"invalid_type"}),r;const o=[];r.value=new Set;for(const a of n){const s=t.valueType._zod.run({value:a,issues:[]},i);s instanceof Promise?o.push(s.then(u=>Mo(u,r))):Mo(s,r)}return o.length?Promise.all(o).then(()=>r):r}});function Mo(e,t){e.issues.length&&t.issues.push(...e.issues),t.value.add(e.value)}const zc=l("$ZodEnum",(e,t)=>{A.init(e,t);const r=xr(t.entries);e._zod.values=new Set(r),e._zod.pattern=new RegExp(`^(${r.filter(i=>Kt.has(typeof i)).map(i=>typeof i=="string"?Ne(i):i.toString()).join("|")})$`),e._zod.parse=(i,n)=>{const o=i.value;return e._zod.values.has(o)||i.issues.push({code:"invalid_value",values:r,input:o,inst:e}),i}}),Oc=l("$ZodLiteral",(e,t)=>{A.init(e,t),e._zod.values=new Set(t.values),e._zod.pattern=new RegExp(`^(${t.values.map(r=>typeof r=="string"?Ne(r):r?r.toString():String(r)).join("|")})$`),e._zod.parse=(r,i)=>{const n=r.value;return e._zod.values.has(n)||r.issues.push({code:"invalid_value",values:t.values,input:n,inst:e}),r}}),Nc=l("$ZodFile",(e,t)=>{A.init(e,t),e._zod.parse=(r,i)=>{const n=r.value;return n instanceof File||r.issues.push({expected:"file",code:"invalid_type",input:n,inst:e}),r}}),Jr=l("$ZodTransform",(e,t)=>{A.init(e,t),e._zod.parse=(r,i)=>{const n=t.transform(r.value,r);if(i.async)return(n instanceof Promise?n:Promise.resolve(n)).then(a=>(r.value=a,r));if(n instanceof Promise)throw new Me;return r.value=n,r}}),jc=l("$ZodOptional",(e,t)=>{A.init(e,t),e._zod.optin="optional",e._zod.optout="optional",R(e._zod,"values",()=>t.innerType._zod.values?new Set([...t.innerType._zod.values,void 0]):void 0),R(e._zod,"pattern",()=>{const r=t.innerType._zod.pattern;return r?new RegExp(`^(${mn(r.source)})?$`):void 0}),e._zod.parse=(r,i)=>t.innerType._zod.optin==="optional"?t.innerType._zod.run(r,i):r.value===void 0?r:t.innerType._zod.run(r,i)}),Uc=l("$ZodNullable",(e,t)=>{A.init(e,t),R(e._zod,"optin",()=>t.innerType._zod.optin),R(e._zod,"optout",()=>t.innerType._zod.optout),R(e._zod,"pattern",()=>{const r=t.innerType._zod.pattern;return r?new RegExp(`^(${mn(r.source)}|null)$`):void 0}),R(e._zod,"values",()=>t.innerType._zod.values?new Set([...t.innerType._zod.values,null]):void 0),e._zod.parse=(r,i)=>r.value===null?r:t.innerType._zod.run(r,i)}),Pc=l("$ZodDefault",(e,t)=>{A.init(e,t),e._zod.optin="optional",R(e._zod,"values",()=>t.innerType._zod.values),e._zod.parse=(r,i)=>{if(r.value===void 0)return r.value=t.defaultValue,r;const n=t.innerType._zod.run(r,i);return n instanceof Promise?n.then(o=>Fo(o,t)):Fo(n,t)}});function Fo(e,t){return e.value===void 0&&(e.value=t.defaultValue),e}const Ac=l("$ZodPrefault",(e,t)=>{A.init(e,t),e._zod.optin="optional",R(e._zod,"values",()=>t.innerType._zod.values),e._zod.parse=(r,i)=>(r.value===void 0&&(r.value=t.defaultValue),t.innerType._zod.run(r,i))}),Ec=l("$ZodNonOptional",(e,t)=>{A.init(e,t),R(e._zod,"values",()=>{const r=t.innerType._zod.values;return r?new Set([...r].filter(i=>i!==void 0)):void 0}),e._zod.parse=(r,i)=>{const n=t.innerType._zod.run(r,i);return n instanceof Promise?n.then(o=>Bo(o,e)):Bo(n,e)}});function Bo(e,t){return!e.issues.length&&e.value===void 0&&e.issues.push({code:"invalid_type",expected:"nonoptional",input:e.value,inst:t}),e}const Tc=l("$ZodSuccess",(e,t)=>{A.init(e,t),e._zod.parse=(r,i)=>{const n=t.innerType._zod.run(r,i);return n instanceof Promise?n.then(o=>(r.value=o.issues.length===0,r)):(r.value=n.issues.length===0,r)}}),Dc=l("$ZodCatch",(e,t)=>{A.init(e,t),e._zod.optin="optional",R(e._zod,"optout",()=>t.innerType._zod.optout),R(e._zod,"values",()=>t.innerType._zod.values),e._zod.parse=(r,i)=>{const n=t.innerType._zod.run(r,i);return n instanceof Promise?n.then(o=>(r.value=o.value,o.issues.length&&(r.value=t.catchValue({...r,error:{issues:o.issues.map(a=>le(a,i,ee()))},input:r.value}),r.issues=[]),r)):(r.value=n.value,n.issues.length&&(r.value=t.catchValue({...r,error:{issues:n.issues.map(o=>le(o,i,ee()))},input:r.value}),r.issues=[]),r)}}),Rc=l("$ZodNaN",(e,t)=>{A.init(e,t),e._zod.parse=(r,i)=>((typeof r.value!="number"||!Number.isNaN(r.value))&&r.issues.push({input:r.value,inst:e,expected:"nan",code:"invalid_type"}),r)}),Wr=l("$ZodPipe",(e,t)=>{A.init(e,t),R(e._zod,"values",()=>t.in._zod.values),R(e._zod,"optin",()=>t.in._zod.optin),R(e._zod,"optout",()=>t.out._zod.optout),e._zod.parse=(r,i)=>{const n=t.in._zod.run(r,i);return n instanceof Promise?n.then(o=>Jo(o,t,i)):Jo(n,t,i)}});function Jo(e,t,r){return Le(e)?e:t.out._zod.run({value:e.value,issues:e.issues},r)}const Cc=l("$ZodReadonly",(e,t)=>{A.init(e,t),R(e._zod,"propValues",()=>t.innerType._zod.propValues),R(e._zod,"values",()=>t.innerType._zod.values),R(e._zod,"optin",()=>t.innerType._zod.optin),R(e._zod,"optout",()=>t.innerType._zod.optout),e._zod.parse=(r,i)=>{const n=t.innerType._zod.run(r,i);return n instanceof Promise?n.then(Wo):Wo(n)}});function Wo(e){return e.value=Object.freeze(e.value),e}const Zc=l("$ZodTemplateLiteral",(e,t)=>{A.init(e,t);const r=[];for(const i of t.parts)if(i instanceof A){if(!i._zod.pattern)throw new Error(`Invalid template literal part, no pattern found: ${[...i._zod.traits].shift()}`);const n=i._zod.pattern instanceof RegExp?i._zod.pattern.source:i._zod.pattern;if(!n)throw new Error(`Invalid template literal part: ${i._zod.traits}`);const o=n.startsWith("^")?1:0,a=n.endsWith("$")?n.length-1:n.length;r.push(n.slice(o,a))}else if(i===null||Ss.has(typeof i))r.push(Ne(`${i}`));else throw new Error(`Invalid template literal part: ${i}`);e._zod.pattern=new RegExp(`^${r.join("")}$`),e._zod.parse=(i,n)=>typeof i.value!="string"?(i.issues.push({input:i.value,inst:e,expected:"template_literal",code:"invalid_type"}),i):(e._zod.pattern.lastIndex=0,e._zod.pattern.test(i.value)||i.issues.push({input:i.value,inst:e,code:"invalid_format",format:"template_literal",pattern:e._zod.pattern.source}),i)}),Lc=l("$ZodPromise",(e,t)=>{A.init(e,t),e._zod.parse=(r,i)=>Promise.resolve(r.value).then(n=>t.innerType._zod.run({value:n,issues:[]},i))}),Mc=l("$ZodLazy",(e,t)=>{A.init(e,t),R(e._zod,"innerType",()=>t.getter()),R(e._zod,"pattern",()=>e._zod.innerType._zod.pattern),R(e._zod,"propValues",()=>e._zod.innerType._zod.propValues),R(e._zod,"optin",()=>e._zod.innerType._zod.optin),R(e._zod,"optout",()=>e._zod.innerType._zod.optout),e._zod.parse=(r,i)=>e._zod.innerType._zod.run(r,i)}),Fc=l("$ZodCustom",(e,t)=>{J.init(e,t),A.init(e,t),e._zod.parse=(r,i)=>r,e._zod.check=r=>{const i=r.value,n=t.fn(i);if(n instanceof Promise)return n.then(o=>qo(o,r,i,e));qo(n,r,i,e)}});function qo(e,t,r,i){if(!e){const n={code:"custom",input:r,inst:i,path:[...i._zod.def.path??[]],continue:!i._zod.def.abort};i._zod.def.params&&(n.params=i._zod.def.params),t.issues.push(Fe(n))}}const hm=()=>{const e={string:{unit:"حرف",verb:"أن يحوي"},file:{unit:"بايت",verb:"أن يحوي"},array:{unit:"عنصر",verb:"أن يحوي"},set:{unit:"عنصر",verb:"أن يحوي"}};function t(n){return e[n]??null}const r=n=>{const o=typeof n;switch(o){case"number":return Number.isNaN(n)?"NaN":"number";case"object":{if(Array.isArray(n))return"array";if(n===null)return"null";if(Object.getPrototypeOf(n)!==Object.prototype&&n.constructor)return n.constructor.name}}return o},i={regex:"مدخل",email:"بريد إلكتروني",url:"رابط",emoji:"إيموجي",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"تاريخ ووقت بمعيار ISO",date:"تاريخ بمعيار ISO",time:"وقت بمعيار ISO",duration:"مدة بمعيار ISO",ipv4:"عنوان IPv4",ipv6:"عنوان IPv6",cidrv4:"مدى عناوين بصيغة IPv4",cidrv6:"مدى عناوين بصيغة IPv6",base64:"نَص بترميز base64-encoded",base64url:"نَص بترميز base64url-encoded",json_string:"نَص على هيئة JSON",e164:"رقم هاتف بمعيار E.164",jwt:"JWT",template_literal:"مدخل"};return n=>{switch(n.code){case"invalid_type":return`مدخلات غير مقبولة: يفترض إدخال ${n.expected}، ولكن تم إدخال ${r(n.input)}`;case"invalid_value":return n.values.length===1?`مدخلات غير مقبولة: يفترض إدخال ${E(n.values[0])}`:`اختيار غير مقبول: يتوقع انتقاء أحد هذه الخيارات: ${k(n.values,"|")}`;case"too_big":{const o=n.inclusive?"<=":"<",a=t(n.origin);return a?` أكبر من اللازم: يفترض أن تكون ${n.origin??"القيمة"} ${o} ${n.maximum.toString()} ${a.unit??"عنصر"}`:`أكبر من اللازم: يفترض أن تكون ${n.origin??"القيمة"} ${o} ${n.maximum.toString()}`}case"too_small":{const o=n.inclusive?">=":">",a=t(n.origin);return a?`أصغر من اللازم: يفترض لـ ${n.origin} أن يكون ${o} ${n.minimum.toString()} ${a.unit}`:`أصغر من اللازم: يفترض لـ ${n.origin} أن يكون ${o} ${n.minimum.toString()}`}case"invalid_format":{const o=n;return o.format==="starts_with"?`نَص غير مقبول: يجب أن يبدأ بـ "${n.prefix}"`:o.format==="ends_with"?`نَص غير مقبول: يجب أن ينتهي بـ "${o.suffix}"`:o.format==="includes"?`نَص غير مقبول: يجب أن يتضمَّن "${o.includes}"`:o.format==="regex"?`نَص غير مقبول: يجب أن يطابق النمط ${o.pattern}`:`${i[o.format]??n.format} غير مقبول`}case"not_multiple_of":return`رقم غير مقبول: يجب أن يكون من مضاعفات ${n.divisor}`;case"unrecognized_keys":return`معرف${n.keys.length>1?"ات":""} غريب${n.keys.length>1?"ة":""}: ${k(n.keys,"، ")}`;case"invalid_key":return`معرف غير مقبول في ${n.origin}`;case"invalid_union":return"مدخل غير مقبول";case"invalid_element":return`مدخل غير مقبول في ${n.origin}`;default:return"مدخل غير مقبول"}}};function pm(){return{localeError:hm()}}const gm=()=>{const e={string:{unit:"simvol",verb:"olmalıdır"},file:{unit:"bayt",verb:"olmalıdır"},array:{unit:"element",verb:"olmalıdır"},set:{unit:"element",verb:"olmalıdır"}};function t(n){return e[n]??null}const r=n=>{const o=typeof n;switch(o){case"number":return Number.isNaN(n)?"NaN":"number";case"object":{if(Array.isArray(n))return"array";if(n===null)return"null";if(Object.getPrototypeOf(n)!==Object.prototype&&n.constructor)return n.constructor.name}}return o},i={regex:"input",email:"email address",url:"URL",emoji:"emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"ISO datetime",date:"ISO date",time:"ISO time",duration:"ISO duration",ipv4:"IPv4 address",ipv6:"IPv6 address",cidrv4:"IPv4 range",cidrv6:"IPv6 range",base64:"base64-encoded string",base64url:"base64url-encoded string",json_string:"JSON string",e164:"E.164 number",jwt:"JWT",template_literal:"input"};return n=>{switch(n.code){case"invalid_type":return`Yanlış dəyər: gözlənilən ${n.expected}, daxil olan ${r(n.input)}`;case"invalid_value":return n.values.length===1?`Yanlış dəyər: gözlənilən ${E(n.values[0])}`:`Yanlış seçim: aşağıdakılardan biri olmalıdır: ${k(n.values,"|")}`;case"too_big":{const o=n.inclusive?"<=":"<",a=t(n.origin);return a?`Çox böyük: gözlənilən ${n.origin??"dəyər"} ${o}${n.maximum.toString()} ${a.unit??"element"}`:`Çox böyük: gözlənilən ${n.origin??"dəyər"} ${o}${n.maximum.toString()}`}case"too_small":{const o=n.inclusive?">=":">",a=t(n.origin);return a?`Çox kiçik: gözlənilən ${n.origin} ${o}${n.minimum.toString()} ${a.unit}`:`Çox kiçik: gözlənilən ${n.origin} ${o}${n.minimum.toString()}`}case"invalid_format":{const o=n;return o.format==="starts_with"?`Yanlış mətn: "${o.prefix}" ilə başlamalıdır`:o.format==="ends_with"?`Yanlış mətn: "${o.suffix}" ilə bitməlidir`:o.format==="includes"?`Yanlış mətn: "${o.includes}" daxil olmalıdır`:o.format==="regex"?`Yanlış mətn: ${o.pattern} şablonuna uyğun olmalıdır`:`Yanlış ${i[o.format]??n.format}`}case"not_multiple_of":return`Yanlış ədəd: ${n.divisor} ilə bölünə bilən olmalıdır`;case"unrecognized_keys":return`Tanınmayan açar${n.keys.length>1?"lar":""}: ${k(n.keys,", ")}`;case"invalid_key":return`${n.origin} daxilində yanlış açar`;case"invalid_union":return"Yanlış dəyər";case"invalid_element":return`${n.origin} daxilində yanlış dəyər`;default:return"Yanlış dəyər"}}};function vm(){return{localeError:gm()}}function Vo(e,t,r,i){const n=Math.abs(e),o=n%10,a=n%100;return a>=11&&a<=19?i:o===1?t:o>=2&&o<=4?r:i}const _m=()=>{const e={string:{unit:{one:"сімвал",few:"сімвалы",many:"сімвалаў"},verb:"мець"},array:{unit:{one:"элемент",few:"элементы",many:"элементаў"},verb:"мець"},set:{unit:{one:"элемент",few:"элементы",many:"элементаў"},verb:"мець"},file:{unit:{one:"байт",few:"байты",many:"байтаў"},verb:"мець"}};function t(n){return e[n]??null}const r=n=>{const o=typeof n;switch(o){case"number":return Number.isNaN(n)?"NaN":"лік";case"object":{if(Array.isArray(n))return"масіў";if(n===null)return"null";if(Object.getPrototypeOf(n)!==Object.prototype&&n.constructor)return n.constructor.name}}return o},i={regex:"увод",email:"email адрас",url:"URL",emoji:"эмодзі",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"ISO дата і час",date:"ISO дата",time:"ISO час",duration:"ISO працягласць",ipv4:"IPv4 адрас",ipv6:"IPv6 адрас",cidrv4:"IPv4 дыяпазон",cidrv6:"IPv6 дыяпазон",base64:"радок у фармаце base64",base64url:"радок у фармаце base64url",json_string:"JSON радок",e164:"нумар E.164",jwt:"JWT",template_literal:"увод"};return n=>{switch(n.code){case"invalid_type":return`Няправільны ўвод: чакаўся ${n.expected}, атрымана ${r(n.input)}`;case"invalid_value":return n.values.length===1?`Няправільны ўвод: чакалася ${E(n.values[0])}`:`Няправільны варыянт: чакаўся адзін з ${k(n.values,"|")}`;case"too_big":{const o=n.inclusive?"<=":"<",a=t(n.origin);if(a){const s=Number(n.maximum),u=Vo(s,a.unit.one,a.unit.few,a.unit.many);return`Занадта вялікі: чакалася, што ${n.origin??"значэнне"} павінна ${a.verb} ${o}${n.maximum.toString()} ${u}`}return`Занадта вялікі: чакалася, што ${n.origin??"значэнне"} павінна быць ${o}${n.maximum.toString()}`}case"too_small":{const o=n.inclusive?">=":">",a=t(n.origin);if(a){const s=Number(n.minimum),u=Vo(s,a.unit.one,a.unit.few,a.unit.many);return`Занадта малы: чакалася, што ${n.origin} павінна ${a.verb} ${o}${n.minimum.toString()} ${u}`}return`Занадта малы: чакалася, што ${n.origin} павінна быць ${o}${n.minimum.toString()}`}case"invalid_format":{const o=n;return o.format==="starts_with"?`Няправільны радок: павінен пачынацца з "${o.prefix}"`:o.format==="ends_with"?`Няправільны радок: павінен заканчвацца на "${o.suffix}"`:o.format==="includes"?`Няправільны радок: павінен змяшчаць "${o.includes}"`:o.format==="regex"?`Няправільны радок: павінен адпавядаць шаблону ${o.pattern}`:`Няправільны ${i[o.format]??n.format}`}case"not_multiple_of":return`Няправільны лік: павінен быць кратным ${n.divisor}`;case"unrecognized_keys":return`Нераспазнаны ${n.keys.length>1?"ключы":"ключ"}: ${k(n.keys,", ")}`;case"invalid_key":return`Няправільны ключ у ${n.origin}`;case"invalid_union":return"Няправільны ўвод";case"invalid_element":return`Няправільнае значэнне ў ${n.origin}`;default:return"Няправільны ўвод"}}};function bm(){return{localeError:_m()}}const ym=()=>{const e={string:{unit:"caràcters",verb:"contenir"},file:{unit:"bytes",verb:"contenir"},array:{unit:"elements",verb:"contenir"},set:{unit:"elements",verb:"contenir"}};function t(n){return e[n]??null}const r=n=>{const o=typeof n;switch(o){case"number":return Number.isNaN(n)?"NaN":"number";case"object":{if(Array.isArray(n))return"array";if(n===null)return"null";if(Object.getPrototypeOf(n)!==Object.prototype&&n.constructor)return n.constructor.name}}return o},i={regex:"entrada",email:"adreça electrònica",url:"URL",emoji:"emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"data i hora ISO",date:"data ISO",time:"hora ISO",duration:"durada ISO",ipv4:"adreça IPv4",ipv6:"adreça IPv6",cidrv4:"rang IPv4",cidrv6:"rang IPv6",base64:"cadena codificada en base64",base64url:"cadena codificada en base64url",json_string:"cadena JSON",e164:"número E.164",jwt:"JWT",template_literal:"entrada"};return n=>{switch(n.code){case"invalid_type":return`Tipus invàlid: s'esperava ${n.expected}, s'ha rebut ${r(n.input)}`;case"invalid_value":return n.values.length===1?`Valor invàlid: s'esperava ${E(n.values[0])}`:`Opció invàlida: s'esperava una de ${k(n.values," o ")}`;case"too_big":{const o=n.inclusive?"com a màxim":"menys de",a=t(n.origin);return a?`Massa gran: s'esperava que ${n.origin??"el valor"} contingués ${o} ${n.maximum.toString()} ${a.unit??"elements"}`:`Massa gran: s'esperava que ${n.origin??"el valor"} fos ${o} ${n.maximum.toString()}`}case"too_small":{const o=n.inclusive?"com a mínim":"més de",a=t(n.origin);return a?`Massa petit: s'esperava que ${n.origin} contingués ${o} ${n.minimum.toString()} ${a.unit}`:`Massa petit: s'esperava que ${n.origin} fos ${o} ${n.minimum.toString()}`}case"invalid_format":{const o=n;return o.format==="starts_with"?`Format invàlid: ha de començar amb "${o.prefix}"`:o.format==="ends_with"?`Format invàlid: ha d'acabar amb "${o.suffix}"`:o.format==="includes"?`Format invàlid: ha d'incloure "${o.includes}"`:o.format==="regex"?`Format invàlid: ha de coincidir amb el patró ${o.pattern}`:`Format invàlid per a ${i[o.format]??n.format}`}case"not_multiple_of":return`Número invàlid: ha de ser múltiple de ${n.divisor}`;case"unrecognized_keys":return`Clau${n.keys.length>1?"s":""} no reconeguda${n.keys.length>1?"s":""}: ${k(n.keys,", ")}`;case"invalid_key":return`Clau invàlida a ${n.origin}`;case"invalid_union":return"Entrada invàlida";case"invalid_element":return`Element invàlid a ${n.origin}`;default:return"Entrada invàlida"}}};function $m(){return{localeError:ym()}}const wm=()=>{const e={string:{unit:"znaků",verb:"mít"},file:{unit:"bajtů",verb:"mít"},array:{unit:"prvků",verb:"mít"},set:{unit:"prvků",verb:"mít"}};function t(n){return e[n]??null}const r=n=>{const o=typeof n;switch(o){case"number":return Number.isNaN(n)?"NaN":"číslo";case"string":return"řetězec";case"boolean":return"boolean";case"bigint":return"bigint";case"function":return"funkce";case"symbol":return"symbol";case"undefined":return"undefined";case"object":{if(Array.isArray(n))return"pole";if(n===null)return"null";if(Object.getPrototypeOf(n)!==Object.prototype&&n.constructor)return n.constructor.name}}return o},i={regex:"regulární výraz",email:"e-mailová adresa",url:"URL",emoji:"emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"datum a čas ve formátu ISO",date:"datum ve formátu ISO",time:"čas ve formátu ISO",duration:"doba trvání ISO",ipv4:"IPv4 adresa",ipv6:"IPv6 adresa",cidrv4:"rozsah IPv4",cidrv6:"rozsah IPv6",base64:"řetězec zakódovaný ve formátu base64",base64url:"řetězec zakódovaný ve formátu base64url",json_string:"řetězec ve formátu JSON",e164:"číslo E.164",jwt:"JWT",template_literal:"vstup"};return n=>{switch(n.code){case"invalid_type":return`Neplatný vstup: očekáváno ${n.expected}, obdrženo ${r(n.input)}`;case"invalid_value":return n.values.length===1?`Neplatný vstup: očekáváno ${E(n.values[0])}`:`Neplatná možnost: očekávána jedna z hodnot ${k(n.values,"|")}`;case"too_big":{const o=n.inclusive?"<=":"<",a=t(n.origin);return a?`Hodnota je příliš velká: ${n.origin??"hodnota"} musí mít ${o}${n.maximum.toString()} ${a.unit??"prvků"}`:`Hodnota je příliš velká: ${n.origin??"hodnota"} musí být ${o}${n.maximum.toString()}`}case"too_small":{const o=n.inclusive?">=":">",a=t(n.origin);return a?`Hodnota je příliš malá: ${n.origin??"hodnota"} musí mít ${o}${n.minimum.toString()} ${a.unit??"prvků"}`:`Hodnota je příliš malá: ${n.origin??"hodnota"} musí být ${o}${n.minimum.toString()}`}case"invalid_format":{const o=n;return o.format==="starts_with"?`Neplatný řetězec: musí začínat na "${o.prefix}"`:o.format==="ends_with"?`Neplatný řetězec: musí končit na "${o.suffix}"`:o.format==="includes"?`Neplatný řetězec: musí obsahovat "${o.includes}"`:o.format==="regex"?`Neplatný řetězec: musí odpovídat vzoru ${o.pattern}`:`Neplatný formát ${i[o.format]??n.format}`}case"not_multiple_of":return`Neplatné číslo: musí být násobkem ${n.divisor}`;case"unrecognized_keys":return`Neznámé klíče: ${k(n.keys,", ")}`;case"invalid_key":return`Neplatný klíč v ${n.origin}`;case"invalid_union":return"Neplatný vstup";case"invalid_element":return`Neplatná hodnota v ${n.origin}`;default:return"Neplatný vstup"}}};function km(){return{localeError:wm()}}const Sm=()=>{const e={string:{unit:"Zeichen",verb:"zu haben"},file:{unit:"Bytes",verb:"zu haben"},array:{unit:"Elemente",verb:"zu haben"},set:{unit:"Elemente",verb:"zu haben"}};function t(n){return e[n]??null}const r=n=>{const o=typeof n;switch(o){case"number":return Number.isNaN(n)?"NaN":"Zahl";case"object":{if(Array.isArray(n))return"Array";if(n===null)return"null";if(Object.getPrototypeOf(n)!==Object.prototype&&n.constructor)return n.constructor.name}}return o},i={regex:"Eingabe",email:"E-Mail-Adresse",url:"URL",emoji:"Emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"ISO-Datum und -Uhrzeit",date:"ISO-Datum",time:"ISO-Uhrzeit",duration:"ISO-Dauer",ipv4:"IPv4-Adresse",ipv6:"IPv6-Adresse",cidrv4:"IPv4-Bereich",cidrv6:"IPv6-Bereich",base64:"Base64-codierter String",base64url:"Base64-URL-codierter String",json_string:"JSON-String",e164:"E.164-Nummer",jwt:"JWT",template_literal:"Eingabe"};return n=>{switch(n.code){case"invalid_type":return`Ungültige Eingabe: erwartet ${n.expected}, erhalten ${r(n.input)}`;case"invalid_value":return n.values.length===1?`Ungültige Eingabe: erwartet ${E(n.values[0])}`:`Ungültige Option: erwartet eine von ${k(n.values,"|")}`;case"too_big":{const o=n.inclusive?"<=":"<",a=t(n.origin);return a?`Zu groß: erwartet, dass ${n.origin??"Wert"} ${o}${n.maximum.toString()} ${a.unit??"Elemente"} hat`:`Zu groß: erwartet, dass ${n.origin??"Wert"} ${o}${n.maximum.toString()} ist`}case"too_small":{const o=n.inclusive?">=":">",a=t(n.origin);return a?`Zu klein: erwartet, dass ${n.origin} ${o}${n.minimum.toString()} ${a.unit} hat`:`Zu klein: erwartet, dass ${n.origin} ${o}${n.minimum.toString()} ist`}case"invalid_format":{const o=n;return o.format==="starts_with"?`Ungültiger String: muss mit "${o.prefix}" beginnen`:o.format==="ends_with"?`Ungültiger String: muss mit "${o.suffix}" enden`:o.format==="includes"?`Ungültiger String: muss "${o.includes}" enthalten`:o.format==="regex"?`Ungültiger String: muss dem Muster ${o.pattern} entsprechen`:`Ungültig: ${i[o.format]??n.format}`}case"not_multiple_of":return`Ungültige Zahl: muss ein Vielfaches von ${n.divisor} sein`;case"unrecognized_keys":return`${n.keys.length>1?"Unbekannte Schlüssel":"Unbekannter Schlüssel"}: ${k(n.keys,", ")}`;case"invalid_key":return`Ungültiger Schlüssel in ${n.origin}`;case"invalid_union":return"Ungültige Eingabe";case"invalid_element":return`Ungültiger Wert in ${n.origin}`;default:return"Ungültige Eingabe"}}};function Im(){return{localeError:Sm()}}const xm=e=>{const t=typeof e;switch(t){case"number":return Number.isNaN(e)?"NaN":"number";case"object":{if(Array.isArray(e))return"array";if(e===null)return"null";if(Object.getPrototypeOf(e)!==Object.prototype&&e.constructor)return e.constructor.name}}return t},zm=()=>{const e={string:{unit:"characters",verb:"to have"},file:{unit:"bytes",verb:"to have"},array:{unit:"items",verb:"to have"},set:{unit:"items",verb:"to have"}};function t(i){return e[i]??null}const r={regex:"input",email:"email address",url:"URL",emoji:"emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"ISO datetime",date:"ISO date",time:"ISO time",duration:"ISO duration",ipv4:"IPv4 address",ipv6:"IPv6 address",cidrv4:"IPv4 range",cidrv6:"IPv6 range",base64:"base64-encoded string",base64url:"base64url-encoded string",json_string:"JSON string",e164:"E.164 number",jwt:"JWT",template_literal:"input"};return i=>{switch(i.code){case"invalid_type":return`Invalid input: expected ${i.expected}, received ${xm(i.input)}`;case"invalid_value":return i.values.length===1?`Invalid input: expected ${E(i.values[0])}`:`Invalid option: expected one of ${k(i.values,"|")}`;case"too_big":{const n=i.inclusive?"<=":"<",o=t(i.origin);return o?`Too big: expected ${i.origin??"value"} to have ${n}${i.maximum.toString()} ${o.unit??"elements"}`:`Too big: expected ${i.origin??"value"} to be ${n}${i.maximum.toString()}`}case"too_small":{const n=i.inclusive?">=":">",o=t(i.origin);return o?`Too small: expected ${i.origin} to have ${n}${i.minimum.toString()} ${o.unit}`:`Too small: expected ${i.origin} to be ${n}${i.minimum.toString()}`}case"invalid_format":{const n=i;return n.format==="starts_with"?`Invalid string: must start with "${n.prefix}"`:n.format==="ends_with"?`Invalid string: must end with "${n.suffix}"`:n.format==="includes"?`Invalid string: must include "${n.includes}"`:n.format==="regex"?`Invalid string: must match pattern ${n.pattern}`:`Invalid ${r[n.format]??i.format}`}case"not_multiple_of":return`Invalid number: must be a multiple of ${i.divisor}`;case"unrecognized_keys":return`Unrecognized key${i.keys.length>1?"s":""}: ${k(i.keys,", ")}`;case"invalid_key":return`Invalid key in ${i.origin}`;case"invalid_union":return"Invalid input";case"invalid_element":return`Invalid value in ${i.origin}`;default:return"Invalid input"}}};function Bc(){return{localeError:zm()}}const Om=e=>{const t=typeof e;switch(t){case"number":return Number.isNaN(e)?"NaN":"nombro";case"object":{if(Array.isArray(e))return"tabelo";if(e===null)return"senvalora";if(Object.getPrototypeOf(e)!==Object.prototype&&e.constructor)return e.constructor.name}}return t},Nm=()=>{const e={string:{unit:"karaktrojn",verb:"havi"},file:{unit:"bajtojn",verb:"havi"},array:{unit:"elementojn",verb:"havi"},set:{unit:"elementojn",verb:"havi"}};function t(i){return e[i]??null}const r={regex:"enigo",email:"retadreso",url:"URL",emoji:"emoĝio",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"ISO-datotempo",date:"ISO-dato",time:"ISO-tempo",duration:"ISO-daŭro",ipv4:"IPv4-adreso",ipv6:"IPv6-adreso",cidrv4:"IPv4-rango",cidrv6:"IPv6-rango",base64:"64-ume kodita karaktraro",base64url:"URL-64-ume kodita karaktraro",json_string:"JSON-karaktraro",e164:"E.164-nombro",jwt:"JWT",template_literal:"enigo"};return i=>{switch(i.code){case"invalid_type":return`Nevalida enigo: atendiĝis ${i.expected}, riceviĝis ${Om(i.input)}`;case"invalid_value":return i.values.length===1?`Nevalida enigo: atendiĝis ${E(i.values[0])}`:`Nevalida opcio: atendiĝis unu el ${k(i.values,"|")}`;case"too_big":{const n=i.inclusive?"<=":"<",o=t(i.origin);return o?`Tro granda: atendiĝis ke ${i.origin??"valoro"} havu ${n}${i.maximum.toString()} ${o.unit??"elementojn"}`:`Tro granda: atendiĝis ke ${i.origin??"valoro"} havu ${n}${i.maximum.toString()}`}case"too_small":{const n=i.inclusive?">=":">",o=t(i.origin);return o?`Tro malgranda: atendiĝis ke ${i.origin} havu ${n}${i.minimum.toString()} ${o.unit}`:`Tro malgranda: atendiĝis ke ${i.origin} estu ${n}${i.minimum.toString()}`}case"invalid_format":{const n=i;return n.format==="starts_with"?`Nevalida karaktraro: devas komenciĝi per "${n.prefix}"`:n.format==="ends_with"?`Nevalida karaktraro: devas finiĝi per "${n.suffix}"`:n.format==="includes"?`Nevalida karaktraro: devas inkluzivi "${n.includes}"`:n.format==="regex"?`Nevalida karaktraro: devas kongrui kun la modelo ${n.pattern}`:`Nevalida ${r[n.format]??i.format}`}case"not_multiple_of":return`Nevalida nombro: devas esti oblo de ${i.divisor}`;case"unrecognized_keys":return`Nekonata${i.keys.length>1?"j":""} ŝlosilo${i.keys.length>1?"j":""}: ${k(i.keys,", ")}`;case"invalid_key":return`Nevalida ŝlosilo en ${i.origin}`;case"invalid_union":return"Nevalida enigo";case"invalid_element":return`Nevalida valoro en ${i.origin}`;default:return"Nevalida enigo"}}};function jm(){return{localeError:Nm()}}const Um=()=>{const e={string:{unit:"caracteres",verb:"tener"},file:{unit:"bytes",verb:"tener"},array:{unit:"elementos",verb:"tener"},set:{unit:"elementos",verb:"tener"}};function t(n){return e[n]??null}const r=n=>{const o=typeof n;switch(o){case"number":return Number.isNaN(n)?"NaN":"número";case"object":{if(Array.isArray(n))return"arreglo";if(n===null)return"nulo";if(Object.getPrototypeOf(n)!==Object.prototype)return n.constructor.name}}return o},i={regex:"entrada",email:"dirección de correo electrónico",url:"URL",emoji:"emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"fecha y hora ISO",date:"fecha ISO",time:"hora ISO",duration:"duración ISO",ipv4:"dirección IPv4",ipv6:"dirección IPv6",cidrv4:"rango IPv4",cidrv6:"rango IPv6",base64:"cadena codificada en base64",base64url:"URL codificada en base64",json_string:"cadena JSON",e164:"número E.164",jwt:"JWT",template_literal:"entrada"};return n=>{switch(n.code){case"invalid_type":return`Entrada inválida: se esperaba ${n.expected}, recibido ${r(n.input)}`;case"invalid_value":return n.values.length===1?`Entrada inválida: se esperaba ${E(n.values[0])}`:`Opción inválida: se esperaba una de ${k(n.values,"|")}`;case"too_big":{const o=n.inclusive?"<=":"<",a=t(n.origin);return a?`Demasiado grande: se esperaba que ${n.origin??"valor"} tuviera ${o}${n.maximum.toString()} ${a.unit??"elementos"}`:`Demasiado grande: se esperaba que ${n.origin??"valor"} fuera ${o}${n.maximum.toString()}`}case"too_small":{const o=n.inclusive?">=":">",a=t(n.origin);return a?`Demasiado pequeño: se esperaba que ${n.origin} tuviera ${o}${n.minimum.toString()} ${a.unit}`:`Demasiado pequeño: se esperaba que ${n.origin} fuera ${o}${n.minimum.toString()}`}case"invalid_format":{const o=n;return o.format==="starts_with"?`Cadena inválida: debe comenzar con "${o.prefix}"`:o.format==="ends_with"?`Cadena inválida: debe terminar en "${o.suffix}"`:o.format==="includes"?`Cadena inválida: debe incluir "${o.includes}"`:o.format==="regex"?`Cadena inválida: debe coincidir con el patrón ${o.pattern}`:`Inválido ${i[o.format]??n.format}`}case"not_multiple_of":return`Número inválido: debe ser múltiplo de ${n.divisor}`;case"unrecognized_keys":return`Llave${n.keys.length>1?"s":""} desconocida${n.keys.length>1?"s":""}: ${k(n.keys,", ")}`;case"invalid_key":return`Llave inválida en ${n.origin}`;case"invalid_union":return"Entrada inválida";case"invalid_element":return`Valor inválido en ${n.origin}`;default:return"Entrada inválida"}}};function Pm(){return{localeError:Um()}}const Am=()=>{const e={string:{unit:"کاراکتر",verb:"داشته باشد"},file:{unit:"بایت",verb:"داشته باشد"},array:{unit:"آیتم",verb:"داشته باشد"},set:{unit:"آیتم",verb:"داشته باشد"}};function t(n){return e[n]??null}const r=n=>{const o=typeof n;switch(o){case"number":return Number.isNaN(n)?"NaN":"عدد";case"object":{if(Array.isArray(n))return"آرایه";if(n===null)return"null";if(Object.getPrototypeOf(n)!==Object.prototype&&n.constructor)return n.constructor.name}}return o},i={regex:"ورودی",email:"آدرس ایمیل",url:"URL",emoji:"ایموجی",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"تاریخ و زمان ایزو",date:"تاریخ ایزو",time:"زمان ایزو",duration:"مدت زمان ایزو",ipv4:"IPv4 آدرس",ipv6:"IPv6 آدرس",cidrv4:"IPv4 دامنه",cidrv6:"IPv6 دامنه",base64:"base64-encoded رشته",base64url:"base64url-encoded رشته",json_string:"JSON رشته",e164:"E.164 عدد",jwt:"JWT",template_literal:"ورودی"};return n=>{switch(n.code){case"invalid_type":return`ورودی نامعتبر: می‌بایست ${n.expected} می‌بود، ${r(n.input)} دریافت شد`;case"invalid_value":return n.values.length===1?`ورودی نامعتبر: می‌بایست ${E(n.values[0])} می‌بود`:`گزینه نامعتبر: می‌بایست یکی از ${k(n.values,"|")} می‌بود`;case"too_big":{const o=n.inclusive?"<=":"<",a=t(n.origin);return a?`خیلی بزرگ: ${n.origin??"مقدار"} باید ${o}${n.maximum.toString()} ${a.unit??"عنصر"} باشد`:`خیلی بزرگ: ${n.origin??"مقدار"} باید ${o}${n.maximum.toString()} باشد`}case"too_small":{const o=n.inclusive?">=":">",a=t(n.origin);return a?`خیلی کوچک: ${n.origin} باید ${o}${n.minimum.toString()} ${a.unit} باشد`:`خیلی کوچک: ${n.origin} باید ${o}${n.minimum.toString()} باشد`}case"invalid_format":{const o=n;return o.format==="starts_with"?`رشته نامعتبر: باید با "${o.prefix}" شروع شود`:o.format==="ends_with"?`رشته نامعتبر: باید با "${o.suffix}" تمام شود`:o.format==="includes"?`رشته نامعتبر: باید شامل "${o.includes}" باشد`:o.format==="regex"?`رشته نامعتبر: باید با الگوی ${o.pattern} مطابقت داشته باشد`:`${i[o.format]??n.format} نامعتبر`}case"not_multiple_of":return`عدد نامعتبر: باید مضرب ${n.divisor} باشد`;case"unrecognized_keys":return`کلید${n.keys.length>1?"های":""} ناشناس: ${k(n.keys,", ")}`;case"invalid_key":return`کلید ناشناس در ${n.origin}`;case"invalid_union":return"ورودی نامعتبر";case"invalid_element":return`مقدار نامعتبر در ${n.origin}`;default:return"ورودی نامعتبر"}}};function Em(){return{localeError:Am()}}const Tm=()=>{const e={string:{unit:"merkkiä",subject:"merkkijonon"},file:{unit:"tavua",subject:"tiedoston"},array:{unit:"alkiota",subject:"listan"},set:{unit:"alkiota",subject:"joukon"},number:{unit:"",subject:"luvun"},bigint:{unit:"",subject:"suuren kokonaisluvun"},int:{unit:"",subject:"kokonaisluvun"},date:{unit:"",subject:"päivämäärän"}};function t(n){return e[n]??null}const r=n=>{const o=typeof n;switch(o){case"number":return Number.isNaN(n)?"NaN":"number";case"object":{if(Array.isArray(n))return"array";if(n===null)return"null";if(Object.getPrototypeOf(n)!==Object.prototype&&n.constructor)return n.constructor.name}}return o},i={regex:"säännöllinen lauseke",email:"sähköpostiosoite",url:"URL-osoite",emoji:"emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"ISO-aikaleima",date:"ISO-päivämäärä",time:"ISO-aika",duration:"ISO-kesto",ipv4:"IPv4-osoite",ipv6:"IPv6-osoite",cidrv4:"IPv4-alue",cidrv6:"IPv6-alue",base64:"base64-koodattu merkkijono",base64url:"base64url-koodattu merkkijono",json_string:"JSON-merkkijono",e164:"E.164-luku",jwt:"JWT",template_literal:"templaattimerkkijono"};return n=>{switch(n.code){case"invalid_type":return`Virheellinen tyyppi: odotettiin ${n.expected}, oli ${r(n.input)}`;case"invalid_value":return n.values.length===1?`Virheellinen syöte: täytyy olla ${E(n.values[0])}`:`Virheellinen valinta: täytyy olla yksi seuraavista: ${k(n.values,"|")}`;case"too_big":{const o=n.inclusive?"<=":"<",a=t(n.origin);return a?`Liian suuri: ${a.subject} täytyy olla ${o}${n.maximum.toString()} ${a.unit}`.trim():`Liian suuri: arvon täytyy olla ${o}${n.maximum.toString()}`}case"too_small":{const o=n.inclusive?">=":">",a=t(n.origin);return a?`Liian pieni: ${a.subject} täytyy olla ${o}${n.minimum.toString()} ${a.unit}`.trim():`Liian pieni: arvon täytyy olla ${o}${n.minimum.toString()}`}case"invalid_format":{const o=n;return o.format==="starts_with"?`Virheellinen syöte: täytyy alkaa "${o.prefix}"`:o.format==="ends_with"?`Virheellinen syöte: täytyy loppua "${o.suffix}"`:o.format==="includes"?`Virheellinen syöte: täytyy sisältää "${o.includes}"`:o.format==="regex"?`Virheellinen syöte: täytyy vastata säännöllistä lauseketta ${o.pattern}`:`Virheellinen ${i[o.format]??n.format}`}case"not_multiple_of":return`Virheellinen luku: täytyy olla luvun ${n.divisor} monikerta`;case"unrecognized_keys":return`${n.keys.length>1?"Tuntemattomat avaimet":"Tuntematon avain"}: ${k(n.keys,", ")}`;case"invalid_key":return"Virheellinen avain tietueessa";case"invalid_union":return"Virheellinen unioni";case"invalid_element":return"Virheellinen arvo joukossa";default:return"Virheellinen syöte"}}};function Dm(){return{localeError:Tm()}}const Rm=()=>{const e={string:{unit:"caractères",verb:"avoir"},file:{unit:"octets",verb:"avoir"},array:{unit:"éléments",verb:"avoir"},set:{unit:"éléments",verb:"avoir"}};function t(n){return e[n]??null}const r=n=>{const o=typeof n;switch(o){case"number":return Number.isNaN(n)?"NaN":"nombre";case"object":{if(Array.isArray(n))return"tableau";if(n===null)return"null";if(Object.getPrototypeOf(n)!==Object.prototype&&n.constructor)return n.constructor.name}}return o},i={regex:"entrée",email:"adresse e-mail",url:"URL",emoji:"emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"date et heure ISO",date:"date ISO",time:"heure ISO",duration:"durée ISO",ipv4:"adresse IPv4",ipv6:"adresse IPv6",cidrv4:"plage IPv4",cidrv6:"plage IPv6",base64:"chaîne encodée en base64",base64url:"chaîne encodée en base64url",json_string:"chaîne JSON",e164:"numéro E.164",jwt:"JWT",template_literal:"entrée"};return n=>{switch(n.code){case"invalid_type":return`Entrée invalide : ${n.expected} attendu, ${r(n.input)} reçu`;case"invalid_value":return n.values.length===1?`Entrée invalide : ${E(n.values[0])} attendu`:`Option invalide : une valeur parmi ${k(n.values,"|")} attendue`;case"too_big":{const o=n.inclusive?"<=":"<",a=t(n.origin);return a?`Trop grand : ${n.origin??"valeur"} doit ${a.verb} ${o}${n.maximum.toString()} ${a.unit??"élément(s)"}`:`Trop grand : ${n.origin??"valeur"} doit être ${o}${n.maximum.toString()}`}case"too_small":{const o=n.inclusive?">=":">",a=t(n.origin);return a?`Trop petit : ${n.origin} doit ${a.verb} ${o}${n.minimum.toString()} ${a.unit}`:`Trop petit : ${n.origin} doit être ${o}${n.minimum.toString()}`}case"invalid_format":{const o=n;return o.format==="starts_with"?`Chaîne invalide : doit commencer par "${o.prefix}"`:o.format==="ends_with"?`Chaîne invalide : doit se terminer par "${o.suffix}"`:o.format==="includes"?`Chaîne invalide : doit inclure "${o.includes}"`:o.format==="regex"?`Chaîne invalide : doit correspondre au modèle ${o.pattern}`:`${i[o.format]??n.format} invalide`}case"not_multiple_of":return`Nombre invalide : doit être un multiple de ${n.divisor}`;case"unrecognized_keys":return`Clé${n.keys.length>1?"s":""} non reconnue${n.keys.length>1?"s":""} : ${k(n.keys,", ")}`;case"invalid_key":return`Clé invalide dans ${n.origin}`;case"invalid_union":return"Entrée invalide";case"invalid_element":return`Valeur invalide dans ${n.origin}`;default:return"Entrée invalide"}}};function Cm(){return{localeError:Rm()}}const Zm=()=>{const e={string:{unit:"caractères",verb:"avoir"},file:{unit:"octets",verb:"avoir"},array:{unit:"éléments",verb:"avoir"},set:{unit:"éléments",verb:"avoir"}};function t(n){return e[n]??null}const r=n=>{const o=typeof n;switch(o){case"number":return Number.isNaN(n)?"NaN":"number";case"object":{if(Array.isArray(n))return"array";if(n===null)return"null";if(Object.getPrototypeOf(n)!==Object.prototype&&n.constructor)return n.constructor.name}}return o},i={regex:"entrée",email:"adresse courriel",url:"URL",emoji:"emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"date-heure ISO",date:"date ISO",time:"heure ISO",duration:"durée ISO",ipv4:"adresse IPv4",ipv6:"adresse IPv6",cidrv4:"plage IPv4",cidrv6:"plage IPv6",base64:"chaîne encodée en base64",base64url:"chaîne encodée en base64url",json_string:"chaîne JSON",e164:"numéro E.164",jwt:"JWT",template_literal:"entrée"};return n=>{switch(n.code){case"invalid_type":return`Entrée invalide : attendu ${n.expected}, reçu ${r(n.input)}`;case"invalid_value":return n.values.length===1?`Entrée invalide : attendu ${E(n.values[0])}`:`Option invalide : attendu l'une des valeurs suivantes ${k(n.values,"|")}`;case"too_big":{const o=n.inclusive?"≤":"<",a=t(n.origin);return a?`Trop grand : attendu que ${n.origin??"la valeur"} ait ${o}${n.maximum.toString()} ${a.unit}`:`Trop grand : attendu que ${n.origin??"la valeur"} soit ${o}${n.maximum.toString()}`}case"too_small":{const o=n.inclusive?"≥":">",a=t(n.origin);return a?`Trop petit : attendu que ${n.origin} ait ${o}${n.minimum.toString()} ${a.unit}`:`Trop petit : attendu que ${n.origin} soit ${o}${n.minimum.toString()}`}case"invalid_format":{const o=n;return o.format==="starts_with"?`Chaîne invalide : doit commencer par "${o.prefix}"`:o.format==="ends_with"?`Chaîne invalide : doit se terminer par "${o.suffix}"`:o.format==="includes"?`Chaîne invalide : doit inclure "${o.includes}"`:o.format==="regex"?`Chaîne invalide : doit correspondre au motif ${o.pattern}`:`${i[o.format]??n.format} invalide`}case"not_multiple_of":return`Nombre invalide : doit être un multiple de ${n.divisor}`;case"unrecognized_keys":return`Clé${n.keys.length>1?"s":""} non reconnue${n.keys.length>1?"s":""} : ${k(n.keys,", ")}`;case"invalid_key":return`Clé invalide dans ${n.origin}`;case"invalid_union":return"Entrée invalide";case"invalid_element":return`Valeur invalide dans ${n.origin}`;default:return"Entrée invalide"}}};function Lm(){return{localeError:Zm()}}const Mm=()=>{const e={string:{unit:"אותיות",verb:"לכלול"},file:{unit:"בייטים",verb:"לכלול"},array:{unit:"פריטים",verb:"לכלול"},set:{unit:"פריטים",verb:"לכלול"}};function t(n){return e[n]??null}const r=n=>{const o=typeof n;switch(o){case"number":return Number.isNaN(n)?"NaN":"number";case"object":{if(Array.isArray(n))return"array";if(n===null)return"null";if(Object.getPrototypeOf(n)!==Object.prototype&&n.constructor)return n.constructor.name}}return o},i={regex:"קלט",email:"כתובת אימייל",url:"כתובת רשת",emoji:"אימוג'י",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"תאריך וזמן ISO",date:"תאריך ISO",time:"זמן ISO",duration:"משך זמן ISO",ipv4:"כתובת IPv4",ipv6:"כתובת IPv6",cidrv4:"טווח IPv4",cidrv6:"טווח IPv6",base64:"מחרוזת בבסיס 64",base64url:"מחרוזת בבסיס 64 לכתובות רשת",json_string:"מחרוזת JSON",e164:"מספר E.164",jwt:"JWT",template_literal:"קלט"};return n=>{switch(n.code){case"invalid_type":return`קלט לא תקין: צריך ${n.expected}, התקבל ${r(n.input)}`;case"invalid_value":return n.values.length===1?`קלט לא תקין: צריך ${E(n.values[0])}`:`קלט לא תקין: צריך אחת מהאפשרויות  ${k(n.values,"|")}`;case"too_big":{const o=n.inclusive?"<=":"<",a=t(n.origin);return a?`גדול מדי: ${n.origin??"value"} צריך להיות ${o}${n.maximum.toString()} ${a.unit??"elements"}`:`גדול מדי: ${n.origin??"value"} צריך להיות ${o}${n.maximum.toString()}`}case"too_small":{const o=n.inclusive?">=":">",a=t(n.origin);return a?`קטן מדי: ${n.origin} צריך להיות ${o}${n.minimum.toString()} ${a.unit}`:`קטן מדי: ${n.origin} צריך להיות ${o}${n.minimum.toString()}`}case"invalid_format":{const o=n;return o.format==="starts_with"?`מחרוזת לא תקינה: חייבת להתחיל ב"${o.prefix}"`:o.format==="ends_with"?`מחרוזת לא תקינה: חייבת להסתיים ב "${o.suffix}"`:o.format==="includes"?`מחרוזת לא תקינה: חייבת לכלול "${o.includes}"`:o.format==="regex"?`מחרוזת לא תקינה: חייבת להתאים לתבנית ${o.pattern}`:`${i[o.format]??n.format} לא תקין`}case"not_multiple_of":return`מספר לא תקין: חייב להיות מכפלה של ${n.divisor}`;case"unrecognized_keys":return`מפתח${n.keys.length>1?"ות":""} לא מזוה${n.keys.length>1?"ים":"ה"}: ${k(n.keys,", ")}`;case"invalid_key":return`מפתח לא תקין ב${n.origin}`;case"invalid_union":return"קלט לא תקין";case"invalid_element":return`ערך לא תקין ב${n.origin}`;default:return"קלט לא תקין"}}};function Fm(){return{localeError:Mm()}}const Bm=()=>{const e={string:{unit:"karakter",verb:"legyen"},file:{unit:"byte",verb:"legyen"},array:{unit:"elem",verb:"legyen"},set:{unit:"elem",verb:"legyen"}};function t(n){return e[n]??null}const r=n=>{const o=typeof n;switch(o){case"number":return Number.isNaN(n)?"NaN":"szám";case"object":{if(Array.isArray(n))return"tömb";if(n===null)return"null";if(Object.getPrototypeOf(n)!==Object.prototype&&n.constructor)return n.constructor.name}}return o},i={regex:"bemenet",email:"email cím",url:"URL",emoji:"emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"ISO időbélyeg",date:"ISO dátum",time:"ISO idő",duration:"ISO időintervallum",ipv4:"IPv4 cím",ipv6:"IPv6 cím",cidrv4:"IPv4 tartomány",cidrv6:"IPv6 tartomány",base64:"base64-kódolt string",base64url:"base64url-kódolt string",json_string:"JSON string",e164:"E.164 szám",jwt:"JWT",template_literal:"bemenet"};return n=>{switch(n.code){case"invalid_type":return`Érvénytelen bemenet: a várt érték ${n.expected}, a kapott érték ${r(n.input)}`;case"invalid_value":return n.values.length===1?`Érvénytelen bemenet: a várt érték ${E(n.values[0])}`:`Érvénytelen opció: valamelyik érték várt ${k(n.values,"|")}`;case"too_big":{const o=n.inclusive?"<=":"<",a=t(n.origin);return a?`Túl nagy: ${n.origin??"érték"} mérete túl nagy ${o}${n.maximum.toString()} ${a.unit??"elem"}`:`Túl nagy: a bemeneti érték ${n.origin??"érték"} túl nagy: ${o}${n.maximum.toString()}`}case"too_small":{const o=n.inclusive?">=":">",a=t(n.origin);return a?`Túl kicsi: a bemeneti érték ${n.origin} mérete túl kicsi ${o}${n.minimum.toString()} ${a.unit}`:`Túl kicsi: a bemeneti érték ${n.origin} túl kicsi ${o}${n.minimum.toString()}`}case"invalid_format":{const o=n;return o.format==="starts_with"?`Érvénytelen string: "${o.prefix}" értékkel kell kezdődnie`:o.format==="ends_with"?`Érvénytelen string: "${o.suffix}" értékkel kell végződnie`:o.format==="includes"?`Érvénytelen string: "${o.includes}" értéket kell tartalmaznia`:o.format==="regex"?`Érvénytelen string: ${o.pattern} mintának kell megfelelnie`:`Érvénytelen ${i[o.format]??n.format}`}case"not_multiple_of":return`Érvénytelen szám: ${n.divisor} többszörösének kell lennie`;case"unrecognized_keys":return`Ismeretlen kulcs${n.keys.length>1?"s":""}: ${k(n.keys,", ")}`;case"invalid_key":return`Érvénytelen kulcs ${n.origin}`;case"invalid_union":return"Érvénytelen bemenet";case"invalid_element":return`Érvénytelen érték: ${n.origin}`;default:return"Érvénytelen bemenet"}}};function Jm(){return{localeError:Bm()}}const Wm=()=>{const e={string:{unit:"karakter",verb:"memiliki"},file:{unit:"byte",verb:"memiliki"},array:{unit:"item",verb:"memiliki"},set:{unit:"item",verb:"memiliki"}};function t(n){return e[n]??null}const r=n=>{const o=typeof n;switch(o){case"number":return Number.isNaN(n)?"NaN":"number";case"object":{if(Array.isArray(n))return"array";if(n===null)return"null";if(Object.getPrototypeOf(n)!==Object.prototype&&n.constructor)return n.constructor.name}}return o},i={regex:"input",email:"alamat email",url:"URL",emoji:"emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"tanggal dan waktu format ISO",date:"tanggal format ISO",time:"jam format ISO",duration:"durasi format ISO",ipv4:"alamat IPv4",ipv6:"alamat IPv6",cidrv4:"rentang alamat IPv4",cidrv6:"rentang alamat IPv6",base64:"string dengan enkode base64",base64url:"string dengan enkode base64url",json_string:"string JSON",e164:"angka E.164",jwt:"JWT",template_literal:"input"};return n=>{switch(n.code){case"invalid_type":return`Input tidak valid: diharapkan ${n.expected}, diterima ${r(n.input)}`;case"invalid_value":return n.values.length===1?`Input tidak valid: diharapkan ${E(n.values[0])}`:`Pilihan tidak valid: diharapkan salah satu dari ${k(n.values,"|")}`;case"too_big":{const o=n.inclusive?"<=":"<",a=t(n.origin);return a?`Terlalu besar: diharapkan ${n.origin??"value"} memiliki ${o}${n.maximum.toString()} ${a.unit??"elemen"}`:`Terlalu besar: diharapkan ${n.origin??"value"} menjadi ${o}${n.maximum.toString()}`}case"too_small":{const o=n.inclusive?">=":">",a=t(n.origin);return a?`Terlalu kecil: diharapkan ${n.origin} memiliki ${o}${n.minimum.toString()} ${a.unit}`:`Terlalu kecil: diharapkan ${n.origin} menjadi ${o}${n.minimum.toString()}`}case"invalid_format":{const o=n;return o.format==="starts_with"?`String tidak valid: harus dimulai dengan "${o.prefix}"`:o.format==="ends_with"?`String tidak valid: harus berakhir dengan "${o.suffix}"`:o.format==="includes"?`String tidak valid: harus menyertakan "${o.includes}"`:o.format==="regex"?`String tidak valid: harus sesuai pola ${o.pattern}`:`${i[o.format]??n.format} tidak valid`}case"not_multiple_of":return`Angka tidak valid: harus kelipatan dari ${n.divisor}`;case"unrecognized_keys":return`Kunci tidak dikenali ${n.keys.length>1?"s":""}: ${k(n.keys,", ")}`;case"invalid_key":return`Kunci tidak valid di ${n.origin}`;case"invalid_union":return"Input tidak valid";case"invalid_element":return`Nilai tidak valid di ${n.origin}`;default:return"Input tidak valid"}}};function qm(){return{localeError:Wm()}}const Vm=()=>{const e={string:{unit:"caratteri",verb:"avere"},file:{unit:"byte",verb:"avere"},array:{unit:"elementi",verb:"avere"},set:{unit:"elementi",verb:"avere"}};function t(n){return e[n]??null}const r=n=>{const o=typeof n;switch(o){case"number":return Number.isNaN(n)?"NaN":"numero";case"object":{if(Array.isArray(n))return"vettore";if(n===null)return"null";if(Object.getPrototypeOf(n)!==Object.prototype&&n.constructor)return n.constructor.name}}return o},i={regex:"input",email:"indirizzo email",url:"URL",emoji:"emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"data e ora ISO",date:"data ISO",time:"ora ISO",duration:"durata ISO",ipv4:"indirizzo IPv4",ipv6:"indirizzo IPv6",cidrv4:"intervallo IPv4",cidrv6:"intervallo IPv6",base64:"stringa codificata in base64",base64url:"URL codificata in base64",json_string:"stringa JSON",e164:"numero E.164",jwt:"JWT",template_literal:"input"};return n=>{switch(n.code){case"invalid_type":return`Input non valido: atteso ${n.expected}, ricevuto ${r(n.input)}`;case"invalid_value":return n.values.length===1?`Input non valido: atteso ${E(n.values[0])}`:`Opzione non valida: atteso uno tra ${k(n.values,"|")}`;case"too_big":{const o=n.inclusive?"<=":"<",a=t(n.origin);return a?`Troppo grande: ${n.origin??"valore"} deve avere ${o}${n.maximum.toString()} ${a.unit??"elementi"}`:`Troppo grande: ${n.origin??"valore"} deve essere ${o}${n.maximum.toString()}`}case"too_small":{const o=n.inclusive?">=":">",a=t(n.origin);return a?`Troppo piccolo: ${n.origin} deve avere ${o}${n.minimum.toString()} ${a.unit}`:`Troppo piccolo: ${n.origin} deve essere ${o}${n.minimum.toString()}`}case"invalid_format":{const o=n;return o.format==="starts_with"?`Stringa non valida: deve iniziare con "${o.prefix}"`:o.format==="ends_with"?`Stringa non valida: deve terminare con "${o.suffix}"`:o.format==="includes"?`Stringa non valida: deve includere "${o.includes}"`:o.format==="regex"?`Stringa non valida: deve corrispondere al pattern ${o.pattern}`:`Invalid ${i[o.format]??n.format}`}case"not_multiple_of":return`Numero non valido: deve essere un multiplo di ${n.divisor}`;case"unrecognized_keys":return`Chiav${n.keys.length>1?"i":"e"} non riconosciut${n.keys.length>1?"e":"a"}: ${k(n.keys,", ")}`;case"invalid_key":return`Chiave non valida in ${n.origin}`;case"invalid_union":return"Input non valido";case"invalid_element":return`Valore non valido in ${n.origin}`;default:return"Input non valido"}}};function Xm(){return{localeError:Vm()}}const Km=()=>{const e={string:{unit:"文字",verb:"である"},file:{unit:"バイト",verb:"である"},array:{unit:"要素",verb:"である"},set:{unit:"要素",verb:"である"}};function t(n){return e[n]??null}const r=n=>{const o=typeof n;switch(o){case"number":return Number.isNaN(n)?"NaN":"数値";case"object":{if(Array.isArray(n))return"配列";if(n===null)return"null";if(Object.getPrototypeOf(n)!==Object.prototype&&n.constructor)return n.constructor.name}}return o},i={regex:"入力値",email:"メールアドレス",url:"URL",emoji:"絵文字",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"ISO日時",date:"ISO日付",time:"ISO時刻",duration:"ISO期間",ipv4:"IPv4アドレス",ipv6:"IPv6アドレス",cidrv4:"IPv4範囲",cidrv6:"IPv6範囲",base64:"base64エンコード文字列",base64url:"base64urlエンコード文字列",json_string:"JSON文字列",e164:"E.164番号",jwt:"JWT",template_literal:"入力値"};return n=>{switch(n.code){case"invalid_type":return`無効な入力: ${n.expected}が期待されましたが、${r(n.input)}が入力されました`;case"invalid_value":return n.values.length===1?`無効な入力: ${E(n.values[0])}が期待されました`:`無効な選択: ${k(n.values,"、")}のいずれかである必要があります`;case"too_big":{const o=n.inclusive?"以下である":"より小さい",a=t(n.origin);return a?`大きすぎる値: ${n.origin??"値"}は${n.maximum.toString()}${a.unit??"要素"}${o}必要があります`:`大きすぎる値: ${n.origin??"値"}は${n.maximum.toString()}${o}必要があります`}case"too_small":{const o=n.inclusive?"以上である":"より大きい",a=t(n.origin);return a?`小さすぎる値: ${n.origin}は${n.minimum.toString()}${a.unit}${o}必要があります`:`小さすぎる値: ${n.origin}は${n.minimum.toString()}${o}必要があります`}case"invalid_format":{const o=n;return o.format==="starts_with"?`無効な文字列: "${o.prefix}"で始まる必要があります`:o.format==="ends_with"?`無効な文字列: "${o.suffix}"で終わる必要があります`:o.format==="includes"?`無効な文字列: "${o.includes}"を含む必要があります`:o.format==="regex"?`無効な文字列: パターン${o.pattern}に一致する必要があります`:`無効な${i[o.format]??n.format}`}case"not_multiple_of":return`無効な数値: ${n.divisor}の倍数である必要があります`;case"unrecognized_keys":return`認識されていないキー${n.keys.length>1?"群":""}: ${k(n.keys,"、")}`;case"invalid_key":return`${n.origin}内の無効なキー`;case"invalid_union":return"無効な入力";case"invalid_element":return`${n.origin}内の無効な値`;default:return"無効な入力"}}};function Gm(){return{localeError:Km()}}const Hm=()=>{const e={string:{unit:"តួអក្សរ",verb:"គួរមាន"},file:{unit:"បៃ",verb:"គួរមាន"},array:{unit:"ធាតុ",verb:"គួរមាន"},set:{unit:"ធាតុ",verb:"គួរមាន"}};function t(n){return e[n]??null}const r=n=>{const o=typeof n;switch(o){case"number":return Number.isNaN(n)?"មិនមែនជាលេខ (NaN)":"លេខ";case"object":{if(Array.isArray(n))return"អារេ (Array)";if(n===null)return"គ្មានតម្លៃ (null)";if(Object.getPrototypeOf(n)!==Object.prototype&&n.constructor)return n.constructor.name}}return o},i={regex:"ទិន្នន័យបញ្ចូល",email:"អាសយដ្ឋានអ៊ីមែល",url:"URL",emoji:"សញ្ញាអារម្មណ៍",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"កាលបរិច្ឆេទ និងម៉ោង ISO",date:"កាលបរិច្ឆេទ ISO",time:"ម៉ោង ISO",duration:"រយៈពេល ISO",ipv4:"អាសយដ្ឋាន IPv4",ipv6:"អាសយដ្ឋាន IPv6",cidrv4:"ដែនអាសយដ្ឋាន IPv4",cidrv6:"ដែនអាសយដ្ឋាន IPv6",base64:"ខ្សែអក្សរអ៊ិកូដ base64",base64url:"ខ្សែអក្សរអ៊ិកូដ base64url",json_string:"ខ្សែអក្សរ JSON",e164:"លេខ E.164",jwt:"JWT",template_literal:"ទិន្នន័យបញ្ចូល"};return n=>{switch(n.code){case"invalid_type":return`ទិន្នន័យបញ្ចូលមិនត្រឹមត្រូវ៖ ត្រូវការ ${n.expected} ប៉ុន្តែទទួលបាន ${r(n.input)}`;case"invalid_value":return n.values.length===1?`ទិន្នន័យបញ្ចូលមិនត្រឹមត្រូវ៖ ត្រូវការ ${E(n.values[0])}`:`ជម្រើសមិនត្រឹមត្រូវ៖ ត្រូវជាមួយក្នុងចំណោម ${k(n.values,"|")}`;case"too_big":{const o=n.inclusive?"<=":"<",a=t(n.origin);return a?`ធំពេក៖ ត្រូវការ ${n.origin??"តម្លៃ"} ${o} ${n.maximum.toString()} ${a.unit??"ធាតុ"}`:`ធំពេក៖ ត្រូវការ ${n.origin??"តម្លៃ"} ${o} ${n.maximum.toString()}`}case"too_small":{const o=n.inclusive?">=":">",a=t(n.origin);return a?`តូចពេក៖ ត្រូវការ ${n.origin} ${o} ${n.minimum.toString()} ${a.unit}`:`តូចពេក៖ ត្រូវការ ${n.origin} ${o} ${n.minimum.toString()}`}case"invalid_format":{const o=n;return o.format==="starts_with"?`ខ្សែអក្សរមិនត្រឹមត្រូវ៖ ត្រូវចាប់ផ្តើមដោយ "${o.prefix}"`:o.format==="ends_with"?`ខ្សែអក្សរមិនត្រឹមត្រូវ៖ ត្រូវបញ្ចប់ដោយ "${o.suffix}"`:o.format==="includes"?`ខ្សែអក្សរមិនត្រឹមត្រូវ៖ ត្រូវមាន "${o.includes}"`:o.format==="regex"?`ខ្សែអក្សរមិនត្រឹមត្រូវ៖ ត្រូវតែផ្គូផ្គងនឹងទម្រង់ដែលបានកំណត់ ${o.pattern}`:`មិនត្រឹមត្រូវ៖ ${i[o.format]??n.format}`}case"not_multiple_of":return`លេខមិនត្រឹមត្រូវ៖ ត្រូវតែជាពហុគុណនៃ ${n.divisor}`;case"unrecognized_keys":return`រកឃើញសោមិនស្គាល់៖ ${k(n.keys,", ")}`;case"invalid_key":return`សោមិនត្រឹមត្រូវនៅក្នុង ${n.origin}`;case"invalid_union":return"ទិន្នន័យមិនត្រឹមត្រូវ";case"invalid_element":return`ទិន្នន័យមិនត្រឹមត្រូវនៅក្នុង ${n.origin}`;default:return"ទិន្នន័យមិនត្រឹមត្រូវ"}}};function Ym(){return{localeError:Hm()}}const Qm=()=>{const e={string:{unit:"문자",verb:"to have"},file:{unit:"바이트",verb:"to have"},array:{unit:"개",verb:"to have"},set:{unit:"개",verb:"to have"}};function t(n){return e[n]??null}const r=n=>{const o=typeof n;switch(o){case"number":return Number.isNaN(n)?"NaN":"number";case"object":{if(Array.isArray(n))return"array";if(n===null)return"null";if(Object.getPrototypeOf(n)!==Object.prototype&&n.constructor)return n.constructor.name}}return o},i={regex:"입력",email:"이메일 주소",url:"URL",emoji:"이모지",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"ISO 날짜시간",date:"ISO 날짜",time:"ISO 시간",duration:"ISO 기간",ipv4:"IPv4 주소",ipv6:"IPv6 주소",cidrv4:"IPv4 범위",cidrv6:"IPv6 범위",base64:"base64 인코딩 문자열",base64url:"base64url 인코딩 문자열",json_string:"JSON 문자열",e164:"E.164 번호",jwt:"JWT",template_literal:"입력"};return n=>{switch(n.code){case"invalid_type":return`잘못된 입력: 예상 타입은 ${n.expected}, 받은 타입은 ${r(n.input)}입니다`;case"invalid_value":return n.values.length===1?`잘못된 입력: 값은 ${E(n.values[0])} 이어야 합니다`:`잘못된 옵션: ${k(n.values,"또는 ")} 중 하나여야 합니다`;case"too_big":{const o=n.inclusive?"이하":"미만",a=o==="미만"?"이어야 합니다":"여야 합니다",s=t(n.origin),u=(s==null?void 0:s.unit)??"요소";return s?`${n.origin??"값"}이 너무 큽니다: ${n.maximum.toString()}${u} ${o}${a}`:`${n.origin??"값"}이 너무 큽니다: ${n.maximum.toString()} ${o}${a}`}case"too_small":{const o=n.inclusive?"이상":"초과",a=o==="이상"?"이어야 합니다":"여야 합니다",s=t(n.origin),u=(s==null?void 0:s.unit)??"요소";return s?`${n.origin??"값"}이 너무 작습니다: ${n.minimum.toString()}${u} ${o}${a}`:`${n.origin??"값"}이 너무 작습니다: ${n.minimum.toString()} ${o}${a}`}case"invalid_format":{const o=n;return o.format==="starts_with"?`잘못된 문자열: "${o.prefix}"(으)로 시작해야 합니다`:o.format==="ends_with"?`잘못된 문자열: "${o.suffix}"(으)로 끝나야 합니다`:o.format==="includes"?`잘못된 문자열: "${o.includes}"을(를) 포함해야 합니다`:o.format==="regex"?`잘못된 문자열: 정규식 ${o.pattern} 패턴과 일치해야 합니다`:`잘못된 ${i[o.format]??n.format}`}case"not_multiple_of":return`잘못된 숫자: ${n.divisor}의 배수여야 합니다`;case"unrecognized_keys":return`인식할 수 없는 키: ${k(n.keys,", ")}`;case"invalid_key":return`잘못된 키: ${n.origin}`;case"invalid_union":return"잘못된 입력";case"invalid_element":return`잘못된 값: ${n.origin}`;default:return"잘못된 입력"}}};function eh(){return{localeError:Qm()}}const th=()=>{const e={string:{unit:"знаци",verb:"да имаат"},file:{unit:"бајти",verb:"да имаат"},array:{unit:"ставки",verb:"да имаат"},set:{unit:"ставки",verb:"да имаат"}};function t(n){return e[n]??null}const r=n=>{const o=typeof n;switch(o){case"number":return Number.isNaN(n)?"NaN":"број";case"object":{if(Array.isArray(n))return"низа";if(n===null)return"null";if(Object.getPrototypeOf(n)!==Object.prototype&&n.constructor)return n.constructor.name}}return o},i={regex:"внес",email:"адреса на е-пошта",url:"URL",emoji:"емоџи",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"ISO датум и време",date:"ISO датум",time:"ISO време",duration:"ISO времетраење",ipv4:"IPv4 адреса",ipv6:"IPv6 адреса",cidrv4:"IPv4 опсег",cidrv6:"IPv6 опсег",base64:"base64-енкодирана низа",base64url:"base64url-енкодирана низа",json_string:"JSON низа",e164:"E.164 број",jwt:"JWT",template_literal:"внес"};return n=>{switch(n.code){case"invalid_type":return`Грешен внес: се очекува ${n.expected}, примено ${r(n.input)}`;case"invalid_value":return n.values.length===1?`Invalid input: expected ${E(n.values[0])}`:`Грешана опција: се очекува една ${k(n.values,"|")}`;case"too_big":{const o=n.inclusive?"<=":"<",a=t(n.origin);return a?`Премногу голем: се очекува ${n.origin??"вредноста"} да има ${o}${n.maximum.toString()} ${a.unit??"елементи"}`:`Премногу голем: се очекува ${n.origin??"вредноста"} да биде ${o}${n.maximum.toString()}`}case"too_small":{const o=n.inclusive?">=":">",a=t(n.origin);return a?`Премногу мал: се очекува ${n.origin} да има ${o}${n.minimum.toString()} ${a.unit}`:`Премногу мал: се очекува ${n.origin} да биде ${o}${n.minimum.toString()}`}case"invalid_format":{const o=n;return o.format==="starts_with"?`Неважечка низа: мора да започнува со "${o.prefix}"`:o.format==="ends_with"?`Неважечка низа: мора да завршува со "${o.suffix}"`:o.format==="includes"?`Неважечка низа: мора да вклучува "${o.includes}"`:o.format==="regex"?`Неважечка низа: мора да одгоара на патернот ${o.pattern}`:`Invalid ${i[o.format]??n.format}`}case"not_multiple_of":return`Грешен број: мора да биде делив со ${n.divisor}`;case"unrecognized_keys":return`${n.keys.length>1?"Непрепознаени клучеви":"Непрепознаен клуч"}: ${k(n.keys,", ")}`;case"invalid_key":return`Грешен клуч во ${n.origin}`;case"invalid_union":return"Грешен внес";case"invalid_element":return`Грешна вредност во ${n.origin}`;default:return"Грешен внес"}}};function nh(){return{localeError:th()}}const rh=()=>{const e={string:{unit:"aksara",verb:"mempunyai"},file:{unit:"bait",verb:"mempunyai"},array:{unit:"elemen",verb:"mempunyai"},set:{unit:"elemen",verb:"mempunyai"}};function t(n){return e[n]??null}const r=n=>{const o=typeof n;switch(o){case"number":return Number.isNaN(n)?"NaN":"nombor";case"object":{if(Array.isArray(n))return"array";if(n===null)return"null";if(Object.getPrototypeOf(n)!==Object.prototype&&n.constructor)return n.constructor.name}}return o},i={regex:"input",email:"alamat e-mel",url:"URL",emoji:"emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"tarikh masa ISO",date:"tarikh ISO",time:"masa ISO",duration:"tempoh ISO",ipv4:"alamat IPv4",ipv6:"alamat IPv6",cidrv4:"julat IPv4",cidrv6:"julat IPv6",base64:"string dikodkan base64",base64url:"string dikodkan base64url",json_string:"string JSON",e164:"nombor E.164",jwt:"JWT",template_literal:"input"};return n=>{switch(n.code){case"invalid_type":return`Input tidak sah: dijangka ${n.expected}, diterima ${r(n.input)}`;case"invalid_value":return n.values.length===1?`Input tidak sah: dijangka ${E(n.values[0])}`:`Pilihan tidak sah: dijangka salah satu daripada ${k(n.values,"|")}`;case"too_big":{const o=n.inclusive?"<=":"<",a=t(n.origin);return a?`Terlalu besar: dijangka ${n.origin??"nilai"} ${a.verb} ${o}${n.maximum.toString()} ${a.unit??"elemen"}`:`Terlalu besar: dijangka ${n.origin??"nilai"} adalah ${o}${n.maximum.toString()}`}case"too_small":{const o=n.inclusive?">=":">",a=t(n.origin);return a?`Terlalu kecil: dijangka ${n.origin} ${a.verb} ${o}${n.minimum.toString()} ${a.unit}`:`Terlalu kecil: dijangka ${n.origin} adalah ${o}${n.minimum.toString()}`}case"invalid_format":{const o=n;return o.format==="starts_with"?`String tidak sah: mesti bermula dengan "${o.prefix}"`:o.format==="ends_with"?`String tidak sah: mesti berakhir dengan "${o.suffix}"`:o.format==="includes"?`String tidak sah: mesti mengandungi "${o.includes}"`:o.format==="regex"?`String tidak sah: mesti sepadan dengan corak ${o.pattern}`:`${i[o.format]??n.format} tidak sah`}case"not_multiple_of":return`Nombor tidak sah: perlu gandaan ${n.divisor}`;case"unrecognized_keys":return`Kunci tidak dikenali: ${k(n.keys,", ")}`;case"invalid_key":return`Kunci tidak sah dalam ${n.origin}`;case"invalid_union":return"Input tidak sah";case"invalid_element":return`Nilai tidak sah dalam ${n.origin}`;default:return"Input tidak sah"}}};function ih(){return{localeError:rh()}}const oh=()=>{const e={string:{unit:"tekens"},file:{unit:"bytes"},array:{unit:"elementen"},set:{unit:"elementen"}};function t(n){return e[n]??null}const r=n=>{const o=typeof n;switch(o){case"number":return Number.isNaN(n)?"NaN":"getal";case"object":{if(Array.isArray(n))return"array";if(n===null)return"null";if(Object.getPrototypeOf(n)!==Object.prototype&&n.constructor)return n.constructor.name}}return o},i={regex:"invoer",email:"emailadres",url:"URL",emoji:"emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"ISO datum en tijd",date:"ISO datum",time:"ISO tijd",duration:"ISO duur",ipv4:"IPv4-adres",ipv6:"IPv6-adres",cidrv4:"IPv4-bereik",cidrv6:"IPv6-bereik",base64:"base64-gecodeerde tekst",base64url:"base64 URL-gecodeerde tekst",json_string:"JSON string",e164:"E.164-nummer",jwt:"JWT",template_literal:"invoer"};return n=>{switch(n.code){case"invalid_type":return`Ongeldige invoer: verwacht ${n.expected}, ontving ${r(n.input)}`;case"invalid_value":return n.values.length===1?`Ongeldige invoer: verwacht ${E(n.values[0])}`:`Ongeldige optie: verwacht één van ${k(n.values,"|")}`;case"too_big":{const o=n.inclusive?"<=":"<",a=t(n.origin);return a?`Te lang: verwacht dat ${n.origin??"waarde"} ${o}${n.maximum.toString()} ${a.unit??"elementen"} bevat`:`Te lang: verwacht dat ${n.origin??"waarde"} ${o}${n.maximum.toString()} is`}case"too_small":{const o=n.inclusive?">=":">",a=t(n.origin);return a?`Te kort: verwacht dat ${n.origin} ${o}${n.minimum.toString()} ${a.unit} bevat`:`Te kort: verwacht dat ${n.origin} ${o}${n.minimum.toString()} is`}case"invalid_format":{const o=n;return o.format==="starts_with"?`Ongeldige tekst: moet met "${o.prefix}" beginnen`:o.format==="ends_with"?`Ongeldige tekst: moet op "${o.suffix}" eindigen`:o.format==="includes"?`Ongeldige tekst: moet "${o.includes}" bevatten`:o.format==="regex"?`Ongeldige tekst: moet overeenkomen met patroon ${o.pattern}`:`Ongeldig: ${i[o.format]??n.format}`}case"not_multiple_of":return`Ongeldig getal: moet een veelvoud van ${n.divisor} zijn`;case"unrecognized_keys":return`Onbekende key${n.keys.length>1?"s":""}: ${k(n.keys,", ")}`;case"invalid_key":return`Ongeldige key in ${n.origin}`;case"invalid_union":return"Ongeldige invoer";case"invalid_element":return`Ongeldige waarde in ${n.origin}`;default:return"Ongeldige invoer"}}};function ah(){return{localeError:oh()}}const sh=()=>{const e={string:{unit:"tegn",verb:"å ha"},file:{unit:"bytes",verb:"å ha"},array:{unit:"elementer",verb:"å inneholde"},set:{unit:"elementer",verb:"å inneholde"}};function t(n){return e[n]??null}const r=n=>{const o=typeof n;switch(o){case"number":return Number.isNaN(n)?"NaN":"tall";case"object":{if(Array.isArray(n))return"liste";if(n===null)return"null";if(Object.getPrototypeOf(n)!==Object.prototype&&n.constructor)return n.constructor.name}}return o},i={regex:"input",email:"e-postadresse",url:"URL",emoji:"emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"ISO dato- og klokkeslett",date:"ISO-dato",time:"ISO-klokkeslett",duration:"ISO-varighet",ipv4:"IPv4-område",ipv6:"IPv6-område",cidrv4:"IPv4-spekter",cidrv6:"IPv6-spekter",base64:"base64-enkodet streng",base64url:"base64url-enkodet streng",json_string:"JSON-streng",e164:"E.164-nummer",jwt:"JWT",template_literal:"input"};return n=>{switch(n.code){case"invalid_type":return`Ugyldig input: forventet ${n.expected}, fikk ${r(n.input)}`;case"invalid_value":return n.values.length===1?`Ugyldig verdi: forventet ${E(n.values[0])}`:`Ugyldig valg: forventet en av ${k(n.values,"|")}`;case"too_big":{const o=n.inclusive?"<=":"<",a=t(n.origin);return a?`For stor(t): forventet ${n.origin??"value"} til å ha ${o}${n.maximum.toString()} ${a.unit??"elementer"}`:`For stor(t): forventet ${n.origin??"value"} til å ha ${o}${n.maximum.toString()}`}case"too_small":{const o=n.inclusive?">=":">",a=t(n.origin);return a?`For lite(n): forventet ${n.origin} til å ha ${o}${n.minimum.toString()} ${a.unit}`:`For lite(n): forventet ${n.origin} til å ha ${o}${n.minimum.toString()}`}case"invalid_format":{const o=n;return o.format==="starts_with"?`Ugyldig streng: må starte med "${o.prefix}"`:o.format==="ends_with"?`Ugyldig streng: må ende med "${o.suffix}"`:o.format==="includes"?`Ugyldig streng: må inneholde "${o.includes}"`:o.format==="regex"?`Ugyldig streng: må matche mønsteret ${o.pattern}`:`Ugyldig ${i[o.format]??n.format}`}case"not_multiple_of":return`Ugyldig tall: må være et multiplum av ${n.divisor}`;case"unrecognized_keys":return`${n.keys.length>1?"Ukjente nøkler":"Ukjent nøkkel"}: ${k(n.keys,", ")}`;case"invalid_key":return`Ugyldig nøkkel i ${n.origin}`;case"invalid_union":return"Ugyldig input";case"invalid_element":return`Ugyldig verdi i ${n.origin}`;default:return"Ugyldig input"}}};function uh(){return{localeError:sh()}}const ch=()=>{const e={string:{unit:"harf",verb:"olmalıdır"},file:{unit:"bayt",verb:"olmalıdır"},array:{unit:"unsur",verb:"olmalıdır"},set:{unit:"unsur",verb:"olmalıdır"}};function t(n){return e[n]??null}const r=n=>{const o=typeof n;switch(o){case"number":return Number.isNaN(n)?"NaN":"numara";case"object":{if(Array.isArray(n))return"saf";if(n===null)return"gayb";if(Object.getPrototypeOf(n)!==Object.prototype&&n.constructor)return n.constructor.name}}return o},i={regex:"giren",email:"epostagâh",url:"URL",emoji:"emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"ISO hengâmı",date:"ISO tarihi",time:"ISO zamanı",duration:"ISO müddeti",ipv4:"IPv4 nişânı",ipv6:"IPv6 nişânı",cidrv4:"IPv4 menzili",cidrv6:"IPv6 menzili",base64:"base64-şifreli metin",base64url:"base64url-şifreli metin",json_string:"JSON metin",e164:"E.164 sayısı",jwt:"JWT",template_literal:"giren"};return n=>{switch(n.code){case"invalid_type":return`Fâsit giren: umulan ${n.expected}, alınan ${r(n.input)}`;case"invalid_value":return n.values.length===1?`Fâsit giren: umulan ${E(n.values[0])}`:`Fâsit tercih: mûteberler ${k(n.values,"|")}`;case"too_big":{const o=n.inclusive?"<=":"<",a=t(n.origin);return a?`Fazla büyük: ${n.origin??"value"}, ${o}${n.maximum.toString()} ${a.unit??"elements"} sahip olmalıydı.`:`Fazla büyük: ${n.origin??"value"}, ${o}${n.maximum.toString()} olmalıydı.`}case"too_small":{const o=n.inclusive?">=":">",a=t(n.origin);return a?`Fazla küçük: ${n.origin}, ${o}${n.minimum.toString()} ${a.unit} sahip olmalıydı.`:`Fazla küçük: ${n.origin}, ${o}${n.minimum.toString()} olmalıydı.`}case"invalid_format":{const o=n;return o.format==="starts_with"?`Fâsit metin: "${o.prefix}" ile başlamalı.`:o.format==="ends_with"?`Fâsit metin: "${o.suffix}" ile bitmeli.`:o.format==="includes"?`Fâsit metin: "${o.includes}" ihtivâ etmeli.`:o.format==="regex"?`Fâsit metin: ${o.pattern} nakşına uymalı.`:`Fâsit ${i[o.format]??n.format}`}case"not_multiple_of":return`Fâsit sayı: ${n.divisor} katı olmalıydı.`;case"unrecognized_keys":return`Tanınmayan anahtar ${n.keys.length>1?"s":""}: ${k(n.keys,", ")}`;case"invalid_key":return`${n.origin} için tanınmayan anahtar var.`;case"invalid_union":return"Giren tanınamadı.";case"invalid_element":return`${n.origin} için tanınmayan kıymet var.`;default:return"Kıymet tanınamadı."}}};function lh(){return{localeError:ch()}}const dh=()=>{const e={string:{unit:"توکي",verb:"ولري"},file:{unit:"بایټس",verb:"ولري"},array:{unit:"توکي",verb:"ولري"},set:{unit:"توکي",verb:"ولري"}};function t(n){return e[n]??null}const r=n=>{const o=typeof n;switch(o){case"number":return Number.isNaN(n)?"NaN":"عدد";case"object":{if(Array.isArray(n))return"ارې";if(n===null)return"null";if(Object.getPrototypeOf(n)!==Object.prototype&&n.constructor)return n.constructor.name}}return o},i={regex:"ورودي",email:"بریښنالیک",url:"یو آر ال",emoji:"ایموجي",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"نیټه او وخت",date:"نېټه",time:"وخت",duration:"موده",ipv4:"د IPv4 پته",ipv6:"د IPv6 پته",cidrv4:"د IPv4 ساحه",cidrv6:"د IPv6 ساحه",base64:"base64-encoded متن",base64url:"base64url-encoded متن",json_string:"JSON متن",e164:"د E.164 شمېره",jwt:"JWT",template_literal:"ورودي"};return n=>{switch(n.code){case"invalid_type":return`ناسم ورودي: باید ${n.expected} وای, مګر ${r(n.input)} ترلاسه شو`;case"invalid_value":return n.values.length===1?`ناسم ورودي: باید ${E(n.values[0])} وای`:`ناسم انتخاب: باید یو له ${k(n.values,"|")} څخه وای`;case"too_big":{const o=n.inclusive?"<=":"<",a=t(n.origin);return a?`ډیر لوی: ${n.origin??"ارزښت"} باید ${o}${n.maximum.toString()} ${a.unit??"عنصرونه"} ولري`:`ډیر لوی: ${n.origin??"ارزښت"} باید ${o}${n.maximum.toString()} وي`}case"too_small":{const o=n.inclusive?">=":">",a=t(n.origin);return a?`ډیر کوچنی: ${n.origin} باید ${o}${n.minimum.toString()} ${a.unit} ولري`:`ډیر کوچنی: ${n.origin} باید ${o}${n.minimum.toString()} وي`}case"invalid_format":{const o=n;return o.format==="starts_with"?`ناسم متن: باید د "${o.prefix}" سره پیل شي`:o.format==="ends_with"?`ناسم متن: باید د "${o.suffix}" سره پای ته ورسيږي`:o.format==="includes"?`ناسم متن: باید "${o.includes}" ولري`:o.format==="regex"?`ناسم متن: باید د ${o.pattern} سره مطابقت ولري`:`${i[o.format]??n.format} ناسم دی`}case"not_multiple_of":return`ناسم عدد: باید د ${n.divisor} مضرب وي`;case"unrecognized_keys":return`ناسم ${n.keys.length>1?"کلیډونه":"کلیډ"}: ${k(n.keys,", ")}`;case"invalid_key":return`ناسم کلیډ په ${n.origin} کې`;case"invalid_union":return"ناسمه ورودي";case"invalid_element":return`ناسم عنصر په ${n.origin} کې`;default:return"ناسمه ورودي"}}};function fh(){return{localeError:dh()}}const mh=()=>{const e={string:{unit:"znaków",verb:"mieć"},file:{unit:"bajtów",verb:"mieć"},array:{unit:"elementów",verb:"mieć"},set:{unit:"elementów",verb:"mieć"}};function t(n){return e[n]??null}const r=n=>{const o=typeof n;switch(o){case"number":return Number.isNaN(n)?"NaN":"liczba";case"object":{if(Array.isArray(n))return"tablica";if(n===null)return"null";if(Object.getPrototypeOf(n)!==Object.prototype&&n.constructor)return n.constructor.name}}return o},i={regex:"wyrażenie",email:"adres email",url:"URL",emoji:"emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"data i godzina w formacie ISO",date:"data w formacie ISO",time:"godzina w formacie ISO",duration:"czas trwania ISO",ipv4:"adres IPv4",ipv6:"adres IPv6",cidrv4:"zakres IPv4",cidrv6:"zakres IPv6",base64:"ciąg znaków zakodowany w formacie base64",base64url:"ciąg znaków zakodowany w formacie base64url",json_string:"ciąg znaków w formacie JSON",e164:"liczba E.164",jwt:"JWT",template_literal:"wejście"};return n=>{switch(n.code){case"invalid_type":return`Nieprawidłowe dane wejściowe: oczekiwano ${n.expected}, otrzymano ${r(n.input)}`;case"invalid_value":return n.values.length===1?`Nieprawidłowe dane wejściowe: oczekiwano ${E(n.values[0])}`:`Nieprawidłowa opcja: oczekiwano jednej z wartości ${k(n.values,"|")}`;case"too_big":{const o=n.inclusive?"<=":"<",a=t(n.origin);return a?`Za duża wartość: oczekiwano, że ${n.origin??"wartość"} będzie mieć ${o}${n.maximum.toString()} ${a.unit??"elementów"}`:`Zbyt duż(y/a/e): oczekiwano, że ${n.origin??"wartość"} będzie wynosić ${o}${n.maximum.toString()}`}case"too_small":{const o=n.inclusive?">=":">",a=t(n.origin);return a?`Za mała wartość: oczekiwano, że ${n.origin??"wartość"} będzie mieć ${o}${n.minimum.toString()} ${a.unit??"elementów"}`:`Zbyt mał(y/a/e): oczekiwano, że ${n.origin??"wartość"} będzie wynosić ${o}${n.minimum.toString()}`}case"invalid_format":{const o=n;return o.format==="starts_with"?`Nieprawidłowy ciąg znaków: musi zaczynać się od "${o.prefix}"`:o.format==="ends_with"?`Nieprawidłowy ciąg znaków: musi kończyć się na "${o.suffix}"`:o.format==="includes"?`Nieprawidłowy ciąg znaków: musi zawierać "${o.includes}"`:o.format==="regex"?`Nieprawidłowy ciąg znaków: musi odpowiadać wzorcowi ${o.pattern}`:`Nieprawidłow(y/a/e) ${i[o.format]??n.format}`}case"not_multiple_of":return`Nieprawidłowa liczba: musi być wielokrotnością ${n.divisor}`;case"unrecognized_keys":return`Nierozpoznane klucze${n.keys.length>1?"s":""}: ${k(n.keys,", ")}`;case"invalid_key":return`Nieprawidłowy klucz w ${n.origin}`;case"invalid_union":return"Nieprawidłowe dane wejściowe";case"invalid_element":return`Nieprawidłowa wartość w ${n.origin}`;default:return"Nieprawidłowe dane wejściowe"}}};function hh(){return{localeError:mh()}}const ph=()=>{const e={string:{unit:"caracteres",verb:"ter"},file:{unit:"bytes",verb:"ter"},array:{unit:"itens",verb:"ter"},set:{unit:"itens",verb:"ter"}};function t(n){return e[n]??null}const r=n=>{const o=typeof n;switch(o){case"number":return Number.isNaN(n)?"NaN":"número";case"object":{if(Array.isArray(n))return"array";if(n===null)return"nulo";if(Object.getPrototypeOf(n)!==Object.prototype&&n.constructor)return n.constructor.name}}return o},i={regex:"padrão",email:"endereço de e-mail",url:"URL",emoji:"emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"data e hora ISO",date:"data ISO",time:"hora ISO",duration:"duração ISO",ipv4:"endereço IPv4",ipv6:"endereço IPv6",cidrv4:"faixa de IPv4",cidrv6:"faixa de IPv6",base64:"texto codificado em base64",base64url:"URL codificada em base64",json_string:"texto JSON",e164:"número E.164",jwt:"JWT",template_literal:"entrada"};return n=>{switch(n.code){case"invalid_type":return`Tipo inválido: esperado ${n.expected}, recebido ${r(n.input)}`;case"invalid_value":return n.values.length===1?`Entrada inválida: esperado ${E(n.values[0])}`:`Opção inválida: esperada uma das ${k(n.values,"|")}`;case"too_big":{const o=n.inclusive?"<=":"<",a=t(n.origin);return a?`Muito grande: esperado que ${n.origin??"valor"} tivesse ${o}${n.maximum.toString()} ${a.unit??"elementos"}`:`Muito grande: esperado que ${n.origin??"valor"} fosse ${o}${n.maximum.toString()}`}case"too_small":{const o=n.inclusive?">=":">",a=t(n.origin);return a?`Muito pequeno: esperado que ${n.origin} tivesse ${o}${n.minimum.toString()} ${a.unit}`:`Muito pequeno: esperado que ${n.origin} fosse ${o}${n.minimum.toString()}`}case"invalid_format":{const o=n;return o.format==="starts_with"?`Texto inválido: deve começar com "${o.prefix}"`:o.format==="ends_with"?`Texto inválido: deve terminar com "${o.suffix}"`:o.format==="includes"?`Texto inválido: deve incluir "${o.includes}"`:o.format==="regex"?`Texto inválido: deve corresponder ao padrão ${o.pattern}`:`${i[o.format]??n.format} inválido`}case"not_multiple_of":return`Número inválido: deve ser múltiplo de ${n.divisor}`;case"unrecognized_keys":return`Chave${n.keys.length>1?"s":""} desconhecida${n.keys.length>1?"s":""}: ${k(n.keys,", ")}`;case"invalid_key":return`Chave inválida em ${n.origin}`;case"invalid_union":return"Entrada inválida";case"invalid_element":return`Valor inválido em ${n.origin}`;default:return"Campo inválido"}}};function gh(){return{localeError:ph()}}function Xo(e,t,r,i){const n=Math.abs(e),o=n%10,a=n%100;return a>=11&&a<=19?i:o===1?t:o>=2&&o<=4?r:i}const vh=()=>{const e={string:{unit:{one:"символ",few:"символа",many:"символов"},verb:"иметь"},file:{unit:{one:"байт",few:"байта",many:"байт"},verb:"иметь"},array:{unit:{one:"элемент",few:"элемента",many:"элементов"},verb:"иметь"},set:{unit:{one:"элемент",few:"элемента",many:"элементов"},verb:"иметь"}};function t(n){return e[n]??null}const r=n=>{const o=typeof n;switch(o){case"number":return Number.isNaN(n)?"NaN":"число";case"object":{if(Array.isArray(n))return"массив";if(n===null)return"null";if(Object.getPrototypeOf(n)!==Object.prototype&&n.constructor)return n.constructor.name}}return o},i={regex:"ввод",email:"email адрес",url:"URL",emoji:"эмодзи",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"ISO дата и время",date:"ISO дата",time:"ISO время",duration:"ISO длительность",ipv4:"IPv4 адрес",ipv6:"IPv6 адрес",cidrv4:"IPv4 диапазон",cidrv6:"IPv6 диапазон",base64:"строка в формате base64",base64url:"строка в формате base64url",json_string:"JSON строка",e164:"номер E.164",jwt:"JWT",template_literal:"ввод"};return n=>{switch(n.code){case"invalid_type":return`Неверный ввод: ожидалось ${n.expected}, получено ${r(n.input)}`;case"invalid_value":return n.values.length===1?`Неверный ввод: ожидалось ${E(n.values[0])}`:`Неверный вариант: ожидалось одно из ${k(n.values,"|")}`;case"too_big":{const o=n.inclusive?"<=":"<",a=t(n.origin);if(a){const s=Number(n.maximum),u=Xo(s,a.unit.one,a.unit.few,a.unit.many);return`Слишком большое значение: ожидалось, что ${n.origin??"значение"} будет иметь ${o}${n.maximum.toString()} ${u}`}return`Слишком большое значение: ожидалось, что ${n.origin??"значение"} будет ${o}${n.maximum.toString()}`}case"too_small":{const o=n.inclusive?">=":">",a=t(n.origin);if(a){const s=Number(n.minimum),u=Xo(s,a.unit.one,a.unit.few,a.unit.many);return`Слишком маленькое значение: ожидалось, что ${n.origin} будет иметь ${o}${n.minimum.toString()} ${u}`}return`Слишком маленькое значение: ожидалось, что ${n.origin} будет ${o}${n.minimum.toString()}`}case"invalid_format":{const o=n;return o.format==="starts_with"?`Неверная строка: должна начинаться с "${o.prefix}"`:o.format==="ends_with"?`Неверная строка: должна заканчиваться на "${o.suffix}"`:o.format==="includes"?`Неверная строка: должна содержать "${o.includes}"`:o.format==="regex"?`Неверная строка: должна соответствовать шаблону ${o.pattern}`:`Неверный ${i[o.format]??n.format}`}case"not_multiple_of":return`Неверное число: должно быть кратным ${n.divisor}`;case"unrecognized_keys":return`Нераспознанн${n.keys.length>1?"ые":"ый"} ключ${n.keys.length>1?"и":""}: ${k(n.keys,", ")}`;case"invalid_key":return`Неверный ключ в ${n.origin}`;case"invalid_union":return"Неверные входные данные";case"invalid_element":return`Неверное значение в ${n.origin}`;default:return"Неверные входные данные"}}};function _h(){return{localeError:vh()}}const bh=()=>{const e={string:{unit:"znakov",verb:"imeti"},file:{unit:"bajtov",verb:"imeti"},array:{unit:"elementov",verb:"imeti"},set:{unit:"elementov",verb:"imeti"}};function t(n){return e[n]??null}const r=n=>{const o=typeof n;switch(o){case"number":return Number.isNaN(n)?"NaN":"število";case"object":{if(Array.isArray(n))return"tabela";if(n===null)return"null";if(Object.getPrototypeOf(n)!==Object.prototype&&n.constructor)return n.constructor.name}}return o},i={regex:"vnos",email:"e-poštni naslov",url:"URL",emoji:"emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"ISO datum in čas",date:"ISO datum",time:"ISO čas",duration:"ISO trajanje",ipv4:"IPv4 naslov",ipv6:"IPv6 naslov",cidrv4:"obseg IPv4",cidrv6:"obseg IPv6",base64:"base64 kodiran niz",base64url:"base64url kodiran niz",json_string:"JSON niz",e164:"E.164 številka",jwt:"JWT",template_literal:"vnos"};return n=>{switch(n.code){case"invalid_type":return`Neveljaven vnos: pričakovano ${n.expected}, prejeto ${r(n.input)}`;case"invalid_value":return n.values.length===1?`Neveljaven vnos: pričakovano ${E(n.values[0])}`:`Neveljavna možnost: pričakovano eno izmed ${k(n.values,"|")}`;case"too_big":{const o=n.inclusive?"<=":"<",a=t(n.origin);return a?`Preveliko: pričakovano, da bo ${n.origin??"vrednost"} imelo ${o}${n.maximum.toString()} ${a.unit??"elementov"}`:`Preveliko: pričakovano, da bo ${n.origin??"vrednost"} ${o}${n.maximum.toString()}`}case"too_small":{const o=n.inclusive?">=":">",a=t(n.origin);return a?`Premajhno: pričakovano, da bo ${n.origin} imelo ${o}${n.minimum.toString()} ${a.unit}`:`Premajhno: pričakovano, da bo ${n.origin} ${o}${n.minimum.toString()}`}case"invalid_format":{const o=n;return o.format==="starts_with"?`Neveljaven niz: mora se začeti z "${o.prefix}"`:o.format==="ends_with"?`Neveljaven niz: mora se končati z "${o.suffix}"`:o.format==="includes"?`Neveljaven niz: mora vsebovati "${o.includes}"`:o.format==="regex"?`Neveljaven niz: mora ustrezati vzorcu ${o.pattern}`:`Neveljaven ${i[o.format]??n.format}`}case"not_multiple_of":return`Neveljavno število: mora biti večkratnik ${n.divisor}`;case"unrecognized_keys":return`Neprepoznan${n.keys.length>1?"i ključi":" ključ"}: ${k(n.keys,", ")}`;case"invalid_key":return`Neveljaven ključ v ${n.origin}`;case"invalid_union":return"Neveljaven vnos";case"invalid_element":return`Neveljavna vrednost v ${n.origin}`;default:return"Neveljaven vnos"}}};function yh(){return{localeError:bh()}}const $h=()=>{const e={string:{unit:"tecken",verb:"att ha"},file:{unit:"bytes",verb:"att ha"},array:{unit:"objekt",verb:"att innehålla"},set:{unit:"objekt",verb:"att innehålla"}};function t(n){return e[n]??null}const r=n=>{const o=typeof n;switch(o){case"number":return Number.isNaN(n)?"NaN":"antal";case"object":{if(Array.isArray(n))return"lista";if(n===null)return"null";if(Object.getPrototypeOf(n)!==Object.prototype&&n.constructor)return n.constructor.name}}return o},i={regex:"reguljärt uttryck",email:"e-postadress",url:"URL",emoji:"emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"ISO-datum och tid",date:"ISO-datum",time:"ISO-tid",duration:"ISO-varaktighet",ipv4:"IPv4-intervall",ipv6:"IPv6-intervall",cidrv4:"IPv4-spektrum",cidrv6:"IPv6-spektrum",base64:"base64-kodad sträng",base64url:"base64url-kodad sträng",json_string:"JSON-sträng",e164:"E.164-nummer",jwt:"JWT",template_literal:"mall-literal"};return n=>{switch(n.code){case"invalid_type":return`Ogiltig inmatning: förväntat ${n.expected}, fick ${r(n.input)}`;case"invalid_value":return n.values.length===1?`Ogiltig inmatning: förväntat ${E(n.values[0])}`:`Ogiltigt val: förväntade en av ${k(n.values,"|")}`;case"too_big":{const o=n.inclusive?"<=":"<",a=t(n.origin);return a?`För stor(t): förväntade ${n.origin??"värdet"} att ha ${o}${n.maximum.toString()} ${a.unit??"element"}`:`För stor(t): förväntat ${n.origin??"värdet"} att ha ${o}${n.maximum.toString()}`}case"too_small":{const o=n.inclusive?">=":">",a=t(n.origin);return a?`För lite(t): förväntade ${n.origin??"värdet"} att ha ${o}${n.minimum.toString()} ${a.unit}`:`För lite(t): förväntade ${n.origin??"värdet"} att ha ${o}${n.minimum.toString()}`}case"invalid_format":{const o=n;return o.format==="starts_with"?`Ogiltig sträng: måste börja med "${o.prefix}"`:o.format==="ends_with"?`Ogiltig sträng: måste sluta med "${o.suffix}"`:o.format==="includes"?`Ogiltig sträng: måste innehålla "${o.includes}"`:o.format==="regex"?`Ogiltig sträng: måste matcha mönstret "${o.pattern}"`:`Ogiltig(t) ${i[o.format]??n.format}`}case"not_multiple_of":return`Ogiltigt tal: måste vara en multipel av ${n.divisor}`;case"unrecognized_keys":return`${n.keys.length>1?"Okända nycklar":"Okänd nyckel"}: ${k(n.keys,", ")}`;case"invalid_key":return`Ogiltig nyckel i ${n.origin??"värdet"}`;case"invalid_union":return"Ogiltig input";case"invalid_element":return`Ogiltigt värde i ${n.origin??"värdet"}`;default:return"Ogiltig input"}}};function wh(){return{localeError:$h()}}const kh=()=>{const e={string:{unit:"எழுத்துக்கள்",verb:"கொண்டிருக்க வேண்டும்"},file:{unit:"பைட்டுகள்",verb:"கொண்டிருக்க வேண்டும்"},array:{unit:"உறுப்புகள்",verb:"கொண்டிருக்க வேண்டும்"},set:{unit:"உறுப்புகள்",verb:"கொண்டிருக்க வேண்டும்"}};function t(n){return e[n]??null}const r=n=>{const o=typeof n;switch(o){case"number":return Number.isNaN(n)?"எண் அல்லாதது":"எண்";case"object":{if(Array.isArray(n))return"அணி";if(n===null)return"வெறுமை";if(Object.getPrototypeOf(n)!==Object.prototype&&n.constructor)return n.constructor.name}}return o},i={regex:"உள்ளீடு",email:"மின்னஞ்சல் முகவரி",url:"URL",emoji:"emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"ISO தேதி நேரம்",date:"ISO தேதி",time:"ISO நேரம்",duration:"ISO கால அளவு",ipv4:"IPv4 முகவரி",ipv6:"IPv6 முகவரி",cidrv4:"IPv4 வரம்பு",cidrv6:"IPv6 வரம்பு",base64:"base64-encoded சரம்",base64url:"base64url-encoded சரம்",json_string:"JSON சரம்",e164:"E.164 எண்",jwt:"JWT",template_literal:"input"};return n=>{switch(n.code){case"invalid_type":return`தவறான உள்ளீடு: எதிர்பார்க்கப்பட்டது ${n.expected}, பெறப்பட்டது ${r(n.input)}`;case"invalid_value":return n.values.length===1?`தவறான உள்ளீடு: எதிர்பார்க்கப்பட்டது ${E(n.values[0])}`:`தவறான விருப்பம்: எதிர்பார்க்கப்பட்டது ${k(n.values,"|")} இல் ஒன்று`;case"too_big":{const o=n.inclusive?"<=":"<",a=t(n.origin);return a?`மிக பெரியது: எதிர்பார்க்கப்பட்டது ${n.origin??"மதிப்பு"} ${o}${n.maximum.toString()} ${a.unit??"உறுப்புகள்"} ஆக இருக்க வேண்டும்`:`மிக பெரியது: எதிர்பார்க்கப்பட்டது ${n.origin??"மதிப்பு"} ${o}${n.maximum.toString()} ஆக இருக்க வேண்டும்`}case"too_small":{const o=n.inclusive?">=":">",a=t(n.origin);return a?`மிகச் சிறியது: எதிர்பார்க்கப்பட்டது ${n.origin} ${o}${n.minimum.toString()} ${a.unit} ஆக இருக்க வேண்டும்`:`மிகச் சிறியது: எதிர்பார்க்கப்பட்டது ${n.origin} ${o}${n.minimum.toString()} ஆக இருக்க வேண்டும்`}case"invalid_format":{const o=n;return o.format==="starts_with"?`தவறான சரம்: "${o.prefix}" இல் தொடங்க வேண்டும்`:o.format==="ends_with"?`தவறான சரம்: "${o.suffix}" இல் முடிவடைய வேண்டும்`:o.format==="includes"?`தவறான சரம்: "${o.includes}" ஐ உள்ளடக்க வேண்டும்`:o.format==="regex"?`தவறான சரம்: ${o.pattern} முறைபாட்டுடன் பொருந்த வேண்டும்`:`தவறான ${i[o.format]??n.format}`}case"not_multiple_of":return`தவறான எண்: ${n.divisor} இன் பலமாக இருக்க வேண்டும்`;case"unrecognized_keys":return`அடையாளம் தெரியாத விசை${n.keys.length>1?"கள்":""}: ${k(n.keys,", ")}`;case"invalid_key":return`${n.origin} இல் தவறான விசை`;case"invalid_union":return"தவறான உள்ளீடு";case"invalid_element":return`${n.origin} இல் தவறான மதிப்பு`;default:return"தவறான உள்ளீடு"}}};function Sh(){return{localeError:kh()}}const Ih=()=>{const e={string:{unit:"ตัวอักษร",verb:"ควรมี"},file:{unit:"ไบต์",verb:"ควรมี"},array:{unit:"รายการ",verb:"ควรมี"},set:{unit:"รายการ",verb:"ควรมี"}};function t(n){return e[n]??null}const r=n=>{const o=typeof n;switch(o){case"number":return Number.isNaN(n)?"ไม่ใช่ตัวเลข (NaN)":"ตัวเลข";case"object":{if(Array.isArray(n))return"อาร์เรย์ (Array)";if(n===null)return"ไม่มีค่า (null)";if(Object.getPrototypeOf(n)!==Object.prototype&&n.constructor)return n.constructor.name}}return o},i={regex:"ข้อมูลที่ป้อน",email:"ที่อยู่อีเมล",url:"URL",emoji:"อิโมจิ",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"วันที่เวลาแบบ ISO",date:"วันที่แบบ ISO",time:"เวลาแบบ ISO",duration:"ช่วงเวลาแบบ ISO",ipv4:"ที่อยู่ IPv4",ipv6:"ที่อยู่ IPv6",cidrv4:"ช่วง IP แบบ IPv4",cidrv6:"ช่วง IP แบบ IPv6",base64:"ข้อความแบบ Base64",base64url:"ข้อความแบบ Base64 สำหรับ URL",json_string:"ข้อความแบบ JSON",e164:"เบอร์โทรศัพท์ระหว่างประเทศ (E.164)",jwt:"โทเคน JWT",template_literal:"ข้อมูลที่ป้อน"};return n=>{switch(n.code){case"invalid_type":return`ประเภทข้อมูลไม่ถูกต้อง: ควรเป็น ${n.expected} แต่ได้รับ ${r(n.input)}`;case"invalid_value":return n.values.length===1?`ค่าไม่ถูกต้อง: ควรเป็น ${E(n.values[0])}`:`ตัวเลือกไม่ถูกต้อง: ควรเป็นหนึ่งใน ${k(n.values,"|")}`;case"too_big":{const o=n.inclusive?"ไม่เกิน":"น้อยกว่า",a=t(n.origin);return a?`เกินกำหนด: ${n.origin??"ค่า"} ควรมี${o} ${n.maximum.toString()} ${a.unit??"รายการ"}`:`เกินกำหนด: ${n.origin??"ค่า"} ควรมี${o} ${n.maximum.toString()}`}case"too_small":{const o=n.inclusive?"อย่างน้อย":"มากกว่า",a=t(n.origin);return a?`น้อยกว่ากำหนด: ${n.origin} ควรมี${o} ${n.minimum.toString()} ${a.unit}`:`น้อยกว่ากำหนด: ${n.origin} ควรมี${o} ${n.minimum.toString()}`}case"invalid_format":{const o=n;return o.format==="starts_with"?`รูปแบบไม่ถูกต้อง: ข้อความต้องขึ้นต้นด้วย "${o.prefix}"`:o.format==="ends_with"?`รูปแบบไม่ถูกต้อง: ข้อความต้องลงท้ายด้วย "${o.suffix}"`:o.format==="includes"?`รูปแบบไม่ถูกต้อง: ข้อความต้องมี "${o.includes}" อยู่ในข้อความ`:o.format==="regex"?`รูปแบบไม่ถูกต้อง: ต้องตรงกับรูปแบบที่กำหนด ${o.pattern}`:`รูปแบบไม่ถูกต้อง: ${i[o.format]??n.format}`}case"not_multiple_of":return`ตัวเลขไม่ถูกต้อง: ต้องเป็นจำนวนที่หารด้วย ${n.divisor} ได้ลงตัว`;case"unrecognized_keys":return`พบคีย์ที่ไม่รู้จัก: ${k(n.keys,", ")}`;case"invalid_key":return`คีย์ไม่ถูกต้องใน ${n.origin}`;case"invalid_union":return"ข้อมูลไม่ถูกต้อง: ไม่ตรงกับรูปแบบยูเนียนที่กำหนดไว้";case"invalid_element":return`ข้อมูลไม่ถูกต้องใน ${n.origin}`;default:return"ข้อมูลไม่ถูกต้อง"}}};function xh(){return{localeError:Ih()}}const zh=e=>{const t=typeof e;switch(t){case"number":return Number.isNaN(e)?"NaN":"number";case"object":{if(Array.isArray(e))return"array";if(e===null)return"null";if(Object.getPrototypeOf(e)!==Object.prototype&&e.constructor)return e.constructor.name}}return t},Oh=()=>{const e={string:{unit:"karakter",verb:"olmalı"},file:{unit:"bayt",verb:"olmalı"},array:{unit:"öğe",verb:"olmalı"},set:{unit:"öğe",verb:"olmalı"}};function t(i){return e[i]??null}const r={regex:"girdi",email:"e-posta adresi",url:"URL",emoji:"emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"ISO tarih ve saat",date:"ISO tarih",time:"ISO saat",duration:"ISO süre",ipv4:"IPv4 adresi",ipv6:"IPv6 adresi",cidrv4:"IPv4 aralığı",cidrv6:"IPv6 aralığı",base64:"base64 ile şifrelenmiş metin",base64url:"base64url ile şifrelenmiş metin",json_string:"JSON dizesi",e164:"E.164 sayısı",jwt:"JWT",template_literal:"Şablon dizesi"};return i=>{switch(i.code){case"invalid_type":return`Geçersiz değer: beklenen ${i.expected}, alınan ${zh(i.input)}`;case"invalid_value":return i.values.length===1?`Geçersiz değer: beklenen ${E(i.values[0])}`:`Geçersiz seçenek: aşağıdakilerden biri olmalı: ${k(i.values,"|")}`;case"too_big":{const n=i.inclusive?"<=":"<",o=t(i.origin);return o?`Çok büyük: beklenen ${i.origin??"değer"} ${n}${i.maximum.toString()} ${o.unit??"öğe"}`:`Çok büyük: beklenen ${i.origin??"değer"} ${n}${i.maximum.toString()}`}case"too_small":{const n=i.inclusive?">=":">",o=t(i.origin);return o?`Çok küçük: beklenen ${i.origin} ${n}${i.minimum.toString()} ${o.unit}`:`Çok küçük: beklenen ${i.origin} ${n}${i.minimum.toString()}`}case"invalid_format":{const n=i;return n.format==="starts_with"?`Geçersiz metin: "${n.prefix}" ile başlamalı`:n.format==="ends_with"?`Geçersiz metin: "${n.suffix}" ile bitmeli`:n.format==="includes"?`Geçersiz metin: "${n.includes}" içermeli`:n.format==="regex"?`Geçersiz metin: ${n.pattern} desenine uymalı`:`Geçersiz ${r[n.format]??i.format}`}case"not_multiple_of":return`Geçersiz sayı: ${i.divisor} ile tam bölünebilmeli`;case"unrecognized_keys":return`Tanınmayan anahtar${i.keys.length>1?"lar":""}: ${k(i.keys,", ")}`;case"invalid_key":return`${i.origin} içinde geçersiz anahtar`;case"invalid_union":return"Geçersiz değer";case"invalid_element":return`${i.origin} içinde geçersiz değer`;default:return"Geçersiz değer"}}};function Nh(){return{localeError:Oh()}}const jh=()=>{const e={string:{unit:"символів",verb:"матиме"},file:{unit:"байтів",verb:"матиме"},array:{unit:"елементів",verb:"матиме"},set:{unit:"елементів",verb:"матиме"}};function t(n){return e[n]??null}const r=n=>{const o=typeof n;switch(o){case"number":return Number.isNaN(n)?"NaN":"число";case"object":{if(Array.isArray(n))return"масив";if(n===null)return"null";if(Object.getPrototypeOf(n)!==Object.prototype&&n.constructor)return n.constructor.name}}return o},i={regex:"вхідні дані",email:"адреса електронної пошти",url:"URL",emoji:"емодзі",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"дата та час ISO",date:"дата ISO",time:"час ISO",duration:"тривалість ISO",ipv4:"адреса IPv4",ipv6:"адреса IPv6",cidrv4:"діапазон IPv4",cidrv6:"діапазон IPv6",base64:"рядок у кодуванні base64",base64url:"рядок у кодуванні base64url",json_string:"рядок JSON",e164:"номер E.164",jwt:"JWT",template_literal:"вхідні дані"};return n=>{switch(n.code){case"invalid_type":return`Неправильні вхідні дані: очікується ${n.expected}, отримано ${r(n.input)}`;case"invalid_value":return n.values.length===1?`Неправильні вхідні дані: очікується ${E(n.values[0])}`:`Неправильна опція: очікується одне з ${k(n.values,"|")}`;case"too_big":{const o=n.inclusive?"<=":"<",a=t(n.origin);return a?`Занадто велике: очікується, що ${n.origin??"значення"} ${a.verb} ${o}${n.maximum.toString()} ${a.unit??"елементів"}`:`Занадто велике: очікується, що ${n.origin??"значення"} буде ${o}${n.maximum.toString()}`}case"too_small":{const o=n.inclusive?">=":">",a=t(n.origin);return a?`Занадто мале: очікується, що ${n.origin} ${a.verb} ${o}${n.minimum.toString()} ${a.unit}`:`Занадто мале: очікується, що ${n.origin} буде ${o}${n.minimum.toString()}`}case"invalid_format":{const o=n;return o.format==="starts_with"?`Неправильний рядок: повинен починатися з "${o.prefix}"`:o.format==="ends_with"?`Неправильний рядок: повинен закінчуватися на "${o.suffix}"`:o.format==="includes"?`Неправильний рядок: повинен містити "${o.includes}"`:o.format==="regex"?`Неправильний рядок: повинен відповідати шаблону ${o.pattern}`:`Неправильний ${i[o.format]??n.format}`}case"not_multiple_of":return`Неправильне число: повинно бути кратним ${n.divisor}`;case"unrecognized_keys":return`Нерозпізнаний ключ${n.keys.length>1?"і":""}: ${k(n.keys,", ")}`;case"invalid_key":return`Неправильний ключ у ${n.origin}`;case"invalid_union":return"Неправильні вхідні дані";case"invalid_element":return`Неправильне значення у ${n.origin}`;default:return"Неправильні вхідні дані"}}};function Uh(){return{localeError:jh()}}const Ph=()=>{const e={string:{unit:"حروف",verb:"ہونا"},file:{unit:"بائٹس",verb:"ہونا"},array:{unit:"آئٹمز",verb:"ہونا"},set:{unit:"آئٹمز",verb:"ہونا"}};function t(n){return e[n]??null}const r=n=>{const o=typeof n;switch(o){case"number":return Number.isNaN(n)?"NaN":"نمبر";case"object":{if(Array.isArray(n))return"آرے";if(n===null)return"نل";if(Object.getPrototypeOf(n)!==Object.prototype&&n.constructor)return n.constructor.name}}return o},i={regex:"ان پٹ",email:"ای میل ایڈریس",url:"یو آر ایل",emoji:"ایموجی",uuid:"یو یو آئی ڈی",uuidv4:"یو یو آئی ڈی وی 4",uuidv6:"یو یو آئی ڈی وی 6",nanoid:"نینو آئی ڈی",guid:"جی یو آئی ڈی",cuid:"سی یو آئی ڈی",cuid2:"سی یو آئی ڈی 2",ulid:"یو ایل آئی ڈی",xid:"ایکس آئی ڈی",ksuid:"کے ایس یو آئی ڈی",datetime:"آئی ایس او ڈیٹ ٹائم",date:"آئی ایس او تاریخ",time:"آئی ایس او وقت",duration:"آئی ایس او مدت",ipv4:"آئی پی وی 4 ایڈریس",ipv6:"آئی پی وی 6 ایڈریس",cidrv4:"آئی پی وی 4 رینج",cidrv6:"آئی پی وی 6 رینج",base64:"بیس 64 ان کوڈڈ سٹرنگ",base64url:"بیس 64 یو آر ایل ان کوڈڈ سٹرنگ",json_string:"جے ایس او این سٹرنگ",e164:"ای 164 نمبر",jwt:"جے ڈبلیو ٹی",template_literal:"ان پٹ"};return n=>{switch(n.code){case"invalid_type":return`غلط ان پٹ: ${n.expected} متوقع تھا، ${r(n.input)} موصول ہوا`;case"invalid_value":return n.values.length===1?`غلط ان پٹ: ${E(n.values[0])} متوقع تھا`:`غلط آپشن: ${k(n.values,"|")} میں سے ایک متوقع تھا`;case"too_big":{const o=n.inclusive?"<=":"<",a=t(n.origin);return a?`بہت بڑا: ${n.origin??"ویلیو"} کے ${o}${n.maximum.toString()} ${a.unit??"عناصر"} ہونے متوقع تھے`:`بہت بڑا: ${n.origin??"ویلیو"} کا ${o}${n.maximum.toString()} ہونا متوقع تھا`}case"too_small":{const o=n.inclusive?">=":">",a=t(n.origin);return a?`بہت چھوٹا: ${n.origin} کے ${o}${n.minimum.toString()} ${a.unit} ہونے متوقع تھے`:`بہت چھوٹا: ${n.origin} کا ${o}${n.minimum.toString()} ہونا متوقع تھا`}case"invalid_format":{const o=n;return o.format==="starts_with"?`غلط سٹرنگ: "${o.prefix}" سے شروع ہونا چاہیے`:o.format==="ends_with"?`غلط سٹرنگ: "${o.suffix}" پر ختم ہونا چاہیے`:o.format==="includes"?`غلط سٹرنگ: "${o.includes}" شامل ہونا چاہیے`:o.format==="regex"?`غلط سٹرنگ: پیٹرن ${o.pattern} سے میچ ہونا چاہیے`:`غلط ${i[o.format]??n.format}`}case"not_multiple_of":return`غلط نمبر: ${n.divisor} کا مضاعف ہونا چاہیے`;case"unrecognized_keys":return`غیر تسلیم شدہ کی${n.keys.length>1?"ز":""}: ${k(n.keys,"، ")}`;case"invalid_key":return`${n.origin} میں غلط کی`;case"invalid_union":return"غلط ان پٹ";case"invalid_element":return`${n.origin} میں غلط ویلیو`;default:return"غلط ان پٹ"}}};function Ah(){return{localeError:Ph()}}const Eh=()=>{const e={string:{unit:"ký tự",verb:"có"},file:{unit:"byte",verb:"có"},array:{unit:"phần tử",verb:"có"},set:{unit:"phần tử",verb:"có"}};function t(n){return e[n]??null}const r=n=>{const o=typeof n;switch(o){case"number":return Number.isNaN(n)?"NaN":"số";case"object":{if(Array.isArray(n))return"mảng";if(n===null)return"null";if(Object.getPrototypeOf(n)!==Object.prototype&&n.constructor)return n.constructor.name}}return o},i={regex:"đầu vào",email:"địa chỉ email",url:"URL",emoji:"emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"ngày giờ ISO",date:"ngày ISO",time:"giờ ISO",duration:"khoảng thời gian ISO",ipv4:"địa chỉ IPv4",ipv6:"địa chỉ IPv6",cidrv4:"dải IPv4",cidrv6:"dải IPv6",base64:"chuỗi mã hóa base64",base64url:"chuỗi mã hóa base64url",json_string:"chuỗi JSON",e164:"số E.164",jwt:"JWT",template_literal:"đầu vào"};return n=>{switch(n.code){case"invalid_type":return`Đầu vào không hợp lệ: mong đợi ${n.expected}, nhận được ${r(n.input)}`;case"invalid_value":return n.values.length===1?`Đầu vào không hợp lệ: mong đợi ${E(n.values[0])}`:`Tùy chọn không hợp lệ: mong đợi một trong các giá trị ${k(n.values,"|")}`;case"too_big":{const o=n.inclusive?"<=":"<",a=t(n.origin);return a?`Quá lớn: mong đợi ${n.origin??"giá trị"} ${a.verb} ${o}${n.maximum.toString()} ${a.unit??"phần tử"}`:`Quá lớn: mong đợi ${n.origin??"giá trị"} ${o}${n.maximum.toString()}`}case"too_small":{const o=n.inclusive?">=":">",a=t(n.origin);return a?`Quá nhỏ: mong đợi ${n.origin} ${a.verb} ${o}${n.minimum.toString()} ${a.unit}`:`Quá nhỏ: mong đợi ${n.origin} ${o}${n.minimum.toString()}`}case"invalid_format":{const o=n;return o.format==="starts_with"?`Chuỗi không hợp lệ: phải bắt đầu bằng "${o.prefix}"`:o.format==="ends_with"?`Chuỗi không hợp lệ: phải kết thúc bằng "${o.suffix}"`:o.format==="includes"?`Chuỗi không hợp lệ: phải bao gồm "${o.includes}"`:o.format==="regex"?`Chuỗi không hợp lệ: phải khớp với mẫu ${o.pattern}`:`${i[o.format]??n.format} không hợp lệ`}case"not_multiple_of":return`Số không hợp lệ: phải là bội số của ${n.divisor}`;case"unrecognized_keys":return`Khóa không được nhận dạng: ${k(n.keys,", ")}`;case"invalid_key":return`Khóa không hợp lệ trong ${n.origin}`;case"invalid_union":return"Đầu vào không hợp lệ";case"invalid_element":return`Giá trị không hợp lệ trong ${n.origin}`;default:return"Đầu vào không hợp lệ"}}};function Th(){return{localeError:Eh()}}const Dh=()=>{const e={string:{unit:"字符",verb:"包含"},file:{unit:"字节",verb:"包含"},array:{unit:"项",verb:"包含"},set:{unit:"项",verb:"包含"}};function t(n){return e[n]??null}const r=n=>{const o=typeof n;switch(o){case"number":return Number.isNaN(n)?"非数字(NaN)":"数字";case"object":{if(Array.isArray(n))return"数组";if(n===null)return"空值(null)";if(Object.getPrototypeOf(n)!==Object.prototype&&n.constructor)return n.constructor.name}}return o},i={regex:"输入",email:"电子邮件",url:"URL",emoji:"表情符号",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"ISO日期时间",date:"ISO日期",time:"ISO时间",duration:"ISO时长",ipv4:"IPv4地址",ipv6:"IPv6地址",cidrv4:"IPv4网段",cidrv6:"IPv6网段",base64:"base64编码字符串",base64url:"base64url编码字符串",json_string:"JSON字符串",e164:"E.164号码",jwt:"JWT",template_literal:"输入"};return n=>{switch(n.code){case"invalid_type":return`无效输入：期望 ${n.expected}，实际接收 ${r(n.input)}`;case"invalid_value":return n.values.length===1?`无效输入：期望 ${E(n.values[0])}`:`无效选项：期望以下之一 ${k(n.values,"|")}`;case"too_big":{const o=n.inclusive?"<=":"<",a=t(n.origin);return a?`数值过大：期望 ${n.origin??"值"} ${o}${n.maximum.toString()} ${a.unit??"个元素"}`:`数值过大：期望 ${n.origin??"值"} ${o}${n.maximum.toString()}`}case"too_small":{const o=n.inclusive?">=":">",a=t(n.origin);return a?`数值过小：期望 ${n.origin} ${o}${n.minimum.toString()} ${a.unit}`:`数值过小：期望 ${n.origin} ${o}${n.minimum.toString()}`}case"invalid_format":{const o=n;return o.format==="starts_with"?`无效字符串：必须以 "${o.prefix}" 开头`:o.format==="ends_with"?`无效字符串：必须以 "${o.suffix}" 结尾`:o.format==="includes"?`无效字符串：必须包含 "${o.includes}"`:o.format==="regex"?`无效字符串：必须满足正则表达式 ${o.pattern}`:`无效${i[o.format]??n.format}`}case"not_multiple_of":return`无效数字：必须是 ${n.divisor} 的倍数`;case"unrecognized_keys":return`出现未知的键(key): ${k(n.keys,", ")}`;case"invalid_key":return`${n.origin} 中的键(key)无效`;case"invalid_union":return"无效输入";case"invalid_element":return`${n.origin} 中包含无效值(value)`;default:return"无效输入"}}};function Rh(){return{localeError:Dh()}}const Ch=()=>{const e={string:{unit:"字元",verb:"擁有"},file:{unit:"位元組",verb:"擁有"},array:{unit:"項目",verb:"擁有"},set:{unit:"項目",verb:"擁有"}};function t(n){return e[n]??null}const r=n=>{const o=typeof n;switch(o){case"number":return Number.isNaN(n)?"NaN":"number";case"object":{if(Array.isArray(n))return"array";if(n===null)return"null";if(Object.getPrototypeOf(n)!==Object.prototype&&n.constructor)return n.constructor.name}}return o},i={regex:"輸入",email:"郵件地址",url:"URL",emoji:"emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"ISO 日期時間",date:"ISO 日期",time:"ISO 時間",duration:"ISO 期間",ipv4:"IPv4 位址",ipv6:"IPv6 位址",cidrv4:"IPv4 範圍",cidrv6:"IPv6 範圍",base64:"base64 編碼字串",base64url:"base64url 編碼字串",json_string:"JSON 字串",e164:"E.164 數值",jwt:"JWT",template_literal:"輸入"};return n=>{switch(n.code){case"invalid_type":return`無效的輸入值：預期為 ${n.expected}，但收到 ${r(n.input)}`;case"invalid_value":return n.values.length===1?`無效的輸入值：預期為 ${E(n.values[0])}`:`無效的選項：預期為以下其中之一 ${k(n.values,"|")}`;case"too_big":{const o=n.inclusive?"<=":"<",a=t(n.origin);return a?`數值過大：預期 ${n.origin??"值"} 應為 ${o}${n.maximum.toString()} ${a.unit??"個元素"}`:`數值過大：預期 ${n.origin??"值"} 應為 ${o}${n.maximum.toString()}`}case"too_small":{const o=n.inclusive?">=":">",a=t(n.origin);return a?`數值過小：預期 ${n.origin} 應為 ${o}${n.minimum.toString()} ${a.unit}`:`數值過小：預期 ${n.origin} 應為 ${o}${n.minimum.toString()}`}case"invalid_format":{const o=n;return o.format==="starts_with"?`無效的字串：必須以 "${o.prefix}" 開頭`:o.format==="ends_with"?`無效的字串：必須以 "${o.suffix}" 結尾`:o.format==="includes"?`無效的字串：必須包含 "${o.includes}"`:o.format==="regex"?`無效的字串：必須符合格式 ${o.pattern}`:`無效的 ${i[o.format]??n.format}`}case"not_multiple_of":return`無效的數字：必須為 ${n.divisor} 的倍數`;case"unrecognized_keys":return`無法識別的鍵值${n.keys.length>1?"們":""}：${k(n.keys,"、")}`;case"invalid_key":return`${n.origin} 中有無效的鍵值`;case"invalid_union":return"無效的輸入值";case"invalid_element":return`${n.origin} 中有無效的值`;default:return"無效的輸入值"}}};function Zh(){return{localeError:Ch()}}const Jc=Object.freeze(Object.defineProperty({__proto__:null,ar:pm,az:vm,be:bm,ca:$m,cs:km,de:Im,en:Bc,eo:jm,es:Pm,fa:Em,fi:Dm,fr:Cm,frCA:Lm,he:Fm,hu:Jm,id:qm,it:Xm,ja:Gm,kh:Ym,ko:eh,mk:nh,ms:ih,nl:ah,no:uh,ota:lh,pl:hh,ps:fh,pt:gh,ru:_h,sl:yh,sv:wh,ta:Sh,th:xh,tr:Nh,ua:Uh,ur:Ah,vi:Th,zhCN:Rh,zhTW:Zh},Symbol.toStringTag,{value:"Module"})),Wc=Symbol("ZodOutput"),qc=Symbol("ZodInput");class qr{constructor(){this._map=new Map,this._idmap=new Map}add(t,...r){const i=r[0];if(this._map.set(t,i),i&&typeof i=="object"&&"id"in i){if(this._idmap.has(i.id))throw new Error(`ID ${i.id} already exists in the registry`);this._idmap.set(i.id,t)}return this}clear(){return this._map=new Map,this._idmap=new Map,this}remove(t){const r=this._map.get(t);return r&&typeof r=="object"&&"id"in r&&this._idmap.delete(r.id),this._map.delete(t),this}get(t){const r=t._zod.parent;if(r){const i={...this.get(r)??{}};return delete i.id,{...i,...this._map.get(t)}}return this._map.get(t)}has(t){return this._map.has(t)}}function Vr(){return new qr}const Se=Vr();function Vc(e,t){return new e({type:"string",...$(t)})}function Xc(e,t){return new e({type:"string",coerce:!0,...$(t)})}function Xr(e,t){return new e({type:"string",format:"email",check:"string_format",abort:!1,...$(t)})}function Ht(e,t){return new e({type:"string",format:"guid",check:"string_format",abort:!1,...$(t)})}function Kr(e,t){return new e({type:"string",format:"uuid",check:"string_format",abort:!1,...$(t)})}function Gr(e,t){return new e({type:"string",format:"uuid",check:"string_format",abort:!1,version:"v4",...$(t)})}function Hr(e,t){return new e({type:"string",format:"uuid",check:"string_format",abort:!1,version:"v6",...$(t)})}function Yr(e,t){return new e({type:"string",format:"uuid",check:"string_format",abort:!1,version:"v7",...$(t)})}function Qr(e,t){return new e({type:"string",format:"url",check:"string_format",abort:!1,...$(t)})}function ei(e,t){return new e({type:"string",format:"emoji",check:"string_format",abort:!1,...$(t)})}function ti(e,t){return new e({type:"string",format:"nanoid",check:"string_format",abort:!1,...$(t)})}function ni(e,t){return new e({type:"string",format:"cuid",check:"string_format",abort:!1,...$(t)})}function ri(e,t){return new e({type:"string",format:"cuid2",check:"string_format",abort:!1,...$(t)})}function ii(e,t){return new e({type:"string",format:"ulid",check:"string_format",abort:!1,...$(t)})}function oi(e,t){return new e({type:"string",format:"xid",check:"string_format",abort:!1,...$(t)})}function ai(e,t){return new e({type:"string",format:"ksuid",check:"string_format",abort:!1,...$(t)})}function si(e,t){return new e({type:"string",format:"ipv4",check:"string_format",abort:!1,...$(t)})}function ui(e,t){return new e({type:"string",format:"ipv6",check:"string_format",abort:!1,...$(t)})}function ci(e,t){return new e({type:"string",format:"cidrv4",check:"string_format",abort:!1,...$(t)})}function li(e,t){return new e({type:"string",format:"cidrv6",check:"string_format",abort:!1,...$(t)})}function di(e,t){return new e({type:"string",format:"base64",check:"string_format",abort:!1,...$(t)})}function fi(e,t){return new e({type:"string",format:"base64url",check:"string_format",abort:!1,...$(t)})}function mi(e,t){return new e({type:"string",format:"e164",check:"string_format",abort:!1,...$(t)})}function hi(e,t){return new e({type:"string",format:"jwt",check:"string_format",abort:!1,...$(t)})}const Kc={Any:null,Minute:-1,Second:0,Millisecond:3,Microsecond:6};function Gc(e,t){return new e({type:"string",format:"datetime",check:"string_format",offset:!1,local:!1,precision:null,...$(t)})}function Hc(e,t){return new e({type:"string",format:"date",check:"string_format",...$(t)})}function Yc(e,t){return new e({type:"string",format:"time",check:"string_format",precision:null,...$(t)})}function Qc(e,t){return new e({type:"string",format:"duration",check:"string_format",...$(t)})}function el(e,t){return new e({type:"number",checks:[],...$(t)})}function tl(e,t){return new e({type:"number",coerce:!0,checks:[],...$(t)})}function nl(e,t){return new e({type:"number",check:"number_format",abort:!1,format:"safeint",...$(t)})}function rl(e,t){return new e({type:"number",check:"number_format",abort:!1,format:"float32",...$(t)})}function il(e,t){return new e({type:"number",check:"number_format",abort:!1,format:"float64",...$(t)})}function ol(e,t){return new e({type:"number",check:"number_format",abort:!1,format:"int32",...$(t)})}function al(e,t){return new e({type:"number",check:"number_format",abort:!1,format:"uint32",...$(t)})}function sl(e,t){return new e({type:"boolean",...$(t)})}function ul(e,t){return new e({type:"boolean",coerce:!0,...$(t)})}function cl(e,t){return new e({type:"bigint",...$(t)})}function ll(e,t){return new e({type:"bigint",coerce:!0,...$(t)})}function dl(e,t){return new e({type:"bigint",check:"bigint_format",abort:!1,format:"int64",...$(t)})}function fl(e,t){return new e({type:"bigint",check:"bigint_format",abort:!1,format:"uint64",...$(t)})}function ml(e,t){return new e({type:"symbol",...$(t)})}function hl(e,t){return new e({type:"undefined",...$(t)})}function pl(e,t){return new e({type:"null",...$(t)})}function gl(e){return new e({type:"any"})}function Yt(e){return new e({type:"unknown"})}function vl(e,t){return new e({type:"never",...$(t)})}function _l(e,t){return new e({type:"void",...$(t)})}function bl(e,t){return new e({type:"date",...$(t)})}function yl(e,t){return new e({type:"date",coerce:!0,...$(t)})}function $l(e,t){return new e({type:"nan",...$(t)})}function xe(e,t){return new Dr({check:"less_than",...$(t),value:e,inclusive:!1})}function ce(e,t){return new Dr({check:"less_than",...$(t),value:e,inclusive:!0})}function ze(e,t){return new Rr({check:"greater_than",...$(t),value:e,inclusive:!1})}function oe(e,t){return new Rr({check:"greater_than",...$(t),value:e,inclusive:!0})}function wl(e){return ze(0,e)}function kl(e){return xe(0,e)}function Sl(e){return ce(0,e)}function Il(e){return oe(0,e)}function dt(e,t){return new yu({check:"multiple_of",...$(t),value:e})}function vn(e,t){return new ku({check:"max_size",...$(t),maximum:e})}function ft(e,t){return new Su({check:"min_size",...$(t),minimum:e})}function pi(e,t){return new Iu({check:"size_equals",...$(t),size:e})}function _n(e,t){return new xu({check:"max_length",...$(t),maximum:e})}function Je(e,t){return new zu({check:"min_length",...$(t),minimum:e})}function bn(e,t){return new Ou({check:"length_equals",...$(t),length:e})}function gi(e,t){return new Nu({check:"string_format",format:"regex",...$(t),pattern:e})}function vi(e){return new ju({check:"string_format",format:"lowercase",...$(e)})}function _i(e){return new Uu({check:"string_format",format:"uppercase",...$(e)})}function bi(e,t){return new Pu({check:"string_format",format:"includes",...$(t),includes:e})}function yi(e,t){return new Au({check:"string_format",format:"starts_with",...$(t),prefix:e})}function $i(e,t){return new Eu({check:"string_format",format:"ends_with",...$(t),suffix:e})}function xl(e,t,r){return new Tu({check:"property",property:e,schema:t,...$(r)})}function wi(e,t){return new Du({check:"mime_type",mime:e,...$(t)})}function je(e){return new Ru({check:"overwrite",tx:e})}function ki(e){return je(t=>t.normalize(e))}function Si(){return je(e=>e.trim())}function Ii(){return je(e=>e.toLowerCase())}function xi(){return je(e=>e.toUpperCase())}function zi(e,t,r){return new e({type:"array",element:t,...$(r)})}function Lh(e,t,r){return new e({type:"union",options:t,...$(r)})}function Mh(e,t,r,i){return new e({type:"union",options:r,discriminator:t,...$(i)})}function Fh(e,t,r){return new e({type:"intersection",left:t,right:r})}function zl(e,t,r,i){const n=r instanceof A,o=n?i:r,a=n?r:null;return new e({type:"tuple",items:t,rest:a,...$(o)})}function Bh(e,t,r,i){return new e({type:"record",keyType:t,valueType:r,...$(i)})}function Jh(e,t,r,i){return new e({type:"map",keyType:t,valueType:r,...$(i)})}function Wh(e,t,r){return new e({type:"set",valueType:t,...$(r)})}function qh(e,t,r){const i=Array.isArray(t)?Object.fromEntries(t.map(n=>[n,n])):t;return new e({type:"enum",entries:i,...$(r)})}function Vh(e,t,r){return new e({type:"enum",entries:t,...$(r)})}function Xh(e,t,r){return new e({type:"literal",values:Array.isArray(t)?t:[t],...$(r)})}function Ol(e,t){return new e({type:"file",...$(t)})}function Kh(e,t){return new e({type:"transform",transform:t})}function Gh(e,t){return new e({type:"optional",innerType:t})}function Hh(e,t){return new e({type:"nullable",innerType:t})}function Yh(e,t,r){return new e({type:"default",innerType:t,get defaultValue(){return typeof r=="function"?r():r}})}function Qh(e,t,r){return new e({type:"nonoptional",innerType:t,...$(r)})}function ep(e,t){return new e({type:"success",innerType:t})}function tp(e,t,r){return new e({type:"catch",innerType:t,catchValue:typeof r=="function"?r:()=>r})}function np(e,t,r){return new e({type:"pipe",in:t,out:r})}function rp(e,t){return new e({type:"readonly",innerType:t})}function ip(e,t,r){return new e({type:"template_literal",parts:t,...$(r)})}function op(e,t){return new e({type:"lazy",getter:t})}function ap(e,t){return new e({type:"promise",innerType:t})}function Nl(e,t,r){const i=$(r);return i.abort??(i.abort=!0),new e({type:"custom",check:"custom",fn:t,...i})}function jl(e,t,r){return new e({type:"custom",check:"custom",fn:t,...$(r)})}function Ul(e,t){const r=$(t);let i=r.truthy??["true","1","yes","on","y","enabled"],n=r.falsy??["false","0","no","off","n","disabled"];r.case!=="sensitive"&&(i=i.map(_=>typeof _=="string"?_.toLowerCase():_),n=n.map(_=>typeof _=="string"?_.toLowerCase():_));const o=new Set(i),a=new Set(n),s=e.Pipe??Wr,u=e.Boolean??Lr,c=e.String??yt,p=e.Transform??Jr,d=new p({type:"transform",transform:(_,S)=>{let b=_;return r.case!=="sensitive"&&(b=b.toLowerCase()),o.has(b)?!0:a.has(b)?!1:(S.issues.push({code:"invalid_value",expected:"stringbool",values:[...o,...a],input:S.value,inst:d}),{})},error:r.error}),y=new s({type:"pipe",in:new c({type:"string",error:r.error}),out:d,error:r.error});return new s({type:"pipe",in:y,out:new u({type:"boolean",error:r.error}),error:r.error})}function Pl(e,t,r,i={}){const n=$(i),o={...$(i),check:"string_format",type:"string",format:t,fn:typeof r=="function"?r:s=>r.test(s),...n};return r instanceof RegExp&&(o.pattern=r),new e(o)}class Al{constructor(t){this._def=t,this.def=t}implement(t){if(typeof t!="function")throw new Error("implement() must be called with a function");const r=(...i)=>{const n=this._def.input?Yn(this._def.input,i,void 0,{callee:r}):i;if(!Array.isArray(n))throw new Error("Invalid arguments schema: not an array or tuple schema.");const o=t(...n);return this._def.output?Yn(this._def.output,o,void 0,{callee:r}):o};return r}implementAsync(t){if(typeof t!="function")throw new Error("implement() must be called with a function");const r=async(...i)=>{const n=this._def.input?await Qn(this._def.input,i,void 0,{callee:r}):i;if(!Array.isArray(n))throw new Error("Invalid arguments schema: not an array or tuple schema.");const o=await t(...n);return this._def.output?Qn(this._def.output,o,void 0,{callee:r}):o};return r}input(...t){const r=this.constructor;return Array.isArray(t[0])?new r({type:"function",input:new gn({type:"tuple",items:t[0],rest:t[1]}),output:this._def.output}):new r({type:"function",input:t[0],output:this._def.output})}output(t){const r=this.constructor;return new r({type:"function",input:this._def.input,output:t})}}function El(e){return new Al({type:"function",input:Array.isArray(e==null?void 0:e.input)?zl(gn,e==null?void 0:e.input):(e==null?void 0:e.input)??zi(Fr,Yt(Gt)),output:(e==null?void 0:e.output)??Yt(Gt)})}class tr{constructor(t){this.counter=0,this.metadataRegistry=(t==null?void 0:t.metadata)??Se,this.target=(t==null?void 0:t.target)??"draft-2020-12",this.unrepresentable=(t==null?void 0:t.unrepresentable)??"throw",this.override=(t==null?void 0:t.override)??(()=>{}),this.io=(t==null?void 0:t.io)??"output",this.seen=new Map}process(t,r={path:[],schemaPath:[]}){var d,y,v;var i;const n=t._zod.def,o={guid:"uuid",url:"uri",datetime:"date-time",json_string:"json-string",regex:""},a=this.seen.get(t);if(a)return a.count++,r.schemaPath.includes(t)&&(a.cycle=r.path),a.schema;const s={schema:{},count:1,cycle:void 0,path:r.path};this.seen.set(t,s);const u=(y=(d=t._zod).toJSONSchema)==null?void 0:y.call(d);if(u)s.schema=u;else{const _={...r,schemaPath:[...r.schemaPath,t],path:r.path},S=t._zod.parent;if(S)s.ref=S,this.process(S,_),this.seen.get(S).isParent=!0;else{const b=s.schema;switch(n.type){case"string":{const m=b;m.type="string";const{minimum:h,maximum:g,format:w,patterns:x,contentEncoding:z}=t._zod.bag;if(typeof h=="number"&&(m.minLength=h),typeof g=="number"&&(m.maxLength=g),w&&(m.format=o[w]??w,m.format===""&&delete m.format),z&&(m.contentEncoding=z),x&&x.size>0){const N=[...x];N.length===1?m.pattern=N[0].source:N.length>1&&(s.schema.allOf=[...N.map(F=>({...this.target==="draft-7"?{type:"string"}:{},pattern:F.source}))])}break}case"number":{const m=b,{minimum:h,maximum:g,format:w,multipleOf:x,exclusiveMaximum:z,exclusiveMinimum:N}=t._zod.bag;typeof w=="string"&&w.includes("int")?m.type="integer":m.type="number",typeof N=="number"&&(m.exclusiveMinimum=N),typeof h=="number"&&(m.minimum=h,typeof N=="number"&&(N>=h?delete m.minimum:delete m.exclusiveMinimum)),typeof z=="number"&&(m.exclusiveMaximum=z),typeof g=="number"&&(m.maximum=g,typeof z=="number"&&(z<=g?delete m.maximum:delete m.exclusiveMaximum)),typeof x=="number"&&(m.multipleOf=x);break}case"boolean":{const m=b;m.type="boolean";break}case"bigint":{if(this.unrepresentable==="throw")throw new Error("BigInt cannot be represented in JSON Schema");break}case"symbol":{if(this.unrepresentable==="throw")throw new Error("Symbols cannot be represented in JSON Schema");break}case"null":{b.type="null";break}case"any":break;case"unknown":break;case"undefined":{if(this.unrepresentable==="throw")throw new Error("Undefined cannot be represented in JSON Schema");break}case"void":{if(this.unrepresentable==="throw")throw new Error("Void cannot be represented in JSON Schema");break}case"never":{b.not={};break}case"date":{if(this.unrepresentable==="throw")throw new Error("Date cannot be represented in JSON Schema");break}case"array":{const m=b,{minimum:h,maximum:g}=t._zod.bag;typeof h=="number"&&(m.minItems=h),typeof g=="number"&&(m.maxItems=g),m.type="array",m.items=this.process(n.element,{..._,path:[..._.path,"items"]});break}case"object":{const m=b;m.type="object",m.properties={};const h=n.shape;for(const x in h)m.properties[x]=this.process(h[x],{..._,path:[..._.path,"properties",x]});const g=new Set(Object.keys(h)),w=new Set([...g].filter(x=>{const z=n.shape[x]._zod;return this.io==="input"?z.optin===void 0:z.optout===void 0}));w.size>0&&(m.required=Array.from(w)),((v=n.catchall)==null?void 0:v._zod.def.type)==="never"?m.additionalProperties=!1:n.catchall?n.catchall&&(m.additionalProperties=this.process(n.catchall,{..._,path:[..._.path,"additionalProperties"]})):this.io==="output"&&(m.additionalProperties=!1);break}case"union":{const m=b;m.anyOf=n.options.map((h,g)=>this.process(h,{..._,path:[..._.path,"anyOf",g]}));break}case"intersection":{const m=b,h=this.process(n.left,{..._,path:[..._.path,"allOf",0]}),g=this.process(n.right,{..._,path:[..._.path,"allOf",1]}),w=z=>"allOf"in z&&Object.keys(z).length===1,x=[...w(h)?h.allOf:[h],...w(g)?g.allOf:[g]];m.allOf=x;break}case"tuple":{const m=b;m.type="array";const h=n.items.map((x,z)=>this.process(x,{..._,path:[..._.path,"prefixItems",z]}));if(this.target==="draft-2020-12"?m.prefixItems=h:m.items=h,n.rest){const x=this.process(n.rest,{..._,path:[..._.path,"items"]});this.target==="draft-2020-12"?m.items=x:m.additionalItems=x}n.rest&&(m.items=this.process(n.rest,{..._,path:[..._.path,"items"]}));const{minimum:g,maximum:w}=t._zod.bag;typeof g=="number"&&(m.minItems=g),typeof w=="number"&&(m.maxItems=w);break}case"record":{const m=b;m.type="object",m.propertyNames=this.process(n.keyType,{..._,path:[..._.path,"propertyNames"]}),m.additionalProperties=this.process(n.valueType,{..._,path:[..._.path,"additionalProperties"]});break}case"map":{if(this.unrepresentable==="throw")throw new Error("Map cannot be represented in JSON Schema");break}case"set":{if(this.unrepresentable==="throw")throw new Error("Set cannot be represented in JSON Schema");break}case"enum":{const m=b,h=xr(n.entries);h.every(g=>typeof g=="number")&&(m.type="number"),h.every(g=>typeof g=="string")&&(m.type="string"),m.enum=h;break}case"literal":{const m=b,h=[];for(const g of n.values)if(g===void 0){if(this.unrepresentable==="throw")throw new Error("Literal `undefined` cannot be represented in JSON Schema")}else if(typeof g=="bigint"){if(this.unrepresentable==="throw")throw new Error("BigInt literals cannot be represented in JSON Schema");h.push(Number(g))}else h.push(g);if(h.length!==0)if(h.length===1){const g=h[0];m.type=g===null?"null":typeof g,m.const=g}else h.every(g=>typeof g=="number")&&(m.type="number"),h.every(g=>typeof g=="string")&&(m.type="string"),h.every(g=>typeof g=="boolean")&&(m.type="string"),h.every(g=>g===null)&&(m.type="null"),m.enum=h;break}case"file":{const m=b,h={type:"string",format:"binary",contentEncoding:"binary"},{minimum:g,maximum:w,mime:x}=t._zod.bag;g!==void 0&&(h.minLength=g),w!==void 0&&(h.maxLength=w),x?x.length===1?(h.contentMediaType=x[0],Object.assign(m,h)):m.anyOf=x.map(z=>({...h,contentMediaType:z})):Object.assign(m,h);break}case"transform":{if(this.unrepresentable==="throw")throw new Error("Transforms cannot be represented in JSON Schema");break}case"nullable":{const m=this.process(n.innerType,_);b.anyOf=[m,{type:"null"}];break}case"nonoptional":{this.process(n.innerType,_),s.ref=n.innerType;break}case"success":{const m=b;m.type="boolean";break}case"default":{this.process(n.innerType,_),s.ref=n.innerType,b.default=JSON.parse(JSON.stringify(n.defaultValue));break}case"prefault":{this.process(n.innerType,_),s.ref=n.innerType,this.io==="input"&&(b._prefault=JSON.parse(JSON.stringify(n.defaultValue)));break}case"catch":{this.process(n.innerType,_),s.ref=n.innerType;let m;try{m=n.catchValue(void 0)}catch{throw new Error("Dynamic catch values are not supported in JSON Schema")}b.default=m;break}case"nan":{if(this.unrepresentable==="throw")throw new Error("NaN cannot be represented in JSON Schema");break}case"template_literal":{const m=b,h=t._zod.pattern;if(!h)throw new Error("Pattern not found in template literal");m.type="string",m.pattern=h.source;break}case"pipe":{const m=this.io==="input"?n.in._zod.def.type==="transform"?n.out:n.in:n.out;this.process(m,_),s.ref=m;break}case"readonly":{this.process(n.innerType,_),s.ref=n.innerType,b.readOnly=!0;break}case"promise":{this.process(n.innerType,_),s.ref=n.innerType;break}case"optional":{this.process(n.innerType,_),s.ref=n.innerType;break}case"lazy":{const m=t._zod.innerType;this.process(m,_),s.ref=m;break}case"custom":{if(this.unrepresentable==="throw")throw new Error("Custom types cannot be represented in JSON Schema");break}}}}const c=this.metadataRegistry.get(t);return c&&Object.assign(s.schema,c),this.io==="input"&&V(t)&&(delete s.schema.examples,delete s.schema.default),this.io==="input"&&s.schema._prefault&&((i=s.schema).default??(i.default=s.schema._prefault)),delete s.schema._prefault,this.seen.get(t).schema}emit(t,r){var p,d,y,v,_,S;const i={cycles:(r==null?void 0:r.cycles)??"ref",reused:(r==null?void 0:r.reused)??"inline",external:(r==null?void 0:r.external)??void 0},n=this.seen.get(t);if(!n)throw new Error("Unprocessed schema. This is a bug in Zod.");const o=b=>{var x;const m=this.target==="draft-2020-12"?"$defs":"definitions";if(i.external){const z=(x=i.external.registry.get(b[0]))==null?void 0:x.id,N=i.external.uri??(C=>C);if(z)return{ref:N(z)};const F=b[1].defId??b[1].schema.id??`schema${this.counter++}`;return b[1].defId=F,{defId:F,ref:`${N("__shared")}#/${m}/${F}`}}if(b[1]===n)return{ref:"#"};const g=`#/${m}/`,w=b[1].schema.id??`__schema${this.counter++}`;return{defId:w,ref:g+w}},a=b=>{if(b[1].schema.$ref)return;const m=b[1],{ref:h,defId:g}=o(b);m.def={...m.schema},g&&(m.defId=g);const w=m.schema;for(const x in w)delete w[x];w.$ref=h};if(i.cycles==="throw")for(const b of this.seen.entries()){const m=b[1];if(m.cycle)throw new Error(`Cycle detected: #/${(p=m.cycle)==null?void 0:p.join("/")}/<root>

Set the \`cycles\` parameter to \`"ref"\` to resolve cyclical schemas with defs.`)}for(const b of this.seen.entries()){const m=b[1];if(t===b[0]){a(b);continue}if(i.external){const g=(d=i.external.registry.get(b[0]))==null?void 0:d.id;if(t!==b[0]&&g){a(b);continue}}if((y=this.metadataRegistry.get(b[0]))==null?void 0:y.id){a(b);continue}if(m.cycle){a(b);continue}if(m.count>1&&i.reused==="ref"){a(b);continue}}const s=(b,m)=>{const h=this.seen.get(b),g=h.def??h.schema,w={...g};if(h.ref===null)return;const x=h.ref;if(h.ref=null,x){s(x,m);const z=this.seen.get(x).schema;z.$ref&&m.target==="draft-7"?(g.allOf=g.allOf??[],g.allOf.push(z)):(Object.assign(g,z),Object.assign(g,w))}h.isParent||this.override({zodSchema:b,jsonSchema:g,path:h.path??[]})};for(const b of[...this.seen.entries()].reverse())s(b[0],{target:this.target});const u={};if(this.target==="draft-2020-12"?u.$schema="https://json-schema.org/draft/2020-12/schema":this.target==="draft-7"?u.$schema="http://json-schema.org/draft-07/schema#":console.warn(`Invalid target: ${this.target}`),(v=i.external)!=null&&v.uri){const b=(_=i.external.registry.get(t))==null?void 0:_.id;if(!b)throw new Error("Schema is missing an `id` property");u.$id=i.external.uri(b)}Object.assign(u,n.def);const c=((S=i.external)==null?void 0:S.defs)??{};for(const b of this.seen.entries()){const m=b[1];m.def&&m.defId&&(c[m.defId]=m.def)}i.external||Object.keys(c).length>0&&(this.target==="draft-2020-12"?u.$defs=c:u.definitions=c);try{return JSON.parse(JSON.stringify(u))}catch{throw new Error("Error converting schema to JSON.")}}}function Tl(e,t){if(e instanceof qr){const i=new tr(t),n={};for(const s of e._idmap.entries()){const[u,c]=s;i.process(c)}const o={},a={registry:e,uri:t==null?void 0:t.uri,defs:n};for(const s of e._idmap.entries()){const[u,c]=s;o[u]=i.emit(c,{...t,external:a})}if(Object.keys(n).length>0){const s=i.target==="draft-2020-12"?"$defs":"definitions";o.__shared={[s]:n}}return{schemas:o}}const r=new tr(t);return r.process(e),r.emit(e,t)}function V(e,t){const r=t??{seen:new Set};if(r.seen.has(e))return!1;r.seen.add(e);const n=e._zod.def;switch(n.type){case"string":case"number":case"bigint":case"boolean":case"date":case"symbol":case"undefined":case"null":case"any":case"unknown":case"never":case"void":case"literal":case"enum":case"nan":case"file":case"template_literal":return!1;case"array":return V(n.element,r);case"object":{for(const o in n.shape)if(V(n.shape[o],r))return!0;return!1}case"union":{for(const o of n.options)if(V(o,r))return!0;return!1}case"intersection":return V(n.left,r)||V(n.right,r);case"tuple":{for(const o of n.items)if(V(o,r))return!0;return!!(n.rest&&V(n.rest,r))}case"record":return V(n.keyType,r)||V(n.valueType,r);case"map":return V(n.keyType,r)||V(n.valueType,r);case"set":return V(n.valueType,r);case"promise":case"optional":case"nonoptional":case"nullable":case"readonly":return V(n.innerType,r);case"lazy":return V(n.getter(),r);case"default":return V(n.innerType,r);case"prefault":return V(n.innerType,r);case"custom":return!1;case"transform":return!0;case"pipe":return V(n.in,r)||V(n.out,r);case"success":return!1;case"catch":return!1}throw new Error(`Unknown schema type: ${n.type}`)}const sp=Object.freeze(Object.defineProperty({__proto__:null},Symbol.toStringTag,{value:"Module"})),up=Object.freeze(Object.defineProperty({__proto__:null,$ZodAny:vc,$ZodArray:Fr,$ZodAsyncError:Me,$ZodBase64:oc,$ZodBase64URL:sc,$ZodBigInt:Mr,$ZodBigIntFormat:mc,$ZodBoolean:Lr,$ZodCIDRv4:rc,$ZodCIDRv6:ic,$ZodCUID:qu,$ZodCUID2:Vu,$ZodCatch:Dc,$ZodCheck:J,$ZodCheckBigIntFormat:wu,$ZodCheckEndsWith:Eu,$ZodCheckGreaterThan:Rr,$ZodCheckIncludes:Pu,$ZodCheckLengthEquals:Ou,$ZodCheckLessThan:Dr,$ZodCheckLowerCase:ju,$ZodCheckMaxLength:xu,$ZodCheckMaxSize:ku,$ZodCheckMimeType:Du,$ZodCheckMinLength:zu,$ZodCheckMinSize:Su,$ZodCheckMultipleOf:yu,$ZodCheckNumberFormat:$u,$ZodCheckOverwrite:Ru,$ZodCheckProperty:Tu,$ZodCheckRegex:Nu,$ZodCheckSizeEquals:Iu,$ZodCheckStartsWith:Au,$ZodCheckStringFormat:bt,$ZodCheckUpperCase:Uu,$ZodCustom:Fc,$ZodCustomStringFormat:dc,$ZodDate:yc,$ZodDefault:Pc,$ZodDiscriminatedUnion:wc,$ZodE164:uc,$ZodEmail:Fu,$ZodEmoji:Ju,$ZodEnum:zc,$ZodError:Or,$ZodFile:Nc,$ZodFunction:Al,$ZodGUID:Lu,$ZodIPv4:tc,$ZodIPv6:nc,$ZodISODate:Yu,$ZodISODateTime:Hu,$ZodISODuration:ec,$ZodISOTime:Qu,$ZodIntersection:kc,$ZodJWT:lc,$ZodKSUID:Gu,$ZodLazy:Mc,$ZodLiteral:Oc,$ZodMap:Ic,$ZodNaN:Rc,$ZodNanoID:Wu,$ZodNever:_c,$ZodNonOptional:Ec,$ZodNull:gc,$ZodNullable:Uc,$ZodNumber:Zr,$ZodNumberFormat:fc,$ZodObject:$c,$ZodOptional:jc,$ZodPipe:Wr,$ZodPrefault:Ac,$ZodPromise:Lc,$ZodReadonly:Cc,$ZodRealError:_t,$ZodRecord:Sc,$ZodRegistry:qr,$ZodSet:xc,$ZodString:yt,$ZodStringFormat:L,$ZodSuccess:Tc,$ZodSymbol:hc,$ZodTemplateLiteral:Zc,$ZodTransform:Jr,$ZodTuple:gn,$ZodType:A,$ZodULID:Xu,$ZodURL:Bu,$ZodUUID:Mu,$ZodUndefined:pc,$ZodUnion:Br,$ZodUnknown:Gt,$ZodVoid:bc,$ZodXID:Ku,$brand:ys,$constructor:l,$input:qc,$output:Wc,Doc:Cu,JSONSchema:sp,JSONSchemaGenerator:tr,NEVER:bs,TimePrecision:Kc,_any:gl,_array:zi,_base64:di,_base64url:fi,_bigint:cl,_boolean:sl,_catch:tp,_cidrv4:ci,_cidrv6:li,_coercedBigint:ll,_coercedBoolean:ul,_coercedDate:yl,_coercedNumber:tl,_coercedString:Xc,_cuid:ni,_cuid2:ri,_custom:Nl,_date:bl,_default:Yh,_discriminatedUnion:Mh,_e164:mi,_email:Xr,_emoji:ei,_endsWith:$i,_enum:qh,_file:Ol,_float32:rl,_float64:il,_gt:ze,_gte:oe,_guid:Ht,_includes:bi,_int:nl,_int32:ol,_int64:dl,_intersection:Fh,_ipv4:si,_ipv6:ui,_isoDate:Hc,_isoDateTime:Gc,_isoDuration:Qc,_isoTime:Yc,_jwt:hi,_ksuid:ai,_lazy:op,_length:bn,_literal:Xh,_lowercase:vi,_lt:xe,_lte:ce,_map:Jh,_max:ce,_maxLength:_n,_maxSize:vn,_mime:wi,_min:oe,_minLength:Je,_minSize:ft,_multipleOf:dt,_nan:$l,_nanoid:ti,_nativeEnum:Vh,_negative:kl,_never:vl,_nonnegative:Il,_nonoptional:Qh,_nonpositive:Sl,_normalize:ki,_null:pl,_nullable:Hh,_number:el,_optional:Gh,_overwrite:je,_parse:Ur,_parseAsync:Pr,_pipe:np,_positive:wl,_promise:ap,_property:xl,_readonly:rp,_record:Bh,_refine:jl,_regex:gi,_safeParse:Ar,_safeParseAsync:Er,_set:Wh,_size:pi,_startsWith:yi,_string:Vc,_stringFormat:Pl,_stringbool:Ul,_success:ep,_symbol:ml,_templateLiteral:ip,_toLowerCase:Ii,_toUpperCase:xi,_transform:Kh,_trim:Si,_tuple:zl,_uint32:al,_uint64:fl,_ulid:ii,_undefined:hl,_union:Lh,_unknown:Yt,_uppercase:_i,_url:Qr,_uuid:Kr,_uuidv4:Gr,_uuidv6:Hr,_uuidv7:Yr,_void:_l,_xid:oi,clone:he,config:ee,flattenError:Nr,formatError:jr,function:El,globalConfig:Xt,globalRegistry:Se,isValidBase64:Cr,isValidBase64URL:ac,isValidJWT:cc,locales:Jc,parse:Yn,parseAsync:Qn,prettifyError:Rs,regexes:_u,registry:Vr,safeParse:Cs,safeParseAsync:Zs,toDotPath:Ds,toJSONSchema:Tl,treeifyError:Ts,util:im,version:Zu},Symbol.toStringTag,{value:"Module"})),Oi=l("ZodISODateTime",(e,t)=>{Hu.init(e,t),M.init(e,t)});function Dl(e){return Gc(Oi,e)}const Ni=l("ZodISODate",(e,t)=>{Yu.init(e,t),M.init(e,t)});function Rl(e){return Hc(Ni,e)}const ji=l("ZodISOTime",(e,t)=>{Qu.init(e,t),M.init(e,t)});function Cl(e){return Yc(ji,e)}const Ui=l("ZodISODuration",(e,t)=>{ec.init(e,t),M.init(e,t)});function Zl(e){return Qc(Ui,e)}const cp=Object.freeze(Object.defineProperty({__proto__:null,ZodISODate:Ni,ZodISODateTime:Oi,ZodISODuration:Ui,ZodISOTime:ji,date:Rl,datetime:Dl,duration:Zl,time:Cl},Symbol.toStringTag,{value:"Module"})),Ll=(e,t)=>{Or.init(e,t),e.name="ZodError",Object.defineProperties(e,{format:{value:r=>jr(e,r)},flatten:{value:r=>Nr(e,r)},addIssue:{value:r=>e.issues.push(r)},addIssues:{value:r=>e.issues.push(...r)},isEmpty:{get(){return e.issues.length===0}}})},lp=l("ZodError",Ll),$t=l("ZodError",Ll,{Parent:Error}),Ml=Ur($t),Fl=Pr($t),Bl=Ar($t),Jl=Er($t),T=l("ZodType",(e,t)=>(A.init(e,t),e.def=t,Object.defineProperty(e,"_def",{value:t}),e.check=(...r)=>e.clone({...t,checks:[...t.checks??[],...r.map(i=>typeof i=="function"?{_zod:{check:i,def:{check:"custom"},onattach:[]}}:i)]}),e.clone=(r,i)=>he(e,r,i),e.brand=()=>e,e.register=(r,i)=>(r.add(e,i),e),e.parse=(r,i)=>Ml(e,r,i,{callee:e.parse}),e.safeParse=(r,i)=>Bl(e,r,i),e.parseAsync=async(r,i)=>Fl(e,r,i,{callee:e.parseAsync}),e.safeParseAsync=async(r,i)=>Jl(e,r,i),e.spa=e.safeParseAsync,e.refine=(r,i)=>e.check(Ud(r,i)),e.superRefine=r=>e.check(Pd(r)),e.overwrite=r=>e.check(je(r)),e.optional=()=>tn(e),e.nullable=()=>nn(e),e.nullish=()=>tn(nn(e)),e.nonoptional=r=>bd(e,r),e.array=()=>Yi(e),e.or=r=>xn([e,r]),e.and=r=>od(e,r),e.transform=r=>rn(e,no(r)),e.default=r=>gd(e,r),e.prefault=r=>_d(e,r),e.catch=r=>wd(e,r),e.pipe=r=>rn(e,r),e.readonly=()=>Id(e),e.describe=r=>{const i=e.clone();return Se.add(i,{description:r}),i},Object.defineProperty(e,"description",{get(){var r;return(r=Se.get(e))==null?void 0:r.description},configurable:!0}),e.meta=(...r)=>{if(r.length===0)return Se.get(e);const i=e.clone();return Se.add(i,r[0]),i},e.isOptional=()=>e.safeParse(void 0).success,e.isNullable=()=>e.safeParse(null).success,e)),Pi=l("_ZodString",(e,t)=>{yt.init(e,t),T.init(e,t);const r=e._zod.bag;e.format=r.format??null,e.minLength=r.minimum??null,e.maxLength=r.maximum??null,e.regex=(...i)=>e.check(gi(...i)),e.includes=(...i)=>e.check(bi(...i)),e.startsWith=(...i)=>e.check(yi(...i)),e.endsWith=(...i)=>e.check($i(...i)),e.min=(...i)=>e.check(Je(...i)),e.max=(...i)=>e.check(_n(...i)),e.length=(...i)=>e.check(bn(...i)),e.nonempty=(...i)=>e.check(Je(1,...i)),e.lowercase=i=>e.check(vi(i)),e.uppercase=i=>e.check(_i(i)),e.trim=()=>e.check(Si()),e.normalize=(...i)=>e.check(ki(...i)),e.toLowerCase=()=>e.check(Ii()),e.toUpperCase=()=>e.check(xi())}),yn=l("ZodString",(e,t)=>{yt.init(e,t),Pi.init(e,t),e.email=r=>e.check(Xr(Ai,r)),e.url=r=>e.check(Qr(Ei,r)),e.jwt=r=>e.check(hi(Ki,r)),e.emoji=r=>e.check(ei(Ti,r)),e.guid=r=>e.check(Ht(Qt,r)),e.uuid=r=>e.check(Kr(ve,r)),e.uuidv4=r=>e.check(Gr(ve,r)),e.uuidv6=r=>e.check(Hr(ve,r)),e.uuidv7=r=>e.check(Yr(ve,r)),e.nanoid=r=>e.check(ti(Di,r)),e.guid=r=>e.check(Ht(Qt,r)),e.cuid=r=>e.check(ni(Ri,r)),e.cuid2=r=>e.check(ri(Ci,r)),e.ulid=r=>e.check(ii(Zi,r)),e.base64=r=>e.check(di(qi,r)),e.base64url=r=>e.check(fi(Vi,r)),e.xid=r=>e.check(oi(Li,r)),e.ksuid=r=>e.check(ai(Mi,r)),e.ipv4=r=>e.check(si(Fi,r)),e.ipv6=r=>e.check(ui(Bi,r)),e.cidrv4=r=>e.check(ci(Ji,r)),e.cidrv6=r=>e.check(li(Wi,r)),e.e164=r=>e.check(mi(Xi,r)),e.datetime=r=>e.check(Dl(r)),e.date=r=>e.check(Rl(r)),e.time=r=>e.check(Cl(r)),e.duration=r=>e.check(Zl(r))});function nr(e){return Vc(yn,e)}const M=l("ZodStringFormat",(e,t)=>{L.init(e,t),Pi.init(e,t)}),Ai=l("ZodEmail",(e,t)=>{Fu.init(e,t),M.init(e,t)});function dp(e){return Xr(Ai,e)}const Qt=l("ZodGUID",(e,t)=>{Lu.init(e,t),M.init(e,t)});function fp(e){return Ht(Qt,e)}const ve=l("ZodUUID",(e,t)=>{Mu.init(e,t),M.init(e,t)});function mp(e){return Kr(ve,e)}function hp(e){return Gr(ve,e)}function pp(e){return Hr(ve,e)}function gp(e){return Yr(ve,e)}const Ei=l("ZodURL",(e,t)=>{Bu.init(e,t),M.init(e,t)});function vp(e){return Qr(Ei,e)}const Ti=l("ZodEmoji",(e,t)=>{Ju.init(e,t),M.init(e,t)});function _p(e){return ei(Ti,e)}const Di=l("ZodNanoID",(e,t)=>{Wu.init(e,t),M.init(e,t)});function bp(e){return ti(Di,e)}const Ri=l("ZodCUID",(e,t)=>{qu.init(e,t),M.init(e,t)});function yp(e){return ni(Ri,e)}const Ci=l("ZodCUID2",(e,t)=>{Vu.init(e,t),M.init(e,t)});function $p(e){return ri(Ci,e)}const Zi=l("ZodULID",(e,t)=>{Xu.init(e,t),M.init(e,t)});function wp(e){return ii(Zi,e)}const Li=l("ZodXID",(e,t)=>{Ku.init(e,t),M.init(e,t)});function kp(e){return oi(Li,e)}const Mi=l("ZodKSUID",(e,t)=>{Gu.init(e,t),M.init(e,t)});function Sp(e){return ai(Mi,e)}const Fi=l("ZodIPv4",(e,t)=>{tc.init(e,t),M.init(e,t)});function Ip(e){return si(Fi,e)}const Bi=l("ZodIPv6",(e,t)=>{nc.init(e,t),M.init(e,t)});function xp(e){return ui(Bi,e)}const Ji=l("ZodCIDRv4",(e,t)=>{rc.init(e,t),M.init(e,t)});function zp(e){return ci(Ji,e)}const Wi=l("ZodCIDRv6",(e,t)=>{ic.init(e,t),M.init(e,t)});function Op(e){return li(Wi,e)}const qi=l("ZodBase64",(e,t)=>{oc.init(e,t),M.init(e,t)});function Np(e){return di(qi,e)}const Vi=l("ZodBase64URL",(e,t)=>{sc.init(e,t),M.init(e,t)});function jp(e){return fi(Vi,e)}const Xi=l("ZodE164",(e,t)=>{uc.init(e,t),M.init(e,t)});function Up(e){return mi(Xi,e)}const Ki=l("ZodJWT",(e,t)=>{lc.init(e,t),M.init(e,t)});function Pp(e){return hi(Ki,e)}const Wl=l("ZodCustomStringFormat",(e,t)=>{dc.init(e,t),M.init(e,t)});function Ap(e,t,r={}){return Pl(Wl,e,t,r)}const $n=l("ZodNumber",(e,t)=>{Zr.init(e,t),T.init(e,t),e.gt=(i,n)=>e.check(ze(i,n)),e.gte=(i,n)=>e.check(oe(i,n)),e.min=(i,n)=>e.check(oe(i,n)),e.lt=(i,n)=>e.check(xe(i,n)),e.lte=(i,n)=>e.check(ce(i,n)),e.max=(i,n)=>e.check(ce(i,n)),e.int=i=>e.check(rr(i)),e.safe=i=>e.check(rr(i)),e.positive=i=>e.check(ze(0,i)),e.nonnegative=i=>e.check(oe(0,i)),e.negative=i=>e.check(xe(0,i)),e.nonpositive=i=>e.check(ce(0,i)),e.multipleOf=(i,n)=>e.check(dt(i,n)),e.step=(i,n)=>e.check(dt(i,n)),e.finite=()=>e;const r=e._zod.bag;e.minValue=Math.max(r.minimum??Number.NEGATIVE_INFINITY,r.exclusiveMinimum??Number.NEGATIVE_INFINITY)??null,e.maxValue=Math.min(r.maximum??Number.POSITIVE_INFINITY,r.exclusiveMaximum??Number.POSITIVE_INFINITY)??null,e.isInt=(r.format??"").includes("int")||Number.isSafeInteger(r.multipleOf??.5),e.isFinite=!0,e.format=r.format??null});function ql(e){return el($n,e)}const Ve=l("ZodNumberFormat",(e,t)=>{fc.init(e,t),$n.init(e,t)});function rr(e){return nl(Ve,e)}function Ep(e){return rl(Ve,e)}function Tp(e){return il(Ve,e)}function Dp(e){return ol(Ve,e)}function Rp(e){return al(Ve,e)}const wn=l("ZodBoolean",(e,t)=>{Lr.init(e,t),T.init(e,t)});function Vl(e){return sl(wn,e)}const kn=l("ZodBigInt",(e,t)=>{Mr.init(e,t),T.init(e,t),e.gte=(i,n)=>e.check(oe(i,n)),e.min=(i,n)=>e.check(oe(i,n)),e.gt=(i,n)=>e.check(ze(i,n)),e.gte=(i,n)=>e.check(oe(i,n)),e.min=(i,n)=>e.check(oe(i,n)),e.lt=(i,n)=>e.check(xe(i,n)),e.lte=(i,n)=>e.check(ce(i,n)),e.max=(i,n)=>e.check(ce(i,n)),e.positive=i=>e.check(ze(BigInt(0),i)),e.negative=i=>e.check(xe(BigInt(0),i)),e.nonpositive=i=>e.check(ce(BigInt(0),i)),e.nonnegative=i=>e.check(oe(BigInt(0),i)),e.multipleOf=(i,n)=>e.check(dt(i,n));const r=e._zod.bag;e.minValue=r.minimum??null,e.maxValue=r.maximum??null,e.format=r.format??null});function Cp(e){return cl(kn,e)}const Gi=l("ZodBigIntFormat",(e,t)=>{mc.init(e,t),kn.init(e,t)});function Zp(e){return dl(Gi,e)}function Lp(e){return fl(Gi,e)}const Xl=l("ZodSymbol",(e,t)=>{hc.init(e,t),T.init(e,t)});function Mp(e){return ml(Xl,e)}const Kl=l("ZodUndefined",(e,t)=>{pc.init(e,t),T.init(e,t)});function Fp(e){return hl(Kl,e)}const Gl=l("ZodNull",(e,t)=>{gc.init(e,t),T.init(e,t)});function Hl(e){return pl(Gl,e)}const Yl=l("ZodAny",(e,t)=>{vc.init(e,t),T.init(e,t)});function Bp(){return gl(Yl)}const Ql=l("ZodUnknown",(e,t)=>{Gt.init(e,t),T.init(e,t)});function en(){return Yt(Ql)}const ed=l("ZodNever",(e,t)=>{_c.init(e,t),T.init(e,t)});function Sn(e){return vl(ed,e)}const td=l("ZodVoid",(e,t)=>{bc.init(e,t),T.init(e,t)});function Jp(e){return _l(td,e)}const Hi=l("ZodDate",(e,t)=>{yc.init(e,t),T.init(e,t),e.min=(i,n)=>e.check(oe(i,n)),e.max=(i,n)=>e.check(ce(i,n));const r=e._zod.bag;e.minDate=r.minimum?new Date(r.minimum):null,e.maxDate=r.maximum?new Date(r.maximum):null});function Wp(e){return bl(Hi,e)}const nd=l("ZodArray",(e,t)=>{Fr.init(e,t),T.init(e,t),e.element=t.element,e.min=(r,i)=>e.check(Je(r,i)),e.nonempty=r=>e.check(Je(1,r)),e.max=(r,i)=>e.check(_n(r,i)),e.length=(r,i)=>e.check(bn(r,i)),e.unwrap=()=>e.element});function Yi(e,t){return zi(nd,e,t)}function qp(e){const t=e._zod.def.shape;return fd(Object.keys(t))}const In=l("ZodObject",(e,t)=>{$c.init(e,t),T.init(e,t),R(e,"shape",()=>t.shape),e.keyof=()=>ld(Object.keys(e._zod.def.shape)),e.catchall=r=>e.clone({...e._zod.def,catchall:r}),e.passthrough=()=>e.clone({...e._zod.def,catchall:en()}),e.loose=()=>e.clone({...e._zod.def,catchall:en()}),e.strict=()=>e.clone({...e._zod.def,catchall:Sn()}),e.strip=()=>e.clone({...e._zod.def,catchall:void 0}),e.extend=r=>js(e,r),e.merge=r=>Us(e,r),e.pick=r=>Os(e,r),e.omit=r=>Ns(e,r),e.partial=(...r)=>Ps(ro,e,r[0]),e.required=(...r)=>As(io,e,r[0])});function Vp(e,t){const r={type:"object",get shape(){return qe(this,"shape",{...e}),this.shape},...$(t)};return new In(r)}function Xp(e,t){return new In({type:"object",get shape(){return qe(this,"shape",{...e}),this.shape},catchall:Sn(),...$(t)})}function Kp(e,t){return new In({type:"object",get shape(){return qe(this,"shape",{...e}),this.shape},catchall:en(),...$(t)})}const Qi=l("ZodUnion",(e,t)=>{Br.init(e,t),T.init(e,t),e.options=t.options});function xn(e,t){return new Qi({type:"union",options:e,...$(t)})}const rd=l("ZodDiscriminatedUnion",(e,t)=>{Qi.init(e,t),wc.init(e,t)});function Gp(e,t,r){return new rd({type:"union",options:t,discriminator:e,...$(r)})}const id=l("ZodIntersection",(e,t)=>{kc.init(e,t),T.init(e,t)});function od(e,t){return new id({type:"intersection",left:e,right:t})}const ad=l("ZodTuple",(e,t)=>{gn.init(e,t),T.init(e,t),e.rest=r=>e.clone({...e._zod.def,rest:r})});function Hp(e,t,r){const i=t instanceof A,n=i?r:t,o=i?t:null;return new ad({type:"tuple",items:e,rest:o,...$(n)})}const eo=l("ZodRecord",(e,t)=>{Sc.init(e,t),T.init(e,t),e.keyType=t.keyType,e.valueType=t.valueType});function sd(e,t,r){return new eo({type:"record",keyType:e,valueType:t,...$(r)})}function Yp(e,t,r){return new eo({type:"record",keyType:xn([e,Sn()]),valueType:t,...$(r)})}const ud=l("ZodMap",(e,t)=>{Ic.init(e,t),T.init(e,t),e.keyType=t.keyType,e.valueType=t.valueType});function Qp(e,t,r){return new ud({type:"map",keyType:e,valueType:t,...$(r)})}const cd=l("ZodSet",(e,t)=>{xc.init(e,t),T.init(e,t),e.min=(...r)=>e.check(ft(...r)),e.nonempty=r=>e.check(ft(1,r)),e.max=(...r)=>e.check(vn(...r)),e.size=(...r)=>e.check(pi(...r))});function eg(e,t){return new cd({type:"set",valueType:e,...$(t)})}const mt=l("ZodEnum",(e,t)=>{zc.init(e,t),T.init(e,t),e.enum=t.entries,e.options=Object.values(t.entries);const r=new Set(Object.keys(t.entries));e.extract=(i,n)=>{const o={};for(const a of i)if(r.has(a))o[a]=t.entries[a];else throw new Error(`Key ${a} not found in enum`);return new mt({...t,checks:[],...$(n),entries:o})},e.exclude=(i,n)=>{const o={...t.entries};for(const a of i)if(r.has(a))delete o[a];else throw new Error(`Key ${a} not found in enum`);return new mt({...t,checks:[],...$(n),entries:o})}});function ld(e,t){const r=Array.isArray(e)?Object.fromEntries(e.map(i=>[i,i])):e;return new mt({type:"enum",entries:r,...$(t)})}function tg(e,t){return new mt({type:"enum",entries:e,...$(t)})}const dd=l("ZodLiteral",(e,t)=>{Oc.init(e,t),T.init(e,t),e.values=new Set(t.values),Object.defineProperty(e,"value",{get(){if(t.values.length>1)throw new Error("This schema contains multiple valid literal values. Use `.values` instead.");return t.values[0]}})});function fd(e,t){return new dd({type:"literal",values:Array.isArray(e)?e:[e],...$(t)})}const md=l("ZodFile",(e,t)=>{Nc.init(e,t),T.init(e,t),e.min=(r,i)=>e.check(ft(r,i)),e.max=(r,i)=>e.check(vn(r,i)),e.mime=(r,i)=>e.check(wi(Array.isArray(r)?r:[r],i))});function ng(e){return Ol(md,e)}const to=l("ZodTransform",(e,t)=>{Jr.init(e,t),T.init(e,t),e._zod.parse=(r,i)=>{r.addIssue=o=>{if(typeof o=="string")r.issues.push(Fe(o,r.value,t));else{const a=o;a.fatal&&(a.continue=!1),a.code??(a.code="custom"),a.input??(a.input=r.value),a.inst??(a.inst=e),a.continue??(a.continue=!0),r.issues.push(Fe(a))}};const n=t.transform(r.value,r);return n instanceof Promise?n.then(o=>(r.value=o,r)):(r.value=n,r)}});function no(e){return new to({type:"transform",transform:e})}const ro=l("ZodOptional",(e,t)=>{jc.init(e,t),T.init(e,t),e.unwrap=()=>e._zod.def.innerType});function tn(e){return new ro({type:"optional",innerType:e})}const hd=l("ZodNullable",(e,t)=>{Uc.init(e,t),T.init(e,t),e.unwrap=()=>e._zod.def.innerType});function nn(e){return new hd({type:"nullable",innerType:e})}function rg(e){return tn(nn(e))}const pd=l("ZodDefault",(e,t)=>{Pc.init(e,t),T.init(e,t),e.unwrap=()=>e._zod.def.innerType,e.removeDefault=e.unwrap});function gd(e,t){return new pd({type:"default",innerType:e,get defaultValue(){return typeof t=="function"?t():t}})}const vd=l("ZodPrefault",(e,t)=>{Ac.init(e,t),T.init(e,t),e.unwrap=()=>e._zod.def.innerType});function _d(e,t){return new vd({type:"prefault",innerType:e,get defaultValue(){return typeof t=="function"?t():t}})}const io=l("ZodNonOptional",(e,t)=>{Ec.init(e,t),T.init(e,t),e.unwrap=()=>e._zod.def.innerType});function bd(e,t){return new io({type:"nonoptional",innerType:e,...$(t)})}const yd=l("ZodSuccess",(e,t)=>{Tc.init(e,t),T.init(e,t),e.unwrap=()=>e._zod.def.innerType});function ig(e){return new yd({type:"success",innerType:e})}const $d=l("ZodCatch",(e,t)=>{Dc.init(e,t),T.init(e,t),e.unwrap=()=>e._zod.def.innerType,e.removeCatch=e.unwrap});function wd(e,t){return new $d({type:"catch",innerType:e,catchValue:typeof t=="function"?t:()=>t})}const kd=l("ZodNaN",(e,t)=>{Rc.init(e,t),T.init(e,t)});function og(e){return $l(kd,e)}const oo=l("ZodPipe",(e,t)=>{Wr.init(e,t),T.init(e,t),e.in=t.in,e.out=t.out});function rn(e,t){return new oo({type:"pipe",in:e,out:t})}const Sd=l("ZodReadonly",(e,t)=>{Cc.init(e,t),T.init(e,t)});function Id(e){return new Sd({type:"readonly",innerType:e})}const xd=l("ZodTemplateLiteral",(e,t)=>{Zc.init(e,t),T.init(e,t)});function ag(e,t){return new xd({type:"template_literal",parts:e,...$(t)})}const zd=l("ZodLazy",(e,t)=>{Mc.init(e,t),T.init(e,t),e.unwrap=()=>e._zod.def.getter()});function Od(e){return new zd({type:"lazy",getter:e})}const Nd=l("ZodPromise",(e,t)=>{Lc.init(e,t),T.init(e,t),e.unwrap=()=>e._zod.def.innerType});function sg(e){return new Nd({type:"promise",innerType:e})}const zn=l("ZodCustom",(e,t)=>{Fc.init(e,t),T.init(e,t)});function jd(e){const t=new J({check:"custom"});return t._zod.check=e,t}function ug(e,t){return Nl(zn,e??(()=>!0),t)}function Ud(e,t={}){return jl(zn,e,t)}function Pd(e){const t=jd(r=>(r.addIssue=i=>{if(typeof i=="string")r.issues.push(Fe(i,r.value,t._zod.def));else{const n=i;n.fatal&&(n.continue=!1),n.code??(n.code="custom"),n.input??(n.input=r.value),n.inst??(n.inst=t),n.continue??(n.continue=!t._zod.def.abort),r.issues.push(Fe(n))}},e(r.value,r)));return t}function cg(e,t={error:`Input not instance of ${e.name}`}){const r=new zn({type:"custom",check:"custom",fn:i=>i instanceof e,abort:!0,...$(t)});return r._zod.bag.Class=e,r}const lg=(...e)=>Ul({Pipe:oo,Boolean:wn,String:yn,Transform:to},...e);function dg(e){const t=Od(()=>xn([nr(e),ql(),Vl(),Hl(),Yi(t),sd(nr(),t)]));return t}function fg(e,t){return rn(no(e),t)}const mg={invalid_type:"invalid_type",too_big:"too_big",too_small:"too_small",invalid_format:"invalid_format",not_multiple_of:"not_multiple_of",unrecognized_keys:"unrecognized_keys",invalid_union:"invalid_union",invalid_key:"invalid_key",invalid_element:"invalid_element",invalid_value:"invalid_value",custom:"custom"};function hg(e){ee({customError:e})}function pg(){return ee().customError}function gg(e){return Xc(yn,e)}function vg(e){return tl($n,e)}function _g(e){return ul(wn,e)}function bg(e){return ll(kn,e)}function yg(e){return yl(Hi,e)}const $g=Object.freeze(Object.defineProperty({__proto__:null,bigint:bg,boolean:_g,date:yg,number:vg,string:gg},Symbol.toStringTag,{value:"Module"}));ee(Bc());const Ko=Object.freeze(Object.defineProperty({__proto__:null,$brand:ys,$input:qc,$output:Wc,NEVER:bs,TimePrecision:Kc,ZodAny:Yl,ZodArray:nd,ZodBase64:qi,ZodBase64URL:Vi,ZodBigInt:kn,ZodBigIntFormat:Gi,ZodBoolean:wn,ZodCIDRv4:Ji,ZodCIDRv6:Wi,ZodCUID:Ri,ZodCUID2:Ci,ZodCatch:$d,ZodCustom:zn,ZodCustomStringFormat:Wl,ZodDate:Hi,ZodDefault:pd,ZodDiscriminatedUnion:rd,ZodE164:Xi,ZodEmail:Ai,ZodEmoji:Ti,ZodEnum:mt,ZodError:lp,ZodFile:md,ZodGUID:Qt,ZodIPv4:Fi,ZodIPv6:Bi,ZodISODate:Ni,ZodISODateTime:Oi,ZodISODuration:Ui,ZodISOTime:ji,ZodIntersection:id,ZodIssueCode:mg,ZodJWT:Ki,ZodKSUID:Mi,ZodLazy:zd,ZodLiteral:dd,ZodMap:ud,ZodNaN:kd,ZodNanoID:Di,ZodNever:ed,ZodNonOptional:io,ZodNull:Gl,ZodNullable:hd,ZodNumber:$n,ZodNumberFormat:Ve,ZodObject:In,ZodOptional:ro,ZodPipe:oo,ZodPrefault:vd,ZodPromise:Nd,ZodReadonly:Sd,ZodRealError:$t,ZodRecord:eo,ZodSet:cd,ZodString:yn,ZodStringFormat:M,ZodSuccess:yd,ZodSymbol:Xl,ZodTemplateLiteral:xd,ZodTransform:to,ZodTuple:ad,ZodType:T,ZodULID:Zi,ZodURL:Ei,ZodUUID:ve,ZodUndefined:Kl,ZodUnion:Qi,ZodUnknown:Ql,ZodVoid:td,ZodXID:Li,_ZodString:Pi,_default:gd,any:Bp,array:Yi,base64:Np,base64url:jp,bigint:Cp,boolean:Vl,catch:wd,check:jd,cidrv4:zp,cidrv6:Op,clone:he,coerce:$g,config:ee,core:up,cuid:yp,cuid2:$p,custom:ug,date:Wp,discriminatedUnion:Gp,e164:Up,email:dp,emoji:_p,endsWith:$i,enum:ld,file:ng,flattenError:Nr,float32:Ep,float64:Tp,formatError:jr,function:El,getErrorMap:pg,globalRegistry:Se,gt:ze,gte:oe,guid:fp,includes:bi,instanceof:cg,int:rr,int32:Dp,int64:Zp,intersection:od,ipv4:Ip,ipv6:xp,iso:cp,json:dg,jwt:Pp,keyof:qp,ksuid:Sp,lazy:Od,length:bn,literal:fd,locales:Jc,looseObject:Kp,lowercase:vi,lt:xe,lte:ce,map:Qp,maxLength:_n,maxSize:vn,mime:wi,minLength:Je,minSize:ft,multipleOf:dt,nan:og,nanoid:bp,nativeEnum:tg,negative:kl,never:Sn,nonnegative:Il,nonoptional:bd,nonpositive:Sl,normalize:ki,null:Hl,nullable:nn,nullish:rg,number:ql,object:Vp,optional:tn,overwrite:je,parse:Ml,parseAsync:Fl,partialRecord:Yp,pipe:rn,positive:wl,prefault:_d,preprocess:fg,prettifyError:Rs,promise:sg,property:xl,readonly:Id,record:sd,refine:Ud,regex:gi,regexes:_u,registry:Vr,safeParse:Bl,safeParseAsync:Jl,set:eg,setErrorMap:hg,size:pi,startsWith:yi,strictObject:Xp,string:nr,stringFormat:Ap,stringbool:lg,success:ig,superRefine:Pd,symbol:Mp,templateLiteral:ag,toJSONSchema:Tl,toLowerCase:Ii,toUpperCase:xi,transform:no,treeifyError:Ts,trim:Si,tuple:Hp,uint32:Rp,uint64:Lp,ulid:wp,undefined:Fp,union:xn,unknown:en,uppercase:_i,url:vp,uuid:mp,uuidv4:hp,uuidv6:pp,uuidv7:gp,void:Jp,xid:kp},Symbol.toStringTag,{value:"Module"}));var ir;(e=>{e.tools=[],e.handlers=new Map;function t(r,i,n,o){e.tools.push({type:"function",function:{name:r,description:i,parameters:Ko.toJSONSchema(n),strict:!0}}),e.handlers.set(r,o)}t("get_all_nodes","获取所有节点",Ko.object({}),()=>{})})(ir||(ir={}));class wg{constructor(){Nn(this,"openai",new D({apiKey:"",dangerouslyAllowBrowser:!0,fetch:Dd}))}async updateConfig(){this.openai.baseURL=await wt.get("aiApiBaseUrl"),this.openai.apiKey=await wt.get("aiApiKey")}async*chat(t,r){var a,s,u,c;await this.updateConfig();const i=await wt.get("enableStream"),n=await wt.get("openaiResponseType"),o={messages:t,model:r.model,tools:ir.tools};if(i){const p=await this.openai.chat.completions.create({...o,stream:!0,stream_options:{include_usage:!0}});for await(const d of p)yield((s=(a=d.choices[0])==null?void 0:a.delta)==null?void 0:s.content)||""}else{const p=await this.openai.chat.completions.create({...o,stream:!1});n==="response"?yield JSON.stringify(p):yield((c=(u=p.choices[0])==null?void 0:u.message)==null?void 0:c.content)||""}}async getModels(){return await this.updateConfig(),(await this.openai.models.list()).data.map(r=>r.id.replaceAll("models/",""))}}Nn(wg,"className","OpenAIEngine");export{wg as OpenAIEngine};
