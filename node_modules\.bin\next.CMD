@SETLOCAL
@IF NOT DEFINED NODE_PATH (
  @SET "NODE_PATH=D:\ssh\learn\project-graph-origin\node_modules\.pnpm\next@15.4.3_@babel+core@7.2_9cb668e83455c3a04e9d2212d787f7cd\node_modules\next\dist\bin\node_modules;D:\ssh\learn\project-graph-origin\node_modules\.pnpm\next@15.4.3_@babel+core@7.2_9cb668e83455c3a04e9d2212d787f7cd\node_modules\next\dist\node_modules;D:\ssh\learn\project-graph-origin\node_modules\.pnpm\next@15.4.3_@babel+core@7.2_9cb668e83455c3a04e9d2212d787f7cd\node_modules\next\node_modules;D:\ssh\learn\project-graph-origin\node_modules\.pnpm\next@15.4.3_@babel+core@7.2_9cb668e83455c3a04e9d2212d787f7cd\node_modules;D:\ssh\learn\project-graph-origin\node_modules\.pnpm\node_modules"
) ELSE (
  @SET "NODE_PATH=D:\ssh\learn\project-graph-origin\node_modules\.pnpm\next@15.4.3_@babel+core@7.2_9cb668e83455c3a04e9d2212d787f7cd\node_modules\next\dist\bin\node_modules;D:\ssh\learn\project-graph-origin\node_modules\.pnpm\next@15.4.3_@babel+core@7.2_9cb668e83455c3a04e9d2212d787f7cd\node_modules\next\dist\node_modules;D:\ssh\learn\project-graph-origin\node_modules\.pnpm\next@15.4.3_@babel+core@7.2_9cb668e83455c3a04e9d2212d787f7cd\node_modules\next\node_modules;D:\ssh\learn\project-graph-origin\node_modules\.pnpm\next@15.4.3_@babel+core@7.2_9cb668e83455c3a04e9d2212d787f7cd\node_modules;D:\ssh\learn\project-graph-origin\node_modules\.pnpm\node_modules;%NODE_PATH%"
)
@IF EXIST "%~dp0\node.exe" (
  "%~dp0\node.exe"  "%~dp0\..\..\..\node_modules\.pnpm\next@15.4.3_@babel+core@7.2_9cb668e83455c3a04e9d2212d787f7cd\node_modules\next\dist\bin\next" %*
) ELSE (
  @SET PATHEXT=%PATHEXT:;.JS;=;%
  node  "%~dp0\..\..\..\node_modules\.pnpm\next@15.4.3_@babel+core@7.2_9cb668e83455c3a04e9d2212d787f7cd\node_modules\next\dist\bin\next" %*
)
