<!DOCTYPE html><html class="default" lang="en" data-base=".."><head><meta charset="utf-8"/><meta http-equiv="x-ua-compatible" content="IE=edge"/><title>LiveClientRealtimeInput | @google/genai</title><meta name="description" content="Documentation for @google/genai"/><meta name="viewport" content="width=device-width, initial-scale=1"/><link rel="stylesheet" href="../assets/style.css"/><link rel="stylesheet" href="../assets/highlight.css"/><script defer src="../assets/main.js"></script><script async src="../assets/icons.js" id="tsd-icons-script"></script><script async src="../assets/search.js" id="tsd-search-script"></script><script async src="../assets/navigation.js" id="tsd-nav-script"></script></head><body><script>document.documentElement.dataset.theme = localStorage.getItem("tsd-theme") || "os";document.body.style.display="none";setTimeout(() => app?app.showPage():document.body.style.removeProperty("display"),500)</script><header class="tsd-page-toolbar"><div class="tsd-toolbar-contents container"><div class="table-cell" id="tsd-search"><div class="field"><label for="tsd-search-field" class="tsd-widget tsd-toolbar-icon search no-caption"><svg width="16" height="16" viewBox="0 0 16 16" fill="none" aria-hidden="true"><use href="../assets/icons.svg#icon-search"></use></svg></label><input type="text" id="tsd-search-field" aria-label="Search"/></div><div class="field"><div id="tsd-toolbar-links"></div></div><ul class="results"><li class="state loading">Preparing search index...</li><li class="state failure">The search index is not available</li></ul><a href="../index.html" class="title">@google/genai</a></div><div class="table-cell" id="tsd-widgets"><a href="#" class="tsd-widget tsd-toolbar-icon menu no-caption" data-toggle="menu" aria-label="Menu"><svg width="16" height="16" viewBox="0 0 16 16" fill="none" aria-hidden="true"><use href="../assets/icons.svg#icon-menu"></use></svg></a></div></div></header><div class="container container-main"><div class="col-content"><div class="tsd-page-title"><ul class="tsd-breadcrumb"><li><a href="../modules.html">@google/genai</a></li><li><a href="../modules/types.html">types</a></li><li><a href="types.LiveClientRealtimeInput.html">LiveClientRealtimeInput</a></li></ul><h1>Interface LiveClientRealtimeInput</h1></div><section class="tsd-panel tsd-comment"><div class="tsd-comment tsd-typography"><p>User input that is sent in real time.</p>
<p>This is different from <code>LiveClientContent</code> in a few ways:</p>
<ul>
<li>Can be sent continuously without interruption to model generation.</li>
<li>If there is a need to mix data interleaved across the
<code>LiveClientContent</code> and the <code>LiveClientRealtimeInput</code>, server attempts to
optimize for best response, but there are no guarantees.</li>
<li>End of turn is not explicitly specified, but is rather derived from user
activity (for example, end of speech).</li>
<li>Even before the end of turn, the data is processed incrementally
to optimize for a fast start of the response from the model.</li>
<li>Is always assumed to be the user's input (cannot be used to populate
conversation history).</li>
</ul>
</div><div class="tsd-comment tsd-typography"></div></section><div class="tsd-signature"><span class="tsd-signature-keyword">interface</span> <span class="tsd-kind-interface">LiveClientRealtimeInput</span> <span class="tsd-signature-symbol">{</span><br/>    <a class="tsd-kind-property" href="types.LiveClientRealtimeInput.html#activityend">activityEnd</a><span class="tsd-signature-symbol">?:</span> <a href="types.ActivityEnd.html" class="tsd-signature-type tsd-kind-interface">ActivityEnd</a><span class="tsd-signature-symbol">;</span><br/>    <a class="tsd-kind-property" href="types.LiveClientRealtimeInput.html#activitystart">activityStart</a><span class="tsd-signature-symbol">?:</span> <a href="types.ActivityStart.html" class="tsd-signature-type tsd-kind-interface">ActivityStart</a><span class="tsd-signature-symbol">;</span><br/>    <a class="tsd-kind-property" href="types.LiveClientRealtimeInput.html#audio">audio</a><span class="tsd-signature-symbol">?:</span> <a href="types.Blob.html" class="tsd-signature-type tsd-kind-interface">Blob</a><span class="tsd-signature-symbol">;</span><br/>    <a class="tsd-kind-property" href="types.LiveClientRealtimeInput.html#audiostreamend">audioStreamEnd</a><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">;</span><br/>    <a class="tsd-kind-property" href="types.LiveClientRealtimeInput.html#mediachunks">mediaChunks</a><span class="tsd-signature-symbol">?:</span> <a href="types.Blob.html" class="tsd-signature-type tsd-kind-interface">Blob</a><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">;</span><br/>    <a class="tsd-kind-property" href="types.LiveClientRealtimeInput.html#text">text</a><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">;</span><br/>    <a class="tsd-kind-property" href="types.LiveClientRealtimeInput.html#video">video</a><span class="tsd-signature-symbol">?:</span> <a href="types.Blob.html" class="tsd-signature-type tsd-kind-interface">Blob</a><span class="tsd-signature-symbol">;</span><br/><span class="tsd-signature-symbol">}</span></div><aside class="tsd-sources"><ul><li>Defined in src/types.ts:4888</li></ul></aside><section class="tsd-panel-group tsd-index-group"><section class="tsd-panel tsd-index-panel"><details class="tsd-index-content tsd-accordion" open><summary class="tsd-accordion-summary tsd-index-summary"><h5 class="tsd-index-heading uppercase" role="button" aria-expanded="false" tabIndex="0"><svg width="16" height="16" viewBox="0 0 16 16" fill="none" aria-hidden="true"><use href="../assets/icons.svg#icon-chevronSmall"></use></svg> Index</h5></summary><div class="tsd-accordion-details"><section class="tsd-index-section"><h3 class="tsd-index-heading">Properties</h3><div class="tsd-index-list"><a href="types.LiveClientRealtimeInput.html#activityend" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>activity<wbr/>End?</span></a>
<a href="types.LiveClientRealtimeInput.html#activitystart" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>activity<wbr/>Start?</span></a>
<a href="types.LiveClientRealtimeInput.html#audio" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>audio?</span></a>
<a href="types.LiveClientRealtimeInput.html#audiostreamend" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>audio<wbr/>Stream<wbr/>End?</span></a>
<a href="types.LiveClientRealtimeInput.html#mediachunks" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>media<wbr/>Chunks?</span></a>
<a href="types.LiveClientRealtimeInput.html#text" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>text?</span></a>
<a href="types.LiveClientRealtimeInput.html#video" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>video?</span></a>
</div></section></div></details></section></section><details class="tsd-panel-group tsd-member-group tsd-accordion" open><summary class="tsd-accordion-summary" data-key="section-Properties"><h2><svg width="20" height="20" viewBox="0 0 24 24" fill="none" aria-hidden="true"><use href="../assets/icons.svg#icon-chevronDown"></use></svg> Properties</h2></summary><section><section class="tsd-panel tsd-member"><a id="activityend" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><code class="tsd-tag">Optional</code><span>activity<wbr/>End</span><a href="#activityend" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-property">activityEnd</span><span class="tsd-signature-symbol">?:</span> <a href="types.ActivityEnd.html" class="tsd-signature-type tsd-kind-interface">ActivityEnd</a></div><div class="tsd-comment tsd-typography"><p>Marks the end of user activity.</p>
</div><div class="tsd-comment tsd-typography"></div><aside class="tsd-sources"><ul><li>Defined in src/types.ts:4910</li></ul></aside></section><section class="tsd-panel tsd-member"><a id="activitystart" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><code class="tsd-tag">Optional</code><span>activity<wbr/>Start</span><a href="#activitystart" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-property">activityStart</span><span class="tsd-signature-symbol">?:</span> <a href="types.ActivityStart.html" class="tsd-signature-type tsd-kind-interface">ActivityStart</a></div><div class="tsd-comment tsd-typography"><p>Marks the start of user activity.</p>
</div><div class="tsd-comment tsd-typography"></div><aside class="tsd-sources"><ul><li>Defined in src/types.ts:4908</li></ul></aside></section><section class="tsd-panel tsd-member"><a id="audio" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><code class="tsd-tag">Optional</code><span>audio</span><a href="#audio" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-property">audio</span><span class="tsd-signature-symbol">?:</span> <a href="types.Blob.html" class="tsd-signature-type tsd-kind-interface">Blob</a></div><div class="tsd-comment tsd-typography"><p>The realtime audio input stream.</p>
</div><div class="tsd-comment tsd-typography"></div><aside class="tsd-sources"><ul><li>Defined in src/types.ts:4892</li></ul></aside></section><section class="tsd-panel tsd-member"><a id="audiostreamend" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><code class="tsd-tag">Optional</code><span>audio<wbr/>Stream<wbr/>End</span><a href="#audiostreamend" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-property">audioStreamEnd</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">boolean</span></div><div class="tsd-comment tsd-typography"><p>Indicates that the audio stream has ended, e.g. because the microphone was
turned off.</p>
<p>This should only be sent when automatic activity detection is enabled
(which is the default).</p>
<p>The client can reopen the stream by sending an audio message.</p>
</div><div class="tsd-comment tsd-typography"></div><aside class="tsd-sources"><ul><li>Defined in src/types.ts:4902</li></ul></aside></section><section class="tsd-panel tsd-member"><a id="mediachunks" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><code class="tsd-tag">Optional</code><span>media<wbr/>Chunks</span><a href="#mediachunks" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-property">mediaChunks</span><span class="tsd-signature-symbol">?:</span> <a href="types.Blob.html" class="tsd-signature-type tsd-kind-interface">Blob</a><span class="tsd-signature-symbol">[]</span></div><div class="tsd-comment tsd-typography"><p>Inlined bytes data for media input.</p>
</div><div class="tsd-comment tsd-typography"></div><aside class="tsd-sources"><ul><li>Defined in src/types.ts:4890</li></ul></aside></section><section class="tsd-panel tsd-member"><a id="text" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><code class="tsd-tag">Optional</code><span>text</span><a href="#text" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-property">text</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">string</span></div><div class="tsd-comment tsd-typography"><p>The realtime text input stream.</p>
</div><div class="tsd-comment tsd-typography"></div><aside class="tsd-sources"><ul><li>Defined in src/types.ts:4906</li></ul></aside></section><section class="tsd-panel tsd-member"><a id="video" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><code class="tsd-tag">Optional</code><span>video</span><a href="#video" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-property">video</span><span class="tsd-signature-symbol">?:</span> <a href="types.Blob.html" class="tsd-signature-type tsd-kind-interface">Blob</a></div><div class="tsd-comment tsd-typography"><p>The realtime video input stream.</p>
</div><div class="tsd-comment tsd-typography"></div><aside class="tsd-sources"><ul><li>Defined in src/types.ts:4904</li></ul></aside></section></section></details></div><div class="col-sidebar"><div class="page-menu"><div class="tsd-navigation settings"><details class="tsd-accordion"><summary class="tsd-accordion-summary"><h3><svg width="20" height="20" viewBox="0 0 24 24" fill="none" aria-hidden="true"><use href="../assets/icons.svg#icon-chevronDown"></use></svg>Settings</h3></summary><div class="tsd-accordion-details"><div class="tsd-theme-toggle"><label class="settings-label" for="tsd-theme">Theme</label><select id="tsd-theme"><option value="os">OS</option><option value="light">Light</option><option value="dark">Dark</option></select></div></div></details></div><details open class="tsd-accordion tsd-page-navigation"><summary class="tsd-accordion-summary"><h3><svg width="20" height="20" viewBox="0 0 24 24" fill="none" aria-hidden="true"><use href="../assets/icons.svg#icon-chevronDown"></use></svg>On This Page</h3></summary><div class="tsd-accordion-details"><details open class="tsd-accordion tsd-page-navigation-section"><summary class="tsd-accordion-summary" data-key="section-Properties"><svg width="20" height="20" viewBox="0 0 24 24" fill="none" aria-hidden="true"><use href="../assets/icons.svg#icon-chevronDown"></use></svg>Properties</summary><div><a href="#activityend" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>activity<wbr/>End</span></a><a href="#activitystart" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>activity<wbr/>Start</span></a><a href="#audio" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>audio</span></a><a href="#audiostreamend" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>audio<wbr/>Stream<wbr/>End</span></a><a href="#mediachunks" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>media<wbr/>Chunks</span></a><a href="#text" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>text</span></a><a href="#video" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>video</span></a></div></details></div></details></div><div class="site-menu"><nav class="tsd-navigation"><a href="../modules.html">@google/genai</a><ul class="tsd-small-nested-navigation" id="tsd-nav-container"><li>Loading...</li></ul></nav></div></div></div><footer><p class="tsd-generator">Generated using <a href="https://typedoc.org/" target="_blank">TypeDoc</a></p></footer><div class="overlay"></div></body></html>
