var ss=Object.defineProperty;var rs=(r,e,t)=>e in r?ss(r,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):r[e]=t;var Ne=(r,e,t)=>rs(r,typeof e!="symbol"?e+"":e,t);import{u as ns,S as is}from"./index-dHdkUThP.js";function u(r,e,t,s,n){if(typeof e=="function"?r!==e||!0:!e.has(r))throw new TypeError("Cannot write private member to an object whose class did not declare it");return e.set(r,t),t}function a(r,e,t,s){if(t==="a"&&!s)throw new TypeError("Private accessor was defined without a getter");if(typeof e=="function"?r!==e||!s:!e.has(r))throw new TypeError("Cannot read private member from an object whose class did not declare it");return t==="m"?s:t==="a"?s.call(r):s?s.value:e.get(r)}let Et=function(){const{crypto:r}=globalThis;if(r!=null&&r.randomUUID)return Et=r.randomUUID.bind(r),r.randomUUID();const e=new Uint8Array(1),t=r?()=>r.getRandomValues(e)[0]:()=>Math.random()*255&255;return"10000000-1000-4000-8000-100000000000".replace(/[018]/g,s=>(+s^t()&15>>+s/4).toString(16))};function re(r){return typeof r=="object"&&r!==null&&("name"in r&&r.name==="AbortError"||"message"in r&&String(r.message).includes("FetchRequestCanceledException"))}const He=r=>{if(r instanceof Error)return r;if(typeof r=="object"&&r!==null){try{if(Object.prototype.toString.call(r)==="[object Error]"){const e=new Error(r.message,r.cause?{cause:r.cause}:{});return r.stack&&(e.stack=r.stack),r.cause&&!e.cause&&(e.cause=r.cause),r.name&&(e.name=r.name),e}}catch{}try{return new Error(JSON.stringify(r))}catch{}}return new Error(r)};class d extends Error{}class _ extends d{constructor(e,t,s,n){super(`${_.makeMessage(e,t,s)}`),this.status=e,this.headers=n,this.requestID=n==null?void 0:n.get("request-id"),this.error=t}static makeMessage(e,t,s){const n=t!=null&&t.message?typeof t.message=="string"?t.message:JSON.stringify(t.message):t?JSON.stringify(t):s;return e&&n?`${e} ${n}`:e?`${e} status code (no body)`:n||"(no status code or body)"}static generate(e,t,s,n){if(!e||!n)return new Te({message:s,cause:He(t)});const i=t;return e===400?new Tt(e,i,s,n):e===401?new $t(e,i,s,n):e===403?new Ot(e,i,s,n):e===404?new It(e,i,s,n):e===409?new Nt(e,i,s,n):e===422?new vt(e,i,s,n):e===429?new jt(e,i,s,n):e>=500?new Lt(e,i,s,n):new _(e,i,s,n)}}class P extends _{constructor({message:e}={}){super(void 0,void 0,e||"Request was aborted.",void 0)}}class Te extends _{constructor({message:e,cause:t}){super(void 0,void 0,e||"Connection error.",void 0),t&&(this.cause=t)}}class At extends Te{constructor({message:e}={}){super({message:e??"Request timed out."})}}class Tt extends _{}class $t extends _{}class Ot extends _{}class It extends _{}class Nt extends _{}class vt extends _{}class jt extends _{}class Lt extends _{}const as=/^[a-z][a-z0-9+.-]*:/i,os=r=>as.test(r);let Xe=r=>(Xe=Array.isArray,Xe(r)),it=Xe;function at(r){return typeof r!="object"?{}:r??{}}function cs(r){if(!r)return!0;for(const e in r)return!1;return!0}function ls(r,e){return Object.prototype.hasOwnProperty.call(r,e)}const us=(r,e)=>{if(typeof e!="number"||!Number.isInteger(e))throw new d(`${r} must be an integer`);if(e<0)throw new d(`${r} must be a positive integer`);return e},qt=r=>{try{return JSON.parse(r)}catch{return}},hs=r=>new Promise(e=>setTimeout(e,r)),F="0.57.0",ds=()=>typeof window<"u"&&typeof window.document<"u"&&typeof navigator<"u";function fs(){return typeof Deno<"u"&&Deno.build!=null?"deno":typeof EdgeRuntime<"u"?"edge":Object.prototype.toString.call(typeof globalThis.process<"u"?globalThis.process:0)==="[object process]"?"node":"unknown"}const gs=()=>{var t;const r=fs();if(r==="deno")return{"X-Stainless-Lang":"js","X-Stainless-Package-Version":F,"X-Stainless-OS":ct(Deno.build.os),"X-Stainless-Arch":ot(Deno.build.arch),"X-Stainless-Runtime":"deno","X-Stainless-Runtime-Version":typeof Deno.version=="string"?Deno.version:((t=Deno.version)==null?void 0:t.deno)??"unknown"};if(typeof EdgeRuntime<"u")return{"X-Stainless-Lang":"js","X-Stainless-Package-Version":F,"X-Stainless-OS":"Unknown","X-Stainless-Arch":`other:${EdgeRuntime}`,"X-Stainless-Runtime":"edge","X-Stainless-Runtime-Version":globalThis.process.version};if(r==="node")return{"X-Stainless-Lang":"js","X-Stainless-Package-Version":F,"X-Stainless-OS":ct(globalThis.process.platform??"unknown"),"X-Stainless-Arch":ot(globalThis.process.arch??"unknown"),"X-Stainless-Runtime":"node","X-Stainless-Runtime-Version":globalThis.process.version??"unknown"};const e=ms();return e?{"X-Stainless-Lang":"js","X-Stainless-Package-Version":F,"X-Stainless-OS":"Unknown","X-Stainless-Arch":"unknown","X-Stainless-Runtime":`browser:${e.browser}`,"X-Stainless-Runtime-Version":e.version}:{"X-Stainless-Lang":"js","X-Stainless-Package-Version":F,"X-Stainless-OS":"Unknown","X-Stainless-Arch":"unknown","X-Stainless-Runtime":"unknown","X-Stainless-Runtime-Version":"unknown"}};function ms(){if(typeof navigator>"u"||!navigator)return null;const r=[{key:"edge",pattern:/Edge(?:\W+(\d+)\.(\d+)(?:\.(\d+))?)?/},{key:"ie",pattern:/MSIE(?:\W+(\d+)\.(\d+)(?:\.(\d+))?)?/},{key:"ie",pattern:/Trident(?:.*rv\:(\d+)\.(\d+)(?:\.(\d+))?)?/},{key:"chrome",pattern:/Chrome(?:\W+(\d+)\.(\d+)(?:\.(\d+))?)?/},{key:"firefox",pattern:/Firefox(?:\W+(\d+)\.(\d+)(?:\.(\d+))?)?/},{key:"safari",pattern:/(?:Version\W+(\d+)\.(\d+)(?:\.(\d+))?)?(?:\W+Mobile\S*)?\W+Safari/}];for(const{key:e,pattern:t}of r){const s=t.exec(navigator.userAgent);if(s){const n=s[1]||0,i=s[2]||0,o=s[3]||0;return{browser:e,version:`${n}.${i}.${o}`}}}return null}const ot=r=>r==="x32"?"x32":r==="x86_64"||r==="x64"?"x64":r==="arm"?"arm":r==="aarch64"||r==="arm64"?"arm64":r?`other:${r}`:"unknown",ct=r=>(r=r.toLowerCase(),r.includes("ios")?"iOS":r==="android"?"Android":r==="darwin"?"MacOS":r==="win32"?"Windows":r==="freebsd"?"FreeBSD":r==="openbsd"?"OpenBSD":r==="linux"?"Linux":r?`Other:${r}`:"Unknown");let lt;const ps=()=>lt??(lt=gs());function ys(){if(typeof fetch<"u")return fetch;throw new Error("`fetch` is not defined as a global; Either pass `fetch` to the client, `new Anthropic({ fetch })` or polyfill the global, `globalThis.fetch = fetch`")}function Bt(...r){const e=globalThis.ReadableStream;if(typeof e>"u")throw new Error("`ReadableStream` is not defined as a global; You will need to polyfill it, `globalThis.ReadableStream = ReadableStream`");return new e(...r)}function Ut(r){let e=Symbol.asyncIterator in r?r[Symbol.asyncIterator]():r[Symbol.iterator]();return Bt({start(){},async pull(t){const{done:s,value:n}=await e.next();s?t.close():t.enqueue(n)},async cancel(){var t;await((t=e.return)==null?void 0:t.call(e))}})}function ze(r){if(r[Symbol.asyncIterator])return r;const e=r.getReader();return{async next(){try{const t=await e.read();return t!=null&&t.done&&e.releaseLock(),t}catch(t){throw e.releaseLock(),t}},async return(){const t=e.cancel();return e.releaseLock(),await t,{done:!0,value:void 0}},[Symbol.asyncIterator](){return this}}}async function _s(r){var s,n;if(r===null||typeof r!="object")return;if(r[Symbol.asyncIterator]){await((n=(s=r[Symbol.asyncIterator]()).return)==null?void 0:n.call(s));return}const e=r.getReader(),t=e.cancel();e.releaseLock(),await t}const ws=({headers:r,body:e})=>({bodyHeaders:{"content-type":"application/json"},body:JSON.stringify(e)});function bs(r){let e=0;for(const n of r)e+=n.length;const t=new Uint8Array(e);let s=0;for(const n of r)t.set(n,s),s+=n.length;return t}let ut;function Qe(r){let e;return(ut??(e=new globalThis.TextEncoder,ut=e.encode.bind(e)))(r)}let ht;function dt(r){let e;return(ht??(e=new globalThis.TextDecoder,ht=e.decode.bind(e)))(r)}var S,k;class ne{constructor(){S.set(this,void 0),k.set(this,void 0),u(this,S,new Uint8Array),u(this,k,null)}decode(e){if(e==null)return[];const t=e instanceof ArrayBuffer?new Uint8Array(e):typeof e=="string"?Qe(e):e;u(this,S,bs([a(this,S,"f"),t]));const s=[];let n;for(;(n=Ss(a(this,S,"f"),a(this,k,"f")))!=null;){if(n.carriage&&a(this,k,"f")==null){u(this,k,n.index);continue}if(a(this,k,"f")!=null&&(n.index!==a(this,k,"f")+1||n.carriage)){s.push(dt(a(this,S,"f").subarray(0,a(this,k,"f")-1))),u(this,S,a(this,S,"f").subarray(a(this,k,"f"))),u(this,k,null);continue}const i=a(this,k,"f")!==null?n.preceding-1:n.preceding,o=dt(a(this,S,"f").subarray(0,i));s.push(o),u(this,S,a(this,S,"f").subarray(n.index)),u(this,k,null)}return s}flush(){return a(this,S,"f").length?this.decode(`
`):[]}}S=new WeakMap,k=new WeakMap;ne.NEWLINE_CHARS=new Set([`
`,"\r"]);ne.NEWLINE_REGEXP=/\r\n|[\n\r]/g;function Ss(r,e){for(let n=e??0;n<r.length;n++){if(r[n]===10)return{preceding:n,index:n+1,carriage:!1};if(r[n]===13)return{preceding:n,index:n+1,carriage:!0}}return null}function ks(r){for(let s=0;s<r.length-1;s++){if(r[s]===10&&r[s+1]===10||r[s]===13&&r[s+1]===13)return s+2;if(r[s]===13&&r[s+1]===10&&s+3<r.length&&r[s+2]===13&&r[s+3]===10)return s+4}return-1}const Pe={off:0,error:200,warn:300,info:400,debug:500},ft=(r,e,t)=>{if(r){if(ls(Pe,r))return r;w(t).warn(`${e} was set to ${JSON.stringify(r)}, expected one of ${JSON.stringify(Object.keys(Pe))}`)}};function te(){}function ce(r,e,t){return!e||Pe[r]>Pe[t]?te:e[r].bind(e)}const Ms={error:te,warn:te,info:te,debug:te};let gt=new WeakMap;function w(r){const e=r.logger,t=r.logLevel??"off";if(!e)return Ms;const s=gt.get(e);if(s&&s[0]===t)return s[1];const n={error:ce("error",e,t),warn:ce("warn",e,t),info:ce("info",e,t),debug:ce("debug",e,t)};return gt.set(e,[t,n]),n}const L=r=>(r.options&&(r.options={...r.options},delete r.options.headers),r.headers&&(r.headers=Object.fromEntries((r.headers instanceof Headers?[...r.headers]:Object.entries(r.headers)).map(([e,t])=>[e,e.toLowerCase()==="x-api-key"||e.toLowerCase()==="authorization"||e.toLowerCase()==="cookie"||e.toLowerCase()==="set-cookie"?"***":t]))),"retryOfRequestLogID"in r&&(r.retryOfRequestLogID&&(r.retryOf=r.retryOfRequestLogID),delete r.retryOfRequestLogID),r);var H;class T{constructor(e,t,s){this.iterator=e,H.set(this,void 0),this.controller=t,u(this,H,s)}static fromSSEResponse(e,t,s){let n=!1;const i=s?w(s):console;async function*o(){if(n)throw new d("Cannot iterate over a consumed stream, use `.tee()` to split the stream.");n=!0;let c=!1;try{for await(const l of xs(e,t)){if(l.event==="completion")try{yield JSON.parse(l.data)}catch(f){throw i.error("Could not parse message into JSON:",l.data),i.error("From chunk:",l.raw),f}if(l.event==="message_start"||l.event==="message_delta"||l.event==="message_stop"||l.event==="content_block_start"||l.event==="content_block_delta"||l.event==="content_block_stop")try{yield JSON.parse(l.data)}catch(f){throw i.error("Could not parse message into JSON:",l.data),i.error("From chunk:",l.raw),f}if(l.event!=="ping"&&l.event==="error")throw new _(void 0,qt(l.data)??l.data,void 0,e.headers)}c=!0}catch(l){if(re(l))return;throw l}finally{c||t.abort()}}return new T(o,t,s)}static fromReadableStream(e,t,s){let n=!1;async function*i(){const c=new ne,l=ze(e);for await(const f of l)for(const y of c.decode(f))yield y;for(const f of c.flush())yield f}async function*o(){if(n)throw new d("Cannot iterate over a consumed stream, use `.tee()` to split the stream.");n=!0;let c=!1;try{for await(const l of i())c||l&&(yield JSON.parse(l));c=!0}catch(l){if(re(l))return;throw l}finally{c||t.abort()}}return new T(o,t,s)}[(H=new WeakMap,Symbol.asyncIterator)](){return this.iterator()}tee(){const e=[],t=[],s=this.iterator(),n=i=>({next:()=>{if(i.length===0){const o=s.next();e.push(o),t.push(o)}return i.shift()}});return[new T(()=>n(e),this.controller,a(this,H,"f")),new T(()=>n(t),this.controller,a(this,H,"f"))]}toReadableStream(){const e=this;let t;return Bt({async start(){t=e[Symbol.asyncIterator]()},async pull(s){try{const{value:n,done:i}=await t.next();if(i)return s.close();const o=Qe(JSON.stringify(n)+`
`);s.enqueue(o)}catch(n){s.error(n)}},async cancel(){var s;await((s=t.return)==null?void 0:s.call(t))}})}}async function*xs(r,e){if(!r.body)throw e.abort(),typeof globalThis.navigator<"u"&&globalThis.navigator.product==="ReactNative"?new d("The default react-native fetch implementation does not support streaming. Please use expo/fetch: https://docs.expo.dev/versions/latest/sdk/expo/#expofetch-api"):new d("Attempted to iterate over a response with no body");const t=new Ps,s=new ne,n=ze(r.body);for await(const i of Rs(n))for(const o of s.decode(i)){const c=t.decode(o);c&&(yield c)}for(const i of s.flush()){const o=t.decode(i);o&&(yield o)}}async function*Rs(r){let e=new Uint8Array;for await(const t of r){if(t==null)continue;const s=t instanceof ArrayBuffer?new Uint8Array(t):typeof t=="string"?Qe(t):t;let n=new Uint8Array(e.length+s.length);n.set(e),n.set(s,e.length),e=n;let i;for(;(i=ks(e))!==-1;)yield e.slice(0,i),e=e.slice(i)}e.length>0&&(yield e)}class Ps{constructor(){this.event=null,this.data=[],this.chunks=[]}decode(e){if(e.endsWith("\r")&&(e=e.substring(0,e.length-1)),!e){if(!this.event&&!this.data.length)return null;const i={event:this.event,data:this.data.join(`
`),raw:this.chunks};return this.event=null,this.data=[],this.chunks=[],i}if(this.chunks.push(e),e.startsWith(":"))return null;let[t,s,n]=Es(e,":");return n.startsWith(" ")&&(n=n.substring(1)),t==="event"?this.event=n:t==="data"&&this.data.push(n),null}}function Es(r,e){const t=r.indexOf(e);return t!==-1?[r.substring(0,t),e,r.substring(t+e.length)]:[r,"",""]}async function Ft(r,e){const{response:t,requestLogID:s,retryOfRequestLogID:n,startTime:i}=e,o=await(async()=>{var m;if(e.options.stream)return w(r).debug("response",t.status,t.url,t.headers,t.body),e.options.__streamClass?e.options.__streamClass.fromSSEResponse(t,e.controller):T.fromSSEResponse(t,e.controller);if(t.status===204)return null;if(e.options.__binaryResponse)return t;const c=t.headers.get("content-type"),l=(m=c==null?void 0:c.split(";")[0])==null?void 0:m.trim();if((l==null?void 0:l.includes("application/json"))||(l==null?void 0:l.endsWith("+json"))){const b=await t.json();return Wt(b,t)}return await t.text()})();return w(r).debug(`[${s}] response parsed`,L({retryOfRequestLogID:n,url:t.url,status:t.status,body:o,durationMs:Date.now()-i})),o}function Wt(r,e){return!r||typeof r!="object"||Array.isArray(r)?r:Object.defineProperty(r,"_request_id",{value:e.headers.get("request-id"),enumerable:!1})}var se;class $e extends Promise{constructor(e,t,s=Ft){super(n=>{n(null)}),this.responsePromise=t,this.parseResponse=s,se.set(this,void 0),u(this,se,e)}_thenUnwrap(e){return new $e(a(this,se,"f"),this.responsePromise,async(t,s)=>Wt(e(await this.parseResponse(t,s),s),s.response))}asResponse(){return this.responsePromise.then(e=>e.response)}async withResponse(){const[e,t]=await Promise.all([this.parse(),this.asResponse()]);return{data:e,response:t,request_id:t.headers.get("request-id")}}parse(){return this.parsedPromise||(this.parsedPromise=this.responsePromise.then(e=>this.parseResponse(a(this,se,"f"),e))),this.parsedPromise}then(e,t){return this.parse().then(e,t)}catch(e){return this.parse().catch(e)}finally(e){return this.parse().finally(e)}}se=new WeakMap;var le;class As{constructor(e,t,s,n){le.set(this,void 0),u(this,le,e),this.options=n,this.response=t,this.body=s}hasNextPage(){return this.getPaginatedItems().length?this.nextPageRequestOptions()!=null:!1}async getNextPage(){const e=this.nextPageRequestOptions();if(!e)throw new d("No next page expected; please check `.hasNextPage()` before calling `.getNextPage()`.");return await a(this,le,"f").requestAPIList(this.constructor,e)}async*iterPages(){let e=this;for(yield e;e.hasNextPage();)e=await e.getNextPage(),yield e}async*[(le=new WeakMap,Symbol.asyncIterator)](){for await(const e of this.iterPages())for(const t of e.getPaginatedItems())yield t}}class Ts extends $e{constructor(e,t,s){super(e,t,async(n,i)=>new s(n,i.response,await Ft(n,i),i.options))}async*[Symbol.asyncIterator](){const e=await this;for await(const t of e)yield t}}class ie extends As{constructor(e,t,s,n){super(e,t,s,n),this.data=s.data||[],this.has_more=s.has_more||!1,this.first_id=s.first_id||null,this.last_id=s.last_id||null}getPaginatedItems(){return this.data??[]}hasNextPage(){return this.has_more===!1?!1:super.hasNextPage()}nextPageRequestOptions(){var t;if((t=this.options.query)!=null&&t.before_id){const s=this.first_id;return s?{...this.options,query:{...at(this.options.query),before_id:s}}:null}const e=this.last_id;return e?{...this.options,query:{...at(this.options.query),after_id:e}}:null}}const Dt=()=>{var r;if(typeof File>"u"){const{process:e}=globalThis,t=typeof((r=e==null?void 0:e.versions)==null?void 0:r.node)=="string"&&parseInt(e.versions.node.split("."))<20;throw new Error("`File` is not defined as a global, which is required for file uploads."+(t?" Update to Node 20 LTS or newer, or set `globalThis.File` to `import('node:buffer').File`.":""))}};function D(r,e,t){return Dt(),new File(r,e??"unknown_file",t)}function xe(r){return(typeof r=="object"&&r!==null&&("name"in r&&r.name&&String(r.name)||"url"in r&&r.url&&String(r.url)||"filename"in r&&r.filename&&String(r.filename)||"path"in r&&r.path&&String(r.path))||"").split(/[\\/]/).pop()||void 0}const Ct=r=>r!=null&&typeof r=="object"&&typeof r[Symbol.asyncIterator]=="function",$s=async(r,e)=>({...r,body:await Is(r.body,e)}),mt=new WeakMap;function Os(r){const e=typeof r=="function"?r:r.fetch,t=mt.get(e);if(t)return t;const s=(async()=>{try{const n="Response"in e?e.Response:(await e("data:,")).constructor,i=new FormData;return i.toString()!==await new n(i).text()}catch{return!0}})();return mt.set(e,s),s}const Is=async(r,e)=>{if(!await Os(e))throw new TypeError("The provided fetch function does not support file uploads with the current global FormData class.");const t=new FormData;return await Promise.all(Object.entries(r||{}).map(([s,n])=>Je(t,s,n))),t},Ns=r=>r instanceof Blob&&"name"in r,Je=async(r,e,t)=>{if(t!==void 0){if(t==null)throw new TypeError(`Received null for "${e}"; to pass null in FormData, you must use the string 'null'`);if(typeof t=="string"||typeof t=="number"||typeof t=="boolean")r.append(e,String(t));else if(t instanceof Response){let s={};const n=t.headers.get("Content-Type");n&&(s={type:n}),r.append(e,D([await t.blob()],xe(t),s))}else if(Ct(t))r.append(e,D([await new Response(Ut(t)).blob()],xe(t)));else if(Ns(t))r.append(e,D([t],xe(t),{type:t.type}));else if(Array.isArray(t))await Promise.all(t.map(s=>Je(r,e+"[]",s)));else if(typeof t=="object")await Promise.all(Object.entries(t).map(([s,n])=>Je(r,`${e}[${s}]`,n)));else throw new TypeError(`Invalid value given to form, expected a string, number, boolean, object, Array, File or Blob but got ${t} instead`)}},Ht=r=>r!=null&&typeof r=="object"&&typeof r.size=="number"&&typeof r.type=="string"&&typeof r.text=="function"&&typeof r.slice=="function"&&typeof r.arrayBuffer=="function",vs=r=>r!=null&&typeof r=="object"&&typeof r.name=="string"&&typeof r.lastModified=="number"&&Ht(r),js=r=>r!=null&&typeof r=="object"&&typeof r.url=="string"&&typeof r.blob=="function";async function Ls(r,e,t){if(Dt(),r=await r,e||(e=xe(r)),vs(r))return r instanceof File&&e==null&&t==null?r:D([await r.arrayBuffer()],e??r.name,{type:r.type,lastModified:r.lastModified,...t});if(js(r)){const n=await r.blob();return e||(e=new URL(r.url).pathname.split(/[\\/]/).pop()),D(await Ke(n),e,t)}const s=await Ke(r);if(!(t!=null&&t.type)){const n=s.find(i=>typeof i=="object"&&"type"in i&&i.type);typeof n=="string"&&(t={...t,type:n})}return D(s,e,t)}async function Ke(r){var t;let e=[];if(typeof r=="string"||ArrayBuffer.isView(r)||r instanceof ArrayBuffer)e.push(r);else if(Ht(r))e.push(r instanceof Blob?r:await r.arrayBuffer());else if(Ct(r))for await(const s of r)e.push(...await Ke(s));else{const s=(t=r==null?void 0:r.constructor)==null?void 0:t.name;throw new Error(`Unexpected data type: ${typeof r}${s?`; constructor: ${s}`:""}${qs(r)}`)}return e}function qs(r){return typeof r!="object"||r===null?"":`; props: [${Object.getOwnPropertyNames(r).map(t=>`"${t}"`).join(", ")}]`}class N{constructor(e){this._client=e}}const Xt=Symbol.for("brand.privateNullableHeaders");function*Bs(r){if(!r)return;if(Xt in r){const{values:s,nulls:n}=r;yield*s.entries();for(const i of n)yield[i,null];return}let e=!1,t;r instanceof Headers?t=r.entries():it(r)?t=r:(e=!0,t=Object.entries(r??{}));for(let s of t){const n=s[0];if(typeof n!="string")throw new TypeError("expected header name to be a string");const i=it(s[1])?s[1]:[s[1]];let o=!1;for(const c of i)c!==void 0&&(e&&!o&&(o=!0,yield[n,null]),yield[n,c])}}const g=r=>{const e=new Headers,t=new Set;for(const s of r){const n=new Set;for(const[i,o]of Bs(s)){const c=i.toLowerCase();n.has(c)||(e.delete(i),n.add(c)),o===null?(e.delete(i),t.add(c)):(e.append(i,o),t.delete(c))}}return{[Xt]:!0,values:e,nulls:t}};function Jt(r){return r.replace(/[^A-Za-z0-9\-._~!$&'()*+,;=:@]+/g,encodeURIComponent)}const pt=Object.freeze(Object.create(null)),Us=(r=Jt)=>function(t,...s){if(t.length===1)return t[0];let n=!1;const i=[],o=t.reduce((y,m,b)=>{var q;/[?#]/.test(m)&&(n=!0);const h=s[b];let R=(n?encodeURIComponent:r)(""+h);return b!==s.length&&(h==null||typeof h=="object"&&h.toString===((q=Object.getPrototypeOf(Object.getPrototypeOf(h.hasOwnProperty??pt)??pt))==null?void 0:q.toString))&&(R=h+"",i.push({start:y.length+m.length,length:R.length,error:`Value of type ${Object.prototype.toString.call(h).slice(8,-1)} is not a valid path parameter`})),y+m+(b===s.length?"":R)},""),c=o.split(/[?#]/,1)[0],l=new RegExp("(?<=^|\\/)(?:\\.|%2e){1,2}(?=\\/|$)","gi");let f;for(;(f=l.exec(c))!==null;)i.push({start:f.index,length:f[0].length,error:`Value "${f[0]}" can't be safely passed as a path parameter`});if(i.sort((y,m)=>y.start-m.start),i.length>0){let y=0;const m=i.reduce((b,h)=>{const R=" ".repeat(h.start-y),q="^".repeat(h.length);return y=h.start+h.length,b+R+q},"");throw new d(`Path parameters result in path with invalid segments:
${i.map(b=>b.error).join(`
`)}
${o}
${m}`)}return o},E=Us(Jt);class Kt extends N{list(e={},t){const{betas:s,...n}=e??{};return this._client.getAPIList("/v1/files",ie,{query:n,...t,headers:g([{"anthropic-beta":[...s??[],"files-api-2025-04-14"].toString()},t==null?void 0:t.headers])})}delete(e,t={},s){const{betas:n}=t??{};return this._client.delete(E`/v1/files/${e}`,{...s,headers:g([{"anthropic-beta":[...n??[],"files-api-2025-04-14"].toString()},s==null?void 0:s.headers])})}download(e,t={},s){const{betas:n}=t??{};return this._client.get(E`/v1/files/${e}/content`,{...s,headers:g([{"anthropic-beta":[...n??[],"files-api-2025-04-14"].toString(),Accept:"application/binary"},s==null?void 0:s.headers]),__binaryResponse:!0})}retrieveMetadata(e,t={},s){const{betas:n}=t??{};return this._client.get(E`/v1/files/${e}`,{...s,headers:g([{"anthropic-beta":[...n??[],"files-api-2025-04-14"].toString()},s==null?void 0:s.headers])})}upload(e,t){const{betas:s,...n}=e;return this._client.post("/v1/files",$s({body:n,...t,headers:g([{"anthropic-beta":[...s??[],"files-api-2025-04-14"].toString()},t==null?void 0:t.headers])},this._client))}}let Vt=class extends N{retrieve(e,t={},s){const{betas:n}=t??{};return this._client.get(E`/v1/models/${e}?beta=true`,{...s,headers:g([{...(n==null?void 0:n.toString())!=null?{"anthropic-beta":n==null?void 0:n.toString()}:void 0},s==null?void 0:s.headers])})}list(e={},t){const{betas:s,...n}=e??{};return this._client.getAPIList("/v1/models?beta=true",ie,{query:n,...t,headers:g([{...(s==null?void 0:s.toString())!=null?{"anthropic-beta":s==null?void 0:s.toString()}:void 0},t==null?void 0:t.headers])})}};class Oe{constructor(e,t){this.iterator=e,this.controller=t}async*decoder(){const e=new ne;for await(const t of this.iterator)for(const s of e.decode(t))yield JSON.parse(s);for(const t of e.flush())yield JSON.parse(t)}[Symbol.asyncIterator](){return this.decoder()}static fromResponse(e,t){if(!e.body)throw t.abort(),typeof globalThis.navigator<"u"&&globalThis.navigator.product==="ReactNative"?new d("The default react-native fetch implementation does not support streaming. Please use expo/fetch: https://docs.expo.dev/versions/latest/sdk/expo/#expofetch-api"):new d("Attempted to iterate over a response with no body");return new Oe(ze(e.body),t)}}let zt=class extends N{create(e,t){const{betas:s,...n}=e;return this._client.post("/v1/messages/batches?beta=true",{body:n,...t,headers:g([{"anthropic-beta":[...s??[],"message-batches-2024-09-24"].toString()},t==null?void 0:t.headers])})}retrieve(e,t={},s){const{betas:n}=t??{};return this._client.get(E`/v1/messages/batches/${e}?beta=true`,{...s,headers:g([{"anthropic-beta":[...n??[],"message-batches-2024-09-24"].toString()},s==null?void 0:s.headers])})}list(e={},t){const{betas:s,...n}=e??{};return this._client.getAPIList("/v1/messages/batches?beta=true",ie,{query:n,...t,headers:g([{"anthropic-beta":[...s??[],"message-batches-2024-09-24"].toString()},t==null?void 0:t.headers])})}delete(e,t={},s){const{betas:n}=t??{};return this._client.delete(E`/v1/messages/batches/${e}?beta=true`,{...s,headers:g([{"anthropic-beta":[...n??[],"message-batches-2024-09-24"].toString()},s==null?void 0:s.headers])})}cancel(e,t={},s){const{betas:n}=t??{};return this._client.post(E`/v1/messages/batches/${e}/cancel?beta=true`,{...s,headers:g([{"anthropic-beta":[...n??[],"message-batches-2024-09-24"].toString()},s==null?void 0:s.headers])})}async results(e,t={},s){const n=await this.retrieve(e);if(!n.results_url)throw new d(`No batch \`results_url\`; Has it finished processing? ${n.processing_status} - ${n.id}`);const{betas:i}=t??{};return this._client.get(n.results_url,{...s,headers:g([{"anthropic-beta":[...i??[],"message-batches-2024-09-24"].toString(),Accept:"application/binary"},s==null?void 0:s.headers]),stream:!0,__binaryResponse:!0})._thenUnwrap((o,c)=>Oe.fromResponse(c.response,c.controller))}};const Fs=r=>{let e=0,t=[];for(;e<r.length;){let s=r[e];if(s==="\\"){e++;continue}if(s==="{"){t.push({type:"brace",value:"{"}),e++;continue}if(s==="}"){t.push({type:"brace",value:"}"}),e++;continue}if(s==="["){t.push({type:"paren",value:"["}),e++;continue}if(s==="]"){t.push({type:"paren",value:"]"}),e++;continue}if(s===":"){t.push({type:"separator",value:":"}),e++;continue}if(s===","){t.push({type:"delimiter",value:","}),e++;continue}if(s==='"'){let c="",l=!1;for(s=r[++e];s!=='"';){if(e===r.length){l=!0;break}if(s==="\\"){if(e++,e===r.length){l=!0;break}c+=s+r[e],s=r[++e]}else c+=s,s=r[++e]}s=r[++e],l||t.push({type:"string",value:c});continue}if(s&&/\s/.test(s)){e++;continue}let i=/[0-9]/;if(s&&i.test(s)||s==="-"||s==="."){let c="";for(s==="-"&&(c+=s,s=r[++e]);s&&i.test(s)||s===".";)c+=s,s=r[++e];t.push({type:"number",value:c});continue}let o=/[a-z]/i;if(s&&o.test(s)){let c="";for(;s&&o.test(s)&&e!==r.length;)c+=s,s=r[++e];if(c=="true"||c=="false"||c==="null")t.push({type:"name",value:c});else{e++;continue}continue}e++}return t},W=r=>{if(r.length===0)return r;let e=r[r.length-1];switch(e.type){case"separator":return r=r.slice(0,r.length-1),W(r);case"number":let t=e.value[e.value.length-1];if(t==="."||t==="-")return r=r.slice(0,r.length-1),W(r);case"string":let s=r[r.length-2];if((s==null?void 0:s.type)==="delimiter")return r=r.slice(0,r.length-1),W(r);if((s==null?void 0:s.type)==="brace"&&s.value==="{")return r=r.slice(0,r.length-1),W(r);break;case"delimiter":return r=r.slice(0,r.length-1),W(r)}return r},Ws=r=>{let e=[];return r.map(t=>{t.type==="brace"&&(t.value==="{"?e.push("}"):e.splice(e.lastIndexOf("}"),1)),t.type==="paren"&&(t.value==="["?e.push("]"):e.splice(e.lastIndexOf("]"),1))}),e.length>0&&e.reverse().map(t=>{t==="}"?r.push({type:"brace",value:"}"}):t==="]"&&r.push({type:"paren",value:"]"})}),r},Ds=r=>{let e="";return r.map(t=>{switch(t.type){case"string":e+='"'+t.value+'"';break;default:e+=t.value;break}}),e},Qt=r=>JSON.parse(Ds(Ws(W(Fs(r)))));var M,v,X,ue,J,K,he,V,O,z,de,fe,B,ge,me,ve,yt,pe,je,Le,qe,_t;const wt="__json_buf";function bt(r){return r.type==="tool_use"||r.type==="server_tool_use"||r.type==="mcp_tool_use"}class Ee{constructor(){M.add(this),this.messages=[],this.receivedMessages=[],v.set(this,void 0),this.controller=new AbortController,X.set(this,void 0),ue.set(this,()=>{}),J.set(this,()=>{}),K.set(this,void 0),he.set(this,()=>{}),V.set(this,()=>{}),O.set(this,{}),z.set(this,!1),de.set(this,!1),fe.set(this,!1),B.set(this,!1),ge.set(this,void 0),me.set(this,void 0),pe.set(this,e=>{if(u(this,de,!0),re(e)&&(e=new P),e instanceof P)return u(this,fe,!0),this._emit("abort",e);if(e instanceof d)return this._emit("error",e);if(e instanceof Error){const t=new d(e.message);return t.cause=e,this._emit("error",t)}return this._emit("error",new d(String(e)))}),u(this,X,new Promise((e,t)=>{u(this,ue,e,"f"),u(this,J,t,"f")})),u(this,K,new Promise((e,t)=>{u(this,he,e,"f"),u(this,V,t,"f")})),a(this,X,"f").catch(()=>{}),a(this,K,"f").catch(()=>{})}get response(){return a(this,ge,"f")}get request_id(){return a(this,me,"f")}async withResponse(){const e=await a(this,X,"f");if(!e)throw new Error("Could not resolve a `Response` object");return{data:this,response:e,request_id:e.headers.get("request-id")}}static fromReadableStream(e){const t=new Ee;return t._run(()=>t._fromReadableStream(e)),t}static createMessage(e,t,s){const n=new Ee;for(const i of t.messages)n._addMessageParam(i);return n._run(()=>n._createMessage(e,{...t,stream:!0},{...s,headers:{...s==null?void 0:s.headers,"X-Stainless-Helper-Method":"stream"}})),n}_run(e){e().then(()=>{this._emitFinal(),this._emit("end")},a(this,pe,"f"))}_addMessageParam(e){this.messages.push(e)}_addMessage(e,t=!0){this.receivedMessages.push(e),t&&this._emit("message",e)}async _createMessage(e,t,s){var o;const n=s==null?void 0:s.signal;let i;n&&(n.aborted&&this.controller.abort(),i=this.controller.abort.bind(this.controller),n.addEventListener("abort",i));try{a(this,M,"m",je).call(this);const{response:c,data:l}=await e.create({...t,stream:!0},{...s,signal:this.controller.signal}).withResponse();this._connected(c);for await(const f of l)a(this,M,"m",Le).call(this,f);if((o=l.controller.signal)!=null&&o.aborted)throw new P;a(this,M,"m",qe).call(this)}finally{n&&i&&n.removeEventListener("abort",i)}}_connected(e){this.ended||(u(this,ge,e),u(this,me,e==null?void 0:e.headers.get("request-id")),a(this,ue,"f").call(this,e),this._emit("connect"))}get ended(){return a(this,z,"f")}get errored(){return a(this,de,"f")}get aborted(){return a(this,fe,"f")}abort(){this.controller.abort()}on(e,t){return(a(this,O,"f")[e]||(a(this,O,"f")[e]=[])).push({listener:t}),this}off(e,t){const s=a(this,O,"f")[e];if(!s)return this;const n=s.findIndex(i=>i.listener===t);return n>=0&&s.splice(n,1),this}once(e,t){return(a(this,O,"f")[e]||(a(this,O,"f")[e]=[])).push({listener:t,once:!0}),this}emitted(e){return new Promise((t,s)=>{u(this,B,!0),e!=="error"&&this.once("error",s),this.once(e,t)})}async done(){u(this,B,!0),await a(this,K,"f")}get currentMessage(){return a(this,v,"f")}async finalMessage(){return await this.done(),a(this,M,"m",ve).call(this)}async finalText(){return await this.done(),a(this,M,"m",yt).call(this)}_emit(e,...t){if(a(this,z,"f"))return;e==="end"&&(u(this,z,!0),a(this,he,"f").call(this));const s=a(this,O,"f")[e];if(s&&(a(this,O,"f")[e]=s.filter(n=>!n.once),s.forEach(({listener:n})=>n(...t))),e==="abort"){const n=t[0];!a(this,B,"f")&&!(s!=null&&s.length)&&Promise.reject(n),a(this,J,"f").call(this,n),a(this,V,"f").call(this,n),this._emit("end");return}if(e==="error"){const n=t[0];!a(this,B,"f")&&!(s!=null&&s.length)&&Promise.reject(n),a(this,J,"f").call(this,n),a(this,V,"f").call(this,n),this._emit("end")}}_emitFinal(){this.receivedMessages.at(-1)&&this._emit("finalMessage",a(this,M,"m",ve).call(this))}async _fromReadableStream(e,t){var i;const s=t==null?void 0:t.signal;let n;s&&(s.aborted&&this.controller.abort(),n=this.controller.abort.bind(this.controller),s.addEventListener("abort",n));try{a(this,M,"m",je).call(this),this._connected(null);const o=T.fromReadableStream(e,this.controller);for await(const c of o)a(this,M,"m",Le).call(this,c);if((i=o.controller.signal)!=null&&i.aborted)throw new P;a(this,M,"m",qe).call(this)}finally{s&&n&&s.removeEventListener("abort",n)}}[(v=new WeakMap,X=new WeakMap,ue=new WeakMap,J=new WeakMap,K=new WeakMap,he=new WeakMap,V=new WeakMap,O=new WeakMap,z=new WeakMap,de=new WeakMap,fe=new WeakMap,B=new WeakMap,ge=new WeakMap,me=new WeakMap,pe=new WeakMap,M=new WeakSet,ve=function(){if(this.receivedMessages.length===0)throw new d("stream ended without producing a Message with role=assistant");return this.receivedMessages.at(-1)},yt=function(){if(this.receivedMessages.length===0)throw new d("stream ended without producing a Message with role=assistant");const t=this.receivedMessages.at(-1).content.filter(s=>s.type==="text").map(s=>s.text);if(t.length===0)throw new d("stream ended without producing a content block with type=text");return t.join(" ")},je=function(){this.ended||u(this,v,void 0)},Le=function(t){if(this.ended)return;const s=a(this,M,"m",_t).call(this,t);switch(this._emit("streamEvent",t,s),t.type){case"content_block_delta":{const n=s.content.at(-1);switch(t.delta.type){case"text_delta":{n.type==="text"&&this._emit("text",t.delta.text,n.text||"");break}case"citations_delta":{n.type==="text"&&this._emit("citation",t.delta.citation,n.citations??[]);break}case"input_json_delta":{bt(n)&&n.input&&this._emit("inputJson",t.delta.partial_json,n.input);break}case"thinking_delta":{n.type==="thinking"&&this._emit("thinking",t.delta.thinking,n.thinking);break}case"signature_delta":{n.type==="thinking"&&this._emit("signature",n.signature);break}default:t.delta}break}case"message_stop":{this._addMessageParam(s),this._addMessage(s,!0);break}case"content_block_stop":{this._emit("contentBlock",s.content.at(-1));break}case"message_start":{u(this,v,s);break}}},qe=function(){if(this.ended)throw new d("stream has ended, this shouldn't happen");const t=a(this,v,"f");if(!t)throw new d("request ended without sending any chunks");return u(this,v,void 0),t},_t=function(t){let s=a(this,v,"f");if(t.type==="message_start"){if(s)throw new d(`Unexpected event order, got ${t.type} before receiving "message_stop"`);return t.message}if(!s)throw new d(`Unexpected event order, got ${t.type} before "message_start"`);switch(t.type){case"message_stop":return s;case"message_delta":return s.container=t.delta.container,s.stop_reason=t.delta.stop_reason,s.stop_sequence=t.delta.stop_sequence,s.usage.output_tokens=t.usage.output_tokens,t.usage.input_tokens!=null&&(s.usage.input_tokens=t.usage.input_tokens),t.usage.cache_creation_input_tokens!=null&&(s.usage.cache_creation_input_tokens=t.usage.cache_creation_input_tokens),t.usage.cache_read_input_tokens!=null&&(s.usage.cache_read_input_tokens=t.usage.cache_read_input_tokens),t.usage.server_tool_use!=null&&(s.usage.server_tool_use=t.usage.server_tool_use),s;case"content_block_start":return s.content.push(t.content_block),s;case"content_block_delta":{const n=s.content.at(t.index);switch(t.delta.type){case"text_delta":{(n==null?void 0:n.type)==="text"&&(s.content[t.index]={...n,text:(n.text||"")+t.delta.text});break}case"citations_delta":{(n==null?void 0:n.type)==="text"&&(s.content[t.index]={...n,citations:[...n.citations??[],t.delta.citation]});break}case"input_json_delta":{if(n&&bt(n)){let i=n[wt]||"";i+=t.delta.partial_json;const o={...n};if(Object.defineProperty(o,wt,{value:i,enumerable:!1,writable:!0}),i)try{o.input=Qt(i)}catch(c){const l=new d(`Unable to parse tool parameter JSON from model. Please retry your request or adjust your prompt. Error: ${c}. JSON: ${i}`);a(this,pe,"f").call(this,l)}s.content[t.index]=o}break}case"thinking_delta":{(n==null?void 0:n.type)==="thinking"&&(s.content[t.index]={...n,thinking:n.thinking+t.delta.thinking});break}case"signature_delta":{(n==null?void 0:n.type)==="thinking"&&(s.content[t.index]={...n,signature:t.delta.signature});break}default:t.delta}return s}case"content_block_stop":return s}},Symbol.asyncIterator)](){const e=[],t=[];let s=!1;return this.on("streamEvent",n=>{const i=t.shift();i?i.resolve(n):e.push(n)}),this.on("end",()=>{s=!0;for(const n of t)n.resolve(void 0);t.length=0}),this.on("abort",n=>{s=!0;for(const i of t)i.reject(n);t.length=0}),this.on("error",n=>{s=!0;for(const i of t)i.reject(n);t.length=0}),{next:async()=>e.length?{value:e.shift(),done:!1}:s?{value:void 0,done:!0}:new Promise((i,o)=>t.push({resolve:i,reject:o})).then(i=>i?{value:i,done:!1}:{value:void 0,done:!0}),return:async()=>(this.abort(),{value:void 0,done:!0})}}toReadableStream(){return new T(this[Symbol.asyncIterator].bind(this),this.controller).toReadableStream()}}const Gt={"claude-opus-4-20250514":8192,"claude-opus-4-0":8192,"claude-4-opus-20250514":8192,"anthropic.claude-opus-4-20250514-v1:0":8192,"claude-opus-4@20250514":8192},St={"claude-1.3":"November 6th, 2024","claude-1.3-100k":"November 6th, 2024","claude-instant-1.1":"November 6th, 2024","claude-instant-1.1-100k":"November 6th, 2024","claude-instant-1.2":"November 6th, 2024","claude-3-sonnet-20240229":"July 21st, 2025","claude-3-opus-20240229":"January 5th, 2026","claude-2.1":"July 21st, 2025","claude-2.0":"July 21st, 2025"};let Ge=class extends N{constructor(){super(...arguments),this.batches=new zt(this._client)}create(e,t){const{betas:s,...n}=e;n.model in St&&console.warn(`The model '${n.model}' is deprecated and will reach end-of-life on ${St[n.model]}
Please migrate to a newer model. Visit https://docs.anthropic.com/en/docs/resources/model-deprecations for more information.`);let i=this._client._options.timeout;if(!n.stream&&i==null){const o=Gt[n.model]??void 0;i=this._client.calculateNonstreamingTimeout(n.max_tokens,o)}return this._client.post("/v1/messages?beta=true",{body:n,timeout:i??6e5,...t,headers:g([{...(s==null?void 0:s.toString())!=null?{"anthropic-beta":s==null?void 0:s.toString()}:void 0},t==null?void 0:t.headers]),stream:e.stream??!1})}stream(e,t){return Ee.createMessage(this,e,t)}countTokens(e,t){const{betas:s,...n}=e;return this._client.post("/v1/messages/count_tokens?beta=true",{body:n,...t,headers:g([{"anthropic-beta":[...s??[],"token-counting-2024-11-01"].toString()},t==null?void 0:t.headers])})}};Ge.Batches=zt;class ae extends N{constructor(){super(...arguments),this.models=new Vt(this._client),this.messages=new Ge(this._client),this.files=new Kt(this._client)}}ae.Models=Vt;ae.Messages=Ge;ae.Files=Kt;class Yt extends N{create(e,t){const{betas:s,...n}=e;return this._client.post("/v1/complete",{body:n,timeout:this._client._options.timeout??6e5,...t,headers:g([{...(s==null?void 0:s.toString())!=null?{"anthropic-beta":s==null?void 0:s.toString()}:void 0},t==null?void 0:t.headers]),stream:e.stream??!1})}}var x,j,Q,ye,G,Y,_e,Z,I,ee,we,be,U,Se,ke,Be,kt,Ue,Fe,We,De,Mt;const xt="__json_buf";function Rt(r){return r.type==="tool_use"||r.type==="server_tool_use"}class Ae{constructor(){x.add(this),this.messages=[],this.receivedMessages=[],j.set(this,void 0),this.controller=new AbortController,Q.set(this,void 0),ye.set(this,()=>{}),G.set(this,()=>{}),Y.set(this,void 0),_e.set(this,()=>{}),Z.set(this,()=>{}),I.set(this,{}),ee.set(this,!1),we.set(this,!1),be.set(this,!1),U.set(this,!1),Se.set(this,void 0),ke.set(this,void 0),Ue.set(this,e=>{if(u(this,we,!0),re(e)&&(e=new P),e instanceof P)return u(this,be,!0),this._emit("abort",e);if(e instanceof d)return this._emit("error",e);if(e instanceof Error){const t=new d(e.message);return t.cause=e,this._emit("error",t)}return this._emit("error",new d(String(e)))}),u(this,Q,new Promise((e,t)=>{u(this,ye,e,"f"),u(this,G,t,"f")})),u(this,Y,new Promise((e,t)=>{u(this,_e,e,"f"),u(this,Z,t,"f")})),a(this,Q,"f").catch(()=>{}),a(this,Y,"f").catch(()=>{})}get response(){return a(this,Se,"f")}get request_id(){return a(this,ke,"f")}async withResponse(){const e=await a(this,Q,"f");if(!e)throw new Error("Could not resolve a `Response` object");return{data:this,response:e,request_id:e.headers.get("request-id")}}static fromReadableStream(e){const t=new Ae;return t._run(()=>t._fromReadableStream(e)),t}static createMessage(e,t,s){const n=new Ae;for(const i of t.messages)n._addMessageParam(i);return n._run(()=>n._createMessage(e,{...t,stream:!0},{...s,headers:{...s==null?void 0:s.headers,"X-Stainless-Helper-Method":"stream"}})),n}_run(e){e().then(()=>{this._emitFinal(),this._emit("end")},a(this,Ue,"f"))}_addMessageParam(e){this.messages.push(e)}_addMessage(e,t=!0){this.receivedMessages.push(e),t&&this._emit("message",e)}async _createMessage(e,t,s){var o;const n=s==null?void 0:s.signal;let i;n&&(n.aborted&&this.controller.abort(),i=this.controller.abort.bind(this.controller),n.addEventListener("abort",i));try{a(this,x,"m",Fe).call(this);const{response:c,data:l}=await e.create({...t,stream:!0},{...s,signal:this.controller.signal}).withResponse();this._connected(c);for await(const f of l)a(this,x,"m",We).call(this,f);if((o=l.controller.signal)!=null&&o.aborted)throw new P;a(this,x,"m",De).call(this)}finally{n&&i&&n.removeEventListener("abort",i)}}_connected(e){this.ended||(u(this,Se,e),u(this,ke,e==null?void 0:e.headers.get("request-id")),a(this,ye,"f").call(this,e),this._emit("connect"))}get ended(){return a(this,ee,"f")}get errored(){return a(this,we,"f")}get aborted(){return a(this,be,"f")}abort(){this.controller.abort()}on(e,t){return(a(this,I,"f")[e]||(a(this,I,"f")[e]=[])).push({listener:t}),this}off(e,t){const s=a(this,I,"f")[e];if(!s)return this;const n=s.findIndex(i=>i.listener===t);return n>=0&&s.splice(n,1),this}once(e,t){return(a(this,I,"f")[e]||(a(this,I,"f")[e]=[])).push({listener:t,once:!0}),this}emitted(e){return new Promise((t,s)=>{u(this,U,!0),e!=="error"&&this.once("error",s),this.once(e,t)})}async done(){u(this,U,!0),await a(this,Y,"f")}get currentMessage(){return a(this,j,"f")}async finalMessage(){return await this.done(),a(this,x,"m",Be).call(this)}async finalText(){return await this.done(),a(this,x,"m",kt).call(this)}_emit(e,...t){if(a(this,ee,"f"))return;e==="end"&&(u(this,ee,!0),a(this,_e,"f").call(this));const s=a(this,I,"f")[e];if(s&&(a(this,I,"f")[e]=s.filter(n=>!n.once),s.forEach(({listener:n})=>n(...t))),e==="abort"){const n=t[0];!a(this,U,"f")&&!(s!=null&&s.length)&&Promise.reject(n),a(this,G,"f").call(this,n),a(this,Z,"f").call(this,n),this._emit("end");return}if(e==="error"){const n=t[0];!a(this,U,"f")&&!(s!=null&&s.length)&&Promise.reject(n),a(this,G,"f").call(this,n),a(this,Z,"f").call(this,n),this._emit("end")}}_emitFinal(){this.receivedMessages.at(-1)&&this._emit("finalMessage",a(this,x,"m",Be).call(this))}async _fromReadableStream(e,t){var i;const s=t==null?void 0:t.signal;let n;s&&(s.aborted&&this.controller.abort(),n=this.controller.abort.bind(this.controller),s.addEventListener("abort",n));try{a(this,x,"m",Fe).call(this),this._connected(null);const o=T.fromReadableStream(e,this.controller);for await(const c of o)a(this,x,"m",We).call(this,c);if((i=o.controller.signal)!=null&&i.aborted)throw new P;a(this,x,"m",De).call(this)}finally{s&&n&&s.removeEventListener("abort",n)}}[(j=new WeakMap,Q=new WeakMap,ye=new WeakMap,G=new WeakMap,Y=new WeakMap,_e=new WeakMap,Z=new WeakMap,I=new WeakMap,ee=new WeakMap,we=new WeakMap,be=new WeakMap,U=new WeakMap,Se=new WeakMap,ke=new WeakMap,Ue=new WeakMap,x=new WeakSet,Be=function(){if(this.receivedMessages.length===0)throw new d("stream ended without producing a Message with role=assistant");return this.receivedMessages.at(-1)},kt=function(){if(this.receivedMessages.length===0)throw new d("stream ended without producing a Message with role=assistant");const t=this.receivedMessages.at(-1).content.filter(s=>s.type==="text").map(s=>s.text);if(t.length===0)throw new d("stream ended without producing a content block with type=text");return t.join(" ")},Fe=function(){this.ended||u(this,j,void 0)},We=function(t){if(this.ended)return;const s=a(this,x,"m",Mt).call(this,t);switch(this._emit("streamEvent",t,s),t.type){case"content_block_delta":{const n=s.content.at(-1);switch(t.delta.type){case"text_delta":{n.type==="text"&&this._emit("text",t.delta.text,n.text||"");break}case"citations_delta":{n.type==="text"&&this._emit("citation",t.delta.citation,n.citations??[]);break}case"input_json_delta":{Rt(n)&&n.input&&this._emit("inputJson",t.delta.partial_json,n.input);break}case"thinking_delta":{n.type==="thinking"&&this._emit("thinking",t.delta.thinking,n.thinking);break}case"signature_delta":{n.type==="thinking"&&this._emit("signature",n.signature);break}default:t.delta}break}case"message_stop":{this._addMessageParam(s),this._addMessage(s,!0);break}case"content_block_stop":{this._emit("contentBlock",s.content.at(-1));break}case"message_start":{u(this,j,s);break}}},De=function(){if(this.ended)throw new d("stream has ended, this shouldn't happen");const t=a(this,j,"f");if(!t)throw new d("request ended without sending any chunks");return u(this,j,void 0),t},Mt=function(t){let s=a(this,j,"f");if(t.type==="message_start"){if(s)throw new d(`Unexpected event order, got ${t.type} before receiving "message_stop"`);return t.message}if(!s)throw new d(`Unexpected event order, got ${t.type} before "message_start"`);switch(t.type){case"message_stop":return s;case"message_delta":return s.stop_reason=t.delta.stop_reason,s.stop_sequence=t.delta.stop_sequence,s.usage.output_tokens=t.usage.output_tokens,t.usage.input_tokens!=null&&(s.usage.input_tokens=t.usage.input_tokens),t.usage.cache_creation_input_tokens!=null&&(s.usage.cache_creation_input_tokens=t.usage.cache_creation_input_tokens),t.usage.cache_read_input_tokens!=null&&(s.usage.cache_read_input_tokens=t.usage.cache_read_input_tokens),t.usage.server_tool_use!=null&&(s.usage.server_tool_use=t.usage.server_tool_use),s;case"content_block_start":return s.content.push({...t.content_block}),s;case"content_block_delta":{const n=s.content.at(t.index);switch(t.delta.type){case"text_delta":{(n==null?void 0:n.type)==="text"&&(s.content[t.index]={...n,text:(n.text||"")+t.delta.text});break}case"citations_delta":{(n==null?void 0:n.type)==="text"&&(s.content[t.index]={...n,citations:[...n.citations??[],t.delta.citation]});break}case"input_json_delta":{if(n&&Rt(n)){let i=n[xt]||"";i+=t.delta.partial_json;const o={...n};Object.defineProperty(o,xt,{value:i,enumerable:!1,writable:!0}),i&&(o.input=Qt(i)),s.content[t.index]=o}break}case"thinking_delta":{(n==null?void 0:n.type)==="thinking"&&(s.content[t.index]={...n,thinking:n.thinking+t.delta.thinking});break}case"signature_delta":{(n==null?void 0:n.type)==="thinking"&&(s.content[t.index]={...n,signature:t.delta.signature});break}default:t.delta}return s}case"content_block_stop":return s}},Symbol.asyncIterator)](){const e=[],t=[];let s=!1;return this.on("streamEvent",n=>{const i=t.shift();i?i.resolve(n):e.push(n)}),this.on("end",()=>{s=!0;for(const n of t)n.resolve(void 0);t.length=0}),this.on("abort",n=>{s=!0;for(const i of t)i.reject(n);t.length=0}),this.on("error",n=>{s=!0;for(const i of t)i.reject(n);t.length=0}),{next:async()=>e.length?{value:e.shift(),done:!1}:s?{value:void 0,done:!0}:new Promise((i,o)=>t.push({resolve:i,reject:o})).then(i=>i?{value:i,done:!1}:{value:void 0,done:!0}),return:async()=>(this.abort(),{value:void 0,done:!0})}}toReadableStream(){return new T(this[Symbol.asyncIterator].bind(this),this.controller).toReadableStream()}}class Zt extends N{create(e,t){return this._client.post("/v1/messages/batches",{body:e,...t})}retrieve(e,t){return this._client.get(E`/v1/messages/batches/${e}`,t)}list(e={},t){return this._client.getAPIList("/v1/messages/batches",ie,{query:e,...t})}delete(e,t){return this._client.delete(E`/v1/messages/batches/${e}`,t)}cancel(e,t){return this._client.post(E`/v1/messages/batches/${e}/cancel`,t)}async results(e,t){const s=await this.retrieve(e);if(!s.results_url)throw new d(`No batch \`results_url\`; Has it finished processing? ${s.processing_status} - ${s.id}`);return this._client.get(s.results_url,{...t,headers:g([{Accept:"application/binary"},t==null?void 0:t.headers]),stream:!0,__binaryResponse:!0})._thenUnwrap((n,i)=>Oe.fromResponse(i.response,i.controller))}}class Ye extends N{constructor(){super(...arguments),this.batches=new Zt(this._client)}create(e,t){e.model in Pt&&console.warn(`The model '${e.model}' is deprecated and will reach end-of-life on ${Pt[e.model]}
Please migrate to a newer model. Visit https://docs.anthropic.com/en/docs/resources/model-deprecations for more information.`);let s=this._client._options.timeout;if(!e.stream&&s==null){const n=Gt[e.model]??void 0;s=this._client.calculateNonstreamingTimeout(e.max_tokens,n)}return this._client.post("/v1/messages",{body:e,timeout:s??6e5,...t,stream:e.stream??!1})}stream(e,t){return Ae.createMessage(this,e,t)}countTokens(e,t){return this._client.post("/v1/messages/count_tokens",{body:e,...t})}}const Pt={"claude-1.3":"November 6th, 2024","claude-1.3-100k":"November 6th, 2024","claude-instant-1.1":"November 6th, 2024","claude-instant-1.1-100k":"November 6th, 2024","claude-instant-1.2":"November 6th, 2024","claude-3-sonnet-20240229":"July 21st, 2025","claude-3-opus-20240229":"January 5th, 2026","claude-2.1":"July 21st, 2025","claude-2.0":"July 21st, 2025"};Ye.Batches=Zt;class es extends N{retrieve(e,t={},s){const{betas:n}=t??{};return this._client.get(E`/v1/models/${e}`,{...s,headers:g([{...(n==null?void 0:n.toString())!=null?{"anthropic-beta":n==null?void 0:n.toString()}:void 0},s==null?void 0:s.headers])})}list(e={},t){const{betas:s,...n}=e??{};return this._client.getAPIList("/v1/models",ie,{query:n,...t,headers:g([{...(s==null?void 0:s.toString())!=null?{"anthropic-beta":s==null?void 0:s.toString()}:void 0},t==null?void 0:t.headers])})}}var Ce={};const Me=r=>{var e,t,s,n;if(typeof globalThis.process<"u")return((e=Ce==null?void 0:Ce[r])==null?void 0:e.trim())??void 0;if(typeof globalThis.Deno<"u")return(n=(s=(t=globalThis.Deno.env)==null?void 0:t.get)==null?void 0:s.call(t,r))==null?void 0:n.trim()};var Ve,Ze,Re,ts;class p{constructor({baseURL:e=Me("ANTHROPIC_BASE_URL"),apiKey:t=Me("ANTHROPIC_API_KEY")??null,authToken:s=Me("ANTHROPIC_AUTH_TOKEN")??null,...n}={}){Ve.add(this),Re.set(this,void 0);const i={apiKey:t,authToken:s,...n,baseURL:e||"https://api.anthropic.com"};if(!i.dangerouslyAllowBrowser&&ds())throw new d(`It looks like you're running in a browser-like environment.

This is disabled by default, as it risks exposing your secret API credentials to attackers.
If you understand the risks and have appropriate mitigations in place,
you can set the \`dangerouslyAllowBrowser\` option to \`true\`, e.g.,

new Anthropic({ apiKey, dangerouslyAllowBrowser: true });
`);this.baseURL=i.baseURL,this.timeout=i.timeout??Ze.DEFAULT_TIMEOUT,this.logger=i.logger??console;const o="warn";this.logLevel=o,this.logLevel=ft(i.logLevel,"ClientOptions.logLevel",this)??ft(Me("ANTHROPIC_LOG"),"process.env['ANTHROPIC_LOG']",this)??o,this.fetchOptions=i.fetchOptions,this.maxRetries=i.maxRetries??2,this.fetch=i.fetch??ys(),u(this,Re,ws),this._options=i,this.apiKey=t,this.authToken=s}withOptions(e){return new this.constructor({...this._options,baseURL:this.baseURL,maxRetries:this.maxRetries,timeout:this.timeout,logger:this.logger,logLevel:this.logLevel,fetch:this.fetch,fetchOptions:this.fetchOptions,apiKey:this.apiKey,authToken:this.authToken,...e})}defaultQuery(){return this._options.defaultQuery}validateHeaders({values:e,nulls:t}){if(!(this.apiKey&&e.get("x-api-key"))&&!t.has("x-api-key")&&!(this.authToken&&e.get("authorization"))&&!t.has("authorization"))throw new Error('Could not resolve authentication method. Expected either apiKey or authToken to be set. Or for one of the "X-Api-Key" or "Authorization" headers to be explicitly omitted')}async authHeaders(e){return g([await this.apiKeyAuth(e),await this.bearerAuth(e)])}async apiKeyAuth(e){if(this.apiKey!=null)return g([{"X-Api-Key":this.apiKey}])}async bearerAuth(e){if(this.authToken!=null)return g([{Authorization:`Bearer ${this.authToken}`}])}stringifyQuery(e){return Object.entries(e).filter(([t,s])=>typeof s<"u").map(([t,s])=>{if(typeof s=="string"||typeof s=="number"||typeof s=="boolean")return`${encodeURIComponent(t)}=${encodeURIComponent(s)}`;if(s===null)return`${encodeURIComponent(t)}=`;throw new d(`Cannot stringify type ${typeof s}; Expected string, number, boolean, or null. If you need to pass nested query parameters, you can manually encode them, e.g. { query: { 'foo[key1]': value1, 'foo[key2]': value2 } }, and please open a GitHub issue requesting better support for your use case.`)}).join("&")}getUserAgent(){return`${this.constructor.name}/JS ${F}`}defaultIdempotencyKey(){return`stainless-node-retry-${Et()}`}makeStatusError(e,t,s,n){return _.generate(e,t,s,n)}buildURL(e,t,s){const n=!a(this,Ve,"m",ts).call(this)&&s||this.baseURL,i=os(e)?new URL(e):new URL(n+(n.endsWith("/")&&e.startsWith("/")?e.slice(1):e)),o=this.defaultQuery();return cs(o)||(t={...o,...t}),typeof t=="object"&&t&&!Array.isArray(t)&&(i.search=this.stringifyQuery(t)),i.toString()}_calculateNonstreamingTimeout(e){if(3600*e/128e3>600)throw new d("Streaming is strongly recommended for operations that may take longer than 10 minutes. See https://github.com/anthropics/anthropic-sdk-typescript#streaming-responses for more details");return 600*1e3}async prepareOptions(e){}async prepareRequest(e,{url:t,options:s}){}get(e,t){return this.methodRequest("get",e,t)}post(e,t){return this.methodRequest("post",e,t)}patch(e,t){return this.methodRequest("patch",e,t)}put(e,t){return this.methodRequest("put",e,t)}delete(e,t){return this.methodRequest("delete",e,t)}methodRequest(e,t,s){return this.request(Promise.resolve(s).then(n=>({method:e,path:t,...n})))}request(e,t=null){return new $e(this,this.makeRequest(e,t,void 0))}async makeRequest(e,t,s){var et,tt;const n=await e,i=n.maxRetries??this.maxRetries;t==null&&(t=i),await this.prepareOptions(n);const{req:o,url:c,timeout:l}=await this.buildRequest(n,{retryCount:i-t});await this.prepareRequest(o,{url:c,options:n});const f="log_"+(Math.random()*(1<<24)|0).toString(16).padStart(6,"0"),y=s===void 0?"":`, retryOf: ${s}`,m=Date.now();if(w(this).debug(`[${f}] sending request`,L({retryOfRequestLogID:s,method:n.method,url:c,options:n,headers:o.headers})),(et=n.signal)!=null&&et.aborted)throw new P;const b=new AbortController,h=await this.fetchWithTimeout(c,o,l,b).catch(He),R=Date.now();if(h instanceof Error){const $=`retrying, ${t} attempts remaining`;if((tt=n.signal)!=null&&tt.aborted)throw new P;const A=re(h)||/timed? ?out/i.test(String(h)+("cause"in h?String(h.cause):""));if(t)return w(this).info(`[${f}] connection ${A?"timed out":"failed"} - ${$}`),w(this).debug(`[${f}] connection ${A?"timed out":"failed"} (${$})`,L({retryOfRequestLogID:s,url:c,durationMs:R-m,message:h.message})),this.retryRequest(n,t,s??f);throw w(this).info(`[${f}] connection ${A?"timed out":"failed"} - error; no more retries left`),w(this).debug(`[${f}] connection ${A?"timed out":"failed"} (error; no more retries left)`,L({retryOfRequestLogID:s,url:c,durationMs:R-m,message:h.message})),A?new At:new Te({cause:h})}const q=[...h.headers.entries()].filter(([$])=>$==="request-id").map(([$,A])=>", "+$+": "+JSON.stringify(A)).join(""),Ie=`[${f}${y}${q}] ${o.method} ${c} ${h.ok?"succeeded":"failed"} with status ${h.status} in ${R-m}ms`;if(!h.ok){const $=await this.shouldRetry(h);if(t&&$){const oe=`retrying, ${t} attempts remaining`;return await _s(h.body),w(this).info(`${Ie} - ${oe}`),w(this).debug(`[${f}] response error (${oe})`,L({retryOfRequestLogID:s,url:h.url,status:h.status,headers:h.headers,durationMs:R-m})),this.retryRequest(n,t,s??f,h.headers)}const A=$?"error; no more retries left":"error; not retryable";w(this).info(`${Ie} - ${A}`);const st=await h.text().catch(oe=>He(oe).message),rt=qt(st),nt=rt?void 0:st;throw w(this).debug(`[${f}] response error (${A})`,L({retryOfRequestLogID:s,url:h.url,status:h.status,headers:h.headers,message:nt,durationMs:Date.now()-m})),this.makeStatusError(h.status,rt,nt,h.headers)}return w(this).info(Ie),w(this).debug(`[${f}] response start`,L({retryOfRequestLogID:s,url:h.url,status:h.status,headers:h.headers,durationMs:R-m})),{response:h,options:n,controller:b,requestLogID:f,retryOfRequestLogID:s,startTime:m}}getAPIList(e,t,s){return this.requestAPIList(t,{method:"get",path:e,...s})}requestAPIList(e,t){const s=this.makeRequest(t,null,void 0);return new Ts(this,s,e)}async fetchWithTimeout(e,t,s,n){const{signal:i,method:o,...c}=t||{};i&&i.addEventListener("abort",()=>n.abort());const l=setTimeout(()=>n.abort(),s),f=globalThis.ReadableStream&&c.body instanceof globalThis.ReadableStream||typeof c.body=="object"&&c.body!==null&&Symbol.asyncIterator in c.body,y={signal:n.signal,...f?{duplex:"half"}:{},method:"GET",...c};o&&(y.method=o.toUpperCase());try{return await this.fetch.call(void 0,e,y)}finally{clearTimeout(l)}}async shouldRetry(e){const t=e.headers.get("x-should-retry");return t==="true"?!0:t==="false"?!1:e.status===408||e.status===409||e.status===429||e.status>=500}async retryRequest(e,t,s,n){let i;const o=n==null?void 0:n.get("retry-after-ms");if(o){const l=parseFloat(o);Number.isNaN(l)||(i=l)}const c=n==null?void 0:n.get("retry-after");if(c&&!i){const l=parseFloat(c);Number.isNaN(l)?i=Date.parse(c)-Date.now():i=l*1e3}if(!(i&&0<=i&&i<60*1e3)){const l=e.maxRetries??this.maxRetries;i=this.calculateDefaultRetryTimeoutMillis(t,l)}return await hs(i),this.makeRequest(e,t-1,s)}calculateDefaultRetryTimeoutMillis(e,t){const i=t-e,o=Math.min(.5*Math.pow(2,i),8),c=1-Math.random()*.25;return o*c*1e3}calculateNonstreamingTimeout(e,t){if(36e5*e/128e3>6e5||t!=null&&e>t)throw new d("Streaming is strongly recommended for operations that may token longer than 10 minutes. See https://github.com/anthropics/anthropic-sdk-typescript#long-requests for more details");return 6e5}async buildRequest(e,{retryCount:t=0}={}){const s={...e},{method:n,path:i,query:o,defaultBaseURL:c}=s,l=this.buildURL(i,o,c);"timeout"in s&&us("timeout",s.timeout),s.timeout=s.timeout??this.timeout;const{bodyHeaders:f,body:y}=this.buildBody({options:s}),m=await this.buildHeaders({options:e,method:n,bodyHeaders:f,retryCount:t});return{req:{method:n,headers:m,...s.signal&&{signal:s.signal},...globalThis.ReadableStream&&y instanceof globalThis.ReadableStream&&{duplex:"half"},...y&&{body:y},...this.fetchOptions??{},...s.fetchOptions??{}},url:l,timeout:s.timeout}}async buildHeaders({options:e,method:t,bodyHeaders:s,retryCount:n}){let i={};this.idempotencyHeader&&t!=="get"&&(e.idempotencyKey||(e.idempotencyKey=this.defaultIdempotencyKey()),i[this.idempotencyHeader]=e.idempotencyKey);const o=g([i,{Accept:"application/json","User-Agent":this.getUserAgent(),"X-Stainless-Retry-Count":String(n),...e.timeout?{"X-Stainless-Timeout":String(Math.trunc(e.timeout/1e3))}:{},...ps(),...this._options.dangerouslyAllowBrowser?{"anthropic-dangerous-direct-browser-access":"true"}:void 0,"anthropic-version":"2023-06-01"},await this.authHeaders(e),this._options.defaultHeaders,s,e.headers]);return this.validateHeaders(o),o.values}buildBody({options:{body:e,headers:t}}){if(!e)return{bodyHeaders:void 0,body:void 0};const s=g([t]);return ArrayBuffer.isView(e)||e instanceof ArrayBuffer||e instanceof DataView||typeof e=="string"&&s.values.has("content-type")||e instanceof Blob||e instanceof FormData||e instanceof URLSearchParams||globalThis.ReadableStream&&e instanceof globalThis.ReadableStream?{bodyHeaders:void 0,body:e}:typeof e=="object"&&(Symbol.asyncIterator in e||Symbol.iterator in e&&"next"in e&&typeof e.next=="function")?{bodyHeaders:void 0,body:Ut(e)}:a(this,Re,"f").call(this,{body:e,headers:s})}}Ze=p,Re=new WeakMap,Ve=new WeakSet,ts=function(){return this.baseURL!=="https://api.anthropic.com"};p.Anthropic=Ze;p.HUMAN_PROMPT=`

Human:`;p.AI_PROMPT=`

Assistant:`;p.DEFAULT_TIMEOUT=6e5;p.AnthropicError=d;p.APIError=_;p.APIConnectionError=Te;p.APIConnectionTimeoutError=At;p.APIUserAbortError=P;p.NotFoundError=It;p.ConflictError=Nt;p.RateLimitError=jt;p.BadRequestError=Tt;p.AuthenticationError=$t;p.InternalServerError=Lt;p.PermissionDeniedError=Ot;p.UnprocessableEntityError=vt;p.toFile=Ls;class C extends p{constructor(){super(...arguments),this.completions=new Yt(this),this.messages=new Ye(this),this.models=new es(this),this.beta=new ae(this)}}C.Completions=Yt;C.Messages=Ye;C.Models=es;C.Beta=ae;const{HUMAN_PROMPT:Qs,AI_PROMPT:Gs}=C;class Cs{constructor(){Ne(this,"anthropic",new C({apiKey:"",fetch:ns}))}async updateConfig(){this.anthropic.apiKey=await is.get("anthropicApiKey")}async*chat(e,t){await this.updateConfig();const s=e.map(i=>({role:i.role==="assistant"?"assistant":"user",content:i.content})),n=this.anthropic.messages.stream({messages:s,model:t.model,max_tokens:4096});for await(const i of n)i.type==="content_block_delta"&&i.delta.type==="text_delta"&&(yield i.delta.text)}async getModels(){return["claude-3-opus-20240229","claude-3-sonnet-20240229","claude-3-haiku-20240307"]}}Ne(Cs,"className","AnthropicEngine");export{Cs as AnthropicEngine};
