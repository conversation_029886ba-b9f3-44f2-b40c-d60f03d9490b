{"version": 3, "sources": ["../../../../node_modules/.pnpm/@tauri-apps+api@2.7.0/node_modules/@tauri-apps/api/image.js"], "sourcesContent": ["import { Resource, invoke } from './core.js';\n\n// Copyright 2019-2024 Tauri Programme within The Commons Conservancy\n// SPDX-License-Identifier: Apache-2.0\n// SPDX-License-Identifier: MIT\n/** An RGBA Image in row-major order from top to bottom. */\nclass Image extends Resource {\n    /**\n     * Creates an Image from a resource ID. For internal use only.\n     *\n     * @ignore\n     */\n    constructor(rid) {\n        super(rid);\n    }\n    /** Creates a new Image using RGBA data, in row-major order from top to bottom, and with specified width and height. */\n    static async new(rgba, width, height) {\n        return invoke('plugin:image|new', {\n            rgba: transformImage(rgba),\n            width,\n            height\n        }).then((rid) => new Image(rid));\n    }\n    /**\n     * Creates a new image using the provided bytes by inferring the file format.\n     * If the format is known, prefer [@link Image.fromPngBytes] or [@link Image.fromIcoBytes].\n     *\n     * Only `ico` and `png` are supported (based on activated feature flag).\n     *\n     * Note that you need the `image-ico` or `image-png` Cargo features to use this API.\n     * To enable it, change your Cargo.toml file:\n     * ```toml\n     * [dependencies]\n     * tauri = { version = \"...\", features = [\"...\", \"image-png\"] }\n     * ```\n     */\n    static async fromBytes(bytes) {\n        return invoke('plugin:image|from_bytes', {\n            bytes: transformImage(bytes)\n        }).then((rid) => new Image(rid));\n    }\n    /**\n     * Creates a new image using the provided path.\n     *\n     * Only `ico` and `png` are supported (based on activated feature flag).\n     *\n     * Note that you need the `image-ico` or `image-png` Cargo features to use this API.\n     * To enable it, change your Cargo.toml file:\n     * ```toml\n     * [dependencies]\n     * tauri = { version = \"...\", features = [\"...\", \"image-png\"] }\n     * ```\n     */\n    static async fromPath(path) {\n        return invoke('plugin:image|from_path', { path }).then((rid) => new Image(rid));\n    }\n    /** Returns the RGBA data for this image, in row-major order from top to bottom.  */\n    async rgba() {\n        return invoke('plugin:image|rgba', {\n            rid: this.rid\n        }).then((buffer) => new Uint8Array(buffer));\n    }\n    /** Returns the size of this image.  */\n    async size() {\n        return invoke('plugin:image|size', { rid: this.rid });\n    }\n}\n/**\n * Transforms image from various types into a type acceptable by Rust.\n *\n * See [tauri::image::JsImage](https://docs.rs/tauri/2/tauri/image/enum.JsImage.html) for more information.\n * Note the API signature is not stable and might change.\n */\nfunction transformImage(image) {\n    const ret = image == null\n        ? null\n        : typeof image === 'string'\n            ? image\n            : image instanceof Image\n                ? image.rid\n                : image;\n    return ret;\n}\n\nexport { Image, transformImage };\n"], "mappings": ";;;;;;AAMA,IAAM,QAAN,MAAM,eAAc,SAAS;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMzB,YAAY,KAAK;AACb,UAAM,GAAG;AAAA,EACb;AAAA;AAAA,EAEA,aAAa,IAAI,MAAM,OAAO,QAAQ;AAClC,WAAO,OAAO,oBAAoB;AAAA,MAC9B,MAAM,eAAe,IAAI;AAAA,MACzB;AAAA,MACA;AAAA,IACJ,CAAC,EAAE,KAAK,CAAC,QAAQ,IAAI,OAAM,GAAG,CAAC;AAAA,EACnC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAcA,aAAa,UAAU,OAAO;AAC1B,WAAO,OAAO,2BAA2B;AAAA,MACrC,OAAO,eAAe,KAAK;AAAA,IAC/B,CAAC,EAAE,KAAK,CAAC,QAAQ,IAAI,OAAM,GAAG,CAAC;AAAA,EACnC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAaA,aAAa,SAAS,MAAM;AACxB,WAAO,OAAO,0BAA0B,EAAE,KAAK,CAAC,EAAE,KAAK,CAAC,QAAQ,IAAI,OAAM,GAAG,CAAC;AAAA,EAClF;AAAA;AAAA,EAEA,MAAM,OAAO;AACT,WAAO,OAAO,qBAAqB;AAAA,MAC/B,KAAK,KAAK;AAAA,IACd,CAAC,EAAE,KAAK,CAAC,WAAW,IAAI,WAAW,MAAM,CAAC;AAAA,EAC9C;AAAA;AAAA,EAEA,MAAM,OAAO;AACT,WAAO,OAAO,qBAAqB,EAAE,KAAK,KAAK,IAAI,CAAC;AAAA,EACxD;AACJ;AAOA,SAAS,eAAe,OAAO;AAC3B,QAAM,MAAM,SAAS,OACf,OACA,OAAO,UAAU,WACb,QACA,iBAAiB,QACb,MAAM,MACN;AACd,SAAO;AACX;", "names": []}