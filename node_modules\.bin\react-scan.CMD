@SETLOCAL
@IF NOT DEFINED NODE_PATH (
  @SET "NODE_PATH=D:\ssh\learn\project-graph-origin\node_modules\.pnpm\react-scan@0.4.3_@types+rea_e51f6fb62478d2c0db6596a7d6587c59\node_modules\react-scan\bin\node_modules;D:\ssh\learn\project-graph-origin\node_modules\.pnpm\react-scan@0.4.3_@types+rea_e51f6fb62478d2c0db6596a7d6587c59\node_modules\react-scan\node_modules;D:\ssh\learn\project-graph-origin\node_modules\.pnpm\react-scan@0.4.3_@types+rea_e51f6fb62478d2c0db6596a7d6587c59\node_modules;D:\ssh\learn\project-graph-origin\node_modules\.pnpm\node_modules"
) ELSE (
  @SET "NODE_PATH=D:\ssh\learn\project-graph-origin\node_modules\.pnpm\react-scan@0.4.3_@types+rea_e51f6fb62478d2c0db6596a7d6587c59\node_modules\react-scan\bin\node_modules;D:\ssh\learn\project-graph-origin\node_modules\.pnpm\react-scan@0.4.3_@types+rea_e51f6fb62478d2c0db6596a7d6587c59\node_modules\react-scan\node_modules;D:\ssh\learn\project-graph-origin\node_modules\.pnpm\react-scan@0.4.3_@types+rea_e51f6fb62478d2c0db6596a7d6587c59\node_modules;D:\ssh\learn\project-graph-origin\node_modules\.pnpm\node_modules;%NODE_PATH%"
)
@IF EXIST "%~dp0\node.exe" (
  "%~dp0\node.exe"  "%~dp0\..\react-scan\bin\cli.js" %*
) ELSE (
  @SET PATHEXT=%PATHEXT:;.JS;=;%
  node  "%~dp0\..\react-scan\bin\cli.js" %*
)
