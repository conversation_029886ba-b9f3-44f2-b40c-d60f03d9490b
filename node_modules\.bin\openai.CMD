@SETLOCAL
@IF NOT DEFINED NODE_PATH (
  @SET "NODE_PATH=D:\ssh\learn\project-graph-origin\node_modules\.pnpm\openai@5.10.2_ws@8.18.3_zod@3.25.76\node_modules\openai\bin\node_modules;D:\ssh\learn\project-graph-origin\node_modules\.pnpm\openai@5.10.2_ws@8.18.3_zod@3.25.76\node_modules\openai\node_modules;D:\ssh\learn\project-graph-origin\node_modules\.pnpm\openai@5.10.2_ws@8.18.3_zod@3.25.76\node_modules;D:\ssh\learn\project-graph-origin\node_modules\.pnpm\node_modules"
) ELSE (
  @SET "NODE_PATH=D:\ssh\learn\project-graph-origin\node_modules\.pnpm\openai@5.10.2_ws@8.18.3_zod@3.25.76\node_modules\openai\bin\node_modules;D:\ssh\learn\project-graph-origin\node_modules\.pnpm\openai@5.10.2_ws@8.18.3_zod@3.25.76\node_modules\openai\node_modules;D:\ssh\learn\project-graph-origin\node_modules\.pnpm\openai@5.10.2_ws@8.18.3_zod@3.25.76\node_modules;D:\ssh\learn\project-graph-origin\node_modules\.pnpm\node_modules;%NODE_PATH%"
)
@IF EXIST "%~dp0\node.exe" (
  "%~dp0\node.exe"  "%~dp0\..\openai\bin\cli" %*
) ELSE (
  @SET PATHEXT=%PATHEXT:;.JS;=;%
  node  "%~dp0\..\openai\bin\cli" %*
)
