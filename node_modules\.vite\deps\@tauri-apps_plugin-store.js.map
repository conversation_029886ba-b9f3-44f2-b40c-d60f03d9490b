{"version": 3, "sources": ["../../../../node_modules/.pnpm/@tauri-apps+plugin-store@2.3.0/node_modules/@tauri-apps/plugin-store/dist-js/index.js"], "sourcesContent": ["import { listen } from '@tauri-apps/api/event';\nimport { Resource, invoke } from '@tauri-apps/api/core';\n\n// Copyright 2019-2023 Tauri Programme within The Commons Conservancy\n// SPDX-License-Identifier: Apache-2.0\n// SPDX-License-Identifier: MIT\n/**\n * Create a new Store or load the existing store with the path.\n *\n * @example\n * ```typescript\n * import { Store } from '@tauri-apps/api/store';\n * const store = await Store.load('store.json');\n * ```\n *\n * @param path Path to save the store in `app_data_dir`\n * @param options Store configuration options\n */\nasync function load(path, options) {\n    return await Store.load(path, options);\n}\n/**\n * Gets an already loaded store.\n *\n * If the store is not loaded, returns `null`. In this case you must {@link Store.load load} it.\n *\n * This function is more useful when you already know the store is loaded\n * and just need to access its instance. Prefer {@link Store.load} otherwise.\n *\n * @example\n * ```typescript\n * import { getStore } from '@tauri-apps/api/store';\n * const store = await getStore('store.json');\n * ```\n *\n * @param path Path of the store.\n */\nasync function getStore(path) {\n    return await Store.get(path);\n}\n/**\n * A lazy loaded key-value store persisted by the backend layer.\n */\nclass LazyStore {\n    get store() {\n        if (!this._store) {\n            this._store = load(this.path, this.options);\n        }\n        return this._store;\n    }\n    /**\n     * Note that the options are not applied if someone else already created the store\n     * @param path Path to save the store in `app_data_dir`\n     * @param options Store configuration options\n     */\n    constructor(path, options) {\n        this.path = path;\n        this.options = options;\n    }\n    /**\n     * Init/load the store if it's not loaded already\n     */\n    async init() {\n        await this.store;\n    }\n    async set(key, value) {\n        return (await this.store).set(key, value);\n    }\n    async get(key) {\n        return (await this.store).get(key);\n    }\n    async has(key) {\n        return (await this.store).has(key);\n    }\n    async delete(key) {\n        return (await this.store).delete(key);\n    }\n    async clear() {\n        await (await this.store).clear();\n    }\n    async reset() {\n        await (await this.store).reset();\n    }\n    async keys() {\n        return (await this.store).keys();\n    }\n    async values() {\n        return (await this.store).values();\n    }\n    async entries() {\n        return (await this.store).entries();\n    }\n    async length() {\n        return (await this.store).length();\n    }\n    async reload() {\n        await (await this.store).reload();\n    }\n    async save() {\n        await (await this.store).save();\n    }\n    async onKeyChange(key, cb) {\n        return (await this.store).onKeyChange(key, cb);\n    }\n    async onChange(cb) {\n        return (await this.store).onChange(cb);\n    }\n    async close() {\n        if (this._store) {\n            await (await this._store).close();\n        }\n    }\n}\n/**\n * A key-value store persisted by the backend layer.\n */\nclass Store extends Resource {\n    constructor(rid) {\n        super(rid);\n    }\n    /**\n     * Create a new Store or load the existing store with the path.\n     *\n     * @example\n     * ```typescript\n     * import { Store } from '@tauri-apps/api/store';\n     * const store = await Store.load('store.json');\n     * ```\n     *\n     * @param path Path to save the store in `app_data_dir`\n     * @param options Store configuration options\n     */\n    static async load(path, options) {\n        const rid = await invoke('plugin:store|load', {\n            path,\n            ...options\n        });\n        return new Store(rid);\n    }\n    /**\n     * Gets an already loaded store.\n     *\n     * If the store is not loaded, returns `null`. In this case you must {@link Store.load load} it.\n     *\n     * This function is more useful when you already know the store is loaded\n     * and just need to access its instance. Prefer {@link Store.load} otherwise.\n     *\n     * @example\n     * ```typescript\n     * import { Store } from '@tauri-apps/api/store';\n     * let store = await Store.get('store.json');\n     * if (!store) {\n     *   store = await Store.load('store.json');\n     * }\n     * ```\n     *\n     * @param path Path of the store.\n     */\n    static async get(path) {\n        return await invoke('plugin:store|get_store', { path }).then((rid) => (rid ? new Store(rid) : null));\n    }\n    async set(key, value) {\n        await invoke('plugin:store|set', {\n            rid: this.rid,\n            key,\n            value\n        });\n    }\n    async get(key) {\n        const [value, exists] = await invoke('plugin:store|get', {\n            rid: this.rid,\n            key\n        });\n        return exists ? value : undefined;\n    }\n    async has(key) {\n        return await invoke('plugin:store|has', {\n            rid: this.rid,\n            key\n        });\n    }\n    async delete(key) {\n        return await invoke('plugin:store|delete', {\n            rid: this.rid,\n            key\n        });\n    }\n    async clear() {\n        await invoke('plugin:store|clear', { rid: this.rid });\n    }\n    async reset() {\n        await invoke('plugin:store|reset', { rid: this.rid });\n    }\n    async keys() {\n        return await invoke('plugin:store|keys', { rid: this.rid });\n    }\n    async values() {\n        return await invoke('plugin:store|values', { rid: this.rid });\n    }\n    async entries() {\n        return await invoke('plugin:store|entries', { rid: this.rid });\n    }\n    async length() {\n        return await invoke('plugin:store|length', { rid: this.rid });\n    }\n    async reload() {\n        await invoke('plugin:store|reload', { rid: this.rid });\n    }\n    async save() {\n        await invoke('plugin:store|save', { rid: this.rid });\n    }\n    async onKeyChange(key, cb) {\n        return await listen('store://change', (event) => {\n            if (event.payload.resourceId === this.rid && event.payload.key === key) {\n                cb(event.payload.exists ? event.payload.value : undefined);\n            }\n        });\n    }\n    async onChange(cb) {\n        return await listen('store://change', (event) => {\n            if (event.payload.resourceId === this.rid) {\n                cb(event.payload.key, event.payload.exists ? event.payload.value : undefined);\n            }\n        });\n    }\n}\n\nexport { LazyStore, Store, getStore, load };\n"], "mappings": ";;;;;;;;;;AAkBA,eAAe,KAAK,MAAM,SAAS;AAC/B,SAAO,MAAM,MAAM,KAAK,MAAM,OAAO;AACzC;AAiBA,eAAe,SAAS,MAAM;AAC1B,SAAO,MAAM,MAAM,IAAI,IAAI;AAC/B;AAIA,IAAM,YAAN,MAAgB;AAAA,EACZ,IAAI,QAAQ;AACR,QAAI,CAAC,KAAK,QAAQ;AACd,WAAK,SAAS,KAAK,KAAK,MAAM,KAAK,OAAO;AAAA,IAC9C;AACA,WAAO,KAAK;AAAA,EAChB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,YAAY,MAAM,SAAS;AACvB,SAAK,OAAO;AACZ,SAAK,UAAU;AAAA,EACnB;AAAA;AAAA;AAAA;AAAA,EAIA,MAAM,OAAO;AACT,UAAM,KAAK;AAAA,EACf;AAAA,EACA,MAAM,IAAI,KAAK,OAAO;AAClB,YAAQ,MAAM,KAAK,OAAO,IAAI,KAAK,KAAK;AAAA,EAC5C;AAAA,EACA,MAAM,IAAI,KAAK;AACX,YAAQ,MAAM,KAAK,OAAO,IAAI,GAAG;AAAA,EACrC;AAAA,EACA,MAAM,IAAI,KAAK;AACX,YAAQ,MAAM,KAAK,OAAO,IAAI,GAAG;AAAA,EACrC;AAAA,EACA,MAAM,OAAO,KAAK;AACd,YAAQ,MAAM,KAAK,OAAO,OAAO,GAAG;AAAA,EACxC;AAAA,EACA,MAAM,QAAQ;AACV,WAAO,MAAM,KAAK,OAAO,MAAM;AAAA,EACnC;AAAA,EACA,MAAM,QAAQ;AACV,WAAO,MAAM,KAAK,OAAO,MAAM;AAAA,EACnC;AAAA,EACA,MAAM,OAAO;AACT,YAAQ,MAAM,KAAK,OAAO,KAAK;AAAA,EACnC;AAAA,EACA,MAAM,SAAS;AACX,YAAQ,MAAM,KAAK,OAAO,OAAO;AAAA,EACrC;AAAA,EACA,MAAM,UAAU;AACZ,YAAQ,MAAM,KAAK,OAAO,QAAQ;AAAA,EACtC;AAAA,EACA,MAAM,SAAS;AACX,YAAQ,MAAM,KAAK,OAAO,OAAO;AAAA,EACrC;AAAA,EACA,MAAM,SAAS;AACX,WAAO,MAAM,KAAK,OAAO,OAAO;AAAA,EACpC;AAAA,EACA,MAAM,OAAO;AACT,WAAO,MAAM,KAAK,OAAO,KAAK;AAAA,EAClC;AAAA,EACA,MAAM,YAAY,KAAK,IAAI;AACvB,YAAQ,MAAM,KAAK,OAAO,YAAY,KAAK,EAAE;AAAA,EACjD;AAAA,EACA,MAAM,SAAS,IAAI;AACf,YAAQ,MAAM,KAAK,OAAO,SAAS,EAAE;AAAA,EACzC;AAAA,EACA,MAAM,QAAQ;AACV,QAAI,KAAK,QAAQ;AACb,aAAO,MAAM,KAAK,QAAQ,MAAM;AAAA,IACpC;AAAA,EACJ;AACJ;AAIA,IAAM,QAAN,MAAM,eAAc,SAAS;AAAA,EACzB,YAAY,KAAK;AACb,UAAM,GAAG;AAAA,EACb;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAaA,aAAa,KAAK,MAAM,SAAS;AAC7B,UAAM,MAAM,MAAM,OAAO,qBAAqB;AAAA,MAC1C;AAAA,MACA,GAAG;AAAA,IACP,CAAC;AACD,WAAO,IAAI,OAAM,GAAG;AAAA,EACxB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAoBA,aAAa,IAAI,MAAM;AACnB,WAAO,MAAM,OAAO,0BAA0B,EAAE,KAAK,CAAC,EAAE,KAAK,CAAC,QAAS,MAAM,IAAI,OAAM,GAAG,IAAI,IAAK;AAAA,EACvG;AAAA,EACA,MAAM,IAAI,KAAK,OAAO;AAClB,UAAM,OAAO,oBAAoB;AAAA,MAC7B,KAAK,KAAK;AAAA,MACV;AAAA,MACA;AAAA,IACJ,CAAC;AAAA,EACL;AAAA,EACA,MAAM,IAAI,KAAK;AACX,UAAM,CAAC,OAAO,MAAM,IAAI,MAAM,OAAO,oBAAoB;AAAA,MACrD,KAAK,KAAK;AAAA,MACV;AAAA,IACJ,CAAC;AACD,WAAO,SAAS,QAAQ;AAAA,EAC5B;AAAA,EACA,MAAM,IAAI,KAAK;AACX,WAAO,MAAM,OAAO,oBAAoB;AAAA,MACpC,KAAK,KAAK;AAAA,MACV;AAAA,IACJ,CAAC;AAAA,EACL;AAAA,EACA,MAAM,OAAO,KAAK;AACd,WAAO,MAAM,OAAO,uBAAuB;AAAA,MACvC,KAAK,KAAK;AAAA,MACV;AAAA,IACJ,CAAC;AAAA,EACL;AAAA,EACA,MAAM,QAAQ;AACV,UAAM,OAAO,sBAAsB,EAAE,KAAK,KAAK,IAAI,CAAC;AAAA,EACxD;AAAA,EACA,MAAM,QAAQ;AACV,UAAM,OAAO,sBAAsB,EAAE,KAAK,KAAK,IAAI,CAAC;AAAA,EACxD;AAAA,EACA,MAAM,OAAO;AACT,WAAO,MAAM,OAAO,qBAAqB,EAAE,KAAK,KAAK,IAAI,CAAC;AAAA,EAC9D;AAAA,EACA,MAAM,SAAS;AACX,WAAO,MAAM,OAAO,uBAAuB,EAAE,KAAK,KAAK,IAAI,CAAC;AAAA,EAChE;AAAA,EACA,MAAM,UAAU;AACZ,WAAO,MAAM,OAAO,wBAAwB,EAAE,KAAK,KAAK,IAAI,CAAC;AAAA,EACjE;AAAA,EACA,MAAM,SAAS;AACX,WAAO,MAAM,OAAO,uBAAuB,EAAE,KAAK,KAAK,IAAI,CAAC;AAAA,EAChE;AAAA,EACA,MAAM,SAAS;AACX,UAAM,OAAO,uBAAuB,EAAE,KAAK,KAAK,IAAI,CAAC;AAAA,EACzD;AAAA,EACA,MAAM,OAAO;AACT,UAAM,OAAO,qBAAqB,EAAE,KAAK,KAAK,IAAI,CAAC;AAAA,EACvD;AAAA,EACA,MAAM,YAAY,KAAK,IAAI;AACvB,WAAO,MAAM,OAAO,kBAAkB,CAAC,UAAU;AAC7C,UAAI,MAAM,QAAQ,eAAe,KAAK,OAAO,MAAM,QAAQ,QAAQ,KAAK;AACpE,WAAG,MAAM,QAAQ,SAAS,MAAM,QAAQ,QAAQ,MAAS;AAAA,MAC7D;AAAA,IACJ,CAAC;AAAA,EACL;AAAA,EACA,MAAM,SAAS,IAAI;AACf,WAAO,MAAM,OAAO,kBAAkB,CAAC,UAAU;AAC7C,UAAI,MAAM,QAAQ,eAAe,KAAK,KAAK;AACvC,WAAG,MAAM,QAAQ,KAAK,MAAM,QAAQ,SAAS,MAAM,QAAQ,QAAQ,MAAS;AAAA,MAChF;AAAA,IACJ,CAAC;AAAA,EACL;AACJ;", "names": []}