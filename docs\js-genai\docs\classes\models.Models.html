<!DOCTYPE html><html class="default" lang="en" data-base=".."><head><meta charset="utf-8"/><meta http-equiv="x-ua-compatible" content="IE=edge"/><title>Models | @google/genai</title><meta name="description" content="Documentation for @google/genai"/><meta name="viewport" content="width=device-width, initial-scale=1"/><link rel="stylesheet" href="../assets/style.css"/><link rel="stylesheet" href="../assets/highlight.css"/><script defer src="../assets/main.js"></script><script async src="../assets/icons.js" id="tsd-icons-script"></script><script async src="../assets/search.js" id="tsd-search-script"></script><script async src="../assets/navigation.js" id="tsd-nav-script"></script></head><body><script>document.documentElement.dataset.theme = localStorage.getItem("tsd-theme") || "os";document.body.style.display="none";setTimeout(() => app?app.showPage():document.body.style.removeProperty("display"),500)</script><header class="tsd-page-toolbar"><div class="tsd-toolbar-contents container"><div class="table-cell" id="tsd-search"><div class="field"><label for="tsd-search-field" class="tsd-widget tsd-toolbar-icon search no-caption"><svg width="16" height="16" viewBox="0 0 16 16" fill="none" aria-hidden="true"><use href="../assets/icons.svg#icon-search"></use></svg></label><input type="text" id="tsd-search-field" aria-label="Search"/></div><div class="field"><div id="tsd-toolbar-links"></div></div><ul class="results"><li class="state loading">Preparing search index...</li><li class="state failure">The search index is not available</li></ul><a href="../index.html" class="title">@google/genai</a></div><div class="table-cell" id="tsd-widgets"><a href="#" class="tsd-widget tsd-toolbar-icon menu no-caption" data-toggle="menu" aria-label="Menu"><svg width="16" height="16" viewBox="0 0 16 16" fill="none" aria-hidden="true"><use href="../assets/icons.svg#icon-menu"></use></svg></a></div></div></header><div class="container container-main"><div class="col-content"><div class="tsd-page-title"><ul class="tsd-breadcrumb"><li><a href="../modules.html">@google/genai</a></li><li><a href="../modules/models.html">models</a></li><li><a href="models.Models.html">Models</a></li></ul><h1>Class Models</h1></div><section class="tsd-panel tsd-hierarchy" data-refl="215"><h4>Hierarchy</h4><ul class="tsd-hierarchy"><li class="tsd-hierarchy-item"><span class="tsd-signature-type">BaseModule</span><ul class="tsd-hierarchy"><li class="tsd-hierarchy-item"><span class="tsd-hierarchy-target">Models</span></li></ul></li></ul></section><aside class="tsd-sources"><ul><li>Defined in src/models.ts:30</li></ul></aside><section class="tsd-panel-group tsd-index-group"><section class="tsd-panel tsd-index-panel"><details class="tsd-index-content tsd-accordion" open><summary class="tsd-accordion-summary tsd-index-summary"><h5 class="tsd-index-heading uppercase" role="button" aria-expanded="false" tabIndex="0"><svg width="16" height="16" viewBox="0 0 16 16" fill="none" aria-hidden="true"><use href="../assets/icons.svg#icon-chevronSmall"></use></svg> Index</h5></summary><div class="tsd-accordion-details"><section class="tsd-index-section"><h3 class="tsd-index-heading">Constructors</h3><div class="tsd-index-list"><a href="models.Models.html#constructor" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Constructor"><use href="../assets/icons.svg#icon-512"></use></svg><span>constructor</span></a>
</div></section><section class="tsd-index-section"><h3 class="tsd-index-heading">Methods</h3><div class="tsd-index-list"><a href="models.Models.html#computetokens" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Method"><use href="../assets/icons.svg#icon-2048"></use></svg><span>compute<wbr/>Tokens</span></a>
<a href="models.Models.html#counttokens" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Method"><use href="../assets/icons.svg#icon-2048"></use></svg><span>count<wbr/>Tokens</span></a>
<a href="models.Models.html#delete" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Method"><use href="../assets/icons.svg#icon-2048"></use></svg><span>delete</span></a>
<a href="models.Models.html#editimage" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Method"><use href="../assets/icons.svg#icon-2048"></use></svg><span>edit<wbr/>Image</span></a>
<a href="models.Models.html#embedcontent" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Method"><use href="../assets/icons.svg#icon-2048"></use></svg><span>embed<wbr/>Content</span></a>
<a href="models.Models.html#generatecontent" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Method"><use href="../assets/icons.svg#icon-2048"></use></svg><span>generate<wbr/>Content</span></a>
<a href="models.Models.html#generatecontentstream" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Method"><use href="../assets/icons.svg#icon-2048"></use></svg><span>generate<wbr/>Content<wbr/>Stream</span></a>
<a href="models.Models.html#generateimages" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Method"><use href="../assets/icons.svg#icon-2048"></use></svg><span>generate<wbr/>Images</span></a>
<a href="models.Models.html#generatevideos" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Method"><use href="../assets/icons.svg#icon-2048"></use></svg><span>generate<wbr/>Videos</span></a>
<a href="models.Models.html#get" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Method"><use href="../assets/icons.svg#icon-2048"></use></svg><span>get</span></a>
<a href="models.Models.html#list" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Method"><use href="../assets/icons.svg#icon-2048"></use></svg><span>list</span></a>
<a href="models.Models.html#update" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Method"><use href="../assets/icons.svg#icon-2048"></use></svg><span>update</span></a>
<a href="models.Models.html#upscaleimage" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Method"><use href="../assets/icons.svg#icon-2048"></use></svg><span>upscale<wbr/>Image</span></a>
</div></section></div></details></section></section><details class="tsd-panel-group tsd-member-group tsd-accordion" open><summary class="tsd-accordion-summary" data-key="section-Constructors"><h2><svg width="20" height="20" viewBox="0 0 24 24" fill="none" aria-hidden="true"><use href="../assets/icons.svg#icon-chevronDown"></use></svg> Constructors</h2></summary><section><section class="tsd-panel tsd-member"><a id="constructor" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><span>constructor</span><a href="#constructor" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><ul class="tsd-signatures"><li class=""><div class="tsd-signature tsd-anchor-link"><a id="constructormodels" class="tsd-anchor"></a><span class="tsd-signature-keyword">new</span> <span class="tsd-kind-constructor-signature">Models</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">apiClient</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">ApiClient</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <a href="models.Models.html" class="tsd-signature-type tsd-kind-class">Models</a><a href="#constructormodels" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></div><div class="tsd-description"><div class="tsd-parameters"><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameter-list"><li><span><span class="tsd-kind-parameter">apiClient</span>: <span class="tsd-signature-type">ApiClient</span></span></li></ul></div><h4 class="tsd-returns-title">Returns <a href="models.Models.html" class="tsd-signature-type tsd-kind-class">Models</a></h4><aside class="tsd-sources"><p>Overrides BaseModule.constructor</p><ul><li>Defined in src/models.ts:31</li></ul></aside></div></li></ul></section></section></details><details class="tsd-panel-group tsd-member-group tsd-accordion" open><summary class="tsd-accordion-summary" data-key="section-Methods"><h2><svg width="20" height="20" viewBox="0 0 24 24" fill="none" aria-hidden="true"><use href="../assets/icons.svg#icon-chevronDown"></use></svg> Methods</h2></summary><section><section class="tsd-panel tsd-member"><a id="computetokens" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><span>compute<wbr/>Tokens</span><a href="#computetokens" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><ul class="tsd-signatures"><li class=""><div class="tsd-signature tsd-anchor-link"><a id="computetokens-1" class="tsd-anchor"></a><span class="tsd-kind-call-signature">computeTokens</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">params</span><span class="tsd-signature-symbol">:</span> <a href="../interfaces/types.ComputeTokensParameters.html" class="tsd-signature-type tsd-kind-interface">ComputeTokensParameters</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="types.ComputeTokensResponse.html" class="tsd-signature-type tsd-kind-class">ComputeTokensResponse</a><span class="tsd-signature-symbol">&gt;</span><a href="#computetokens-1" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></div><div class="tsd-description"><div class="tsd-comment tsd-typography"><p>Given a list of contents, returns a corresponding TokensInfo containing
the list of tokens and list of token ids.</p>
<p>This method is not supported by the Gemini Developer API.</p>
</div><div class="tsd-parameters"><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameter-list"><li><span><span class="tsd-kind-parameter">params</span>: <a href="../interfaces/types.ComputeTokensParameters.html" class="tsd-signature-type tsd-kind-interface">ComputeTokensParameters</a></span><div class="tsd-comment tsd-typography"><p>The parameters for computing tokens.</p>
</div><div class="tsd-comment tsd-typography"></div></li></ul></div><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="types.ComputeTokensResponse.html" class="tsd-signature-type tsd-kind-class">ComputeTokensResponse</a><span class="tsd-signature-symbol">&gt;</span></h4><p>The response from the API.</p>
<div class="tsd-comment tsd-typography"><div class="tsd-tag-example"><h4 class="tsd-anchor-link"><a id="example" class="tsd-anchor"></a>Example<a href="#example" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h4><pre><code class="ts"><span class="hl-5">const</span><span class="hl-1"> </span><span class="hl-6">response</span><span class="hl-1"> = </span><span class="hl-3">await</span><span class="hl-1"> </span><span class="hl-4">ai</span><span class="hl-1">.</span><span class="hl-4">models</span><span class="hl-1">.</span><span class="hl-0">computeTokens</span><span class="hl-1">({</span><br/><span class="hl-1"> </span><span class="hl-4">model:</span><span class="hl-1"> </span><span class="hl-2">&#39;gemini-2.0-flash&#39;</span><span class="hl-1">,</span><br/><span class="hl-1"> </span><span class="hl-4">contents:</span><span class="hl-1"> </span><span class="hl-2">&#39;What is your name?&#39;</span><br/><span class="hl-1">});</span><br/><span class="hl-4">console</span><span class="hl-1">.</span><span class="hl-0">log</span><span class="hl-1">(</span><span class="hl-4">response</span><span class="hl-1">);</span>
</code><button type="button">Copy</button></pre>

</div></div><aside class="tsd-sources"><ul><li>Defined in src/models.ts:1421</li></ul></aside></div></li></ul></section><section class="tsd-panel tsd-member"><a id="counttokens" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><span>count<wbr/>Tokens</span><a href="#counttokens" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><ul class="tsd-signatures"><li class=""><div class="tsd-signature tsd-anchor-link"><a id="counttokens-1" class="tsd-anchor"></a><span class="tsd-kind-call-signature">countTokens</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">params</span><span class="tsd-signature-symbol">:</span> <a href="../interfaces/types.CountTokensParameters.html" class="tsd-signature-type tsd-kind-interface">CountTokensParameters</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="types.CountTokensResponse.html" class="tsd-signature-type tsd-kind-class">CountTokensResponse</a><span class="tsd-signature-symbol">&gt;</span><a href="#counttokens-1" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></div><div class="tsd-description"><div class="tsd-comment tsd-typography"><p>Counts the number of tokens in the given contents. Multimodal input is
supported for Gemini models.</p>
</div><div class="tsd-parameters"><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameter-list"><li><span><span class="tsd-kind-parameter">params</span>: <a href="../interfaces/types.CountTokensParameters.html" class="tsd-signature-type tsd-kind-interface">CountTokensParameters</a></span><div class="tsd-comment tsd-typography"><p>The parameters for counting tokens.</p>
</div><div class="tsd-comment tsd-typography"></div></li></ul></div><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="types.CountTokensResponse.html" class="tsd-signature-type tsd-kind-class">CountTokensResponse</a><span class="tsd-signature-symbol">&gt;</span></h4><p>The response from the API.</p>
<div class="tsd-comment tsd-typography"><div class="tsd-tag-example"><h4 class="tsd-anchor-link"><a id="example-1" class="tsd-anchor"></a>Example<a href="#example-1" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h4><pre><code class="ts"><span class="hl-5">const</span><span class="hl-1"> </span><span class="hl-6">response</span><span class="hl-1"> = </span><span class="hl-3">await</span><span class="hl-1"> </span><span class="hl-4">ai</span><span class="hl-1">.</span><span class="hl-4">models</span><span class="hl-1">.</span><span class="hl-0">countTokens</span><span class="hl-1">({</span><br/><span class="hl-1"> </span><span class="hl-4">model:</span><span class="hl-1"> </span><span class="hl-2">&#39;gemini-2.0-flash&#39;</span><span class="hl-1">,</span><br/><span class="hl-1"> </span><span class="hl-4">contents:</span><span class="hl-1"> </span><span class="hl-2">&#39;The quick brown fox jumps over the lazy dog.&#39;</span><br/><span class="hl-1">});</span><br/><span class="hl-4">console</span><span class="hl-1">.</span><span class="hl-0">log</span><span class="hl-1">(</span><span class="hl-4">response</span><span class="hl-1">);</span>
</code><button type="button">Copy</button></pre>

</div></div><aside class="tsd-sources"><ul><li>Defined in src/models.ts:1327</li></ul></aside></div></li></ul></section><section class="tsd-panel tsd-member"><a id="delete" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><span>delete</span><a href="#delete" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><ul class="tsd-signatures"><li class=""><div class="tsd-signature tsd-anchor-link"><a id="delete-1" class="tsd-anchor"></a><span class="tsd-kind-call-signature">delete</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">params</span><span class="tsd-signature-symbol">:</span> <a href="../interfaces/types.DeleteModelParameters.html" class="tsd-signature-type tsd-kind-interface">DeleteModelParameters</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="types.DeleteModelResponse.html" class="tsd-signature-type tsd-kind-class">DeleteModelResponse</a><span class="tsd-signature-symbol">&gt;</span><a href="#delete-1" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></div><div class="tsd-description"><div class="tsd-comment tsd-typography"><p>Deletes a tuned model by its name.</p>
</div><div class="tsd-parameters"><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameter-list"><li><span><span class="tsd-kind-parameter">params</span>: <a href="../interfaces/types.DeleteModelParameters.html" class="tsd-signature-type tsd-kind-interface">DeleteModelParameters</a></span><div class="tsd-comment tsd-typography"><p>The parameters for deleting the model.</p>
</div><div class="tsd-comment tsd-typography"></div></li></ul></div><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="types.DeleteModelResponse.html" class="tsd-signature-type tsd-kind-class">DeleteModelResponse</a><span class="tsd-signature-symbol">&gt;</span></h4><p>The response from the API.</p>
<div class="tsd-comment tsd-typography"><div class="tsd-tag-example"><h4 class="tsd-anchor-link"><a id="example-2" class="tsd-anchor"></a>Example<a href="#example-2" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h4><pre><code class="ts"><span class="hl-5">const</span><span class="hl-1"> </span><span class="hl-6">response</span><span class="hl-1"> = </span><span class="hl-3">await</span><span class="hl-1"> </span><span class="hl-4">ai</span><span class="hl-1">.</span><span class="hl-4">models</span><span class="hl-1">.</span><span class="hl-0">delete</span><span class="hl-1">({</span><span class="hl-4">model:</span><span class="hl-1"> </span><span class="hl-2">&#39;tuned-model-name&#39;</span><span class="hl-1">});</span>
</code><button type="button">Copy</button></pre>

</div></div><aside class="tsd-sources"><ul><li>Defined in src/models.ts:1235</li></ul></aside></div></li></ul></section><section class="tsd-panel tsd-member"><a id="editimage" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><span>edit<wbr/>Image</span><a href="#editimage" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><ul class="tsd-signatures"><li class=""><div class="tsd-signature tsd-anchor-link"><a id="editimage-1" class="tsd-anchor"></a><span class="tsd-kind-call-signature">editImage</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">params</span><span class="tsd-signature-symbol">:</span> <a href="../interfaces/types.EditImageParameters.html" class="tsd-signature-type tsd-kind-interface">EditImageParameters</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="types.EditImageResponse.html" class="tsd-signature-type tsd-kind-class">EditImageResponse</a><span class="tsd-signature-symbol">&gt;</span><a href="#editimage-1" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></div><div class="tsd-description"><div class="tsd-comment tsd-typography"><p>Edits an image based on a prompt, list of reference images, and configuration.</p>
</div><div class="tsd-parameters"><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameter-list"><li><span><span class="tsd-kind-parameter">params</span>: <a href="../interfaces/types.EditImageParameters.html" class="tsd-signature-type tsd-kind-interface">EditImageParameters</a></span><div class="tsd-comment tsd-typography"><p>The parameters for editing an image.</p>
</div><div class="tsd-comment tsd-typography"></div></li></ul></div><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="types.EditImageResponse.html" class="tsd-signature-type tsd-kind-class">EditImageResponse</a><span class="tsd-signature-symbol">&gt;</span></h4><p>The response from the API.</p>
<div class="tsd-comment tsd-typography"><div class="tsd-tag-example"><h4 class="tsd-anchor-link"><a id="example-3" class="tsd-anchor"></a>Example<a href="#example-3" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h4><pre><code class="ts"><span class="hl-5">const</span><span class="hl-1"> </span><span class="hl-6">response</span><span class="hl-1"> = </span><span class="hl-3">await</span><span class="hl-1"> </span><span class="hl-4">client</span><span class="hl-1">.</span><span class="hl-4">models</span><span class="hl-1">.</span><span class="hl-0">editImage</span><span class="hl-1">({</span><br/><span class="hl-1"> </span><span class="hl-4">model:</span><span class="hl-1"> </span><span class="hl-2">&#39;imagen-3.0-capability-001&#39;</span><span class="hl-1">,</span><br/><span class="hl-1"> </span><span class="hl-4">prompt:</span><span class="hl-1"> </span><span class="hl-2">&#39;Generate an image containing a mug with the product logo [1] visible on the side of the mug.&#39;</span><span class="hl-1">,</span><br/><span class="hl-1"> </span><span class="hl-4">referenceImages:</span><span class="hl-1"> [</span><span class="hl-4">subjectReferenceImage</span><span class="hl-1">]</span><br/><span class="hl-1"> </span><span class="hl-4">config</span><span class="hl-1">: {</span><br/><span class="hl-1">   </span><span class="hl-4">numberOfImages:</span><span class="hl-1"> </span><span class="hl-10">1</span><span class="hl-1">,</span><br/><span class="hl-1">   </span><span class="hl-4">includeRaiReason:</span><span class="hl-1"> </span><span class="hl-5">true</span><span class="hl-1">,</span><br/><span class="hl-1"> },</span><br/><span class="hl-1">});</span><br/><span class="hl-4">console</span><span class="hl-1">.</span><span class="hl-0">log</span><span class="hl-1">(</span><span class="hl-4">response</span><span class="hl-1">?.</span><span class="hl-4">generatedImages</span><span class="hl-1">?.[</span><span class="hl-10">0</span><span class="hl-1">]?.</span><span class="hl-4">image</span><span class="hl-1">?.</span><span class="hl-4">imageBytes</span><span class="hl-1">);</span>
</code><button type="button">Copy</button></pre>

</div></div><aside class="tsd-sources"><ul><li>Defined in src/models.ts:482</li></ul></aside></div></li></ul></section><section class="tsd-panel tsd-member"><a id="embedcontent" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><span>embed<wbr/>Content</span><a href="#embedcontent" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><ul class="tsd-signatures"><li class=""><div class="tsd-signature tsd-anchor-link"><a id="embedcontent-1" class="tsd-anchor"></a><span class="tsd-kind-call-signature">embedContent</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">params</span><span class="tsd-signature-symbol">:</span> <a href="../interfaces/types.EmbedContentParameters.html" class="tsd-signature-type tsd-kind-interface">EmbedContentParameters</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="types.EmbedContentResponse.html" class="tsd-signature-type tsd-kind-class">EmbedContentResponse</a><span class="tsd-signature-symbol">&gt;</span><a href="#embedcontent-1" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></div><div class="tsd-description"><div class="tsd-comment tsd-typography"><p>Calculates embeddings for the given contents. Only text is supported.</p>
</div><div class="tsd-parameters"><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameter-list"><li><span><span class="tsd-kind-parameter">params</span>: <a href="../interfaces/types.EmbedContentParameters.html" class="tsd-signature-type tsd-kind-interface">EmbedContentParameters</a></span><div class="tsd-comment tsd-typography"><p>The parameters for embedding contents.</p>
</div><div class="tsd-comment tsd-typography"></div></li></ul></div><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="types.EmbedContentResponse.html" class="tsd-signature-type tsd-kind-class">EmbedContentResponse</a><span class="tsd-signature-symbol">&gt;</span></h4><p>The response from the API.</p>
<div class="tsd-comment tsd-typography"><div class="tsd-tag-example"><h4 class="tsd-anchor-link"><a id="example-4" class="tsd-anchor"></a>Example<a href="#example-4" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h4><pre><code class="ts"><span class="hl-5">const</span><span class="hl-1"> </span><span class="hl-6">response</span><span class="hl-1"> = </span><span class="hl-3">await</span><span class="hl-1"> </span><span class="hl-4">ai</span><span class="hl-1">.</span><span class="hl-4">models</span><span class="hl-1">.</span><span class="hl-0">embedContent</span><span class="hl-1">({</span><br/><span class="hl-1"> </span><span class="hl-4">model:</span><span class="hl-1"> </span><span class="hl-2">&#39;text-embedding-004&#39;</span><span class="hl-1">,</span><br/><span class="hl-1"> </span><span class="hl-4">contents:</span><span class="hl-1"> [</span><br/><span class="hl-1">   </span><span class="hl-2">&#39;What is your name?&#39;</span><span class="hl-1">,</span><br/><span class="hl-1">   </span><span class="hl-2">&#39;What is your favorite color?&#39;</span><span class="hl-1">,</span><br/><span class="hl-1"> ],</span><br/><span class="hl-1"> </span><span class="hl-4">config:</span><span class="hl-1"> {</span><br/><span class="hl-1">   </span><span class="hl-4">outputDimensionality:</span><span class="hl-1"> </span><span class="hl-10">64</span><span class="hl-1">,</span><br/><span class="hl-1"> },</span><br/><span class="hl-1">});</span><br/><span class="hl-4">console</span><span class="hl-1">.</span><span class="hl-0">log</span><span class="hl-1">(</span><span class="hl-4">response</span><span class="hl-1">);</span>
</code><button type="button">Copy</button></pre>

</div></div><aside class="tsd-sources"><ul><li>Defined in src/models.ts:721</li></ul></aside></div></li></ul></section><section class="tsd-panel tsd-member"><a id="generatecontent" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><span>generate<wbr/>Content</span><a href="#generatecontent" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><ul class="tsd-signatures"><li class=""><div class="tsd-signature tsd-anchor-link"><a id="generatecontent-1" class="tsd-anchor"></a><span class="tsd-kind-call-signature">generateContent</span><span class="tsd-signature-symbol">(</span><br/>    <span class="tsd-kind-parameter">params</span><span class="tsd-signature-symbol">:</span> <a href="../interfaces/types.GenerateContentParameters.html" class="tsd-signature-type tsd-kind-interface">GenerateContentParameters</a><span class="tsd-signature-symbol">,</span><br/><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="types.GenerateContentResponse.html" class="tsd-signature-type tsd-kind-class">GenerateContentResponse</a><span class="tsd-signature-symbol">&gt;</span><a href="#generatecontent-1" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></div><div class="tsd-description"><div class="tsd-comment tsd-typography"><p>Makes an API request to generate content with a given model.</p>
<p>For the <code>model</code> parameter, supported formats for Vertex AI API include:</p>
<ul>
<li>The Gemini model ID, for example: 'gemini-2.0-flash'</li>
<li>The full resource name starts with 'projects/', for example:
'projects/my-project-id/locations/us-central1/publishers/google/models/gemini-2.0-flash'</li>
<li>The partial resource name with 'publishers/', for example:
'publishers/google/models/gemini-2.0-flash' or
'publishers/meta/models/llama-3.1-405b-instruct-maas'</li>
<li><code>/</code> separated publisher and model name, for example:
'google/gemini-2.0-flash' or 'meta/llama-3.1-405b-instruct-maas'</li>
</ul>
<p>For the <code>model</code> parameter, supported formats for Gemini API include:</p>
<ul>
<li>The Gemini model ID, for example: 'gemini-2.0-flash'</li>
<li>The model name starts with 'models/', for example:
'models/gemini-2.0-flash'</li>
<li>For tuned models, the model name starts with 'tunedModels/',
for example:
'tunedModels/1234567890123456789'</li>
</ul>
<p>Some models support multimodal input and output.</p>
</div><div class="tsd-parameters"><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameter-list"><li><span><span class="tsd-kind-parameter">params</span>: <a href="../interfaces/types.GenerateContentParameters.html" class="tsd-signature-type tsd-kind-interface">GenerateContentParameters</a></span><div class="tsd-comment tsd-typography"><p>The parameters for generating content.</p>
</div><div class="tsd-comment tsd-typography"></div></li></ul></div><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="types.GenerateContentResponse.html" class="tsd-signature-type tsd-kind-class">GenerateContentResponse</a><span class="tsd-signature-symbol">&gt;</span></h4><p>The response from generating content.</p>
<div class="tsd-comment tsd-typography"><div class="tsd-tag-example"><h4 class="tsd-anchor-link"><a id="example-5" class="tsd-anchor"></a>Example<a href="#example-5" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h4><pre><code class="ts"><span class="hl-5">const</span><span class="hl-1"> </span><span class="hl-6">response</span><span class="hl-1"> = </span><span class="hl-3">await</span><span class="hl-1"> </span><span class="hl-4">ai</span><span class="hl-1">.</span><span class="hl-4">models</span><span class="hl-1">.</span><span class="hl-0">generateContent</span><span class="hl-1">({</span><br/><span class="hl-1">  </span><span class="hl-4">model:</span><span class="hl-1"> </span><span class="hl-2">&#39;gemini-2.0-flash&#39;</span><span class="hl-1">,</span><br/><span class="hl-1">  </span><span class="hl-4">contents:</span><span class="hl-1"> </span><span class="hl-2">&#39;why is the sky blue?&#39;</span><span class="hl-1">,</span><br/><span class="hl-1">  </span><span class="hl-4">config:</span><span class="hl-1"> {</span><br/><span class="hl-1">    </span><span class="hl-4">candidateCount:</span><span class="hl-1"> </span><span class="hl-10">2</span><span class="hl-1">,</span><br/><span class="hl-1">  }</span><br/><span class="hl-1">});</span><br/><span class="hl-4">console</span><span class="hl-1">.</span><span class="hl-0">log</span><span class="hl-1">(</span><span class="hl-4">response</span><span class="hl-1">);</span>
</code><button type="button">Copy</button></pre>

</div></div><aside class="tsd-sources"><ul><li>Defined in src/models.ts:73</li></ul></aside></div></li></ul></section><section class="tsd-panel tsd-member"><a id="generatecontentstream" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><span>generate<wbr/>Content<wbr/>Stream</span><a href="#generatecontentstream" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><ul class="tsd-signatures"><li class=""><div class="tsd-signature tsd-anchor-link"><a id="generatecontentstream-1" class="tsd-anchor"></a><span class="tsd-kind-call-signature">generateContentStream</span><span class="tsd-signature-symbol">(</span><br/>    <span class="tsd-kind-parameter">params</span><span class="tsd-signature-symbol">:</span> <a href="../interfaces/types.GenerateContentParameters.html" class="tsd-signature-type tsd-kind-interface">GenerateContentParameters</a><span class="tsd-signature-symbol">,</span><br/><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">AsyncGenerator</span><span class="tsd-signature-symbol">&lt;</span><a href="types.GenerateContentResponse.html" class="tsd-signature-type tsd-kind-class">GenerateContentResponse</a><span class="tsd-signature-symbol">,</span> <span class="tsd-signature-type">any</span><span class="tsd-signature-symbol">,</span> <span class="tsd-signature-type">unknown</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">&gt;</span><a href="#generatecontentstream-1" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></div><div class="tsd-description"><div class="tsd-comment tsd-typography"><p>Makes an API request to generate content with a given model and yields the
response in chunks.</p>
<p>For the <code>model</code> parameter, supported formats for Vertex AI API include:</p>
<ul>
<li>The Gemini model ID, for example: 'gemini-2.0-flash'</li>
<li>The full resource name starts with 'projects/', for example:
'projects/my-project-id/locations/us-central1/publishers/google/models/gemini-2.0-flash'</li>
<li>The partial resource name with 'publishers/', for example:
'publishers/google/models/gemini-2.0-flash' or
'publishers/meta/models/llama-3.1-405b-instruct-maas'</li>
<li><code>/</code> separated publisher and model name, for example:
'google/gemini-2.0-flash' or 'meta/llama-3.1-405b-instruct-maas'</li>
</ul>
<p>For the <code>model</code> parameter, supported formats for Gemini API include:</p>
<ul>
<li>The Gemini model ID, for example: 'gemini-2.0-flash'</li>
<li>The model name starts with 'models/', for example:
'models/gemini-2.0-flash'</li>
<li>For tuned models, the model name starts with 'tunedModels/',
for example:
'tunedModels/1234567890123456789'</li>
</ul>
<p>Some models support multimodal input and output.</p>
</div><div class="tsd-parameters"><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameter-list"><li><span><span class="tsd-kind-parameter">params</span>: <a href="../interfaces/types.GenerateContentParameters.html" class="tsd-signature-type tsd-kind-interface">GenerateContentParameters</a></span><div class="tsd-comment tsd-typography"><p>The parameters for generating content with streaming response.</p>
</div><div class="tsd-comment tsd-typography"></div></li></ul></div><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">AsyncGenerator</span><span class="tsd-signature-symbol">&lt;</span><a href="types.GenerateContentResponse.html" class="tsd-signature-type tsd-kind-class">GenerateContentResponse</a><span class="tsd-signature-symbol">,</span> <span class="tsd-signature-type">any</span><span class="tsd-signature-symbol">,</span> <span class="tsd-signature-type">unknown</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">&gt;</span></h4><p>The response from generating content.</p>
<div class="tsd-comment tsd-typography"><div class="tsd-tag-example"><h4 class="tsd-anchor-link"><a id="example-6" class="tsd-anchor"></a>Example<a href="#example-6" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h4><pre><code class="ts"><span class="hl-5">const</span><span class="hl-1"> </span><span class="hl-6">response</span><span class="hl-1"> = </span><span class="hl-3">await</span><span class="hl-1"> </span><span class="hl-4">ai</span><span class="hl-1">.</span><span class="hl-4">models</span><span class="hl-1">.</span><span class="hl-0">generateContentStream</span><span class="hl-1">({</span><br/><span class="hl-1">  </span><span class="hl-4">model:</span><span class="hl-1"> </span><span class="hl-2">&#39;gemini-2.0-flash&#39;</span><span class="hl-1">,</span><br/><span class="hl-1">  </span><span class="hl-4">contents:</span><span class="hl-1"> </span><span class="hl-2">&#39;why is the sky blue?&#39;</span><span class="hl-1">,</span><br/><span class="hl-1">  </span><span class="hl-4">config:</span><span class="hl-1"> {</span><br/><span class="hl-1">    </span><span class="hl-4">maxOutputTokens:</span><span class="hl-1"> </span><span class="hl-10">200</span><span class="hl-1">,</span><br/><span class="hl-1">  }</span><br/><span class="hl-1">});</span><br/><span class="hl-3">for</span><span class="hl-1"> </span><span class="hl-3">await</span><span class="hl-1"> (</span><span class="hl-5">const</span><span class="hl-1"> </span><span class="hl-6">chunk</span><span class="hl-1"> </span><span class="hl-5">of</span><span class="hl-1"> </span><span class="hl-4">response</span><span class="hl-1">) {</span><br/><span class="hl-1">  </span><span class="hl-4">console</span><span class="hl-1">.</span><span class="hl-0">log</span><span class="hl-1">(</span><span class="hl-4">chunk</span><span class="hl-1">);</span><br/><span class="hl-1">}</span>
</code><button type="button">Copy</button></pre>

</div></div><aside class="tsd-sources"><ul><li>Defined in src/models.ts:200</li></ul></aside></div></li></ul></section><section class="tsd-panel tsd-member"><a id="generateimages" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><span>generate<wbr/>Images</span><a href="#generateimages" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><ul class="tsd-signatures"><li class=""><div class="tsd-signature tsd-anchor-link"><a id="generateimages-1" class="tsd-anchor"></a><span class="tsd-kind-call-signature">generateImages</span><span class="tsd-signature-symbol">(</span><br/>    <span class="tsd-kind-parameter">params</span><span class="tsd-signature-symbol">:</span> <a href="../interfaces/types.GenerateImagesParameters.html" class="tsd-signature-type tsd-kind-interface">GenerateImagesParameters</a><span class="tsd-signature-symbol">,</span><br/><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="types.GenerateImagesResponse.html" class="tsd-signature-type tsd-kind-class">GenerateImagesResponse</a><span class="tsd-signature-symbol">&gt;</span><a href="#generateimages-1" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></div><div class="tsd-description"><div class="tsd-comment tsd-typography"><p>Generates an image based on a text description and configuration.</p>
</div><div class="tsd-parameters"><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameter-list"><li><span><span class="tsd-kind-parameter">params</span>: <a href="../interfaces/types.GenerateImagesParameters.html" class="tsd-signature-type tsd-kind-interface">GenerateImagesParameters</a></span><div class="tsd-comment tsd-typography"><p>The parameters for generating images.</p>
</div><div class="tsd-comment tsd-typography"></div></li></ul></div><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="types.GenerateImagesResponse.html" class="tsd-signature-type tsd-kind-class">GenerateImagesResponse</a><span class="tsd-signature-symbol">&gt;</span></h4><p>The response from the API.</p>
<div class="tsd-comment tsd-typography"><div class="tsd-tag-example"><h4 class="tsd-anchor-link"><a id="example-7" class="tsd-anchor"></a>Example<a href="#example-7" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h4><pre><code class="ts"><span class="hl-5">const</span><span class="hl-1"> </span><span class="hl-6">response</span><span class="hl-1"> = </span><span class="hl-3">await</span><span class="hl-1"> </span><span class="hl-4">client</span><span class="hl-1">.</span><span class="hl-4">models</span><span class="hl-1">.</span><span class="hl-0">generateImages</span><span class="hl-1">({</span><br/><span class="hl-1"> </span><span class="hl-4">model:</span><span class="hl-1"> </span><span class="hl-2">&#39;imagen-3.0-generate-002&#39;</span><span class="hl-1">,</span><br/><span class="hl-1"> </span><span class="hl-4">prompt:</span><span class="hl-1"> </span><span class="hl-2">&#39;Robot holding a red skateboard&#39;</span><span class="hl-1">,</span><br/><span class="hl-1"> </span><span class="hl-4">config:</span><span class="hl-1"> {</span><br/><span class="hl-1">   </span><span class="hl-4">numberOfImages:</span><span class="hl-1"> </span><span class="hl-10">1</span><span class="hl-1">,</span><br/><span class="hl-1">   </span><span class="hl-4">includeRaiReason:</span><span class="hl-1"> </span><span class="hl-5">true</span><span class="hl-1">,</span><br/><span class="hl-1"> },</span><br/><span class="hl-1">});</span><br/><span class="hl-4">console</span><span class="hl-1">.</span><span class="hl-0">log</span><span class="hl-1">(</span><span class="hl-4">response</span><span class="hl-1">?.</span><span class="hl-4">generatedImages</span><span class="hl-1">?.[</span><span class="hl-10">0</span><span class="hl-1">]?.</span><span class="hl-4">image</span><span class="hl-1">?.</span><span class="hl-4">imageBytes</span><span class="hl-1">);</span>
</code><button type="button">Copy</button></pre>

</div></div><aside class="tsd-sources"><ul><li>Defined in src/models.ts:392</li></ul></aside></div></li></ul></section><section class="tsd-panel tsd-member"><a id="generatevideos" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><span>generate<wbr/>Videos</span><a href="#generatevideos" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><ul class="tsd-signatures"><li class=""><div class="tsd-signature tsd-anchor-link"><a id="generatevideos-1" class="tsd-anchor"></a><span class="tsd-kind-call-signature">generateVideos</span><span class="tsd-signature-symbol">(</span><br/>    <span class="tsd-kind-parameter">params</span><span class="tsd-signature-symbol">:</span> <a href="../interfaces/types.GenerateVideosParameters.html" class="tsd-signature-type tsd-kind-interface">GenerateVideosParameters</a><span class="tsd-signature-symbol">,</span><br/><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../interfaces/types.GenerateVideosOperation.html" class="tsd-signature-type tsd-kind-interface">GenerateVideosOperation</a><span class="tsd-signature-symbol">&gt;</span><a href="#generatevideos-1" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></div><div class="tsd-description"><div class="tsd-comment tsd-typography"><p>Generates videos based on a text description and configuration.</p>
</div><div class="tsd-parameters"><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameter-list"><li><span><span class="tsd-kind-parameter">params</span>: <a href="../interfaces/types.GenerateVideosParameters.html" class="tsd-signature-type tsd-kind-interface">GenerateVideosParameters</a></span><div class="tsd-comment tsd-typography"><p>The parameters for generating videos.</p>
</div><div class="tsd-comment tsd-typography"></div></li></ul></div><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../interfaces/types.GenerateVideosOperation.html" class="tsd-signature-type tsd-kind-interface">GenerateVideosOperation</a><span class="tsd-signature-symbol">&gt;</span></h4><p>A Promise<GenerateVideosOperation> which allows you to track the progress and eventually retrieve the generated videos using the operations.get method.</p>
<div class="tsd-comment tsd-typography"><div class="tsd-tag-example"><h4 class="tsd-anchor-link"><a id="example-8" class="tsd-anchor"></a>Example<a href="#example-8" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h4><pre><code class="ts"><span class="hl-5">const</span><span class="hl-1"> </span><span class="hl-6">operation</span><span class="hl-1"> = </span><span class="hl-3">await</span><span class="hl-1"> </span><span class="hl-4">ai</span><span class="hl-1">.</span><span class="hl-4">models</span><span class="hl-1">.</span><span class="hl-0">generateVideos</span><span class="hl-1">({</span><br/><span class="hl-1"> </span><span class="hl-4">model:</span><span class="hl-1"> </span><span class="hl-2">&#39;veo-2.0-generate-001&#39;</span><span class="hl-1">,</span><br/><span class="hl-1"> </span><span class="hl-4">prompt:</span><span class="hl-1"> </span><span class="hl-2">&#39;A neon hologram of a cat driving at top speed&#39;</span><span class="hl-1">,</span><br/><span class="hl-1"> </span><span class="hl-4">config:</span><span class="hl-1"> {</span><br/><span class="hl-1">   </span><span class="hl-4">numberOfVideos:</span><span class="hl-1"> </span><span class="hl-10">1</span><br/><span class="hl-1">});</span><br/><br/><span class="hl-0">while</span><span class="hl-1"> (!</span><span class="hl-4">operation</span><span class="hl-1">.</span><span class="hl-4">done</span><span class="hl-1">) {</span><br/><span class="hl-1">  await new </span><span class="hl-0">Promise</span><span class="hl-1">(</span><span class="hl-4">resolve</span><span class="hl-1"> =&gt; </span><span class="hl-0">setTimeout</span><span class="hl-1">(</span><span class="hl-4">resolve</span><span class="hl-1">, </span><span class="hl-10">10000</span><span class="hl-1">));</span><br/><span class="hl-1">  </span><span class="hl-4">operation</span><span class="hl-1"> = </span><span class="hl-3">await</span><span class="hl-1"> </span><span class="hl-4">ai</span><span class="hl-1">.</span><span class="hl-4">operations</span><span class="hl-1">.</span><span class="hl-0">getVideosOperation</span><span class="hl-1">({</span><span class="hl-4">operation:</span><span class="hl-1"> </span><span class="hl-4">operation</span><span class="hl-1">});</span><br/><span class="hl-1">}</span><br/><br/><span class="hl-4">console</span><span class="hl-1">.</span><span class="hl-0">log</span><span class="hl-1">(</span><span class="hl-4">operation</span><span class="hl-1">.</span><span class="hl-4">response</span><span class="hl-1">?.</span><span class="hl-4">generatedVideos</span><span class="hl-1">?.[</span><span class="hl-10">0</span><span class="hl-1">]?.</span><span class="hl-4">video</span><span class="hl-1">?.</span><span class="hl-4">uri</span><span class="hl-1">);</span>
</code><button type="button">Copy</button></pre>

</div></div><aside class="tsd-sources"><ul><li>Defined in src/models.ts:1490</li></ul></aside></div></li></ul></section><section class="tsd-panel tsd-member"><a id="get" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><span>get</span><a href="#get" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><ul class="tsd-signatures"><li class=""><div class="tsd-signature tsd-anchor-link"><a id="get-1" class="tsd-anchor"></a><span class="tsd-kind-call-signature">get</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">params</span><span class="tsd-signature-symbol">:</span> <a href="../interfaces/types.GetModelParameters.html" class="tsd-signature-type tsd-kind-interface">GetModelParameters</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../interfaces/types.Model.html" class="tsd-signature-type tsd-kind-interface">Model</a><span class="tsd-signature-symbol">&gt;</span><a href="#get-1" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></div><div class="tsd-description"><div class="tsd-comment tsd-typography"><p>Fetches information about a model by name.</p>
</div><div class="tsd-parameters"><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameter-list"><li><span><span class="tsd-kind-parameter">params</span>: <a href="../interfaces/types.GetModelParameters.html" class="tsd-signature-type tsd-kind-interface">GetModelParameters</a></span></li></ul></div><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../interfaces/types.Model.html" class="tsd-signature-type tsd-kind-interface">Model</a><span class="tsd-signature-symbol">&gt;</span></h4><div class="tsd-comment tsd-typography"><div class="tsd-tag-example"><h4 class="tsd-anchor-link"><a id="example-9" class="tsd-anchor"></a>Example<a href="#example-9" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h4><pre><code class="ts"><span class="hl-5">const</span><span class="hl-1"> </span><span class="hl-6">modelInfo</span><span class="hl-1"> = </span><span class="hl-3">await</span><span class="hl-1"> </span><span class="hl-4">ai</span><span class="hl-1">.</span><span class="hl-4">models</span><span class="hl-1">.</span><span class="hl-0">get</span><span class="hl-1">({</span><span class="hl-4">model:</span><span class="hl-1"> </span><span class="hl-2">&#39;gemini-2.0-flash&#39;</span><span class="hl-1">});</span>
</code><button type="button">Copy</button></pre>

</div></div><aside class="tsd-sources"><ul><li>Defined in src/models.ts:990</li></ul></aside></div></li></ul></section><section class="tsd-panel tsd-member"><a id="list" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><span>list</span><a href="#list" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><ul class="tsd-signatures"><li class=""><div class="tsd-signature tsd-anchor-link"><a id="list-1" class="tsd-anchor"></a><span class="tsd-kind-call-signature">list</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">params</span><span class="tsd-signature-symbol">?:</span> <a href="../interfaces/types.ListModelsParameters.html" class="tsd-signature-type tsd-kind-interface">ListModelsParameters</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="pagers.Pager.html" class="tsd-signature-type tsd-kind-class">Pager</a><span class="tsd-signature-symbol">&lt;</span><a href="../interfaces/types.Model.html" class="tsd-signature-type tsd-kind-interface">Model</a><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">&gt;</span><a href="#list-1" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></div><div class="tsd-description"><div class="tsd-parameters"><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameter-list"><li><span><code class="tsd-tag">Optional</code><span class="tsd-kind-parameter">params</span>: <a href="../interfaces/types.ListModelsParameters.html" class="tsd-signature-type tsd-kind-interface">ListModelsParameters</a></span></li></ul></div><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="pagers.Pager.html" class="tsd-signature-type tsd-kind-class">Pager</a><span class="tsd-signature-symbol">&lt;</span><a href="../interfaces/types.Model.html" class="tsd-signature-type tsd-kind-interface">Model</a><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">&gt;</span></h4><aside class="tsd-sources"><ul><li>Defined in src/models.ts:428</li></ul></aside></div></li></ul></section><section class="tsd-panel tsd-member"><a id="update" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><span>update</span><a href="#update" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><ul class="tsd-signatures"><li class=""><div class="tsd-signature tsd-anchor-link"><a id="update-1" class="tsd-anchor"></a><span class="tsd-kind-call-signature">update</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">params</span><span class="tsd-signature-symbol">:</span> <a href="../interfaces/types.UpdateModelParameters.html" class="tsd-signature-type tsd-kind-interface">UpdateModelParameters</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../interfaces/types.Model.html" class="tsd-signature-type tsd-kind-interface">Model</a><span class="tsd-signature-symbol">&gt;</span><a href="#update-1" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></div><div class="tsd-description"><div class="tsd-comment tsd-typography"><p>Updates a tuned model by its name.</p>
</div><div class="tsd-parameters"><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameter-list"><li><span><span class="tsd-kind-parameter">params</span>: <a href="../interfaces/types.UpdateModelParameters.html" class="tsd-signature-type tsd-kind-interface">UpdateModelParameters</a></span><div class="tsd-comment tsd-typography"><p>The parameters for updating the model.</p>
</div><div class="tsd-comment tsd-typography"></div></li></ul></div><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../interfaces/types.Model.html" class="tsd-signature-type tsd-kind-interface">Model</a><span class="tsd-signature-symbol">&gt;</span></h4><p>The response from the API.</p>
<div class="tsd-comment tsd-typography"><div class="tsd-tag-example"><h4 class="tsd-anchor-link"><a id="example-10" class="tsd-anchor"></a>Example<a href="#example-10" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h4><pre><code class="ts"><span class="hl-5">const</span><span class="hl-1"> </span><span class="hl-6">response</span><span class="hl-1"> = </span><span class="hl-3">await</span><span class="hl-1"> </span><span class="hl-4">ai</span><span class="hl-1">.</span><span class="hl-4">models</span><span class="hl-1">.</span><span class="hl-0">update</span><span class="hl-1">({</span><br/><span class="hl-1">  </span><span class="hl-4">model:</span><span class="hl-1"> </span><span class="hl-2">&#39;tuned-model-name&#39;</span><span class="hl-1">,</span><br/><span class="hl-1">  </span><span class="hl-4">config:</span><span class="hl-1"> {</span><br/><span class="hl-1">    </span><span class="hl-4">displayName:</span><span class="hl-1"> </span><span class="hl-2">&#39;New display name&#39;</span><span class="hl-1">,</span><br/><span class="hl-1">    </span><span class="hl-4">description:</span><span class="hl-1"> </span><span class="hl-2">&#39;New description&#39;</span><span class="hl-1">,</span><br/><span class="hl-1">  },</span><br/><span class="hl-1">});</span>
</code><button type="button">Copy</button></pre>

</div></div><aside class="tsd-sources"><ul><li>Defined in src/models.ts:1152</li></ul></aside></div></li></ul></section><section class="tsd-panel tsd-member"><a id="upscaleimage" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><span>upscale<wbr/>Image</span><a href="#upscaleimage" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><ul class="tsd-signatures"><li class=""><div class="tsd-signature tsd-anchor-link"><a id="upscaleimage-1" class="tsd-anchor"></a><span class="tsd-kind-call-signature">upscaleImage</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">params</span><span class="tsd-signature-symbol">:</span> <a href="../interfaces/types.UpscaleImageParameters.html" class="tsd-signature-type tsd-kind-interface">UpscaleImageParameters</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="types.UpscaleImageResponse.html" class="tsd-signature-type tsd-kind-class">UpscaleImageResponse</a><span class="tsd-signature-symbol">&gt;</span><a href="#upscaleimage-1" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></div><div class="tsd-description"><div class="tsd-comment tsd-typography"><p>Upscales an image based on an image, upscale factor, and configuration.
Only supported in Vertex AI currently.</p>
</div><div class="tsd-parameters"><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameter-list"><li><span><span class="tsd-kind-parameter">params</span>: <a href="../interfaces/types.UpscaleImageParameters.html" class="tsd-signature-type tsd-kind-interface">UpscaleImageParameters</a></span><div class="tsd-comment tsd-typography"><p>The parameters for upscaling an image.</p>
</div><div class="tsd-comment tsd-typography"></div></li></ul></div><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="types.UpscaleImageResponse.html" class="tsd-signature-type tsd-kind-class">UpscaleImageResponse</a><span class="tsd-signature-symbol">&gt;</span></h4><p>The response from the API.</p>
<div class="tsd-comment tsd-typography"><div class="tsd-tag-example"><h4 class="tsd-anchor-link"><a id="example-11" class="tsd-anchor"></a>Example<a href="#example-11" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h4><pre><code class="ts"><span class="hl-5">const</span><span class="hl-1"> </span><span class="hl-6">response</span><span class="hl-1"> = </span><span class="hl-3">await</span><span class="hl-1"> </span><span class="hl-4">client</span><span class="hl-1">.</span><span class="hl-4">models</span><span class="hl-1">.</span><span class="hl-0">upscaleImage</span><span class="hl-1">({</span><br/><span class="hl-1"> </span><span class="hl-4">model:</span><span class="hl-1"> </span><span class="hl-2">&#39;imagen-3.0-generate-002&#39;</span><span class="hl-1">,</span><br/><span class="hl-1"> </span><span class="hl-4">image:</span><span class="hl-1"> </span><span class="hl-4">image</span><span class="hl-1">,</span><br/><span class="hl-1"> </span><span class="hl-4">upscaleFactor:</span><span class="hl-1"> </span><span class="hl-2">&#39;x2&#39;</span><span class="hl-1">,</span><br/><span class="hl-1"> </span><span class="hl-4">config:</span><span class="hl-1"> {</span><br/><span class="hl-1">   </span><span class="hl-4">includeRaiReason:</span><span class="hl-1"> </span><span class="hl-5">true</span><span class="hl-1">,</span><br/><span class="hl-1"> },</span><br/><span class="hl-1">});</span><br/><span class="hl-4">console</span><span class="hl-1">.</span><span class="hl-0">log</span><span class="hl-1">(</span><span class="hl-4">response</span><span class="hl-1">?.</span><span class="hl-4">generatedImages</span><span class="hl-1">?.[</span><span class="hl-10">0</span><span class="hl-1">]?.</span><span class="hl-4">image</span><span class="hl-1">?.</span><span class="hl-4">imageBytes</span><span class="hl-1">);</span>
</code><button type="button">Copy</button></pre>

</div></div><aside class="tsd-sources"><ul><li>Defined in src/models.ts:521</li></ul></aside></div></li></ul></section></section></details></div><div class="col-sidebar"><div class="page-menu"><div class="tsd-navigation settings"><details class="tsd-accordion"><summary class="tsd-accordion-summary"><h3><svg width="20" height="20" viewBox="0 0 24 24" fill="none" aria-hidden="true"><use href="../assets/icons.svg#icon-chevronDown"></use></svg>Settings</h3></summary><div class="tsd-accordion-details"><div class="tsd-theme-toggle"><label class="settings-label" for="tsd-theme">Theme</label><select id="tsd-theme"><option value="os">OS</option><option value="light">Light</option><option value="dark">Dark</option></select></div></div></details></div><details open class="tsd-accordion tsd-page-navigation"><summary class="tsd-accordion-summary"><h3><svg width="20" height="20" viewBox="0 0 24 24" fill="none" aria-hidden="true"><use href="../assets/icons.svg#icon-chevronDown"></use></svg>On This Page</h3></summary><div class="tsd-accordion-details"><details open class="tsd-accordion tsd-page-navigation-section"><summary class="tsd-accordion-summary" data-key="section-Constructors"><svg width="20" height="20" viewBox="0 0 24 24" fill="none" aria-hidden="true"><use href="../assets/icons.svg#icon-chevronDown"></use></svg>Constructors</summary><div><a href="#constructor" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Constructor"><use href="../assets/icons.svg#icon-512"></use></svg><span>constructor</span></a></div></details><details open class="tsd-accordion tsd-page-navigation-section"><summary class="tsd-accordion-summary" data-key="section-Methods"><svg width="20" height="20" viewBox="0 0 24 24" fill="none" aria-hidden="true"><use href="../assets/icons.svg#icon-chevronDown"></use></svg>Methods</summary><div><a href="#computetokens" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Method"><use href="../assets/icons.svg#icon-2048"></use></svg><span>compute<wbr/>Tokens</span></a><a href="#counttokens" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Method"><use href="../assets/icons.svg#icon-2048"></use></svg><span>count<wbr/>Tokens</span></a><a href="#delete" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Method"><use href="../assets/icons.svg#icon-2048"></use></svg><span>delete</span></a><a href="#editimage" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Method"><use href="../assets/icons.svg#icon-2048"></use></svg><span>edit<wbr/>Image</span></a><a href="#embedcontent" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Method"><use href="../assets/icons.svg#icon-2048"></use></svg><span>embed<wbr/>Content</span></a><a href="#generatecontent" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Method"><use href="../assets/icons.svg#icon-2048"></use></svg><span>generate<wbr/>Content</span></a><a href="#generatecontentstream" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Method"><use href="../assets/icons.svg#icon-2048"></use></svg><span>generate<wbr/>Content<wbr/>Stream</span></a><a href="#generateimages" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Method"><use href="../assets/icons.svg#icon-2048"></use></svg><span>generate<wbr/>Images</span></a><a href="#generatevideos" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Method"><use href="../assets/icons.svg#icon-2048"></use></svg><span>generate<wbr/>Videos</span></a><a href="#get" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Method"><use href="../assets/icons.svg#icon-2048"></use></svg><span>get</span></a><a href="#list" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Method"><use href="../assets/icons.svg#icon-2048"></use></svg><span>list</span></a><a href="#update" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Method"><use href="../assets/icons.svg#icon-2048"></use></svg><span>update</span></a><a href="#upscaleimage" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Method"><use href="../assets/icons.svg#icon-2048"></use></svg><span>upscale<wbr/>Image</span></a></div></details></div></details></div><div class="site-menu"><nav class="tsd-navigation"><a href="../modules.html">@google/genai</a><ul class="tsd-small-nested-navigation" id="tsd-nav-container"><li>Loading...</li></ul></nav></div></div></div><footer><p class="tsd-generator">Generated using <a href="https://typedoc.org/" target="_blank">TypeDoc</a></p></footer><div class="overlay"></div></body></html>
