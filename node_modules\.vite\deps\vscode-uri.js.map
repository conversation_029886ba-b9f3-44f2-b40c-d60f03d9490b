{"version": 3, "sources": ["../../../../node_modules/.pnpm/vscode-uri@3.1.0/node_modules/vscode-uri/lib/esm/webpack:/LIB/node_modules/path-browserify/index.js", "../../../../node_modules/.pnpm/vscode-uri@3.1.0/node_modules/vscode-uri/lib/esm/webpack:/LIB/webpack/bootstrap", "../../../../node_modules/.pnpm/vscode-uri@3.1.0/node_modules/vscode-uri/lib/esm/webpack:/LIB/webpack/runtime/define property getters", "../../../../node_modules/.pnpm/vscode-uri@3.1.0/node_modules/vscode-uri/lib/esm/webpack:/LIB/webpack/runtime/hasOwnProperty shorthand", "../../../../node_modules/.pnpm/vscode-uri@3.1.0/node_modules/vscode-uri/lib/esm/webpack:/LIB/webpack/runtime/make namespace object", "../../../../node_modules/.pnpm/vscode-uri@3.1.0/node_modules/vscode-uri/lib/esm/webpack:/LIB/src/platform.ts", "../../../../node_modules/.pnpm/vscode-uri@3.1.0/node_modules/vscode-uri/lib/esm/webpack:/LIB/src/uri.ts", "../../../../node_modules/.pnpm/vscode-uri@3.1.0/node_modules/vscode-uri/lib/esm/webpack:/LIB/src/utils.ts"], "sourcesContent": ["// 'path' module extracted from Node.js v8.11.1 (only the posix part)\n// transplited with Babel\n\n// Copyright Joyent, Inc. and other Node contributors.\n//\n// Permission is hereby granted, free of charge, to any person obtaining a\n// copy of this software and associated documentation files (the\n// \"Software\"), to deal in the Software without restriction, including\n// without limitation the rights to use, copy, modify, merge, publish,\n// distribute, sublicense, and/or sell copies of the Software, and to permit\n// persons to whom the Software is furnished to do so, subject to the\n// following conditions:\n//\n// The above copyright notice and this permission notice shall be included\n// in all copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS\n// OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n// MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN\n// NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM,\n// DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR\n// OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE\n// USE OR OTHER DEALINGS IN THE SOFTWARE.\n\n'use strict';\n\nfunction assertPath(path) {\n  if (typeof path !== 'string') {\n    throw new TypeError('Path must be a string. Received ' + JSON.stringify(path));\n  }\n}\n\n// Resolves . and .. elements in a path with directory names\nfunction normalizeStringPosix(path, allowAboveRoot) {\n  var res = '';\n  var lastSegmentLength = 0;\n  var lastSlash = -1;\n  var dots = 0;\n  var code;\n  for (var i = 0; i <= path.length; ++i) {\n    if (i < path.length)\n      code = path.charCodeAt(i);\n    else if (code === 47 /*/*/)\n      break;\n    else\n      code = 47 /*/*/;\n    if (code === 47 /*/*/) {\n      if (lastSlash === i - 1 || dots === 1) {\n        // NOOP\n      } else if (lastSlash !== i - 1 && dots === 2) {\n        if (res.length < 2 || lastSegmentLength !== 2 || res.charCodeAt(res.length - 1) !== 46 /*.*/ || res.charCodeAt(res.length - 2) !== 46 /*.*/) {\n          if (res.length > 2) {\n            var lastSlashIndex = res.lastIndexOf('/');\n            if (lastSlashIndex !== res.length - 1) {\n              if (lastSlashIndex === -1) {\n                res = '';\n                lastSegmentLength = 0;\n              } else {\n                res = res.slice(0, lastSlashIndex);\n                lastSegmentLength = res.length - 1 - res.lastIndexOf('/');\n              }\n              lastSlash = i;\n              dots = 0;\n              continue;\n            }\n          } else if (res.length === 2 || res.length === 1) {\n            res = '';\n            lastSegmentLength = 0;\n            lastSlash = i;\n            dots = 0;\n            continue;\n          }\n        }\n        if (allowAboveRoot) {\n          if (res.length > 0)\n            res += '/..';\n          else\n            res = '..';\n          lastSegmentLength = 2;\n        }\n      } else {\n        if (res.length > 0)\n          res += '/' + path.slice(lastSlash + 1, i);\n        else\n          res = path.slice(lastSlash + 1, i);\n        lastSegmentLength = i - lastSlash - 1;\n      }\n      lastSlash = i;\n      dots = 0;\n    } else if (code === 46 /*.*/ && dots !== -1) {\n      ++dots;\n    } else {\n      dots = -1;\n    }\n  }\n  return res;\n}\n\nfunction _format(sep, pathObject) {\n  var dir = pathObject.dir || pathObject.root;\n  var base = pathObject.base || (pathObject.name || '') + (pathObject.ext || '');\n  if (!dir) {\n    return base;\n  }\n  if (dir === pathObject.root) {\n    return dir + base;\n  }\n  return dir + sep + base;\n}\n\nvar posix = {\n  // path.resolve([from ...], to)\n  resolve: function resolve() {\n    var resolvedPath = '';\n    var resolvedAbsolute = false;\n    var cwd;\n\n    for (var i = arguments.length - 1; i >= -1 && !resolvedAbsolute; i--) {\n      var path;\n      if (i >= 0)\n        path = arguments[i];\n      else {\n        if (cwd === undefined)\n          cwd = process.cwd();\n        path = cwd;\n      }\n\n      assertPath(path);\n\n      // Skip empty entries\n      if (path.length === 0) {\n        continue;\n      }\n\n      resolvedPath = path + '/' + resolvedPath;\n      resolvedAbsolute = path.charCodeAt(0) === 47 /*/*/;\n    }\n\n    // At this point the path should be resolved to a full absolute path, but\n    // handle relative paths to be safe (might happen when process.cwd() fails)\n\n    // Normalize the path\n    resolvedPath = normalizeStringPosix(resolvedPath, !resolvedAbsolute);\n\n    if (resolvedAbsolute) {\n      if (resolvedPath.length > 0)\n        return '/' + resolvedPath;\n      else\n        return '/';\n    } else if (resolvedPath.length > 0) {\n      return resolvedPath;\n    } else {\n      return '.';\n    }\n  },\n\n  normalize: function normalize(path) {\n    assertPath(path);\n\n    if (path.length === 0) return '.';\n\n    var isAbsolute = path.charCodeAt(0) === 47 /*/*/;\n    var trailingSeparator = path.charCodeAt(path.length - 1) === 47 /*/*/;\n\n    // Normalize the path\n    path = normalizeStringPosix(path, !isAbsolute);\n\n    if (path.length === 0 && !isAbsolute) path = '.';\n    if (path.length > 0 && trailingSeparator) path += '/';\n\n    if (isAbsolute) return '/' + path;\n    return path;\n  },\n\n  isAbsolute: function isAbsolute(path) {\n    assertPath(path);\n    return path.length > 0 && path.charCodeAt(0) === 47 /*/*/;\n  },\n\n  join: function join() {\n    if (arguments.length === 0)\n      return '.';\n    var joined;\n    for (var i = 0; i < arguments.length; ++i) {\n      var arg = arguments[i];\n      assertPath(arg);\n      if (arg.length > 0) {\n        if (joined === undefined)\n          joined = arg;\n        else\n          joined += '/' + arg;\n      }\n    }\n    if (joined === undefined)\n      return '.';\n    return posix.normalize(joined);\n  },\n\n  relative: function relative(from, to) {\n    assertPath(from);\n    assertPath(to);\n\n    if (from === to) return '';\n\n    from = posix.resolve(from);\n    to = posix.resolve(to);\n\n    if (from === to) return '';\n\n    // Trim any leading backslashes\n    var fromStart = 1;\n    for (; fromStart < from.length; ++fromStart) {\n      if (from.charCodeAt(fromStart) !== 47 /*/*/)\n        break;\n    }\n    var fromEnd = from.length;\n    var fromLen = fromEnd - fromStart;\n\n    // Trim any leading backslashes\n    var toStart = 1;\n    for (; toStart < to.length; ++toStart) {\n      if (to.charCodeAt(toStart) !== 47 /*/*/)\n        break;\n    }\n    var toEnd = to.length;\n    var toLen = toEnd - toStart;\n\n    // Compare paths to find the longest common path from root\n    var length = fromLen < toLen ? fromLen : toLen;\n    var lastCommonSep = -1;\n    var i = 0;\n    for (; i <= length; ++i) {\n      if (i === length) {\n        if (toLen > length) {\n          if (to.charCodeAt(toStart + i) === 47 /*/*/) {\n            // We get here if `from` is the exact base path for `to`.\n            // For example: from='/foo/bar'; to='/foo/bar/baz'\n            return to.slice(toStart + i + 1);\n          } else if (i === 0) {\n            // We get here if `from` is the root\n            // For example: from='/'; to='/foo'\n            return to.slice(toStart + i);\n          }\n        } else if (fromLen > length) {\n          if (from.charCodeAt(fromStart + i) === 47 /*/*/) {\n            // We get here if `to` is the exact base path for `from`.\n            // For example: from='/foo/bar/baz'; to='/foo/bar'\n            lastCommonSep = i;\n          } else if (i === 0) {\n            // We get here if `to` is the root.\n            // For example: from='/foo'; to='/'\n            lastCommonSep = 0;\n          }\n        }\n        break;\n      }\n      var fromCode = from.charCodeAt(fromStart + i);\n      var toCode = to.charCodeAt(toStart + i);\n      if (fromCode !== toCode)\n        break;\n      else if (fromCode === 47 /*/*/)\n        lastCommonSep = i;\n    }\n\n    var out = '';\n    // Generate the relative path based on the path difference between `to`\n    // and `from`\n    for (i = fromStart + lastCommonSep + 1; i <= fromEnd; ++i) {\n      if (i === fromEnd || from.charCodeAt(i) === 47 /*/*/) {\n        if (out.length === 0)\n          out += '..';\n        else\n          out += '/..';\n      }\n    }\n\n    // Lastly, append the rest of the destination (`to`) path that comes after\n    // the common path parts\n    if (out.length > 0)\n      return out + to.slice(toStart + lastCommonSep);\n    else {\n      toStart += lastCommonSep;\n      if (to.charCodeAt(toStart) === 47 /*/*/)\n        ++toStart;\n      return to.slice(toStart);\n    }\n  },\n\n  _makeLong: function _makeLong(path) {\n    return path;\n  },\n\n  dirname: function dirname(path) {\n    assertPath(path);\n    if (path.length === 0) return '.';\n    var code = path.charCodeAt(0);\n    var hasRoot = code === 47 /*/*/;\n    var end = -1;\n    var matchedSlash = true;\n    for (var i = path.length - 1; i >= 1; --i) {\n      code = path.charCodeAt(i);\n      if (code === 47 /*/*/) {\n          if (!matchedSlash) {\n            end = i;\n            break;\n          }\n        } else {\n        // We saw the first non-path separator\n        matchedSlash = false;\n      }\n    }\n\n    if (end === -1) return hasRoot ? '/' : '.';\n    if (hasRoot && end === 1) return '//';\n    return path.slice(0, end);\n  },\n\n  basename: function basename(path, ext) {\n    if (ext !== undefined && typeof ext !== 'string') throw new TypeError('\"ext\" argument must be a string');\n    assertPath(path);\n\n    var start = 0;\n    var end = -1;\n    var matchedSlash = true;\n    var i;\n\n    if (ext !== undefined && ext.length > 0 && ext.length <= path.length) {\n      if (ext.length === path.length && ext === path) return '';\n      var extIdx = ext.length - 1;\n      var firstNonSlashEnd = -1;\n      for (i = path.length - 1; i >= 0; --i) {\n        var code = path.charCodeAt(i);\n        if (code === 47 /*/*/) {\n            // If we reached a path separator that was not part of a set of path\n            // separators at the end of the string, stop now\n            if (!matchedSlash) {\n              start = i + 1;\n              break;\n            }\n          } else {\n          if (firstNonSlashEnd === -1) {\n            // We saw the first non-path separator, remember this index in case\n            // we need it if the extension ends up not matching\n            matchedSlash = false;\n            firstNonSlashEnd = i + 1;\n          }\n          if (extIdx >= 0) {\n            // Try to match the explicit extension\n            if (code === ext.charCodeAt(extIdx)) {\n              if (--extIdx === -1) {\n                // We matched the extension, so mark this as the end of our path\n                // component\n                end = i;\n              }\n            } else {\n              // Extension does not match, so our result is the entire path\n              // component\n              extIdx = -1;\n              end = firstNonSlashEnd;\n            }\n          }\n        }\n      }\n\n      if (start === end) end = firstNonSlashEnd;else if (end === -1) end = path.length;\n      return path.slice(start, end);\n    } else {\n      for (i = path.length - 1; i >= 0; --i) {\n        if (path.charCodeAt(i) === 47 /*/*/) {\n            // If we reached a path separator that was not part of a set of path\n            // separators at the end of the string, stop now\n            if (!matchedSlash) {\n              start = i + 1;\n              break;\n            }\n          } else if (end === -1) {\n          // We saw the first non-path separator, mark this as the end of our\n          // path component\n          matchedSlash = false;\n          end = i + 1;\n        }\n      }\n\n      if (end === -1) return '';\n      return path.slice(start, end);\n    }\n  },\n\n  extname: function extname(path) {\n    assertPath(path);\n    var startDot = -1;\n    var startPart = 0;\n    var end = -1;\n    var matchedSlash = true;\n    // Track the state of characters (if any) we see before our first dot and\n    // after any path separator we find\n    var preDotState = 0;\n    for (var i = path.length - 1; i >= 0; --i) {\n      var code = path.charCodeAt(i);\n      if (code === 47 /*/*/) {\n          // If we reached a path separator that was not part of a set of path\n          // separators at the end of the string, stop now\n          if (!matchedSlash) {\n            startPart = i + 1;\n            break;\n          }\n          continue;\n        }\n      if (end === -1) {\n        // We saw the first non-path separator, mark this as the end of our\n        // extension\n        matchedSlash = false;\n        end = i + 1;\n      }\n      if (code === 46 /*.*/) {\n          // If this is our first dot, mark it as the start of our extension\n          if (startDot === -1)\n            startDot = i;\n          else if (preDotState !== 1)\n            preDotState = 1;\n      } else if (startDot !== -1) {\n        // We saw a non-dot and non-path separator before our dot, so we should\n        // have a good chance at having a non-empty extension\n        preDotState = -1;\n      }\n    }\n\n    if (startDot === -1 || end === -1 ||\n        // We saw a non-dot character immediately before the dot\n        preDotState === 0 ||\n        // The (right-most) trimmed path component is exactly '..'\n        preDotState === 1 && startDot === end - 1 && startDot === startPart + 1) {\n      return '';\n    }\n    return path.slice(startDot, end);\n  },\n\n  format: function format(pathObject) {\n    if (pathObject === null || typeof pathObject !== 'object') {\n      throw new TypeError('The \"pathObject\" argument must be of type Object. Received type ' + typeof pathObject);\n    }\n    return _format('/', pathObject);\n  },\n\n  parse: function parse(path) {\n    assertPath(path);\n\n    var ret = { root: '', dir: '', base: '', ext: '', name: '' };\n    if (path.length === 0) return ret;\n    var code = path.charCodeAt(0);\n    var isAbsolute = code === 47 /*/*/;\n    var start;\n    if (isAbsolute) {\n      ret.root = '/';\n      start = 1;\n    } else {\n      start = 0;\n    }\n    var startDot = -1;\n    var startPart = 0;\n    var end = -1;\n    var matchedSlash = true;\n    var i = path.length - 1;\n\n    // Track the state of characters (if any) we see before our first dot and\n    // after any path separator we find\n    var preDotState = 0;\n\n    // Get non-dir info\n    for (; i >= start; --i) {\n      code = path.charCodeAt(i);\n      if (code === 47 /*/*/) {\n          // If we reached a path separator that was not part of a set of path\n          // separators at the end of the string, stop now\n          if (!matchedSlash) {\n            startPart = i + 1;\n            break;\n          }\n          continue;\n        }\n      if (end === -1) {\n        // We saw the first non-path separator, mark this as the end of our\n        // extension\n        matchedSlash = false;\n        end = i + 1;\n      }\n      if (code === 46 /*.*/) {\n          // If this is our first dot, mark it as the start of our extension\n          if (startDot === -1) startDot = i;else if (preDotState !== 1) preDotState = 1;\n        } else if (startDot !== -1) {\n        // We saw a non-dot and non-path separator before our dot, so we should\n        // have a good chance at having a non-empty extension\n        preDotState = -1;\n      }\n    }\n\n    if (startDot === -1 || end === -1 ||\n    // We saw a non-dot character immediately before the dot\n    preDotState === 0 ||\n    // The (right-most) trimmed path component is exactly '..'\n    preDotState === 1 && startDot === end - 1 && startDot === startPart + 1) {\n      if (end !== -1) {\n        if (startPart === 0 && isAbsolute) ret.base = ret.name = path.slice(1, end);else ret.base = ret.name = path.slice(startPart, end);\n      }\n    } else {\n      if (startPart === 0 && isAbsolute) {\n        ret.name = path.slice(1, startDot);\n        ret.base = path.slice(1, end);\n      } else {\n        ret.name = path.slice(startPart, startDot);\n        ret.base = path.slice(startPart, end);\n      }\n      ret.ext = path.slice(startDot, end);\n    }\n\n    if (startPart > 0) ret.dir = path.slice(0, startPart - 1);else if (isAbsolute) ret.dir = '/';\n\n    return ret;\n  },\n\n  sep: '/',\n  delimiter: ':',\n  win32: null,\n  posix: null\n};\n\nposix.posix = posix;\n\nmodule.exports = posix;\n", "// The module cache\nvar __webpack_module_cache__ = {};\n\n// The require function\nfunction __webpack_require__(moduleId) {\n\t// Check if module is in cache\n\tvar cachedModule = __webpack_module_cache__[moduleId];\n\tif (cachedModule !== undefined) {\n\t\treturn cachedModule.exports;\n\t}\n\t// Create a new module (and put it into the cache)\n\tvar module = __webpack_module_cache__[moduleId] = {\n\t\t// no module.id needed\n\t\t// no module.loaded needed\n\t\texports: {}\n\t};\n\n\t// Execute the module function\n\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n\n\t// Return the exports of the module\n\treturn module.exports;\n}\n\n", "// define getter functions for harmony exports\n__webpack_require__.d = (exports, definition) => {\n\tfor(var key in definition) {\n\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n\t\t}\n\t}\n};", "__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))", "// define __esModule on exports\n__webpack_require__.r = (exports) => {\n\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n\t}\n\tObject.defineProperty(exports, '__esModule', { value: true });\n};", "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\n'use strict';\n\n// !!!!!\n// SEE https://github.com/microsoft/vscode/blob/master/src/vs/base/common/platform.ts\n// !!!!!\n\ndeclare const process: { platform: 'win32' };\ndeclare const navigator: { userAgent: string };\n\nexport let isWindows: boolean;\n\nif (typeof process === 'object') {\n\tisWindows = process.platform === 'win32';\n} else if (typeof navigator === 'object') {\n\tlet userAgent = navigator.userAgent;\n\tisWindows = userAgent.indexOf('Windows') >= 0;\n}\n", "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\n'use strict';\n\nimport { CharCode } from './charCode'\nimport { isWindows } from './platform';\n\nconst _schemePattern = /^\\w[\\w\\d+.-]*$/;\nconst _singleSlashStart = /^\\//;\nconst _doubleSlashStart = /^\\/\\//;\n\nfunction _validateUri(ret: URI, _strict?: boolean): void {\n\n\t// scheme, must be set\n\tif (!ret.scheme && _strict) {\n\t\tthrow new Error(`[UriError]: Scheme is missing: {scheme: \"\", authority: \"${ret.authority}\", path: \"${ret.path}\", query: \"${ret.query}\", fragment: \"${ret.fragment}\"}`);\n\t}\n\n\t// scheme, https://tools.ietf.org/html/rfc3986#section-3.1\n\t// ALPHA *( ALPHA / DIGIT / \"+\" / \"-\" / \".\" )\n\tif (ret.scheme && !_schemePattern.test(ret.scheme)) {\n\t\tthrow new Error('[UriError]: Scheme contains illegal characters.');\n\t}\n\n\t// path, http://tools.ietf.org/html/rfc3986#section-3.3\n\t// If a URI contains an authority component, then the path component\n\t// must either be empty or begin with a slash (\"/\") character.  If a URI\n\t// does not contain an authority component, then the path cannot begin\n\t// with two slash characters (\"//\").\n\tif (ret.path) {\n\t\tif (ret.authority) {\n\t\t\tif (!_singleSlashStart.test(ret.path)) {\n\t\t\t\tthrow new Error('[UriError]: If a URI contains an authority component, then the path component must either be empty or begin with a slash (\"/\") character');\n\t\t\t}\n\t\t} else {\n\t\t\tif (_doubleSlashStart.test(ret.path)) {\n\t\t\t\tthrow new Error('[UriError]: If a URI does not contain an authority component, then the path cannot begin with two slash characters (\"//\")');\n\t\t\t}\n\t\t}\n\t}\n}\n\n// for a while we allowed uris *without* schemes and this is the migration\n// for them, e.g. an uri without scheme and without strict-mode warns and falls\n// back to the file-scheme. that should cause the least carnage and still be a\n// clear warning\nfunction _schemeFix(scheme: string, _strict: boolean): string {\n\tif (!scheme && !_strict) {\n\t\treturn 'file';\n\t}\n\treturn scheme;\n}\n\n// implements a bit of https://tools.ietf.org/html/rfc3986#section-5\nfunction _referenceResolution(scheme: string, path: string): string {\n\n\t// the slash-character is our 'default base' as we don't\n\t// support constructing URIs relative to other URIs. This\n\t// also means that we alter and potentially break paths.\n\t// see https://tools.ietf.org/html/rfc3986#section-5.1.4\n\tswitch (scheme) {\n\t\tcase 'https':\n\t\tcase 'http':\n\t\tcase 'file':\n\t\t\tif (!path) {\n\t\t\t\tpath = _slash;\n\t\t\t} else if (path[0] !== _slash) {\n\t\t\t\tpath = _slash + path;\n\t\t\t}\n\t\t\tbreak;\n\t}\n\treturn path;\n}\n\nconst _empty = '';\nconst _slash = '/';\nconst _regexp = /^(([^:/?#]+?):)?(\\/\\/([^/?#]*))?([^?#]*)(\\?([^#]*))?(#(.*))?/;\n\n/**\n * Uniform Resource Identifier (URI) http://tools.ietf.org/html/rfc3986.\n * This class is a simple parser which creates the basic component parts\n * (http://tools.ietf.org/html/rfc3986#section-3) with minimal validation\n * and encoding.\n *\n * ```txt\n *       foo://example.com:8042/over/there?name=ferret#nose\n *       \\_/   \\______________/\\_________/ \\_________/ \\__/\n *        |           |            |            |        |\n *     scheme     authority       path        query   fragment\n *        |   _____________________|__\n *       / \\ /                        \\\n *       urn:example:animal:ferret:nose\n * ```\n */\nexport class URI implements UriComponents {\n\n\tstatic isUri(thing: any): thing is URI {\n\t\tif (thing instanceof URI) {\n\t\t\treturn true;\n\t\t}\n\t\tif (!thing) {\n\t\t\treturn false;\n\t\t}\n\t\treturn typeof (<URI>thing).authority === 'string'\n\t\t\t&& typeof (<URI>thing).fragment === 'string'\n\t\t\t&& typeof (<URI>thing).path === 'string'\n\t\t\t&& typeof (<URI>thing).query === 'string'\n\t\t\t&& typeof (<URI>thing).scheme === 'string'\n\t\t\t&& typeof (<URI>thing).fsPath === 'string'\n\t\t\t&& typeof (<URI>thing).with === 'function'\n\t\t\t&& typeof (<URI>thing).toString === 'function';\n\t}\n\n\t/**\n\t * scheme is the 'http' part of 'http://www.example.com/some/path?query#fragment'.\n\t * The part before the first colon.\n\t */\n\treadonly scheme: string;\n\n\t/**\n\t * authority is the 'www.example.com' part of 'http://www.example.com/some/path?query#fragment'.\n\t * The part between the first double slashes and the next slash.\n\t */\n\treadonly authority: string;\n\n\t/**\n\t * path is the '/some/path' part of 'http://www.example.com/some/path?query#fragment'.\n\t */\n\treadonly path: string;\n\n\t/**\n\t * query is the 'query' part of 'http://www.example.com/some/path?query#fragment'.\n\t */\n\treadonly query: string;\n\n\t/**\n\t * fragment is the 'fragment' part of 'http://www.example.com/some/path?query#fragment'.\n\t */\n\treadonly fragment: string;\n\n\t/**\n\t * @internal\n\t */\n\tprotected constructor(scheme: string, authority?: string, path?: string, query?: string, fragment?: string, _strict?: boolean);\n\n\t/**\n\t * @internal\n\t */\n\tprotected constructor(components: UriComponents);\n\n\t/**\n\t * @internal\n\t */\n\tprotected constructor(schemeOrData: string | UriComponents, authority?: string, path?: string, query?: string, fragment?: string, _strict: boolean = false) {\n\n\t\tif (typeof schemeOrData === 'object') {\n\t\t\tthis.scheme = schemeOrData.scheme || _empty;\n\t\t\tthis.authority = schemeOrData.authority || _empty;\n\t\t\tthis.path = schemeOrData.path || _empty;\n\t\t\tthis.query = schemeOrData.query || _empty;\n\t\t\tthis.fragment = schemeOrData.fragment || _empty;\n\t\t\t// no validation because it's this URI\n\t\t\t// that creates uri components.\n\t\t\t// _validateUri(this);\n\t\t} else {\n\t\t\tthis.scheme = _schemeFix(schemeOrData, _strict);\n\t\t\tthis.authority = authority || _empty;\n\t\t\tthis.path = _referenceResolution(this.scheme, path || _empty);\n\t\t\tthis.query = query || _empty;\n\t\t\tthis.fragment = fragment || _empty;\n\n\t\t\t_validateUri(this, _strict);\n\t\t}\n\t}\n\n\t// ---- filesystem path -----------------------\n\n\t/**\n\t * Returns a string representing the corresponding file system path of this URI.\n\t * Will handle UNC paths, normalizes windows drive letters to lower-case, and uses the\n\t * platform specific path separator.\n\t *\n\t * * Will *not* validate the path for invalid characters and semantics.\n\t * * Will *not* look at the scheme of this URI.\n\t * * The result shall *not* be used for display purposes but for accessing a file on disk.\n\t *\n\t *\n\t * The *difference* to `URI#path` is the use of the platform specific separator and the handling\n\t * of UNC paths. See the below sample of a file-uri with an authority (UNC path).\n\t *\n\t * ```ts\n\t\tconst u = URI.parse('file://server/c$/folder/file.txt')\n\t\tu.authority === 'server'\n\t\tu.path === '/shares/c$/file.txt'\n\t\tu.fsPath === '\\\\server\\c$\\folder\\file.txt'\n\t```\n\t *\n\t * Using `URI#path` to read a file (using fs-apis) would not be enough because parts of the path,\n\t * namely the server name, would be missing. Therefore `URI#fsPath` exists - it's sugar to ease working\n\t * with URIs that represent files on disk (`file` scheme).\n\t */\n\tget fsPath(): string {\n\t\t// if (this.scheme !== 'file') {\n\t\t// \tconsole.warn(`[UriError] calling fsPath with scheme ${this.scheme}`);\n\t\t// }\n\t\treturn uriToFsPath(this, false);\n\t}\n\n\t// ---- modify to new -------------------------\n\n\twith(change: { scheme?: string; authority?: string | null; path?: string | null; query?: string | null; fragment?: string | null }): URI {\n\n\t\tif (!change) {\n\t\t\treturn this;\n\t\t}\n\n\t\tlet { scheme, authority, path, query, fragment } = change;\n\t\tif (scheme === undefined) {\n\t\t\tscheme = this.scheme;\n\t\t} else if (scheme === null) {\n\t\t\tscheme = _empty;\n\t\t}\n\t\tif (authority === undefined) {\n\t\t\tauthority = this.authority;\n\t\t} else if (authority === null) {\n\t\t\tauthority = _empty;\n\t\t}\n\t\tif (path === undefined) {\n\t\t\tpath = this.path;\n\t\t} else if (path === null) {\n\t\t\tpath = _empty;\n\t\t}\n\t\tif (query === undefined) {\n\t\t\tquery = this.query;\n\t\t} else if (query === null) {\n\t\t\tquery = _empty;\n\t\t}\n\t\tif (fragment === undefined) {\n\t\t\tfragment = this.fragment;\n\t\t} else if (fragment === null) {\n\t\t\tfragment = _empty;\n\t\t}\n\n\t\tif (scheme === this.scheme\n\t\t\t&& authority === this.authority\n\t\t\t&& path === this.path\n\t\t\t&& query === this.query\n\t\t\t&& fragment === this.fragment) {\n\n\t\t\treturn this;\n\t\t}\n\n\t\treturn new Uri(scheme, authority, path, query, fragment);\n\t}\n\n\t// ---- parse & validate ------------------------\n\n\t/**\n\t * Creates a new URI from a string, e.g. `http://www.example.com/some/path`,\n\t * `file:///usr/home`, or `scheme:with/path`.\n\t *\n\t * @param value A string which represents an URI (see `URI#toString`).\n\t */\n\tstatic parse(value: string, _strict: boolean = false): URI {\n\t\tconst match = _regexp.exec(value);\n\t\tif (!match) {\n\t\t\treturn new Uri(_empty, _empty, _empty, _empty, _empty);\n\t\t}\n\t\treturn new Uri(\n\t\t\tmatch[2] || _empty,\n\t\t\tpercentDecode(match[4] || _empty),\n\t\t\tpercentDecode(match[5] || _empty),\n\t\t\tpercentDecode(match[7] || _empty),\n\t\t\tpercentDecode(match[9] || _empty),\n\t\t\t_strict\n\t\t);\n\t}\n\n\t/**\n\t * Creates a new URI from a file system path, e.g. `c:\\my\\files`,\n\t * `/usr/home`, or `\\\\server\\share\\some\\path`.\n\t *\n\t * The *difference* between `URI#parse` and `URI#file` is that the latter treats the argument\n\t * as path, not as stringified-uri. E.g. `URI.file(path)` is **not the same as**\n\t * `URI.parse('file://' + path)` because the path might contain characters that are\n\t * interpreted (# and ?). See the following sample:\n\t * ```ts\n\tconst good = URI.file('/coding/c#/project1');\n\tgood.scheme === 'file';\n\tgood.path === '/coding/c#/project1';\n\tgood.fragment === '';\n\tconst bad = URI.parse('file://' + '/coding/c#/project1');\n\tbad.scheme === 'file';\n\tbad.path === '/coding/c'; // path is now broken\n\tbad.fragment === '/project1';\n\t```\n\t *\n\t * @param path A file system path (see `URI#fsPath`)\n\t */\n\tstatic file(path: string): URI {\n\n\t\tlet authority = _empty;\n\n\t\t// normalize to fwd-slashes on windows,\n\t\t// on other systems bwd-slashes are valid\n\t\t// filename character, eg /f\\oo/ba\\r.txt\n\t\tif (isWindows) {\n\t\t\tpath = path.replace(/\\\\/g, _slash);\n\t\t}\n\n\t\t// check for authority as used in UNC shares\n\t\t// or use the path as given\n\t\tif (path[0] === _slash && path[1] === _slash) {\n\t\t\tconst idx = path.indexOf(_slash, 2);\n\t\t\tif (idx === -1) {\n\t\t\t\tauthority = path.substring(2);\n\t\t\t\tpath = _slash;\n\t\t\t} else {\n\t\t\t\tauthority = path.substring(2, idx);\n\t\t\t\tpath = path.substring(idx) || _slash;\n\t\t\t}\n\t\t}\n\n\t\treturn new Uri('file', authority, path, _empty, _empty);\n\t}\n\n\tstatic from(components: { scheme: string; authority?: string; path?: string; query?: string; fragment?: string }): URI {\n\t\tconst result = new Uri(\n\t\t\tcomponents.scheme,\n\t\t\tcomponents.authority,\n\t\t\tcomponents.path,\n\t\t\tcomponents.query,\n\t\t\tcomponents.fragment,\n\t\t);\n\t\t_validateUri(result, true);\n\t\treturn result;\n\t}\n\n\t// ---- printing/externalize ---------------------------\n\n\t/**\n\t * Creates a string representation for this URI. It's guaranteed that calling\n\t * `URI.parse` with the result of this function creates an URI which is equal\n\t * to this URI.\n\t *\n\t * * The result shall *not* be used for display purposes but for externalization or transport.\n\t * * The result will be encoded using the percentage encoding and encoding happens mostly\n\t * ignore the scheme-specific encoding rules.\n\t *\n\t * @param skipEncoding Do not encode the result, default is `false`\n\t */\n\ttoString(skipEncoding: boolean = false): string {\n\t\treturn _asFormatted(this, skipEncoding);\n\t}\n\n\ttoJSON(): UriComponents {\n\t\treturn this;\n\t}\n\n\tstatic revive(data: UriComponents | URI): URI;\n\tstatic revive(data: UriComponents | URI | undefined): URI | undefined;\n\tstatic revive(data: UriComponents | URI | null): URI | null;\n\tstatic revive(data: UriComponents | URI | undefined | null): URI | undefined | null;\n\tstatic revive(data: UriComponents | URI | undefined | null): URI | undefined | null {\n\t\tif (!data) {\n\t\t\treturn <any>data;\n\t\t} else if (data instanceof URI) {\n\t\t\treturn data;\n\t\t} else {\n\t\t\tconst result = new Uri(data);\n\t\t\tresult._formatted = (<UriState>data).external;\n\t\t\tresult._fsPath = (<UriState>data)._sep === _pathSepMarker ? (<UriState>data).fsPath : null;\n\t\t\treturn result;\n\t\t}\n\t}\n}\n\nexport interface UriComponents {\n\tscheme: string;\n\tauthority: string;\n\tpath: string;\n\tquery: string;\n\tfragment: string;\n}\n\ninterface UriState extends UriComponents {\n\t$mid: number;\n\texternal: string;\n\tfsPath: string;\n\t_sep: 1 | undefined;\n}\n\nconst _pathSepMarker = isWindows ? 1 : undefined;\n\n// This class exists so that URI is compatible with vscode.Uri (API).\nclass Uri extends URI {\n\n\t_formatted: string | null = null;\n\t_fsPath: string | null = null;\n\n\toverride get fsPath(): string {\n\t\tif (!this._fsPath) {\n\t\t\tthis._fsPath = uriToFsPath(this, false);\n\t\t}\n\t\treturn this._fsPath;\n\t}\n\n\toverride toString(skipEncoding: boolean = false): string {\n\t\tif (!skipEncoding) {\n\t\t\tif (!this._formatted) {\n\t\t\t\tthis._formatted = _asFormatted(this, false);\n\t\t\t}\n\t\t\treturn this._formatted;\n\t\t} else {\n\t\t\t// we don't cache that\n\t\t\treturn _asFormatted(this, true);\n\t\t}\n\t}\n\n\toverride toJSON(): UriComponents {\n\t\tconst res = <UriState>{\n\t\t\t$mid: 1\n\t\t};\n\t\t// cached state\n\t\tif (this._fsPath) {\n\t\t\tres.fsPath = this._fsPath;\n\t\t\tres._sep = _pathSepMarker;\n\t\t}\n\t\tif (this._formatted) {\n\t\t\tres.external = this._formatted;\n\t\t}\n\t\t// uri components\n\t\tif (this.path) {\n\t\t\tres.path = this.path;\n\t\t}\n\t\tif (this.scheme) {\n\t\t\tres.scheme = this.scheme;\n\t\t}\n\t\tif (this.authority) {\n\t\t\tres.authority = this.authority;\n\t\t}\n\t\tif (this.query) {\n\t\t\tres.query = this.query;\n\t\t}\n\t\tif (this.fragment) {\n\t\t\tres.fragment = this.fragment;\n\t\t}\n\t\treturn res;\n\t}\n}\n\n// reserved characters: https://tools.ietf.org/html/rfc3986#section-2.2\nconst encodeTable: { [ch: number]: string } = {\n\t[CharCode.Colon]: '%3A', // gen-delims\n\t[CharCode.Slash]: '%2F',\n\t[CharCode.QuestionMark]: '%3F',\n\t[CharCode.Hash]: '%23',\n\t[CharCode.OpenSquareBracket]: '%5B',\n\t[CharCode.CloseSquareBracket]: '%5D',\n\t[CharCode.AtSign]: '%40',\n\n\t[CharCode.ExclamationMark]: '%21', // sub-delims\n\t[CharCode.DollarSign]: '%24',\n\t[CharCode.Ampersand]: '%26',\n\t[CharCode.SingleQuote]: '%27',\n\t[CharCode.OpenParen]: '%28',\n\t[CharCode.CloseParen]: '%29',\n\t[CharCode.Asterisk]: '%2A',\n\t[CharCode.Plus]: '%2B',\n\t[CharCode.Comma]: '%2C',\n\t[CharCode.Semicolon]: '%3B',\n\t[CharCode.Equals]: '%3D',\n\n\t[CharCode.Space]: '%20',\n};\n\nfunction encodeURIComponentFast(uriComponent: string, isPath: boolean, isAuthority: boolean): string {\n\tlet res: string | undefined = undefined;\n\tlet nativeEncodePos = -1;\n\n\tfor (let pos = 0; pos < uriComponent.length; pos++) {\n\t\tconst code = uriComponent.charCodeAt(pos);\n\n\t\t// unreserved characters: https://tools.ietf.org/html/rfc3986#section-2.3\n\t\tif (\n\t\t\t(code >= CharCode.a && code <= CharCode.z)\n\t\t\t|| (code >= CharCode.A && code <= CharCode.Z)\n\t\t\t|| (code >= CharCode.Digit0 && code <= CharCode.Digit9)\n\t\t\t|| code === CharCode.Dash\n\t\t\t|| code === CharCode.Period\n\t\t\t|| code === CharCode.Underline\n\t\t\t|| code === CharCode.Tilde\n\t\t\t|| (isPath && code === CharCode.Slash)\n\t\t\t|| (isAuthority && code === CharCode.OpenSquareBracket)\n\t\t\t|| (isAuthority && code === CharCode.CloseSquareBracket)\n\t\t\t|| (isAuthority && code === CharCode.Colon)\n\t\t) {\n\t\t\t// check if we are delaying native encode\n\t\t\tif (nativeEncodePos !== -1) {\n\t\t\t\tres += encodeURIComponent(uriComponent.substring(nativeEncodePos, pos));\n\t\t\t\tnativeEncodePos = -1;\n\t\t\t}\n\t\t\t// check if we write into a new string (by default we try to return the param)\n\t\t\tif (res !== undefined) {\n\t\t\t\tres += uriComponent.charAt(pos);\n\t\t\t}\n\n\t\t} else {\n\t\t\t// encoding needed, we need to allocate a new string\n\t\t\tif (res === undefined) {\n\t\t\t\tres = uriComponent.substr(0, pos);\n\t\t\t}\n\n\t\t\t// check with default table first\n\t\t\tconst escaped = encodeTable[code];\n\t\t\tif (escaped !== undefined) {\n\n\t\t\t\t// check if we are delaying native encode\n\t\t\t\tif (nativeEncodePos !== -1) {\n\t\t\t\t\tres += encodeURIComponent(uriComponent.substring(nativeEncodePos, pos));\n\t\t\t\t\tnativeEncodePos = -1;\n\t\t\t\t}\n\n\t\t\t\t// append escaped variant to result\n\t\t\t\tres += escaped;\n\n\t\t\t} else if (nativeEncodePos === -1) {\n\t\t\t\t// use native encode only when needed\n\t\t\t\tnativeEncodePos = pos;\n\t\t\t}\n\t\t}\n\t}\n\n\tif (nativeEncodePos !== -1) {\n\t\tres += encodeURIComponent(uriComponent.substring(nativeEncodePos));\n\t}\n\n\treturn res !== undefined ? res : uriComponent;\n}\n\nfunction encodeURIComponentMinimal(path: string): string {\n\tlet res: string | undefined = undefined;\n\tfor (let pos = 0; pos < path.length; pos++) {\n\t\tconst code = path.charCodeAt(pos);\n\t\tif (code === CharCode.Hash || code === CharCode.QuestionMark) {\n\t\t\tif (res === undefined) {\n\t\t\t\tres = path.substr(0, pos);\n\t\t\t}\n\t\t\tres += encodeTable[code];\n\t\t} else {\n\t\t\tif (res !== undefined) {\n\t\t\t\tres += path[pos];\n\t\t\t}\n\t\t}\n\t}\n\treturn res !== undefined ? res : path;\n}\n\n/**\n * Compute `fsPath` for the given uri\n */\nexport function uriToFsPath(uri: URI, keepDriveLetterCasing: boolean): string {\n\n\tlet value: string;\n\tif (uri.authority && uri.path.length > 1 && uri.scheme === 'file') {\n\t\t// unc path: file://shares/c$/far/boo\n\t\tvalue = `//${uri.authority}${uri.path}`;\n\t} else if (\n\t\turi.path.charCodeAt(0) === CharCode.Slash\n\t\t&& (uri.path.charCodeAt(1) >= CharCode.A && uri.path.charCodeAt(1) <= CharCode.Z || uri.path.charCodeAt(1) >= CharCode.a && uri.path.charCodeAt(1) <= CharCode.z)\n\t\t&& uri.path.charCodeAt(2) === CharCode.Colon\n\t) {\n\t\tif (!keepDriveLetterCasing) {\n\t\t\t// windows drive letter: file:///c:/far/boo\n\t\t\tvalue = uri.path[1].toLowerCase() + uri.path.substr(2);\n\t\t} else {\n\t\t\tvalue = uri.path.substr(1);\n\t\t}\n\t} else {\n\t\t// other path\n\t\tvalue = uri.path;\n\t}\n\tif (isWindows) {\n\t\tvalue = value.replace(/\\//g, '\\\\');\n\t}\n\treturn value;\n}\n\n/**\n * Create the external version of a uri\n */\nfunction _asFormatted(uri: URI, skipEncoding: boolean): string {\n\n\tconst encoder = !skipEncoding\n\t\t? encodeURIComponentFast\n\t\t: encodeURIComponentMinimal;\n\n\tlet res = '';\n\tlet { scheme, authority, path, query, fragment } = uri;\n\tif (scheme) {\n\t\tres += scheme;\n\t\tres += ':';\n\t}\n\tif (authority || scheme === 'file') {\n\t\tres += _slash;\n\t\tres += _slash;\n\t}\n\tif (authority) {\n\t\tlet idx = authority.indexOf('@');\n\t\tif (idx !== -1) {\n\t\t\t// <user>@<auth>\n\t\t\tconst userinfo = authority.substr(0, idx);\n\t\t\tauthority = authority.substr(idx + 1);\n\t\t\tidx = userinfo.lastIndexOf(':');\n\t\t\tif (idx === -1) {\n\t\t\t\tres += encoder(userinfo, false, false);\n\t\t\t} else {\n\t\t\t\t// <user>:<pass>@<auth>\n\t\t\t\tres += encoder(userinfo.substr(0, idx), false, false);\n\t\t\t\tres += ':';\n\t\t\t\tres += encoder(userinfo.substr(idx + 1), false, true);\n\t\t\t}\n\t\t\tres += '@';\n\t\t}\n\t\tauthority = authority.toLowerCase();\n\t\tidx = authority.lastIndexOf(':');\n\t\tif (idx === -1) {\n\t\t\tres += encoder(authority, false, true);\n\t\t} else {\n\t\t\t// <auth>:<port>\n\t\t\tres += encoder(authority.substr(0, idx), false, true);\n\t\t\tres += authority.substr(idx);\n\t\t}\n\t}\n\tif (path) {\n\t\t// lower-case windows drive letters in /C:/fff or C:/fff\n\t\tif (path.length >= 3 && path.charCodeAt(0) === CharCode.Slash && path.charCodeAt(2) === CharCode.Colon) {\n\t\t\tconst code = path.charCodeAt(1);\n\t\t\tif (code >= CharCode.A && code <= CharCode.Z) {\n\t\t\t\tpath = `/${String.fromCharCode(code + 32)}:${path.substr(3)}`; // \"/c:\".length === 3\n\t\t\t}\n\t\t} else if (path.length >= 2 && path.charCodeAt(1) === CharCode.Colon) {\n\t\t\tconst code = path.charCodeAt(0);\n\t\t\tif (code >= CharCode.A && code <= CharCode.Z) {\n\t\t\t\tpath = `${String.fromCharCode(code + 32)}:${path.substr(2)}`; // \"/c:\".length === 3\n\t\t\t}\n\t\t}\n\t\t// encode the rest of the path\n\t\tres += encoder(path, true, false);\n\t}\n\tif (query) {\n\t\tres += '?';\n\t\tres += encoder(query, false, false);\n\t}\n\tif (fragment) {\n\t\tres += '#';\n\t\tres += !skipEncoding ? encodeURIComponentFast(fragment, false, false) : fragment;\n\t}\n\treturn res;\n}\n\n// --- decode\n\nfunction decodeURIComponentGraceful(str: string): string {\n\ttry {\n\t\treturn decodeURIComponent(str);\n\t} catch {\n\t\tif (str.length > 3) {\n\t\t\treturn str.substr(0, 3) + decodeURIComponentGraceful(str.substr(3));\n\t\t} else {\n\t\t\treturn str;\n\t\t}\n\t}\n}\n\nconst _rEncodedAsHex = /(%[0-9A-Za-z][0-9A-Za-z])+/g;\n\nfunction percentDecode(str: string): string {\n\tif (!str.match(_rEncodedAsHex)) {\n\t\treturn str;\n\t}\n\treturn str.replace(_rEncodedAsHex, (match) => decodeURIComponentGraceful(match));\n}\n\n/**\n * Mapped-type that replaces all occurrences of URI with UriComponents\n */\nexport type UriDto<T> = { [K in keyof T]: T[K] extends URI\n\t? UriComponents\n\t: UriDto<T[K]> };\n", "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\n\n'use strict';\n\nimport { CharCode } from './charCode';\nimport { URI } from './uri';\nimport * as nodePath from 'path';\n\nconst posixPath = nodePath.posix || nodePath;\nconst slash = '/';\n\nexport namespace Utils {\n\n    /**\n     * Joins one or more input paths to the path of URI. \n     * '/' is used as the directory separation character. \n     * \n     * The resolved path will be normalized. That means:\n     *  - all '..' and '.' segments are resolved.\n     *  - multiple, sequential occurences of '/' are replaced by a single instance of '/'.\n     *  - trailing separators are preserved.\n     * \n     * @param uri The input URI.\n     * @param paths The paths to be joined with the path of URI.\n     * @returns A URI with the joined path. All other properties of the URI (scheme, authority, query, fragments, ...) will be taken from the input URI.\n     */\n    export function joinPath(uri: URI, ...paths: string[]): URI {\n        return uri.with({ path: posixPath.join(uri.path, ...paths) });\n    }\n\n\n    /**\n     * Resolves one or more paths against the path of a URI. \n     * '/' is used as the directory separation character. \n     * \n     * The resolved path will be normalized. That means:\n     *  - all '..' and '.' segments are resolved. \n     *  - multiple, sequential occurences of '/' are replaced by a single instance of '/'.\n     *  - trailing separators are removed.\n     * \n     * @param uri The input URI.\n     * @param paths The paths to resolve against the path of URI.\n     * @returns A URI with the resolved path. All other properties of the URI (scheme, authority, query, fragments, ...) will be taken from the input URI.\n     */\n    export function resolvePath(uri: URI, ...paths: string[]): URI {\n        let path = uri.path; \n        let slashAdded = false;\n        if (path[0] !== slash) {\n            path = slash + path; // make the path abstract: for posixPath.resolve the first segments has to be absolute or cwd is used.\n            slashAdded = true;\n        }\n        let resolvedPath = posixPath.resolve(path, ...paths);\n        if (slashAdded && resolvedPath[0] === slash && !uri.authority) {\n            resolvedPath = resolvedPath.substring(1);\n        }\n        return uri.with({ path: resolvedPath });\n    }\n\n    /**\n     * Returns a URI where the path is the directory name of the input uri, similar to the Unix dirname command. \n     * In the path, '/' is recognized as the directory separation character. Trailing directory separators are ignored.\n     * The orignal URI is returned if the URIs path is empty or does not contain any path segments.\n     * \n     * @param uri The input URI.\n     * @return The last segment of the URIs path.\n     */\n    export function dirname(uri: URI): URI {\n        if (uri.path.length === 0 || uri.path === slash) {\n            return uri;\n        }\n        let path = posixPath.dirname(uri.path);\n        if (path.length === 1 && path.charCodeAt(0) === CharCode.Period) {\n            path = '';\n        }\n        return uri.with({ path });\n    }\n\n    /**\n     * Returns the last segment of the path of a URI, similar to the Unix basename command. \n     * In the path, '/' is recognized as the directory separation character. Trailing directory separators are ignored.\n     * The empty string is returned if the URIs path is empty or does not contain any path segments.\n     * \n     * @param uri The input URI.\n     * @return The base name of the URIs path.\n     */\n    export function basename(uri: URI): string {\n        return posixPath.basename(uri.path);\n    }\n\n    /**\n     * Returns the extension name of the path of a URI, similar to the Unix extname command. \n     * In the path, '/' is recognized as the directory separation character. Trailing directory separators are ignored.\n     * The empty string is returned if the URIs path is empty or does not contain any path segments.\n     * \n     * @param uri The input URI.\n     * @return The extension name of the URIs path.\n     */\n    export function extname(uri: URI): string {\n        return posixPath.extname(uri.path);\n    }\n}"], "mappings": ";;;;;;;;;AA0BA,aAASA,GAAWC,IAAAA;AAClB,UAAoB,YAAA,OAATA,GACT,OAAM,IAAIC,UAAU,qCAAqCC,KAAKC,UAAUH,EAAAA,CAAAA;IAE5E;AAGA,aAASI,GAAqBJ,IAAMK,IAAAA;AAMlC,eADIC,IAJAC,KAAM,IACNC,KAAoB,GACpBC,KAAAA,IACAC,KAAO,GAEFC,KAAI,GAAGA,MAAKX,GAAKY,QAAAA,EAAUD,IAAG;AACrC,YAAIA,KAAIX,GAAKY,OACXN,CAAAA,KAAON,GAAKa,WAAWF,EAAAA;aACpB;AAAA,cAAa,OAATL,GACP;AAEAA,UAAAA,KAAO;QAAQ;AACjB,YAAa,OAATA,IAAmB;AACrB,cAAIG,OAAcE,KAAI,KAAc,MAATD,GAAAA;mBAEhBD,OAAcE,KAAI,KAAc,MAATD,IAAY;AAC5C,gBAAIH,GAAIK,SAAS,KAA2B,MAAtBJ,MAA8D,OAAnCD,GAAIM,WAAWN,GAAIK,SAAS,CAAA,KAAsD,OAAnCL,GAAIM,WAAWN,GAAIK,SAAS,CAAA;AAC1H,kBAAIL,GAAIK,SAAS,GAAG;AAClB,oBAAIE,KAAiBP,GAAIQ,YAAY,GAAA;AACrC,oBAAID,OAAmBP,GAAIK,SAAS,GAAG;AAAA,yBACjCE,MACFP,KAAM,IACNC,KAAoB,KAGpBA,MADAD,KAAMA,GAAIS,MAAM,GAAGF,EAAAA,GACKF,SAAS,IAAIL,GAAIQ,YAAY,GAAA,GAEvDN,KAAYE,IACZD,KAAO;AACP;gBACF;cACF,WAA0B,MAAfH,GAAIK,UAA+B,MAAfL,GAAIK,QAAc;AAC/CL,gBAAAA,KAAM,IACNC,KAAoB,GACpBC,KAAYE,IACZD,KAAO;AACP;cACF;;AAEEL,YAAAA,OACEE,GAAIK,SAAS,IACfL,MAAO,QAEPA,KAAM,MACRC,KAAoB;UAExB,MACMD,CAAAA,GAAIK,SAAS,IACfL,MAAO,MAAMP,GAAKgB,MAAMP,KAAY,GAAGE,EAAAA,IAEvCJ,KAAMP,GAAKgB,MAAMP,KAAY,GAAGE,EAAAA,GAClCH,KAAoBG,KAAIF,KAAY;AAEtCA,UAAAA,KAAYE,IACZD,KAAO;QACT,MAAoB,QAATJ,MAAAA,OAAqBI,KAAAA,EAC5BA,KAEFA,KAAAA;MAEJ;AACA,aAAOH;IACT;AAcA,QAAIU,KAAQ,EAEVC,SAAS,WAAA;AAKP,eAFIC,IAFAC,KAAe,IACfC,KAAAA,OAGKV,KAAIW,UAAUV,SAAS,GAAGD,MAAAA,MAAM,CAAMU,IAAkBV,MAAK;AACpE,YAAIX;AACAW,QAAAA,MAAK,IACPX,KAAOsB,UAAUX,EAAAA,KAAAA,WAEbQ,OACFA,KAAMI,QAAQJ,IAAAA,IAChBnB,KAAOmB,KAGTpB,GAAWC,EAAAA,GAGS,MAAhBA,GAAKY,WAITQ,KAAepB,KAAO,MAAMoB,IAC5BC,KAA0C,OAAvBrB,GAAKa,WAAW,CAAA;MACrC;AAQA,aAFAO,KAAehB,GAAqBgB,IAAAA,CAAeC,EAAAA,GAE/CA,KACED,GAAaR,SAAS,IACjB,MAAMQ,KAEN,MACAA,GAAaR,SAAS,IACxBQ,KAEA;IAEX,GAEAI,WAAW,SAAmBxB,IAAAA;AAG5B,UAFAD,GAAWC,EAAAA,GAES,MAAhBA,GAAKY,OAAc,QAAO;AAE9B,UAAIa,KAAoC,OAAvBzB,GAAKa,WAAW,CAAA,GAC7Ba,KAAyD,OAArC1B,GAAKa,WAAWb,GAAKY,SAAS,CAAA;AAQtD,aAHoB,OAFpBZ,KAAOI,GAAqBJ,IAAAA,CAAOyB,EAAAA,GAE1Bb,UAAiBa,OAAYzB,KAAO,MACzCA,GAAKY,SAAS,KAAKc,OAAmB1B,MAAQ,MAE9CyB,KAAmB,MAAMzB,KACtBA;IACT,GAEAyB,YAAY,SAAoBzB,IAAAA;AAE9B,aADAD,GAAWC,EAAAA,GACJA,GAAKY,SAAS,KAA4B,OAAvBZ,GAAKa,WAAW,CAAA;IAC5C,GAEAc,MAAM,WAAA;AACJ,UAAyB,MAArBL,UAAUV,OACZ,QAAO;AAET,eADIgB,IACKjB,KAAI,GAAGA,KAAIW,UAAUV,QAAAA,EAAUD,IAAG;AACzC,YAAIkB,KAAMP,UAAUX,EAAAA;AACpBZ,QAAAA,GAAW8B,EAAAA,GACPA,GAAIjB,SAAS,MAAA,WACXgB,KACFA,KAASC,KAETD,MAAU,MAAMC;MAEtB;AACA,aAAA,WAAID,KACK,MACFX,GAAMO,UAAUI,EAAAA;IACzB,GAEAE,UAAU,SAAkBC,IAAMC,IAAAA;AAIhC,UAHAjC,GAAWgC,EAAAA,GACXhC,GAAWiC,EAAAA,GAEPD,OAASC,GAAI,QAAO;AAKxB,WAHAD,KAAOd,GAAMC,QAAQa,EAAAA,QACrBC,KAAKf,GAAMC,QAAQc,EAAAA,GAEF,QAAO;AAIxB,eADIC,KAAY,GACTA,KAAYF,GAAKnB,UACa,OAA/BmB,GAAKlB,WAAWoB,EAAAA,GAAAA,EADYA,GAAAA;AASlC,eALIC,KAAUH,GAAKnB,QACfuB,KAAUD,KAAUD,IAGpBG,KAAU,GACPA,KAAUJ,GAAGpB,UACa,OAA3BoB,GAAGnB,WAAWuB,EAAAA,GAAAA,EADUA,GAAAA;AAW9B,eANIC,KADQL,GAAGpB,SACKwB,IAGhBxB,KAASuB,KAAUE,KAAQF,KAAUE,IACrCC,KAAAA,IACA3B,KAAI,GACDA,MAAKC,IAAAA,EAAUD,IAAG;AACvB,YAAIA,OAAMC,IAAQ;AAChB,cAAIyB,KAAQzB,IAAQ;AAClB,gBAAmC,OAA/BoB,GAAGnB,WAAWuB,KAAUzB,EAAAA,EAG1B,QAAOqB,GAAGhB,MAAMoB,KAAUzB,KAAI,CAAA;AACzB,gBAAU,MAANA,GAGT,QAAOqB,GAAGhB,MAAMoB,KAAUzB,EAAAA;UAE9B,MAAWwB,CAAAA,KAAUvB,OACoB,OAAnCmB,GAAKlB,WAAWoB,KAAYtB,EAAAA,IAG9B2B,KAAgB3B,KACD,MAANA,OAGT2B,KAAgB;AAGpB;QACF;AACA,YAAIC,KAAWR,GAAKlB,WAAWoB,KAAYtB,EAAAA;AAE3C,YAAI4B,OADSP,GAAGnB,WAAWuB,KAAUzB,EAAAA,EAEnC;AACoB,eAAb4B,OACPD,KAAgB3B;MACpB;AAEA,UAAI6B,KAAM;AAGV,WAAK7B,KAAIsB,KAAYK,KAAgB,GAAG3B,MAAKuB,IAAAA,EAAWvB,GAClDA,CAAAA,OAAMuB,MAAkC,OAAvBH,GAAKlB,WAAWF,EAAAA,MAChB,MAAf6B,GAAI5B,SACN4B,MAAO,OAEPA,MAAO;AAMb,aAAIA,GAAI5B,SAAS,IACR4B,KAAMR,GAAGhB,MAAMoB,KAAUE,EAAAA,KAEhCF,MAAWE,IACoB,OAA3BN,GAAGnB,WAAWuB,EAAAA,KAAAA,EACdA,IACGJ,GAAGhB,MAAMoB,EAAAA;IAEpB,GAEAK,WAAW,SAAmBzC,IAAAA;AAC5B,aAAOA;IACT,GAEA0C,SAAS,SAAiB1C,IAAAA;AAExB,UADAD,GAAWC,EAAAA,GACS,MAAhBA,GAAKY,OAAc,QAAO;AAK9B,eAJIN,KAAON,GAAKa,WAAW,CAAA,GACvB8B,KAAmB,OAATrC,IACVsC,KAAAA,IACAC,KAAAA,MACKlC,KAAIX,GAAKY,SAAS,GAAGD,MAAK,GAAA,EAAKA,GAEtC,KAAa,QADbL,KAAON,GAAKa,WAAWF,EAAAA,IAAAA;AAEnB,YAAA,CAAKkC,IAAc;AACjBD,UAAAA,KAAMjC;AACN;QACF;MAAA,MAGFkC,CAAAA,KAAAA;AAIJ,aAAA,OAAID,KAAmBD,KAAU,MAAM,MACnCA,MAAmB,MAARC,KAAkB,OAC1B5C,GAAKgB,MAAM,GAAG4B,EAAAA;IACvB,GAEAE,UAAU,SAAkB9C,IAAM+C,IAAAA;AAChC,UAAA,WAAIA,MAAoC,YAAA,OAARA,GAAkB,OAAM,IAAI9C,UAAU,iCAAA;AACtEF,MAAAA,GAAWC,EAAAA;AAEX,UAGIW,IAHAqC,KAAQ,GACRJ,KAAAA,IACAC,KAAAA;AAGJ,UAAA,WAAIE,MAAqBA,GAAInC,SAAS,KAAKmC,GAAInC,UAAUZ,GAAKY,QAAQ;AACpE,YAAImC,GAAInC,WAAWZ,GAAKY,UAAUmC,OAAQ/C,GAAM,QAAO;AACvD,YAAIiD,KAASF,GAAInC,SAAS,GACtBsC,KAAAA;AACJ,aAAKvC,KAAIX,GAAKY,SAAS,GAAGD,MAAK,GAAA,EAAKA,IAAG;AACrC,cAAIL,KAAON,GAAKa,WAAWF,EAAAA;AAC3B,cAAa,OAATL,IAAAA;AAGA,gBAAA,CAAKuC,IAAc;AACjBG,cAAAA,KAAQrC,KAAI;AACZ;YACF;UAAA,MAAA,QAEEuC,OAGFL,KAAAA,OACAK,KAAmBvC,KAAI,IAErBsC,MAAU,MAER3C,OAASyC,GAAIlC,WAAWoC,EAAAA,IAAAA,MACR,EAAZA,OAGJL,KAAMjC,OAKRsC,KAAAA,IACAL,KAAMM;QAId;AAGA,eADIF,OAAUJ,KAAKA,KAAMM,KAAAA,OAA0BN,OAAYA,KAAM5C,GAAKY,SACnEZ,GAAKgB,MAAMgC,IAAOJ,EAAAA;MAC3B;AACE,WAAKjC,KAAIX,GAAKY,SAAS,GAAGD,MAAK,GAAA,EAAKA,GAClC,KAA2B,OAAvBX,GAAKa,WAAWF,EAAAA,GAAAA;AAGhB,YAAA,CAAKkC,IAAc;AACjBG,UAAAA,KAAQrC,KAAI;AACZ;QACF;MAAA,MAAA,QACSiC,OAGXC,KAAAA,OACAD,KAAMjC,KAAI;AAId,aAAA,OAAIiC,KAAmB,KAChB5C,GAAKgB,MAAMgC,IAAOJ,EAAAA;IAE7B,GAEAO,SAAS,SAAiBnD,IAAAA;AACxBD,MAAAA,GAAWC,EAAAA;AAQX,eAPIoD,KAAAA,IACAC,KAAY,GACZT,KAAAA,IACAC,KAAAA,MAGAS,KAAc,GACT3C,KAAIX,GAAKY,SAAS,GAAGD,MAAK,GAAA,EAAKA,IAAG;AACzC,YAAIL,KAAON,GAAKa,WAAWF,EAAAA;AAC3B,YAAa,OAATL,GAAAA,QASAsC,OAGFC,KAAAA,OACAD,KAAMjC,KAAI,IAEC,OAATL,KAAAA,OAEI8C,KACFA,KAAWzC,KACY,MAAhB2C,OACPA,KAAc,KAAA,OACTF,OAGTE,KAAAA;iBArBE,CAAKT,IAAc;AACjBQ,UAAAA,KAAY1C,KAAI;AAChB;QACF;MAoBN;AAEA,aAAA,OAAIyC,MAAAA,OAAmBR,MAEH,MAAhBU,MAEgB,MAAhBA,MAAqBF,OAAaR,KAAM,KAAKQ,OAAaC,KAAY,IACjE,KAEFrD,GAAKgB,MAAMoC,IAAUR,EAAAA;IAC9B,GAEAW,QAAQ,SAAgBC,IAAAA;AACtB,UAAmB,SAAfA,MAA6C,YAAA,OAAfA,GAChC,OAAM,IAAIvD,UAAU,qEAAA,OAA4EuD,EAAAA;AAElG,aAvVJ,SAAiBC,IAAKD,IAAAA;AACpB,YAAIE,KAAMF,GAAWE,OAAOF,GAAWG,MACnCC,KAAOJ,GAAWI,SAASJ,GAAWK,QAAQ,OAAOL,GAAWT,OAAO;AAC3E,eAAKW,KAGDA,OAAQF,GAAWG,OACdD,KAAME,KAERF,KA8UU,MA9UEE,KALVA;MAMX,EA6UmB,GAAKJ,EAAAA;IACtB,GAEAM,OAAO,SAAe9D,IAAAA;AACpBD,MAAAA,GAAWC,EAAAA;AAEX,UAAI+D,KAAM,EAAEJ,MAAM,IAAID,KAAK,IAAIE,MAAM,IAAIb,KAAK,IAAIc,MAAM,GAAA;AACxD,UAAoB,MAAhB7D,GAAKY,OAAc,QAAOmD;AAC9B,UAEIf,IAFA1C,KAAON,GAAKa,WAAW,CAAA,GACvBY,KAAsB,OAATnB;AAEbmB,MAAAA,MACFsC,GAAIJ,OAAO,KACXX,KAAQ,KAERA,KAAQ;AAaV,eAXII,KAAAA,IACAC,KAAY,GACZT,KAAAA,IACAC,KAAAA,MACAlC,KAAIX,GAAKY,SAAS,GAIlB0C,KAAc,GAGX3C,MAAKqC,IAAAA,EAASrC,GAEnB,KAAa,QADbL,KAAON,GAAKa,WAAWF,EAAAA,GAAAA,QAUnBiC,OAGFC,KAAAA,OACAD,KAAMjC,KAAI,IAEC,OAATL,KAAAA,OAEI8C,KAAiBA,KAAWzC,KAA2B,MAAhB2C,OAAmBA,KAAc,KAAA,OACnEF,OAGXE,KAAAA;eAlBE,CAAKT,IAAc;AACjBQ,QAAAA,KAAY1C,KAAI;AAChB;MACF;AAwCN,aAAA,OArBIyC,MAAAA,OAAmBR,MAEP,MAAhBU,MAEgB,MAAhBA,MAAqBF,OAAaR,KAAM,KAAKQ,OAAaC,KAAY,IAAA,OAChET,OACiCmB,GAAIH,OAAOG,GAAIF,OAAhC,MAAdR,MAAmB5B,KAAkCzB,GAAKgB,MAAM,GAAG4B,EAAAA,IAAgC5C,GAAKgB,MAAMqC,IAAWT,EAAAA,MAG7G,MAAdS,MAAmB5B,MACrBsC,GAAIF,OAAO7D,GAAKgB,MAAM,GAAGoC,EAAAA,GACzBW,GAAIH,OAAO5D,GAAKgB,MAAM,GAAG4B,EAAAA,MAEzBmB,GAAIF,OAAO7D,GAAKgB,MAAMqC,IAAWD,EAAAA,GACjCW,GAAIH,OAAO5D,GAAKgB,MAAMqC,IAAWT,EAAAA,IAEnCmB,GAAIhB,MAAM/C,GAAKgB,MAAMoC,IAAUR,EAAAA,IAG7BS,KAAY,IAAGU,GAAIL,MAAM1D,GAAKgB,MAAM,GAAGqC,KAAY,CAAA,IAAY5B,OAAYsC,GAAIL,MAAM,MAElFK;IACT,GAEAN,KAAK,KACLO,WAAW,KACXC,OAAO,MACPhD,OAAO,KAAA;AAGTA,IAAAA,GAAMA,QAAQA,IAEdiD,GAAOC,UAAUlD;EAAAA,EAAAA,GC/gBbmD,IAA2B,CAAC;AAGhC,WAASC,EAAoBC,IAAAA;AAE5B,QAAIC,KAAeH,EAAyBE,EAAAA;AAC5C,QAAA,WAAIC,GACH,QAAOA,GAAaJ;AAGrB,QAAID,KAASE,EAAyBE,EAAAA,IAAY,EAGjDH,SAAS,CAAC,EAAA;AAOX,WAHAK,EAAoBF,EAAAA,EAAUJ,IAAQA,GAAOC,SAASE,CAAAA,GAG/CH,GAAOC;EACf;ACrBAE,IAAoBI,IAAI,CAACN,IAASO,OAAAA;AACjC,aAAQC,MAAOD,GACXL,GAAoBO,EAAEF,IAAYC,EAAAA,KAAAA,CAASN,EAAoBO,EAAET,IAASQ,EAAAA,KAC5EE,OAAOC,eAAeX,IAASQ,IAAK,EAAEI,YAAAA,MAAkBC,KAAKN,GAAWC,EAAAA,EAAAA,CAAAA;EAE1E,GCNDN,EAAoBO,IAAI,CAACK,IAAKC,OAAUL,OAAOM,UAAUC,eAAeC,KAAKJ,IAAKC,EAAAA,GCClFb,EAAoBiB,IAAKnB,CAAAA,OAAAA;AACH,mBAAA,OAAXoB,UAA0BA,OAAOC,eAC1CX,OAAOC,eAAeX,IAASoB,OAAOC,aAAa,EAAEC,OAAO,SAAA,CAAA,GAE7DZ,OAAOC,eAAeX,IAAS,cAAc,EAAEsB,OAAAA,KAAO,CAAA;EAAO;AAAA,MAAA,IAAA,CAAA;ACQvD,MAAIC;AAEX,MAAA,EAAA,EAAA,CAAA,GAAA,EAAA,EAAA,GAAA,EAAA,KAAA,MAAA,GAAA,OAAA,MAAA,EAAA,CAAA,GAAuB,YAAA,OAAZnE,QACVmE,KAAiC,YAArBnE,QAAQoE;WACW,YAAA,OAAdC,WAAwB;AACzC,QAAIC,KAAYD,UAAUC;AAC1BH,QAAYG,GAAUC,QAAQ,SAAA,KAAc;EAAA;ACV7C,QAAMC,IAAiB,kBACjBC,IAAoB,OACpBC,IAAoB;AAE1B,WAASC,EAAanC,IAAUoC,IAAAA;AAG/B,QAAA,CAAKpC,GAAIqC,UAAUD,GAClB,OAAM,IAAIE,MAAM,2DAA2DtC,GAAIuC,SAAAA,aAAsBvC,GAAI/D,IAAAA,cAAkB+D,GAAIwC,KAAAA,iBAAsBxC,GAAIyC,QAAAA,IAAAA;AAK1J,QAAIzC,GAAIqC,UAAAA,CAAWL,EAAeU,KAAK1C,GAAIqC,MAAAA,EAC1C,OAAM,IAAIC,MAAM,iDAAA;AAQjB,QAAItC,GAAI/D;AACP,UAAI+D,GAAIuC,WAAAA;AACP,YAAA,CAAKN,EAAkBS,KAAK1C,GAAI/D,IAAAA,EAC/B,OAAM,IAAIqG,MAAM,0IAAA;MAAA,WAGbJ,EAAkBQ,KAAK1C,GAAI/D,IAAAA,EAC9B,OAAM,IAAIqG,MAAM,2HAAA;;EAIpB;AAkCA,QAAMK,IAAS,IACTC,IAAS,KACTC,IAAU;EAkBT,MAAMC,EAAAA;IA2DZ,YAAsBC,IAAsCR,IAAoBtG,IAAeuG,IAAgBC,IAAmBL,KAAAA,OAAmB;AApC5IC;AAMAE;AAKAtG;AAKAuG;AAKAC;AAiBoB,kBAAA,OAAjBM,MACVC,KAAKX,SAASU,GAAaV,UAAUM,GACrCK,KAAKT,YAAYQ,GAAaR,aAAaI,GAC3CK,KAAK/G,OAAO8G,GAAa9G,QAAQ0G,GACjCK,KAAKR,QAAQO,GAAaP,SAASG,GACnCK,KAAKP,WAAWM,GAAaN,YAAYE,MAKzCK,KAAKX,SAvHR,yBAAoBA,IAAgBD,IAAAA;AACnC,eAAKC,MAAWD,KAGTC,KAFC;MAGT,EAkH4BU,IAAcX,EAAAA,GACvCY,KAAKT,YAAYA,MAAaI,GAC9BK,KAAK/G,OAjHR,SAA8BoG,IAAgBpG,IAAAA;AAM7C,gBAAQoG,IAAAA;UACP,KAAK;UACL,KAAK;UACL,KAAK;AACCpG,YAAAA,KAEMA,GAAK,CAAA,MAAO2G,MACtB3G,KAAO2G,IAAS3G,MAFhBA,KAAO2G;QAAAA;AAMV,eAAO3G;MACR,EA+FoC+G,KAAKX,QAAQpG,MAAQ0G,CAAAA,GACtDK,KAAKR,QAAQA,MAASG,GACtBK,KAAKP,WAAWA,MAAYE,GAE5BR,EAAaa,MAAMZ,EAAAA;IAErB;IA7EA,OAAA,MAAaa,IAAAA;AACZ,aAAIA,cAAiBH,KAAAA,CAAAA,CAGhBG,MAGoC,YAAA,OAArBA,GAAOV,aACU,YAAA,OAApBU,GAAOR,YACS,YAAA,OAAhBQ,GAAOhH,QACU,YAAA,OAAjBgH,GAAOT,SACW,YAAA,OAAlBS,GAAOZ,UACW,YAAA,OAAlBY,GAAOC,UACS,cAAA,OAAhBD,GAAOE,QACa,cAAA,OAApBF,GAAOG;IACzB;IA0FA,IAAA,SAAIF;AAIH,aAAOG,EAAYL,MAAAA,KAAM;IAC1B;IAIA,KAAKM,IAAAA;AAEJ,UAAA,CAAKA,GACJ,QAAON;AAGR,UAAA,EAAI,QAAEX,IAAM,WAAEE,IAAS,MAAEtG,IAAI,OAAEuG,IAAK,UAAEC,GAAAA,IAAaa;AA2BnD,aAAA,WA1BIjB,KACHA,KAASW,KAAKX,SACO,SAAXA,OACVA,KAASM,IAAAA,WAENJ,KACHA,KAAYS,KAAKT,YACO,SAAdA,OACVA,KAAYI,IAAAA,WAET1G,KACHA,KAAO+G,KAAK/G,OACO,SAATA,OACVA,KAAO0G,IAAAA,WAEJH,KACHA,KAAQQ,KAAKR,QACO,SAAVA,OACVA,KAAQG,IAAAA,WAELF,KACHA,KAAWO,KAAKP,WACO,SAAbA,OACVA,KAAWE,IAGRN,OAAWW,KAAKX,UAChBE,OAAcS,KAAKT,aACnBtG,OAAS+G,KAAK/G,QACduG,OAAUQ,KAAKR,SACfC,OAAaO,KAAKP,WAEdO,OAGD,IAAIO,EAAIlB,IAAQE,IAAWtG,IAAMuG,IAAOC,EAAAA;IAChD;IAUA,OAAA,MAAaf,IAAeU,KAAAA,OAAmB;AAC9C,YAAMoB,KAAQX,EAAQY,KAAK/B,EAAAA;AAC3B,aAAK8B,KAGE,IAAID,EACVC,GAAM,CAAA,KAAMb,GACZe,EAAcF,GAAM,CAAA,KAAMb,CAAAA,GAC1Be,EAAcF,GAAM,CAAA,KAAMb,CAAAA,GAC1Be,EAAcF,GAAM,CAAA,KAAMb,CAAAA,GAC1Be,EAAcF,GAAM,CAAA,KAAMb,CAAAA,GAC1BP,EAAAA,IARO,IAAImB,EAAIZ,GAAQA,GAAQA,GAAQA,GAAQA,CAAAA;IAUjD;IAuBA,OAAA,KAAY1G,IAAAA;AAEX,UAAIsG,KAAYI;AAWhB,UANIhB,MACH1F,KAAOA,GAAK0H,QAAQ,OAAOf,CAAAA,IAKxB3G,GAAK,CAAA,MAAO2G,KAAU3G,GAAK,CAAA,MAAO2G,GAAQ;AAC7C,cAAMgB,KAAM3H,GAAK8F,QAAQa,GAAQ,CAAA;AAAA,eAC7BgB,MACHrB,KAAYtG,GAAK4H,UAAU,CAAA,GAC3B5H,KAAO2G,MAEPL,KAAYtG,GAAK4H,UAAU,GAAGD,EAAAA,GAC9B3H,KAAOA,GAAK4H,UAAUD,EAAAA,KAAQhB;MAAAA;AAIhC,aAAO,IAAIW,EAAI,QAAQhB,IAAWtG,IAAM0G,GAAQA,CAAAA;IACjD;IAEA,OAAA,KAAYmB,IAAAA;AACX,YAAMC,KAAS,IAAIR,EAClBO,GAAWzB,QACXyB,GAAWvB,WACXuB,GAAW7H,MACX6H,GAAWtB,OACXsB,GAAWrB,QAAAA;AAGZ,aADAN,EAAa4B,IAAAA,IAAQ,GACdA;IACR;IAeA,SAASC,KAAAA,OAAwB;AAChC,aAAOC,EAAajB,MAAMgB,EAAAA;IAC3B;IAEA,SAAAE;AACC,aAAOlB;IACR;IAMA,OAAA,OAAcmB,IAAAA;AACb,UAAKA,IAEE;AAAA,YAAIA,cAAgBrB,EAC1B,QAAOqB;AACD;AACN,gBAAMJ,KAAS,IAAIR,EAAIY,EAAAA;AAGvB,iBAFAJ,GAAOK,aAAwBD,GAAME,UACrCN,GAAOO,UAAqBH,GAAMI,SAASC,IAA4BL,GAAMjB,SAAS,MAC/Ea;QAAAA;MAAAA;AAPP,aAAYI;IASd;EAAA;AAkBD,QAAMK,IAAiB7C,IAAY,IAAA;EAGnC,MAAM4B,UAAYT,EAAAA;IAAlB;;AAECsB,wCAA4B;AAC5BE,qCAAyB;;IAEzB,IAAA,SAAapB;AAIZ,aAHKF,KAAKsB,YACTtB,KAAKsB,UAAUjB,EAAYL,MAAAA,KAAM,IAE3BA,KAAKsB;IACb;IAES,SAASN,KAAAA,OAAwB;AACzC,aAAKA,KAOGC,EAAajB,MAAAA,IAAM,KANrBA,KAAKoB,eACTpB,KAAKoB,aAAaH,EAAajB,MAAAA,KAAM,IAE/BA,KAAKoB;IAKd;IAES,SAAAF;AACR,YAAM1H,KAAgB,EACrBiI,MAAM,EAAA;AA0BP,aAvBIzB,KAAKsB,YACR9H,GAAI0G,SAASF,KAAKsB,SAClB9H,GAAI+H,OAAOC,IAERxB,KAAKoB,eACR5H,GAAI6H,WAAWrB,KAAKoB,aAGjBpB,KAAK/G,SACRO,GAAIP,OAAO+G,KAAK/G,OAEb+G,KAAKX,WACR7F,GAAI6F,SAASW,KAAKX,SAEfW,KAAKT,cACR/F,GAAI+F,YAAYS,KAAKT,YAElBS,KAAKR,UACRhG,GAAIgG,QAAQQ,KAAKR,QAEdQ,KAAKP,aACRjG,GAAIiG,WAAWO,KAAKP,WAEdjG;IACR;EAAA;AAID,QAAMkI,IAAwC,EAC7C,IAAkB,OAClB,IAAkB,OAClB,IAAyB,OACzB,IAAiB,OACjB,IAA8B,OAC9B,IAA+B,OAC/B,IAAmB,OAEnB,IAA4B,OAC5B,IAAuB,OACvB,IAAsB,OACtB,IAAwB,OACxB,IAAsB,OACtB,IAAuB,OACvB,IAAqB,OACrB,IAAiB,OACjB,IAAkB,OAClB,IAAsB,OACtB,IAAmB,OAEnB,IAAkB,MAAA;AAGnB,WAASC,EAAuBC,IAAsBC,IAAiBC,IAAAA;AACtE,QAAItI,IACAuI,KAAAA;AAEJ,aAASC,KAAM,GAAGA,KAAMJ,GAAa/H,QAAQmI,MAAO;AACnD,YAAMzI,KAAOqI,GAAa9H,WAAWkI,EAAAA;AAGrC,UACEzI,MAAQ,MAAcA,MAAQ,OAC3BA,MAAQ,MAAcA,MAAQ,MAC9BA,MAAQ,MAAmBA,MAAQ,MAC3B,OAATA,MACS,OAATA,MACS,OAATA,MACS,QAATA,MACCsI,MAAmB,OAATtI,MACVuI,MAAwB,OAATvI,MACfuI,MAAwB,OAATvI,MACfuI,MAAwB,OAATvI,GAAAA,QAGfwI,OACHvI,MAAOyI,mBAAmBL,GAAaf,UAAUkB,IAAiBC,EAAAA,CAAAA,GAClED,KAAAA,KAAmB,WAGhBvI,OACHA,MAAOoI,GAAaM,OAAOF,EAAAA;WAGtB;AAAA,mBAEFxI,OACHA,KAAMoI,GAAaO,OAAO,GAAGH,EAAAA;AAI9B,cAAMI,KAAUV,EAAYnI,EAAAA;AAAAA,mBACxB6I,MAAAA,OAGCL,OACHvI,MAAOyI,mBAAmBL,GAAaf,UAAUkB,IAAiBC,EAAAA,CAAAA,GAClED,KAAAA,KAIDvI,MAAO4I,MAAAA,OAEGL,OAEVA,KAAkBC;MAAAA;IAAAA;AASrB,WAAA,OAJID,OACHvI,MAAOyI,mBAAmBL,GAAaf,UAAUkB,EAAAA,CAAAA,IAAAA,WAG3CvI,KAAoBA,KAAMoI;EAClC;AAEA,WAASS,EAA0BpJ,IAAAA;AAClC,QAAIO;AACJ,aAASwI,KAAM,GAAGA,KAAM/I,GAAKY,QAAQmI,MAAO;AAC3C,YAAMzI,KAAON,GAAKa,WAAWkI,EAAAA;AAChB,aAATzI,MAAmC,OAATA,MAAAA,WACzBC,OACHA,KAAMP,GAAKkJ,OAAO,GAAGH,EAAAA,IAEtBxI,MAAOkI,EAAYnI,EAAAA,KAAAA,WAEfC,OACHA,MAAOP,GAAK+I,EAAAA;IAAAA;AAIf,WAAA,WAAOxI,KAAoBA,KAAMP;EAClC;AAKO,WAASoH,EAAYiC,IAAUC,IAAAA;AAErC,QAAI7D;AAsBJ,WAnBCA,KAFG4D,GAAI/C,aAAa+C,GAAIrJ,KAAKY,SAAS,KAAoB,WAAfyI,GAAIjD,SAEvC,KAAKiD,GAAI/C,SAAAA,GAAY+C,GAAIrJ,IAAAA,KAEN,OAA3BqJ,GAAIrJ,KAAKa,WAAW,CAAA,MAChBwI,GAAIrJ,KAAKa,WAAW,CAAA,KAAM,MAAcwI,GAAIrJ,KAAKa,WAAW,CAAA,KAAM,MAAcwI,GAAIrJ,KAAKa,WAAW,CAAA,KAAM,MAAcwI,GAAIrJ,KAAKa,WAAW,CAAA,KAAM,QACxH,OAA3BwI,GAAIrJ,KAAKa,WAAW,CAAA,IAElByI,KAIID,GAAIrJ,KAAKkJ,OAAO,CAAA,IAFhBG,GAAIrJ,KAAK,CAAA,EAAGuJ,YAAAA,IAAgBF,GAAIrJ,KAAKkJ,OAAO,CAAA,IAM7CG,GAAIrJ,MAET0F,MACHD,KAAQA,GAAMiC,QAAQ,OAAO,IAAA,IAEvBjC;EACR;AAKA,WAASuC,EAAaqB,IAAUtB,IAAAA;AAE/B,UAAMyB,KAAWzB,KAEdqB,IADAV;AAGH,QAAInI,KAAM,IAAA,EACN,QAAE6F,IAAM,WAAEE,IAAS,MAAEtG,IAAI,OAAEuG,IAAK,UAAEC,GAAAA,IAAa6C;AASnD,QARIjD,OACH7F,MAAO6F,IACP7F,MAAO,OAEJ+F,MAAwB,WAAXF,QAChB7F,MAAOoG,GACPpG,MAAOoG,IAEJL,IAAW;AACd,UAAIqB,KAAMrB,GAAUR,QAAQ,GAAA;AAC5B,UAAA,OAAI6B,IAAY;AAEf,cAAM8B,KAAWnD,GAAU4C,OAAO,GAAGvB,EAAAA;AACrCrB,QAAAA,KAAYA,GAAU4C,OAAOvB,KAAM,CAAA,GACnCA,KAAM8B,GAAS1I,YAAY,GAAA,GAAA,OACvB4G,KACHpH,MAAOiJ,GAAQC,IAAAA,OAAU,KAAO,KAGhClJ,MAAOiJ,GAAQC,GAASP,OAAO,GAAGvB,EAAAA,GAAAA,OAAM,KAAO,GAC/CpH,MAAO,KACPA,MAAOiJ,GAAQC,GAASP,OAAOvB,KAAM,CAAA,GAAA,OAAI,IAAO,IAEjDpH,MAAO;MAAA;AAER+F,MAAAA,KAAYA,GAAUiD,YAAAA,GACtB5B,KAAMrB,GAAUvF,YAAY,GAAA,GAAA,OACxB4G,KACHpH,MAAOiJ,GAAQlD,IAAAA,OAAW,IAAO,KAGjC/F,MAAOiJ,GAAQlD,GAAU4C,OAAO,GAAGvB,EAAAA,GAAAA,OAAM,IAAO,GAChDpH,MAAO+F,GAAU4C,OAAOvB,EAAAA;IAAAA;AAG1B,QAAI3H,IAAM;AAET,UAAIA,GAAKY,UAAU,KAA4B,OAAvBZ,GAAKa,WAAW,CAAA,KAAgD,OAAvBb,GAAKa,WAAW,CAAA,GAAuB;AACvG,cAAMP,KAAON,GAAKa,WAAW,CAAA;AACzBP,QAAAA,MAAQ,MAAcA,MAAQ,OACjCN,KAAO,IAAI0J,OAAOC,aAAarJ,KAAO,EAAA,CAAA,IAAON,GAAKkJ,OAAO,CAAA,CAAA;MAAA,WAEhDlJ,GAAKY,UAAU,KAA4B,OAAvBZ,GAAKa,WAAW,CAAA,GAAuB;AACrE,cAAMP,KAAON,GAAKa,WAAW,CAAA;AACzBP,QAAAA,MAAQ,MAAcA,MAAQ,OACjCN,KAAO,GAAG0J,OAAOC,aAAarJ,KAAO,EAAA,CAAA,IAAON,GAAKkJ,OAAO,CAAA,CAAA;MAAA;AAI1D3I,MAAAA,MAAOiJ,GAAQxJ,IAAAA,MAAM,KAAM;IAAA;AAU5B,WARIuG,OACHhG,MAAO,KACPA,MAAOiJ,GAAQjD,IAAAA,OAAO,KAAO,IAE1BC,OACHjG,MAAO,KACPA,MAAQwH,KAAgEvB,KAAjDkC,EAAuBlC,IAAAA,OAAU,KAAO,IAEzDjG;EACR;AAIA,WAASqJ,EAA2BC,IAAAA;AACnC,QAAA;AACC,aAAOC,mBAAmBD,EAAAA;IAAAA,QACzB;AACD,aAAIA,GAAIjJ,SAAS,IACTiJ,GAAIX,OAAO,GAAG,CAAA,IAAKU,EAA2BC,GAAIX,OAAO,CAAA,CAAA,IAEzDW;IAAAA;EAGV;AAEA,QAAME,IAAiB;AAEvB,WAAStC,EAAcoC,IAAAA;AACtB,WAAKA,GAAItC,MAAMwC,CAAAA,IAGRF,GAAInC,QAAQqC,GAAiBxC,CAAAA,OAAUqC,EAA2BrC,EAAAA,CAAAA,IAFjEsC;EAGT;AAAA,MAAA,IAAA,EAAA,GAAA;ACjqBA,QAAMG,IAAY,EAAA,SAAkB,GAC9BC,IAAQ;AAEP,MAAUC;AAAAA,GAAjB,SAAiBA,IAAAA;AAeG,IAAAC,GAAAC,WAAhB,SAAyBf,OAAagB,IAAAA;AAClC,aAAOhB,GAAInC,KAAK,EAAElH,MAAMgK,EAAUrI,KAAK0H,GAAIrJ,MAAAA,GAASqK,EAAAA,EAAAA,CAAAA;IACxD,GAgBgBF,GAAAG,cAAhB,SAA4BjB,OAAagB,IAAAA;AACrC,UAAIrK,KAAOqJ,GAAIrJ,MACXuK,KAAAA;AACAvK,MAAAA,GAAK,CAAA,MAAOiK,MACZjK,KAAOiK,IAAQjK,IACfuK,KAAAA;AAEJ,UAAInJ,KAAe4I,EAAU9I,QAAQlB,IAAAA,GAASqK,EAAAA;AAI9C,aAHIE,MAAcnJ,GAAa,CAAA,MAAO6I,KAAAA,CAAUZ,GAAI/C,cAChDlF,KAAeA,GAAawG,UAAU,CAAA,IAEnCyB,GAAInC,KAAK,EAAElH,MAAMoB,GAAAA,CAAAA;IAC5B,GAUgB+I,GAAAzH,UAAhB,SAAwB2G,IAAAA;AACpB,UAAwB,MAApBA,GAAIrJ,KAAKY,UAAgByI,GAAIrJ,SAASiK,EACtC,QAAOZ;AAEX,UAAIrJ,KAAOgK,EAAUtH,QAAQ2G,GAAIrJ,IAAAA;AAIjC,aAHoB,MAAhBA,GAAKY,UAAuC,OAAvBZ,GAAKa,WAAW,CAAA,MACrCb,KAAO,KAEJqJ,GAAInC,KAAK,EAAElH,MAAAA,GAAAA,CAAAA;IACtB,GAUgBmK,GAAArH,WAAhB,SAAyBuG,IAAAA;AACrB,aAAOW,EAAUlH,SAASuG,GAAIrJ,IAAAA;IAClC,GAUgBmK,GAAAhH,UAAhB,SAAwBkG,IAAAA;AACpB,aAAOW,EAAU7G,QAAQkG,GAAIrJ,IAAAA;IACjC;EACH,EAzFgBkK,MAAAA,IAAK,CAAA,EAAA,GAAA,MAAA;AAAA,GAAA;AAAA,IAAA,EAAA,KAAA,MAAA,IAAA;", "names": ["assertPath", "path", "TypeError", "JSON", "stringify", "normalizeStringPosix", "allowAboveRoot", "code", "res", "lastSegmentLength", "lastSlash", "dots", "i", "length", "charCodeAt", "lastSlashIndex", "lastIndexOf", "slice", "posix", "resolve", "cwd", "<PERSON><PERSON><PERSON>", "resolvedAbsolute", "arguments", "process", "normalize", "isAbsolute", "trailingSeparator", "join", "joined", "arg", "relative", "from", "to", "fromStart", "fromEnd", "fromLen", "toStart", "toLen", "lastCommonSep", "fromCode", "out", "_makeLong", "dirname", "hasRoot", "end", "matchedSlash", "basename", "ext", "start", "extIdx", "firstNonSlashEnd", "extname", "startDot", "startPart", "preDotState", "format", "pathObject", "sep", "dir", "root", "base", "name", "parse", "ret", "delimiter", "win32", "module", "exports", "__webpack_module_cache__", "__webpack_require__", "moduleId", "cachedModule", "__webpack_modules__", "d", "definition", "key", "o", "Object", "defineProperty", "enumerable", "get", "obj", "prop", "prototype", "hasOwnProperty", "call", "r", "Symbol", "toStringTag", "value", "isWindows", "platform", "navigator", "userAgent", "indexOf", "_schemePattern", "_singleSlashStart", "_doubleSlashStart", "_validateUri", "_strict", "scheme", "Error", "authority", "query", "fragment", "test", "_empty", "_slash", "_regexp", "URI", "schemeOrData", "this", "thing", "fsPath", "with", "toString", "uriToFsPath", "change", "<PERSON><PERSON>", "match", "exec", "percentDecode", "replace", "idx", "substring", "components", "result", "skip<PERSON><PERSON><PERSON>", "_asFormatted", "toJSON", "data", "_formatted", "external", "_fsPath", "_sep", "_pathSepMarker", "$mid", "encodeTable", "encodeURIComponentFast", "uriComponent", "isPath", "isAuthority", "nativeEncodePos", "pos", "encodeURIComponent", "char<PERSON>t", "substr", "escaped", "encodeURIComponentMinimal", "uri", "keepDriveLetterCasing", "toLowerCase", "encoder", "userinfo", "String", "fromCharCode", "decodeURIComponentGraceful", "str", "decodeURIComponent", "_rEncodedAsHex", "posixPath", "slash", "Utils", "t", "joinPath", "paths", "<PERSON><PERSON><PERSON>", "slashAdded"]}