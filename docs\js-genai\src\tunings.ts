/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */

// Code generated by the Google Gen AI SDK generator DO NOT EDIT.

import {ApiClient} from './_api_client.js';
import * as common from './_common.js';
import {BaseModule} from './_common.js';
import * as converters from './converters/_tunings_converters.js';
import {PagedItem, Pager} from './pagers.js';
import * as types from './types.js';

export class Tunings extends BaseModule {
  constructor(private readonly apiClient: ApiClient) {
    super();
  }

  /**
   * Gets a TuningJob.
   *
   * @param name - The resource name of the tuning job.
   * @return - A TuningJob object.
   *
   * @experimental - The SDK's tuning implementation is experimental, and may
   * change in future versions.
   */
  get = async (
    params: types.GetTuningJobParameters,
  ): Promise<types.TuningJob> => {
    return await this.getInternal(params);
  };

  /**
   * Lists tuning jobs.
   *
   * @param config - The configuration for the list request.
   * @return - A list of tuning jobs.
   *
   * @experimental - The SDK's tuning implementation is experimental, and may
   * change in future versions.
   */
  list = async (
    params: types.ListTuningJobsParameters = {},
  ): Promise<Pager<types.TuningJob>> => {
    return new Pager<types.TuningJob>(
      PagedItem.PAGED_ITEM_TUNING_JOBS,
      (x: types.ListTuningJobsParameters) => this.listInternal(x),
      await this.listInternal(params),
      params,
    );
  };

  /**
   * Creates a supervised fine-tuning job.
   *
   * @param params - The parameters for the tuning job.
   * @return - A TuningJob operation.
   *
   * @experimental - The SDK's tuning implementation is experimental, and may
   * change in future versions.
   */
  tune = async (
    params: types.CreateTuningJobParameters,
  ): Promise<types.TuningJob> => {
    if (this.apiClient.isVertexAI()) {
      return await this.tuneInternal(params);
    } else {
      const operation = await this.tuneMldevInternal(params);
      let tunedModelName = '';
      if (
        operation['metadata'] !== undefined &&
        operation['metadata']['tunedModel'] !== undefined
      ) {
        tunedModelName = operation['metadata']['tunedModel'] as string;
      } else if (
        operation['name'] !== undefined &&
        operation['name'].includes('/operations/')
      ) {
        tunedModelName = operation['name'].split('/operations/')[0];
      }
      const tuningJob: types.TuningJob = {
        name: tunedModelName,
        state: types.JobState.JOB_STATE_QUEUED,
      };

      return tuningJob;
    }
  };

  private async getInternal(
    params: types.GetTuningJobParameters,
  ): Promise<types.TuningJob> {
    let response: Promise<types.TuningJob>;

    let path: string = '';
    let queryParams: Record<string, string> = {};
    if (this.apiClient.isVertexAI()) {
      const body = converters.getTuningJobParametersToVertex(params);
      path = common.formatMap(
        '{name}',
        body['_url'] as Record<string, unknown>,
      );
      queryParams = body['_query'] as Record<string, string>;
      delete body['config'];
      delete body['_url'];
      delete body['_query'];

      response = this.apiClient
        .request({
          path: path,
          queryParams: queryParams,
          body: JSON.stringify(body),
          httpMethod: 'GET',
          httpOptions: params.config?.httpOptions,
          abortSignal: params.config?.abortSignal,
        })
        .then((httpResponse) => {
          return httpResponse.json();
        }) as Promise<types.TuningJob>;

      return response.then((apiResponse) => {
        const resp = converters.tuningJobFromVertex(apiResponse);

        return resp as types.TuningJob;
      });
    } else {
      const body = converters.getTuningJobParametersToMldev(params);
      path = common.formatMap(
        '{name}',
        body['_url'] as Record<string, unknown>,
      );
      queryParams = body['_query'] as Record<string, string>;
      delete body['config'];
      delete body['_url'];
      delete body['_query'];

      response = this.apiClient
        .request({
          path: path,
          queryParams: queryParams,
          body: JSON.stringify(body),
          httpMethod: 'GET',
          httpOptions: params.config?.httpOptions,
          abortSignal: params.config?.abortSignal,
        })
        .then((httpResponse) => {
          return httpResponse.json();
        }) as Promise<types.TuningJob>;

      return response.then((apiResponse) => {
        const resp = converters.tuningJobFromMldev(apiResponse);

        return resp as types.TuningJob;
      });
    }
  }

  private async listInternal(
    params: types.ListTuningJobsParameters,
  ): Promise<types.ListTuningJobsResponse> {
    let response: Promise<types.ListTuningJobsResponse>;

    let path: string = '';
    let queryParams: Record<string, string> = {};
    if (this.apiClient.isVertexAI()) {
      const body = converters.listTuningJobsParametersToVertex(params);
      path = common.formatMap(
        'tuningJobs',
        body['_url'] as Record<string, unknown>,
      );
      queryParams = body['_query'] as Record<string, string>;
      delete body['config'];
      delete body['_url'];
      delete body['_query'];

      response = this.apiClient
        .request({
          path: path,
          queryParams: queryParams,
          body: JSON.stringify(body),
          httpMethod: 'GET',
          httpOptions: params.config?.httpOptions,
          abortSignal: params.config?.abortSignal,
        })
        .then((httpResponse) => {
          return httpResponse.json().then((jsonResponse) => {
            const response = jsonResponse as types.ListTuningJobsResponse;
            response.sdkHttpResponse = {
              headers: httpResponse.headers,
            } as types.HttpResponse;
            return response;
          });
        }) as Promise<types.ListTuningJobsResponse>;

      return response.then((apiResponse) => {
        const resp = converters.listTuningJobsResponseFromVertex(apiResponse);
        const typedResp = new types.ListTuningJobsResponse();
        Object.assign(typedResp, resp);
        return typedResp;
      });
    } else {
      const body = converters.listTuningJobsParametersToMldev(params);
      path = common.formatMap(
        'tunedModels',
        body['_url'] as Record<string, unknown>,
      );
      queryParams = body['_query'] as Record<string, string>;
      delete body['config'];
      delete body['_url'];
      delete body['_query'];

      response = this.apiClient
        .request({
          path: path,
          queryParams: queryParams,
          body: JSON.stringify(body),
          httpMethod: 'GET',
          httpOptions: params.config?.httpOptions,
          abortSignal: params.config?.abortSignal,
        })
        .then((httpResponse) => {
          return httpResponse.json().then((jsonResponse) => {
            const response = jsonResponse as types.ListTuningJobsResponse;
            response.sdkHttpResponse = {
              headers: httpResponse.headers,
            } as types.HttpResponse;
            return response;
          });
        }) as Promise<types.ListTuningJobsResponse>;

      return response.then((apiResponse) => {
        const resp = converters.listTuningJobsResponseFromMldev(apiResponse);
        const typedResp = new types.ListTuningJobsResponse();
        Object.assign(typedResp, resp);
        return typedResp;
      });
    }
  }

  private async tuneInternal(
    params: types.CreateTuningJobParameters,
  ): Promise<types.TuningJob> {
    let response: Promise<types.TuningJob>;

    let path: string = '';
    let queryParams: Record<string, string> = {};
    if (this.apiClient.isVertexAI()) {
      const body = converters.createTuningJobParametersToVertex(params);
      path = common.formatMap(
        'tuningJobs',
        body['_url'] as Record<string, unknown>,
      );
      queryParams = body['_query'] as Record<string, string>;
      delete body['config'];
      delete body['_url'];
      delete body['_query'];

      response = this.apiClient
        .request({
          path: path,
          queryParams: queryParams,
          body: JSON.stringify(body),
          httpMethod: 'POST',
          httpOptions: params.config?.httpOptions,
          abortSignal: params.config?.abortSignal,
        })
        .then((httpResponse) => {
          return httpResponse.json().then((jsonResponse) => {
            const response = jsonResponse as types.TuningJob;
            response.sdkHttpResponse = {
              headers: httpResponse.headers,
            } as types.HttpResponse;
            return response;
          });
        }) as Promise<types.TuningJob>;

      return response.then((apiResponse) => {
        const resp = converters.tuningJobFromVertex(apiResponse);

        return resp as types.TuningJob;
      });
    } else {
      throw new Error('This method is only supported by the Vertex AI.');
    }
  }

  private async tuneMldevInternal(
    params: types.CreateTuningJobParameters,
  ): Promise<types.TuningOperation> {
    let response: Promise<types.TuningOperation>;

    let path: string = '';
    let queryParams: Record<string, string> = {};
    if (this.apiClient.isVertexAI()) {
      throw new Error(
        'This method is only supported by the Gemini Developer API.',
      );
    } else {
      const body = converters.createTuningJobParametersToMldev(params);
      path = common.formatMap(
        'tunedModels',
        body['_url'] as Record<string, unknown>,
      );
      queryParams = body['_query'] as Record<string, string>;
      delete body['config'];
      delete body['_url'];
      delete body['_query'];

      response = this.apiClient
        .request({
          path: path,
          queryParams: queryParams,
          body: JSON.stringify(body),
          httpMethod: 'POST',
          httpOptions: params.config?.httpOptions,
          abortSignal: params.config?.abortSignal,
        })
        .then((httpResponse) => {
          return httpResponse.json().then((jsonResponse) => {
            const response = jsonResponse as types.TuningOperation;
            response.sdkHttpResponse = {
              headers: httpResponse.headers,
            } as types.HttpResponse;
            return response;
          });
        }) as Promise<types.TuningOperation>;

      return response.then((apiResponse) => {
        const resp = converters.tuningOperationFromMldev(apiResponse);

        return resp as types.TuningOperation;
      });
    }
  }
}
