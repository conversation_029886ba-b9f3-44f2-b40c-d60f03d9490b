#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/d/ssh/learn/project-graph-origin/node_modules/.pnpm/@anthropic-ai+sdk@0.57.0/node_modules/@anthropic-ai/sdk/bin/node_modules:/mnt/d/ssh/learn/project-graph-origin/node_modules/.pnpm/@anthropic-ai+sdk@0.57.0/node_modules/@anthropic-ai/sdk/node_modules:/mnt/d/ssh/learn/project-graph-origin/node_modules/.pnpm/@anthropic-ai+sdk@0.57.0/node_modules/@anthropic-ai/node_modules:/mnt/d/ssh/learn/project-graph-origin/node_modules/.pnpm/@anthropic-ai+sdk@0.57.0/node_modules:/mnt/d/ssh/learn/project-graph-origin/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/d/ssh/learn/project-graph-origin/node_modules/.pnpm/@anthropic-ai+sdk@0.57.0/node_modules/@anthropic-ai/sdk/bin/node_modules:/mnt/d/ssh/learn/project-graph-origin/node_modules/.pnpm/@anthropic-ai+sdk@0.57.0/node_modules/@anthropic-ai/sdk/node_modules:/mnt/d/ssh/learn/project-graph-origin/node_modules/.pnpm/@anthropic-ai+sdk@0.57.0/node_modules/@anthropic-ai/node_modules:/mnt/d/ssh/learn/project-graph-origin/node_modules/.pnpm/@anthropic-ai+sdk@0.57.0/node_modules:/mnt/d/ssh/learn/project-graph-origin/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../@anthropic-ai/sdk/bin/cli" "$@"
else
  exec node  "$basedir/../@anthropic-ai/sdk/bin/cli" "$@"
fi
