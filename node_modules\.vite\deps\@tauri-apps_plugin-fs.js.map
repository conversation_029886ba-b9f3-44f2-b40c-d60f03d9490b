{"version": 3, "sources": ["../../../../node_modules/.pnpm/@tauri-apps+api@2.7.0/node_modules/@tauri-apps/api/path.js", "../../../../node_modules/.pnpm/@tauri-apps+plugin-fs@2.4.1/node_modules/@tauri-apps/plugin-fs/dist-js/index.js"], "sourcesContent": ["import { invoke } from './core.js';\n\n// Copyright 2019-2024 Tauri Programme within The Commons Conservancy\n// SPDX-License-Identifier: Apache-2.0\n// SPDX-License-Identifier: MIT\n/**\n * The path module provides utilities for working with file and directory paths.\n *\n * This package is also accessible with `window.__TAURI__.path` when [`app.withGlobalTauri`](https://v2.tauri.app/reference/config/#withglobaltauri) in `tauri.conf.json` is set to `true`.\n *\n * It is recommended to allowlist only the APIs you use for optimal bundle size and security.\n * @module\n */\n/**\n * @since 2.0.0\n */\nvar BaseDirectory;\n(function (BaseDirectory) {\n    /**\n     * @see {@link audioDir} for more information.\n     */\n    BaseDirectory[BaseDirectory[\"Audio\"] = 1] = \"Audio\";\n    /**\n     * @see {@link cacheDir} for more information.\n     */\n    BaseDirectory[BaseDirectory[\"Cache\"] = 2] = \"Cache\";\n    /**\n     * @see {@link configDir} for more information.\n     */\n    BaseDirectory[BaseDirectory[\"Config\"] = 3] = \"Config\";\n    /**\n     * @see {@link dataDir} for more information.\n     */\n    BaseDirectory[BaseDirectory[\"Data\"] = 4] = \"Data\";\n    /**\n     * @see {@link localDataDir} for more information.\n     */\n    BaseDirectory[BaseDirectory[\"LocalData\"] = 5] = \"LocalData\";\n    /**\n     * @see {@link documentDir} for more information.\n     */\n    BaseDirectory[BaseDirectory[\"Document\"] = 6] = \"Document\";\n    /**\n     * @see {@link downloadDir} for more information.\n     */\n    BaseDirectory[BaseDirectory[\"Download\"] = 7] = \"Download\";\n    /**\n     * @see {@link pictureDir} for more information.\n     */\n    BaseDirectory[BaseDirectory[\"Picture\"] = 8] = \"Picture\";\n    /**\n     * @see {@link publicDir} for more information.\n     */\n    BaseDirectory[BaseDirectory[\"Public\"] = 9] = \"Public\";\n    /**\n     * @see {@link videoDir} for more information.\n     */\n    BaseDirectory[BaseDirectory[\"Video\"] = 10] = \"Video\";\n    /**\n     * @see {@link resourceDir} for more information.\n     */\n    BaseDirectory[BaseDirectory[\"Resource\"] = 11] = \"Resource\";\n    /**\n     * @see {@link tempDir} for more information.\n     */\n    BaseDirectory[BaseDirectory[\"Temp\"] = 12] = \"Temp\";\n    /**\n     * @see {@link appConfigDir} for more information.\n     */\n    BaseDirectory[BaseDirectory[\"AppConfig\"] = 13] = \"AppConfig\";\n    /**\n     * @see {@link appDataDir} for more information.\n     */\n    BaseDirectory[BaseDirectory[\"AppData\"] = 14] = \"AppData\";\n    /**\n     * @see {@link appLocalDataDir} for more information.\n     */\n    BaseDirectory[BaseDirectory[\"AppLocalData\"] = 15] = \"AppLocalData\";\n    /**\n     * @see {@link appCacheDir} for more information.\n     */\n    BaseDirectory[BaseDirectory[\"AppCache\"] = 16] = \"AppCache\";\n    /**\n     * @see {@link appLogDir} for more information.\n     */\n    BaseDirectory[BaseDirectory[\"AppLog\"] = 17] = \"AppLog\";\n    /**\n     * @see {@link desktopDir} for more information.\n     */\n    BaseDirectory[BaseDirectory[\"Desktop\"] = 18] = \"Desktop\";\n    /**\n     * @see {@link executableDir} for more information.\n     */\n    BaseDirectory[BaseDirectory[\"Executable\"] = 19] = \"Executable\";\n    /**\n     * @see {@link fontDir} for more information.\n     */\n    BaseDirectory[BaseDirectory[\"Font\"] = 20] = \"Font\";\n    /**\n     * @see {@link homeDir} for more information.\n     */\n    BaseDirectory[BaseDirectory[\"Home\"] = 21] = \"Home\";\n    /**\n     * @see {@link runtimeDir} for more information.\n     */\n    BaseDirectory[BaseDirectory[\"Runtime\"] = 22] = \"Runtime\";\n    /**\n     * @see {@link templateDir} for more information.\n     */\n    BaseDirectory[BaseDirectory[\"Template\"] = 23] = \"Template\";\n})(BaseDirectory || (BaseDirectory = {}));\n/**\n * Returns the path to the suggested directory for your app's config files.\n * Resolves to `${configDir}/${bundleIdentifier}`, where `bundleIdentifier` is the [`identifier`](https://v2.tauri.app/reference/config/#identifier) value configured in `tauri.conf.json`.\n * @example\n * ```typescript\n * import { appConfigDir } from '@tauri-apps/api/path';\n * const appConfigDirPath = await appConfigDir();\n * ```\n *\n * @since 1.2.0\n */\nasync function appConfigDir() {\n    return invoke('plugin:path|resolve_directory', {\n        directory: BaseDirectory.AppConfig\n    });\n}\n/**\n * Returns the path to the suggested directory for your app's data files.\n * Resolves to `${dataDir}/${bundleIdentifier}`, where `bundleIdentifier` is the [`identifier`](https://v2.tauri.app/reference/config/#identifier) value configured in `tauri.conf.json`.\n * @example\n * ```typescript\n * import { appDataDir } from '@tauri-apps/api/path';\n * const appDataDirPath = await appDataDir();\n * ```\n *\n * @since 1.2.0\n */\nasync function appDataDir() {\n    return invoke('plugin:path|resolve_directory', {\n        directory: BaseDirectory.AppData\n    });\n}\n/**\n * Returns the path to the suggested directory for your app's local data files.\n * Resolves to `${localDataDir}/${bundleIdentifier}`, where `bundleIdentifier` is the [`identifier`](https://v2.tauri.app/reference/config/#identifier) value configured in `tauri.conf.json`.\n * @example\n * ```typescript\n * import { appLocalDataDir } from '@tauri-apps/api/path';\n * const appLocalDataDirPath = await appLocalDataDir();\n * ```\n *\n * @since 1.2.0\n */\nasync function appLocalDataDir() {\n    return invoke('plugin:path|resolve_directory', {\n        directory: BaseDirectory.AppLocalData\n    });\n}\n/**\n * Returns the path to the suggested directory for your app's cache files.\n * Resolves to `${cacheDir}/${bundleIdentifier}`, where `bundleIdentifier` is the [`identifier`](https://v2.tauri.app/reference/config/#identifier) value configured in `tauri.conf.json`.\n * @example\n * ```typescript\n * import { appCacheDir } from '@tauri-apps/api/path';\n * const appCacheDirPath = await appCacheDir();\n * ```\n *\n * @since 1.2.0\n */\nasync function appCacheDir() {\n    return invoke('plugin:path|resolve_directory', {\n        directory: BaseDirectory.AppCache\n    });\n}\n/**\n * Returns the path to the user's audio directory.\n *\n * #### Platform-specific\n *\n * - **Linux:** Resolves to [`xdg-user-dirs`](https://www.freedesktop.org/wiki/Software/xdg-user-dirs/)' `XDG_MUSIC_DIR`.\n * - **macOS:** Resolves to `$HOME/Music`.\n * - **Windows:** Resolves to `{FOLDERID_Music}`.\n * @example\n * ```typescript\n * import { audioDir } from '@tauri-apps/api/path';\n * const audioDirPath = await audioDir();\n * ```\n *\n * @since 1.0.0\n */\nasync function audioDir() {\n    return invoke('plugin:path|resolve_directory', {\n        directory: BaseDirectory.Audio\n    });\n}\n/**\n * Returns the path to the user's cache directory.\n *\n * #### Platform-specific\n *\n * - **Linux:** Resolves to `$XDG_CACHE_HOME` or `$HOME/.cache`.\n * - **macOS:** Resolves to `$HOME/Library/Caches`.\n * - **Windows:** Resolves to `{FOLDERID_LocalAppData}`.\n * @example\n * ```typescript\n * import { cacheDir } from '@tauri-apps/api/path';\n * const cacheDirPath = await cacheDir();\n * ```\n *\n * @since 1.0.0\n */\nasync function cacheDir() {\n    return invoke('plugin:path|resolve_directory', {\n        directory: BaseDirectory.Cache\n    });\n}\n/**\n * Returns the path to the user's config directory.\n *\n * #### Platform-specific\n *\n * - **Linux:** Resolves to `$XDG_CONFIG_HOME` or `$HOME/.config`.\n * - **macOS:** Resolves to `$HOME/Library/Application Support`.\n * - **Windows:** Resolves to `{FOLDERID_RoamingAppData}`.\n * @example\n * ```typescript\n * import { configDir } from '@tauri-apps/api/path';\n * const configDirPath = await configDir();\n * ```\n *\n * @since 1.0.0\n */\nasync function configDir() {\n    return invoke('plugin:path|resolve_directory', {\n        directory: BaseDirectory.Config\n    });\n}\n/**\n * Returns the path to the user's data directory.\n *\n * #### Platform-specific\n *\n * - **Linux:** Resolves to `$XDG_DATA_HOME` or `$HOME/.local/share`.\n * - **macOS:** Resolves to `$HOME/Library/Application Support`.\n * - **Windows:** Resolves to `{FOLDERID_RoamingAppData}`.\n * @example\n * ```typescript\n * import { dataDir } from '@tauri-apps/api/path';\n * const dataDirPath = await dataDir();\n * ```\n *\n * @since 1.0.0\n */\nasync function dataDir() {\n    return invoke('plugin:path|resolve_directory', {\n        directory: BaseDirectory.Data\n    });\n}\n/**\n * Returns the path to the user's desktop directory.\n *\n * #### Platform-specific\n *\n * - **Linux:** Resolves to [`xdg-user-dirs`](https://www.freedesktop.org/wiki/Software/xdg-user-dirs/)' `XDG_DESKTOP_DIR`.\n * - **macOS:** Resolves to `$HOME/Desktop`.\n * - **Windows:** Resolves to `{FOLDERID_Desktop}`.\n * @example\n * ```typescript\n * import { desktopDir } from '@tauri-apps/api/path';\n * const desktopPath = await desktopDir();\n * ```\n *\n * @since 1.0.0\n */\nasync function desktopDir() {\n    return invoke('plugin:path|resolve_directory', {\n        directory: BaseDirectory.Desktop\n    });\n}\n/**\n * Returns the path to the user's document directory.\n * @example\n * ```typescript\n * import { documentDir } from '@tauri-apps/api/path';\n * const documentDirPath = await documentDir();\n * ```\n *\n * #### Platform-specific\n *\n * - **Linux:** Resolves to [`xdg-user-dirs`](https://www.freedesktop.org/wiki/Software/xdg-user-dirs/)' `XDG_DOCUMENTS_DIR`.\n * - **macOS:** Resolves to `$HOME/Documents`.\n * - **Windows:** Resolves to `{FOLDERID_Documents}`.\n *\n * @since 1.0.0\n */\nasync function documentDir() {\n    return invoke('plugin:path|resolve_directory', {\n        directory: BaseDirectory.Document\n    });\n}\n/**\n * Returns the path to the user's download directory.\n *\n * #### Platform-specific\n *\n * - **Linux**: Resolves to [`xdg-user-dirs`](https://www.freedesktop.org/wiki/Software/xdg-user-dirs/)' `XDG_DOWNLOAD_DIR`.\n * - **macOS**: Resolves to `$HOME/Downloads`.\n * - **Windows**: Resolves to `{FOLDERID_Downloads}`.\n * @example\n * ```typescript\n * import { downloadDir } from '@tauri-apps/api/path';\n * const downloadDirPath = await downloadDir();\n * ```\n *\n * @since 1.0.0\n */\nasync function downloadDir() {\n    return invoke('plugin:path|resolve_directory', {\n        directory: BaseDirectory.Download\n    });\n}\n/**\n * Returns the path to the user's executable directory.\n *\n * #### Platform-specific\n *\n * - **Linux:** Resolves to `$XDG_BIN_HOME/../bin` or `$XDG_DATA_HOME/../bin` or `$HOME/.local/bin`.\n * - **macOS:** Not supported.\n * - **Windows:** Not supported.\n * @example\n * ```typescript\n * import { executableDir } from '@tauri-apps/api/path';\n * const executableDirPath = await executableDir();\n * ```\n *\n * @since 1.0.0\n */\nasync function executableDir() {\n    return invoke('plugin:path|resolve_directory', {\n        directory: BaseDirectory.Executable\n    });\n}\n/**\n * Returns the path to the user's font directory.\n *\n * #### Platform-specific\n *\n * - **Linux:** Resolves to `$XDG_DATA_HOME/fonts` or `$HOME/.local/share/fonts`.\n * - **macOS:** Resolves to `$HOME/Library/Fonts`.\n * - **Windows:** Not supported.\n * @example\n * ```typescript\n * import { fontDir } from '@tauri-apps/api/path';\n * const fontDirPath = await fontDir();\n * ```\n *\n * @since 1.0.0\n */\nasync function fontDir() {\n    return invoke('plugin:path|resolve_directory', {\n        directory: BaseDirectory.Font\n    });\n}\n/**\n * Returns the path to the user's home directory.\n *\n * #### Platform-specific\n *\n * - **Linux:** Resolves to `$HOME`.\n * - **macOS:** Resolves to `$HOME`.\n * - **Windows:** Resolves to `{FOLDERID_Profile}`.\n * @example\n * ```typescript\n * import { homeDir } from '@tauri-apps/api/path';\n * const homeDirPath = await homeDir();\n * ```\n *\n * @since 1.0.0\n */\nasync function homeDir() {\n    return invoke('plugin:path|resolve_directory', {\n        directory: BaseDirectory.Home\n    });\n}\n/**\n * Returns the path to the user's local data directory.\n *\n * #### Platform-specific\n *\n * - **Linux:** Resolves to `$XDG_DATA_HOME` or `$HOME/.local/share`.\n * - **macOS:** Resolves to `$HOME/Library/Application Support`.\n * - **Windows:** Resolves to `{FOLDERID_LocalAppData}`.\n * @example\n * ```typescript\n * import { localDataDir } from '@tauri-apps/api/path';\n * const localDataDirPath = await localDataDir();\n * ```\n *\n * @since 1.0.0\n */\nasync function localDataDir() {\n    return invoke('plugin:path|resolve_directory', {\n        directory: BaseDirectory.LocalData\n    });\n}\n/**\n * Returns the path to the user's picture directory.\n *\n * #### Platform-specific\n *\n * - **Linux:** Resolves to [`xdg-user-dirs`](https://www.freedesktop.org/wiki/Software/xdg-user-dirs/)' `XDG_PICTURES_DIR`.\n * - **macOS:** Resolves to `$HOME/Pictures`.\n * - **Windows:** Resolves to `{FOLDERID_Pictures}`.\n * @example\n * ```typescript\n * import { pictureDir } from '@tauri-apps/api/path';\n * const pictureDirPath = await pictureDir();\n * ```\n *\n * @since 1.0.0\n */\nasync function pictureDir() {\n    return invoke('plugin:path|resolve_directory', {\n        directory: BaseDirectory.Picture\n    });\n}\n/**\n * Returns the path to the user's public directory.\n *\n * #### Platform-specific\n *\n * - **Linux:** Resolves to [`xdg-user-dirs`](https://www.freedesktop.org/wiki/Software/xdg-user-dirs/)' `XDG_PUBLICSHARE_DIR`.\n * - **macOS:** Resolves to `$HOME/Public`.\n * - **Windows:** Resolves to `{FOLDERID_Public}`.\n * @example\n * ```typescript\n * import { publicDir } from '@tauri-apps/api/path';\n * const publicDirPath = await publicDir();\n * ```\n *\n * @since 1.0.0\n */\nasync function publicDir() {\n    return invoke('plugin:path|resolve_directory', {\n        directory: BaseDirectory.Public\n    });\n}\n/**\n * Returns the path to the application's resource directory.\n * To resolve a resource path, see {@linkcode resolveResource}.\n *\n * ## Platform-specific\n *\n * Although we provide the exact path where this function resolves to,\n * this is not a contract and things might change in the future\n *\n * - **Windows:** Resolves to the directory that contains the main executable.\n * - **Linux:** When running in an AppImage, the `APPDIR` variable will be set to\n *   the mounted location of the app, and the resource dir will be `${APPDIR}/usr/lib/${exe_name}`.\n *   If not running in an AppImage, the path is `/usr/lib/${exe_name}`.\n *   When running the app from `src-tauri/target/(debug|release)/`, the path is `${exe_dir}/../lib/${exe_name}`.\n * - **macOS:** Resolves to `${exe_dir}/../Resources` (inside .app).\n * - **iOS:** Resolves to `${exe_dir}/assets`.\n * - **Android:** Currently the resources are stored in the APK as assets so it's not a normal file system path,\n *   we return a special URI prefix `asset://localhost/` here that can be used with the [file system plugin](https://tauri.app/plugin/file-system/),\n *\n * @example\n * ```typescript\n * import { resourceDir } from '@tauri-apps/api/path';\n * const resourceDirPath = await resourceDir();\n * ```\n *\n * @since 1.0.0\n */\nasync function resourceDir() {\n    return invoke('plugin:path|resolve_directory', {\n        directory: BaseDirectory.Resource\n    });\n}\n/**\n * Resolve the path to a resource file.\n * @example\n * ```typescript\n * import { resolveResource } from '@tauri-apps/api/path';\n * const resourcePath = await resolveResource('script.sh');\n * ```\n *\n * @param resourcePath The path to the resource.\n * Must follow the same syntax as defined in `tauri.conf.json > bundle > resources`, i.e. keeping subfolders and parent dir components (`../`).\n * @returns The full path to the resource.\n *\n * @since 1.0.0\n */\nasync function resolveResource(resourcePath) {\n    return invoke('plugin:path|resolve_directory', {\n        directory: BaseDirectory.Resource,\n        path: resourcePath\n    });\n}\n/**\n * Returns the path to the user's runtime directory.\n *\n * #### Platform-specific\n *\n * - **Linux:** Resolves to `$XDG_RUNTIME_DIR`.\n * - **macOS:** Not supported.\n * - **Windows:** Not supported.\n * @example\n * ```typescript\n * import { runtimeDir } from '@tauri-apps/api/path';\n * const runtimeDirPath = await runtimeDir();\n * ```\n *\n * @since 1.0.0\n */\nasync function runtimeDir() {\n    return invoke('plugin:path|resolve_directory', {\n        directory: BaseDirectory.Runtime\n    });\n}\n/**\n * Returns the path to the user's template directory.\n *\n * #### Platform-specific\n *\n * - **Linux:** Resolves to [`xdg-user-dirs`](https://www.freedesktop.org/wiki/Software/xdg-user-dirs/)' `XDG_TEMPLATES_DIR`.\n * - **macOS:** Not supported.\n * - **Windows:** Resolves to `{FOLDERID_Templates}`.\n * @example\n * ```typescript\n * import { templateDir } from '@tauri-apps/api/path';\n * const templateDirPath = await templateDir();\n * ```\n *\n * @since 1.0.0\n */\nasync function templateDir() {\n    return invoke('plugin:path|resolve_directory', {\n        directory: BaseDirectory.Template\n    });\n}\n/**\n * Returns the path to the user's video directory.\n *\n * #### Platform-specific\n *\n * - **Linux:** Resolves to [`xdg-user-dirs`](https://www.freedesktop.org/wiki/Software/xdg-user-dirs/)' `XDG_VIDEOS_DIR`.\n * - **macOS:** Resolves to `$HOME/Movies`.\n * - **Windows:** Resolves to `{FOLDERID_Videos}`.\n * @example\n * ```typescript\n * import { videoDir } from '@tauri-apps/api/path';\n * const videoDirPath = await videoDir();\n * ```\n *\n * @since 1.0.0\n */\nasync function videoDir() {\n    return invoke('plugin:path|resolve_directory', {\n        directory: BaseDirectory.Video\n    });\n}\n/**\n * Returns the path to the suggested directory for your app's log files.\n *\n * #### Platform-specific\n *\n * - **Linux:** Resolves to `${configDir}/${bundleIdentifier}/logs`.\n * - **macOS:** Resolves to `${homeDir}/Library/Logs/{bundleIdentifier}`\n * - **Windows:** Resolves to `${configDir}/${bundleIdentifier}/logs`.\n * @example\n * ```typescript\n * import { appLogDir } from '@tauri-apps/api/path';\n * const appLogDirPath = await appLogDir();\n * ```\n *\n * @since 1.2.0\n */\nasync function appLogDir() {\n    return invoke('plugin:path|resolve_directory', {\n        directory: BaseDirectory.AppLog\n    });\n}\n/**\n * Returns a temporary directory.\n * @example\n * ```typescript\n * import { tempDir } from '@tauri-apps/api/path';\n * const temp = await tempDir();\n * ```\n *\n * @since 2.0.0\n */\nasync function tempDir() {\n    return invoke('plugin:path|resolve_directory', {\n        directory: BaseDirectory.Temp\n    });\n}\n/**\n * Returns the platform-specific path segment separator:\n * - `\\` on Windows\n * - `/` on POSIX\n *\n * @since 2.0.0\n */\nfunction sep() {\n    return window.__TAURI_INTERNALS__.plugins.path.sep;\n}\n/**\n * Returns the platform-specific path segment delimiter:\n * - `;` on Windows\n * - `:` on POSIX\n *\n * @since 2.0.0\n */\nfunction delimiter() {\n    return window.__TAURI_INTERNALS__.plugins.path.delimiter;\n}\n/**\n * Resolves a sequence of `paths` or `path` segments into an absolute path.\n * @example\n * ```typescript\n * import { resolve, appDataDir } from '@tauri-apps/api/path';\n * const appDataDirPath = await appDataDir();\n * const path = await resolve(appDataDirPath, '..', 'users', 'tauri', 'avatar.png');\n * ```\n *\n * @since 1.0.0\n */\nasync function resolve(...paths) {\n    return invoke('plugin:path|resolve', { paths });\n}\n/**\n * Normalizes the given `path`, resolving `'..'` and `'.'` segments and resolve symbolic links.\n * @example\n * ```typescript\n * import { normalize, appDataDir } from '@tauri-apps/api/path';\n * const appDataDirPath = await appDataDir();\n * const path = await normalize(`${appDataDirPath}/../users/tauri/avatar.png`);\n * ```\n *\n * @since 1.0.0\n */\nasync function normalize(path) {\n    return invoke('plugin:path|normalize', { path });\n}\n/**\n *  Joins all given `path` segments together using the platform-specific separator as a delimiter, then normalizes the resulting path.\n * @example\n * ```typescript\n * import { join, appDataDir } from '@tauri-apps/api/path';\n * const appDataDirPath = await appDataDir();\n * const path = await join(appDataDirPath, 'users', 'tauri', 'avatar.png');\n * ```\n *\n * @since 1.0.0\n */\nasync function join(...paths) {\n    return invoke('plugin:path|join', { paths });\n}\n/**\n * Returns the parent directory of a given `path`. Trailing directory separators are ignored.\n * @example\n * ```typescript\n * import { dirname } from '@tauri-apps/api/path';\n * const dir = await dirname('/path/to/somedir/');\n * assert(dir === '/path/to');\n * ```\n *\n * @since 1.0.0\n */\nasync function dirname(path) {\n    return invoke('plugin:path|dirname', { path });\n}\n/**\n * Returns the extension of the `path`.\n * @example\n * ```typescript\n * import { extname } from '@tauri-apps/api/path';\n * const ext = await extname('/path/to/file.html');\n * assert(ext === 'html');\n * ```\n *\n * @since 1.0.0\n */\nasync function extname(path) {\n    return invoke('plugin:path|extname', { path });\n}\n/**\n * Returns the last portion of a `path`. Trailing directory separators are ignored.\n * @example\n * ```typescript\n * import { basename } from '@tauri-apps/api/path';\n * const base = await basename('path/to/app.conf');\n * assert(base === 'app.conf');\n * ```\n * @param ext An optional file extension to be removed from the returned path.\n *\n * @since 1.0.0\n */\nasync function basename(path, ext) {\n    return invoke('plugin:path|basename', { path, ext });\n}\n/**\n * Returns whether the path is absolute or not.\n * @example\n * ```typescript\n * import { isAbsolute } from '@tauri-apps/api/path';\n * assert(await isAbsolute('/home/<USER>'));\n * ```\n *\n * @since 1.0.0\n */\nasync function isAbsolute(path) {\n    return invoke('plugin:path|is_absolute', { path });\n}\n\nexport { BaseDirectory, appCacheDir, appConfigDir, appDataDir, appLocalDataDir, appLogDir, audioDir, basename, cacheDir, configDir, dataDir, delimiter, desktopDir, dirname, documentDir, downloadDir, executableDir, extname, fontDir, homeDir, isAbsolute, join, localDataDir, normalize, pictureDir, publicDir, resolve, resolveResource, resourceDir, runtimeDir, sep, tempDir, templateDir, videoDir };\n", "export { BaseDirectory } from '@tauri-apps/api/path';\nimport { Resource, invoke, Channel } from '@tauri-apps/api/core';\n\n// Copyright 2019-2023 Tauri Programme within The Commons Conservancy\n// SPDX-License-Identifier: Apache-2.0\n// SPDX-License-Identifier: MIT\n/**\n * Access the file system.\n *\n * ## Security\n *\n * This module prevents path traversal, not allowing parent directory accessors to be used\n * (i.e. \"/usr/path/to/../file\" or \"../path/to/file\" paths are not allowed).\n * Paths accessed with this API must be either relative to one of the {@link BaseDirectory | base directories}\n * or created with the {@link https://v2.tauri.app/reference/javascript/api/namespacepath/ | path API}.\n *\n * The API has a scope configuration that forces you to restrict the paths that can be accessed using glob patterns.\n *\n * The scope configuration is an array of glob patterns describing file/directory paths that are allowed.\n * For instance, this scope configuration allows **all** enabled `fs` APIs to (only) access files in the\n * *databases* directory of the {@link https://v2.tauri.app/reference/javascript/api/namespacepath/#appdatadir | `$APPDATA` directory}:\n * ```json\n * {\n *   \"permissions\": [\n *     {\n *       \"identifier\": \"fs:scope\",\n *       \"allow\": [{ \"path\": \"$APPDATA/databases/*\" }]\n *     }\n *   ]\n * }\n * ```\n *\n * Scopes can also be applied to specific `fs` APIs by using the API's identifier instead of `fs:scope`:\n * ```json\n * {\n *   \"permissions\": [\n *     {\n *       \"identifier\": \"fs:allow-exists\",\n *       \"allow\": [{ \"path\": \"$APPDATA/databases/*\" }]\n *     }\n *   ]\n * }\n * ```\n *\n * Notice the use of the `$APPDATA` variable. The value is injected at runtime, resolving to the {@link https://v2.tauri.app/reference/javascript/api/namespacepath/#appdatadir | app data directory}.\n *\n * The available variables are:\n * {@linkcode https://v2.tauri.app/reference/javascript/api/namespacepath/#appconfigdir | $APPCONFIG},\n * {@linkcode https://v2.tauri.app/reference/javascript/api/namespacepath/#appdatadir | $APPDATA},\n * {@linkcode https://v2.tauri.app/reference/javascript/api/namespacepath/#applocaldatadir | $APPLOCALDATA},\n * {@linkcode https://v2.tauri.app/reference/javascript/api/namespacepath/#appcachedir | $APPCACHE},\n * {@linkcode https://v2.tauri.app/reference/javascript/api/namespacepath/#applogdir | $APPLOG},\n * {@linkcode https://v2.tauri.app/reference/javascript/api/namespacepath/#audiodir | $AUDIO},\n * {@linkcode https://v2.tauri.app/reference/javascript/api/namespacepath/#cachedir | $CACHE},\n * {@linkcode https://v2.tauri.app/reference/javascript/api/namespacepath/#configdir | $CONFIG},\n * {@linkcode https://v2.tauri.app/reference/javascript/api/namespacepath/#datadir | $DATA},\n * {@linkcode https://v2.tauri.app/reference/javascript/api/namespacepath/#localdatadir | $LOCALDATA},\n * {@linkcode https://v2.tauri.app/reference/javascript/api/namespacepath/#desktopdir | $DESKTOP},\n * {@linkcode https://v2.tauri.app/reference/javascript/api/namespacepath/#documentdir | $DOCUMENT},\n * {@linkcode https://v2.tauri.app/reference/javascript/api/namespacepath/#downloaddir | $DOWNLOAD},\n * {@linkcode https://v2.tauri.app/reference/javascript/api/namespacepath/#executabledir | $EXE},\n * {@linkcode https://v2.tauri.app/reference/javascript/api/namespacepath/#fontdir | $FONT},\n * {@linkcode https://v2.tauri.app/reference/javascript/api/namespacepath/#homedir | $HOME},\n * {@linkcode https://v2.tauri.app/reference/javascript/api/namespacepath/#picturedir | $PICTURE},\n * {@linkcode https://v2.tauri.app/reference/javascript/api/namespacepath/#publicdir | $PUBLIC},\n * {@linkcode https://v2.tauri.app/reference/javascript/api/namespacepath/#runtimedir | $RUNTIME},\n * {@linkcode https://v2.tauri.app/reference/javascript/api/namespacepath/#templatedir | $TEMPLATE},\n * {@linkcode https://v2.tauri.app/reference/javascript/api/namespacepath/#videodir | $VIDEO},\n * {@linkcode https://v2.tauri.app/reference/javascript/api/namespacepath/#resourcedir | $RESOURCE},\n * {@linkcode https://v2.tauri.app/reference/javascript/api/namespacepath/#tempdir | $TEMP}.\n *\n * Trying to execute any API with a URL not configured on the scope results in a promise rejection due to denied access.\n *\n * @module\n */\nvar SeekMode;\n(function (SeekMode) {\n    SeekMode[SeekMode[\"Start\"] = 0] = \"Start\";\n    SeekMode[SeekMode[\"Current\"] = 1] = \"Current\";\n    SeekMode[SeekMode[\"End\"] = 2] = \"End\";\n})(SeekMode || (SeekMode = {}));\nfunction parseFileInfo(r) {\n    return {\n        isFile: r.isFile,\n        isDirectory: r.isDirectory,\n        isSymlink: r.isSymlink,\n        size: r.size,\n        mtime: r.mtime !== null ? new Date(r.mtime) : null,\n        atime: r.atime !== null ? new Date(r.atime) : null,\n        birthtime: r.birthtime !== null ? new Date(r.birthtime) : null,\n        readonly: r.readonly,\n        fileAttributes: r.fileAttributes,\n        dev: r.dev,\n        ino: r.ino,\n        mode: r.mode,\n        nlink: r.nlink,\n        uid: r.uid,\n        gid: r.gid,\n        rdev: r.rdev,\n        blksize: r.blksize,\n        blocks: r.blocks\n    };\n}\n// https://gist.github.com/zapthedingbat/38ebfbedd98396624e5b5f2ff462611d\n/** Converts a big-endian eight byte array to number  */\nfunction fromBytes(buffer) {\n    const bytes = new Uint8ClampedArray(buffer);\n    const size = bytes.byteLength;\n    let x = 0;\n    for (let i = 0; i < size; i++) {\n        // eslint-disable-next-line security/detect-object-injection\n        const byte = bytes[i];\n        x *= 0x100;\n        x += byte;\n    }\n    return x;\n}\n/**\n *  The Tauri abstraction for reading and writing files.\n *\n * @since 2.0.0\n */\nclass FileHandle extends Resource {\n    /**\n     * Reads up to `p.byteLength` bytes into `p`. It resolves to the number of\n     * bytes read (`0` < `n` <= `p.byteLength`) and rejects if any error\n     * encountered. Even if `read()` resolves to `n` < `p.byteLength`, it may\n     * use all of `p` as scratch space during the call. If some data is\n     * available but not `p.byteLength` bytes, `read()` conventionally resolves\n     * to what is available instead of waiting for more.\n     *\n     * When `read()` encounters end-of-file condition, it resolves to EOF\n     * (`null`).\n     *\n     * When `read()` encounters an error, it rejects with an error.\n     *\n     * Callers should always process the `n` > `0` bytes returned before\n     * considering the EOF (`null`). Doing so correctly handles I/O errors that\n     * happen after reading some bytes and also both of the allowed EOF\n     * behaviors.\n     *\n     * @example\n     * ```typescript\n     * import { open, BaseDirectory } from \"@tauri-apps/plugin-fs\"\n     * // if \"$APPCONFIG/foo/bar.txt\" contains the text \"hello world\":\n     * const file = await open(\"foo/bar.txt\", { baseDir: BaseDirectory.AppConfig });\n     * const buf = new Uint8Array(100);\n     * const numberOfBytesRead = await file.read(buf); // 11 bytes\n     * const text = new TextDecoder().decode(buf);  // \"hello world\"\n     * await file.close();\n     * ```\n     *\n     * @since 2.0.0\n     */\n    async read(buffer) {\n        if (buffer.byteLength === 0) {\n            return 0;\n        }\n        const data = await invoke('plugin:fs|read', {\n            rid: this.rid,\n            len: buffer.byteLength\n        });\n        // Rust side will never return an empty array for this command and\n        // ensure there is at least 8 elements there.\n        //\n        // This is an optimization to include the number of read bytes (as bigendian bytes)\n        // at the end of returned array to avoid serialization overhead of separate values.\n        const nread = fromBytes(data.slice(-8));\n        const bytes = data instanceof ArrayBuffer ? new Uint8Array(data) : data;\n        buffer.set(bytes.slice(0, bytes.length - 8));\n        return nread === 0 ? null : nread;\n    }\n    /**\n     * Seek sets the offset for the next `read()` or `write()` to offset,\n     * interpreted according to `whence`: `Start` means relative to the\n     * start of the file, `Current` means relative to the current offset,\n     * and `End` means relative to the end. Seek resolves to the new offset\n     * relative to the start of the file.\n     *\n     * Seeking to an offset before the start of the file is an error. Seeking to\n     * any positive offset is legal, but the behavior of subsequent I/O\n     * operations on the underlying object is implementation-dependent.\n     * It returns the number of cursor position.\n     *\n     * @example\n     * ```typescript\n     * import { open, SeekMode, BaseDirectory } from '@tauri-apps/plugin-fs';\n     *\n     * // Given hello.txt pointing to file with \"Hello world\", which is 11 bytes long:\n     * const file = await open('hello.txt', { read: true, write: true, truncate: true, create: true, baseDir: BaseDirectory.AppLocalData });\n     * await file.write(new TextEncoder().encode(\"Hello world\"));\n     *\n     * // Seek 6 bytes from the start of the file\n     * console.log(await file.seek(6, SeekMode.Start)); // \"6\"\n     * // Seek 2 more bytes from the current position\n     * console.log(await file.seek(2, SeekMode.Current)); // \"8\"\n     * // Seek backwards 2 bytes from the end of the file\n     * console.log(await file.seek(-2, SeekMode.End)); // \"9\" (e.g. 11-2)\n     *\n     * await file.close();\n     * ```\n     *\n     * @since 2.0.0\n     */\n    async seek(offset, whence) {\n        return await invoke('plugin:fs|seek', {\n            rid: this.rid,\n            offset,\n            whence\n        });\n    }\n    /**\n     * Returns a {@linkcode FileInfo } for this file.\n     *\n     * @example\n     * ```typescript\n     * import { open, BaseDirectory } from '@tauri-apps/plugin-fs';\n     * const file = await open(\"file.txt\", { read: true, baseDir: BaseDirectory.AppLocalData });\n     * const fileInfo = await file.stat();\n     * console.log(fileInfo.isFile); // true\n     * await file.close();\n     * ```\n     *\n     * @since 2.0.0\n     */\n    async stat() {\n        const res = await invoke('plugin:fs|fstat', {\n            rid: this.rid\n        });\n        return parseFileInfo(res);\n    }\n    /**\n     * Truncates or extends this file, to reach the specified `len`.\n     * If `len` is not specified then the entire file contents are truncated.\n     *\n     * @example\n     * ```typescript\n     * import { open, BaseDirectory } from '@tauri-apps/plugin-fs';\n     *\n     * // truncate the entire file\n     * const file = await open(\"my_file.txt\", { read: true, write: true, create: true, baseDir: BaseDirectory.AppLocalData });\n     * await file.truncate();\n     *\n     * // truncate part of the file\n     * const file = await open(\"my_file.txt\", { read: true, write: true, create: true, baseDir: BaseDirectory.AppLocalData });\n     * await file.write(new TextEncoder().encode(\"Hello World\"));\n     * await file.truncate(7);\n     * const data = new Uint8Array(32);\n     * await file.read(data);\n     * console.log(new TextDecoder().decode(data)); // Hello W\n     * await file.close();\n     * ```\n     *\n     * @since 2.0.0\n     */\n    async truncate(len) {\n        await invoke('plugin:fs|ftruncate', {\n            rid: this.rid,\n            len\n        });\n    }\n    /**\n     * Writes `data.byteLength` bytes from `data` to the underlying data stream. It\n     * resolves to the number of bytes written from `data` (`0` <= `n` <=\n     * `data.byteLength`) or reject with the error encountered that caused the\n     * write to stop early. `write()` must reject with a non-null error if\n     * would resolve to `n` < `data.byteLength`. `write()` must not modify the\n     * slice data, even temporarily.\n     *\n     * @example\n     * ```typescript\n     * import { open, write, BaseDirectory } from '@tauri-apps/plugin-fs';\n     * const encoder = new TextEncoder();\n     * const data = encoder.encode(\"Hello world\");\n     * const file = await open(\"bar.txt\", { write: true, baseDir: BaseDirectory.AppLocalData });\n     * const bytesWritten = await file.write(data); // 11\n     * await file.close();\n     * ```\n     *\n     * @since 2.0.0\n     */\n    async write(data) {\n        return await invoke('plugin:fs|write', {\n            rid: this.rid,\n            data\n        });\n    }\n}\n/**\n * Creates a file if none exists or truncates an existing file and resolves to\n *  an instance of {@linkcode FileHandle }.\n *\n * @example\n * ```typescript\n * import { create, BaseDirectory } from \"@tauri-apps/plugin-fs\"\n * const file = await create(\"foo/bar.txt\", { baseDir: BaseDirectory.AppConfig });\n * await file.write(new TextEncoder().encode(\"Hello world\"));\n * await file.close();\n * ```\n *\n * @since 2.0.0\n */\nasync function create(path, options) {\n    if (path instanceof URL && path.protocol !== 'file:') {\n        throw new TypeError('Must be a file URL.');\n    }\n    const rid = await invoke('plugin:fs|create', {\n        path: path instanceof URL ? path.toString() : path,\n        options\n    });\n    return new FileHandle(rid);\n}\n/**\n * Open a file and resolve to an instance of {@linkcode FileHandle}. The\n * file does not need to previously exist if using the `create` or `createNew`\n * open options. It is the callers responsibility to close the file when finished\n * with it.\n *\n * @example\n * ```typescript\n * import { open, BaseDirectory } from \"@tauri-apps/plugin-fs\"\n * const file = await open(\"foo/bar.txt\", { read: true, write: true, baseDir: BaseDirectory.AppLocalData });\n * // Do work with file\n * await file.close();\n * ```\n *\n * @since 2.0.0\n */\nasync function open(path, options) {\n    if (path instanceof URL && path.protocol !== 'file:') {\n        throw new TypeError('Must be a file URL.');\n    }\n    const rid = await invoke('plugin:fs|open', {\n        path: path instanceof URL ? path.toString() : path,\n        options\n    });\n    return new FileHandle(rid);\n}\n/**\n * Copies the contents and permissions of one file to another specified path, by default creating a new file if needed, else overwriting.\n * @example\n * ```typescript\n * import { copyFile, BaseDirectory } from '@tauri-apps/plugin-fs';\n * await copyFile('app.conf', 'app.conf.bk', { fromPathBaseDir: BaseDirectory.AppConfig, toPathBaseDir: BaseDirectory.AppConfig });\n * ```\n *\n * @since 2.0.0\n */\nasync function copyFile(fromPath, toPath, options) {\n    if ((fromPath instanceof URL && fromPath.protocol !== 'file:')\n        || (toPath instanceof URL && toPath.protocol !== 'file:')) {\n        throw new TypeError('Must be a file URL.');\n    }\n    await invoke('plugin:fs|copy_file', {\n        fromPath: fromPath instanceof URL ? fromPath.toString() : fromPath,\n        toPath: toPath instanceof URL ? toPath.toString() : toPath,\n        options\n    });\n}\n/**\n * Creates a new directory with the specified path.\n * @example\n * ```typescript\n * import { mkdir, BaseDirectory } from '@tauri-apps/plugin-fs';\n * await mkdir('users', { baseDir: BaseDirectory.AppLocalData });\n * ```\n *\n * @since 2.0.0\n */\nasync function mkdir(path, options) {\n    if (path instanceof URL && path.protocol !== 'file:') {\n        throw new TypeError('Must be a file URL.');\n    }\n    await invoke('plugin:fs|mkdir', {\n        path: path instanceof URL ? path.toString() : path,\n        options\n    });\n}\n/**\n * Reads the directory given by path and returns an array of `DirEntry`.\n * @example\n * ```typescript\n * import { readDir, BaseDirectory } from '@tauri-apps/plugin-fs';\n * import { join } from '@tauri-apps/api/path';\n * const dir = \"users\"\n * const entries = await readDir('users', { baseDir: BaseDirectory.AppLocalData });\n * processEntriesRecursively(dir, entries);\n * async function processEntriesRecursively(parent, entries) {\n *   for (const entry of entries) {\n *     console.log(`Entry: ${entry.name}`);\n *     if (entry.isDirectory) {\n *        const dir = await join(parent, entry.name);\n *       processEntriesRecursively(dir, await readDir(dir, { baseDir: BaseDirectory.AppLocalData }))\n *     }\n *   }\n * }\n * ```\n *\n * @since 2.0.0\n */\nasync function readDir(path, options) {\n    if (path instanceof URL && path.protocol !== 'file:') {\n        throw new TypeError('Must be a file URL.');\n    }\n    return await invoke('plugin:fs|read_dir', {\n        path: path instanceof URL ? path.toString() : path,\n        options\n    });\n}\n/**\n * Reads and resolves to the entire contents of a file as an array of bytes.\n * TextDecoder can be used to transform the bytes to string if required.\n * @example\n * ```typescript\n * import { readFile, BaseDirectory } from '@tauri-apps/plugin-fs';\n * const contents = await readFile('avatar.png', { baseDir: BaseDirectory.Resource });\n * ```\n *\n * @since 2.0.0\n */\nasync function readFile(path, options) {\n    if (path instanceof URL && path.protocol !== 'file:') {\n        throw new TypeError('Must be a file URL.');\n    }\n    const arr = await invoke('plugin:fs|read_file', {\n        path: path instanceof URL ? path.toString() : path,\n        options\n    });\n    return arr instanceof ArrayBuffer ? new Uint8Array(arr) : Uint8Array.from(arr);\n}\n/**\n * Reads and returns the entire contents of a file as UTF-8 string.\n * @example\n * ```typescript\n * import { readTextFile, BaseDirectory } from '@tauri-apps/plugin-fs';\n * const contents = await readTextFile('app.conf', { baseDir: BaseDirectory.AppConfig });\n * ```\n *\n * @since 2.0.0\n */\nasync function readTextFile(path, options) {\n    if (path instanceof URL && path.protocol !== 'file:') {\n        throw new TypeError('Must be a file URL.');\n    }\n    const arr = await invoke('plugin:fs|read_text_file', {\n        path: path instanceof URL ? path.toString() : path,\n        options\n    });\n    const bytes = arr instanceof ArrayBuffer ? arr : Uint8Array.from(arr);\n    return new TextDecoder().decode(bytes);\n}\n/**\n * Returns an async {@linkcode AsyncIterableIterator} over the lines of a file as UTF-8 string.\n * @example\n * ```typescript\n * import { readTextFileLines, BaseDirectory } from '@tauri-apps/plugin-fs';\n * const lines = await readTextFileLines('app.conf', { baseDir: BaseDirectory.AppConfig });\n * for await (const line of lines) {\n *   console.log(line);\n * }\n * ```\n * You could also call {@linkcode AsyncIterableIterator.next} to advance the\n * iterator so you can lazily read the next line whenever you want.\n *\n * @since 2.0.0\n */\nasync function readTextFileLines(path, options) {\n    if (path instanceof URL && path.protocol !== 'file:') {\n        throw new TypeError('Must be a file URL.');\n    }\n    const pathStr = path instanceof URL ? path.toString() : path;\n    return await Promise.resolve({\n        path: pathStr,\n        rid: null,\n        async next() {\n            if (this.rid === null) {\n                this.rid = await invoke('plugin:fs|read_text_file_lines', {\n                    path: pathStr,\n                    options\n                });\n            }\n            const arr = await invoke('plugin:fs|read_text_file_lines_next', { rid: this.rid });\n            const bytes = arr instanceof ArrayBuffer ? new Uint8Array(arr) : Uint8Array.from(arr);\n            // Rust side will never return an empty array for this command and\n            // ensure there is at least one elements there.\n            //\n            // This is an optimization to include whether we finished iteration or not (1 or 0)\n            // at the end of returned array to avoid serialization overhead of separate values.\n            const done = bytes[bytes.byteLength - 1] === 1;\n            if (done) {\n                // a full iteration is over, reset rid for next iteration\n                this.rid = null;\n                return { value: null, done };\n            }\n            const line = new TextDecoder().decode(bytes.slice(0, bytes.byteLength));\n            return {\n                value: line,\n                done\n            };\n        },\n        [Symbol.asyncIterator]() {\n            return this;\n        }\n    });\n}\n/**\n * Removes the named file or directory.\n * If the directory is not empty and the `recursive` option isn't set to true, the promise will be rejected.\n * @example\n * ```typescript\n * import { remove, BaseDirectory } from '@tauri-apps/plugin-fs';\n * await remove('users/file.txt', { baseDir: BaseDirectory.AppLocalData });\n * await remove('users', { baseDir: BaseDirectory.AppLocalData });\n * ```\n *\n * @since 2.0.0\n */\nasync function remove(path, options) {\n    if (path instanceof URL && path.protocol !== 'file:') {\n        throw new TypeError('Must be a file URL.');\n    }\n    await invoke('plugin:fs|remove', {\n        path: path instanceof URL ? path.toString() : path,\n        options\n    });\n}\n/**\n * Renames (moves) oldpath to newpath. Paths may be files or directories.\n * If newpath already exists and is not a directory, rename() replaces it.\n * OS-specific restrictions may apply when oldpath and newpath are in different directories.\n *\n * On Unix, this operation does not follow symlinks at either path.\n *\n * @example\n * ```typescript\n * import { rename, BaseDirectory } from '@tauri-apps/plugin-fs';\n * await rename('avatar.png', 'deleted.png', { oldPathBaseDir: BaseDirectory.App, newPathBaseDir: BaseDirectory.AppLocalData });\n * ```\n *\n * @since 2.0.0\n */\nasync function rename(oldPath, newPath, options) {\n    if ((oldPath instanceof URL && oldPath.protocol !== 'file:')\n        || (newPath instanceof URL && newPath.protocol !== 'file:')) {\n        throw new TypeError('Must be a file URL.');\n    }\n    await invoke('plugin:fs|rename', {\n        oldPath: oldPath instanceof URL ? oldPath.toString() : oldPath,\n        newPath: newPath instanceof URL ? newPath.toString() : newPath,\n        options\n    });\n}\n/**\n * Resolves to a {@linkcode FileInfo} for the specified `path`. Will always\n * follow symlinks but will reject if the symlink points to a path outside of the scope.\n *\n * @example\n * ```typescript\n * import { stat, BaseDirectory } from '@tauri-apps/plugin-fs';\n * const fileInfo = await stat(\"hello.txt\", { baseDir: BaseDirectory.AppLocalData });\n * console.log(fileInfo.isFile); // true\n * ```\n *\n * @since 2.0.0\n */\nasync function stat(path, options) {\n    const res = await invoke('plugin:fs|stat', {\n        path: path instanceof URL ? path.toString() : path,\n        options\n    });\n    return parseFileInfo(res);\n}\n/**\n * Resolves to a {@linkcode FileInfo} for the specified `path`. If `path` is a\n * symlink, information for the symlink will be returned instead of what it\n * points to.\n *\n * @example\n * ```typescript\n * import { lstat, BaseDirectory } from '@tauri-apps/plugin-fs';\n * const fileInfo = await lstat(\"hello.txt\", { baseDir: BaseDirectory.AppLocalData });\n * console.log(fileInfo.isFile); // true\n * ```\n *\n * @since 2.0.0\n */\nasync function lstat(path, options) {\n    const res = await invoke('plugin:fs|lstat', {\n        path: path instanceof URL ? path.toString() : path,\n        options\n    });\n    return parseFileInfo(res);\n}\n/**\n * Truncates or extends the specified file, to reach the specified `len`.\n * If `len` is `0` or not specified, then the entire file contents are truncated.\n *\n * @example\n * ```typescript\n * import { truncate, readTextFile, writeTextFile, BaseDirectory } from '@tauri-apps/plugin-fs';\n * // truncate the entire file\n * await truncate(\"my_file.txt\", 0, { baseDir: BaseDirectory.AppLocalData });\n *\n * // truncate part of the file\n * const filePath = \"file.txt\";\n * await writeTextFile(filePath, \"Hello World\", { baseDir: BaseDirectory.AppLocalData });\n * await truncate(filePath, 7, { baseDir: BaseDirectory.AppLocalData });\n * const data = await readTextFile(filePath, { baseDir: BaseDirectory.AppLocalData });\n * console.log(data);  // \"Hello W\"\n * ```\n *\n * @since 2.0.0\n */\nasync function truncate(path, len, options) {\n    if (path instanceof URL && path.protocol !== 'file:') {\n        throw new TypeError('Must be a file URL.');\n    }\n    await invoke('plugin:fs|truncate', {\n        path: path instanceof URL ? path.toString() : path,\n        len,\n        options\n    });\n}\n/**\n * Write `data` to the given `path`, by default creating a new file if needed, else overwriting.\n * @example\n * ```typescript\n * import { writeFile, BaseDirectory } from '@tauri-apps/plugin-fs';\n *\n * let encoder = new TextEncoder();\n * let data = encoder.encode(\"Hello World\");\n * await writeFile('file.txt', data, { baseDir: BaseDirectory.AppLocalData });\n * ```\n *\n * @since 2.0.0\n */\nasync function writeFile(path, data, options) {\n    if (path instanceof URL && path.protocol !== 'file:') {\n        throw new TypeError('Must be a file URL.');\n    }\n    if (data instanceof ReadableStream) {\n        const file = await open(path, { create: true, ...options });\n        const reader = data.getReader();\n        try {\n            while (true) {\n                const { done, value } = await reader.read();\n                if (done)\n                    break;\n                await file.write(value);\n            }\n        }\n        finally {\n            reader.releaseLock();\n            await file.close();\n        }\n    }\n    else {\n        await invoke('plugin:fs|write_file', data, {\n            headers: {\n                path: encodeURIComponent(path instanceof URL ? path.toString() : path),\n                options: JSON.stringify(options)\n            }\n        });\n    }\n}\n/**\n  * Writes UTF-8 string `data` to the given `path`, by default creating a new file if needed, else overwriting.\n    @example\n  * ```typescript\n  * import { writeTextFile, BaseDirectory } from '@tauri-apps/plugin-fs';\n  *\n  * await writeTextFile('file.txt', \"Hello world\", { baseDir: BaseDirectory.AppLocalData });\n  * ```\n  *\n  * @since 2.0.0\n  */\nasync function writeTextFile(path, data, options) {\n    if (path instanceof URL && path.protocol !== 'file:') {\n        throw new TypeError('Must be a file URL.');\n    }\n    const encoder = new TextEncoder();\n    await invoke('plugin:fs|write_text_file', encoder.encode(data), {\n        headers: {\n            path: encodeURIComponent(path instanceof URL ? path.toString() : path),\n            options: JSON.stringify(options)\n        }\n    });\n}\n/**\n * Check if a path exists.\n * @example\n * ```typescript\n * import { exists, BaseDirectory } from '@tauri-apps/plugin-fs';\n * // Check if the `$APPDATA/avatar.png` file exists\n * await exists('avatar.png', { baseDir: BaseDirectory.AppData });\n * ```\n *\n * @since 2.0.0\n */\nasync function exists(path, options) {\n    if (path instanceof URL && path.protocol !== 'file:') {\n        throw new TypeError('Must be a file URL.');\n    }\n    return await invoke('plugin:fs|exists', {\n        path: path instanceof URL ? path.toString() : path,\n        options\n    });\n}\nclass Watcher extends Resource {\n}\nasync function watchInternal(paths, cb, options) {\n    const watchPaths = Array.isArray(paths) ? paths : [paths];\n    for (const path of watchPaths) {\n        if (path instanceof URL && path.protocol !== 'file:') {\n            throw new TypeError('Must be a file URL.');\n        }\n    }\n    const onEvent = new Channel();\n    onEvent.onmessage = cb;\n    const rid = await invoke('plugin:fs|watch', {\n        paths: watchPaths.map((p) => (p instanceof URL ? p.toString() : p)),\n        options,\n        onEvent\n    });\n    const watcher = new Watcher(rid);\n    return () => {\n        void watcher.close();\n    };\n}\n// TODO: Return `Watcher` instead in v3\n/**\n * Watch changes (after a delay) on files or directories.\n *\n * @since 2.0.0\n */\nasync function watch(paths, cb, options) {\n    return await watchInternal(paths, cb, {\n        delayMs: 2000,\n        ...options\n    });\n}\n// TODO: Return `Watcher` instead in v3\n/**\n * Watch changes on files or directories.\n *\n * @since 2.0.0\n */\nasync function watchImmediate(paths, cb, options) {\n    return await watchInternal(paths, cb, {\n        ...options,\n        delayMs: undefined\n    });\n}\n/**\n * Get the size of a file or directory. For files, the `stat` functions can be used as well.\n *\n * If `path` is a directory, this function will recursively iterate over every file and every directory inside of `path` and therefore will be very time consuming if used on larger directories.\n *\n * @example\n * ```typescript\n * import { size, BaseDirectory } from '@tauri-apps/plugin-fs';\n * // Get the size of the `$APPDATA/tauri` directory.\n * const dirSize = await size('tauri', { baseDir: BaseDirectory.AppData });\n * console.log(dirSize); // 1024\n * ```\n *\n * @since 2.1.0\n */\nasync function size(path) {\n    if (path instanceof URL && path.protocol !== 'file:') {\n        throw new TypeError('Must be a file URL.');\n    }\n    return await invoke('plugin:fs|size', {\n        path: path instanceof URL ? path.toString() : path\n    });\n}\n\nexport { FileHandle, SeekMode, copyFile, create, exists, lstat, mkdir, open, readDir, readFile, readTextFile, readTextFileLines, remove, rename, size, stat, truncate, watch, watchImmediate, writeFile, writeTextFile };\n"], "mappings": ";;;;;;;;AAgBA,IAAI;AAAA,CACH,SAAUA,gBAAe;AAItB,EAAAA,eAAcA,eAAc,OAAO,IAAI,CAAC,IAAI;AAI5C,EAAAA,eAAcA,eAAc,OAAO,IAAI,CAAC,IAAI;AAI5C,EAAAA,eAAcA,eAAc,QAAQ,IAAI,CAAC,IAAI;AAI7C,EAAAA,eAAcA,eAAc,MAAM,IAAI,CAAC,IAAI;AAI3C,EAAAA,eAAcA,eAAc,WAAW,IAAI,CAAC,IAAI;AAIhD,EAAAA,eAAcA,eAAc,UAAU,IAAI,CAAC,IAAI;AAI/C,EAAAA,eAAcA,eAAc,UAAU,IAAI,CAAC,IAAI;AAI/C,EAAAA,eAAcA,eAAc,SAAS,IAAI,CAAC,IAAI;AAI9C,EAAAA,eAAcA,eAAc,QAAQ,IAAI,CAAC,IAAI;AAI7C,EAAAA,eAAcA,eAAc,OAAO,IAAI,EAAE,IAAI;AAI7C,EAAAA,eAAcA,eAAc,UAAU,IAAI,EAAE,IAAI;AAIhD,EAAAA,eAAcA,eAAc,MAAM,IAAI,EAAE,IAAI;AAI5C,EAAAA,eAAcA,eAAc,WAAW,IAAI,EAAE,IAAI;AAIjD,EAAAA,eAAcA,eAAc,SAAS,IAAI,EAAE,IAAI;AAI/C,EAAAA,eAAcA,eAAc,cAAc,IAAI,EAAE,IAAI;AAIpD,EAAAA,eAAcA,eAAc,UAAU,IAAI,EAAE,IAAI;AAIhD,EAAAA,eAAcA,eAAc,QAAQ,IAAI,EAAE,IAAI;AAI9C,EAAAA,eAAcA,eAAc,SAAS,IAAI,EAAE,IAAI;AAI/C,EAAAA,eAAcA,eAAc,YAAY,IAAI,EAAE,IAAI;AAIlD,EAAAA,eAAcA,eAAc,MAAM,IAAI,EAAE,IAAI;AAI5C,EAAAA,eAAcA,eAAc,MAAM,IAAI,EAAE,IAAI;AAI5C,EAAAA,eAAcA,eAAc,SAAS,IAAI,EAAE,IAAI;AAI/C,EAAAA,eAAcA,eAAc,UAAU,IAAI,EAAE,IAAI;AACpD,GAAG,kBAAkB,gBAAgB,CAAC,EAAE;;;ACnCxC,IAAI;AAAA,CACH,SAAUC,WAAU;AACjB,EAAAA,UAASA,UAAS,OAAO,IAAI,CAAC,IAAI;AAClC,EAAAA,UAASA,UAAS,SAAS,IAAI,CAAC,IAAI;AACpC,EAAAA,UAASA,UAAS,KAAK,IAAI,CAAC,IAAI;AACpC,GAAG,aAAa,WAAW,CAAC,EAAE;AAC9B,SAAS,cAAc,GAAG;AACtB,SAAO;AAAA,IACH,QAAQ,EAAE;AAAA,IACV,aAAa,EAAE;AAAA,IACf,WAAW,EAAE;AAAA,IACb,MAAM,EAAE;AAAA,IACR,OAAO,EAAE,UAAU,OAAO,IAAI,KAAK,EAAE,KAAK,IAAI;AAAA,IAC9C,OAAO,EAAE,UAAU,OAAO,IAAI,KAAK,EAAE,KAAK,IAAI;AAAA,IAC9C,WAAW,EAAE,cAAc,OAAO,IAAI,KAAK,EAAE,SAAS,IAAI;AAAA,IAC1D,UAAU,EAAE;AAAA,IACZ,gBAAgB,EAAE;AAAA,IAClB,KAAK,EAAE;AAAA,IACP,KAAK,EAAE;AAAA,IACP,MAAM,EAAE;AAAA,IACR,OAAO,EAAE;AAAA,IACT,KAAK,EAAE;AAAA,IACP,KAAK,EAAE;AAAA,IACP,MAAM,EAAE;AAAA,IACR,SAAS,EAAE;AAAA,IACX,QAAQ,EAAE;AAAA,EACd;AACJ;AAGA,SAAS,UAAU,QAAQ;AACvB,QAAM,QAAQ,IAAI,kBAAkB,MAAM;AAC1C,QAAMC,QAAO,MAAM;AACnB,MAAI,IAAI;AACR,WAAS,IAAI,GAAG,IAAIA,OAAM,KAAK;AAE3B,UAAM,OAAO,MAAM,CAAC;AACpB,SAAK;AACL,SAAK;AAAA,EACT;AACA,SAAO;AACX;AAMA,IAAM,aAAN,cAAyB,SAAS;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAgC9B,MAAM,KAAK,QAAQ;AACf,QAAI,OAAO,eAAe,GAAG;AACzB,aAAO;AAAA,IACX;AACA,UAAM,OAAO,MAAM,OAAO,kBAAkB;AAAA,MACxC,KAAK,KAAK;AAAA,MACV,KAAK,OAAO;AAAA,IAChB,CAAC;AAMD,UAAM,QAAQ,UAAU,KAAK,MAAM,EAAE,CAAC;AACtC,UAAM,QAAQ,gBAAgB,cAAc,IAAI,WAAW,IAAI,IAAI;AACnE,WAAO,IAAI,MAAM,MAAM,GAAG,MAAM,SAAS,CAAC,CAAC;AAC3C,WAAO,UAAU,IAAI,OAAO;AAAA,EAChC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAiCA,MAAM,KAAK,QAAQ,QAAQ;AACvB,WAAO,MAAM,OAAO,kBAAkB;AAAA,MAClC,KAAK,KAAK;AAAA,MACV;AAAA,MACA;AAAA,IACJ,CAAC;AAAA,EACL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAeA,MAAM,OAAO;AACT,UAAM,MAAM,MAAM,OAAO,mBAAmB;AAAA,MACxC,KAAK,KAAK;AAAA,IACd,CAAC;AACD,WAAO,cAAc,GAAG;AAAA,EAC5B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAyBA,MAAM,SAAS,KAAK;AAChB,UAAM,OAAO,uBAAuB;AAAA,MAChC,KAAK,KAAK;AAAA,MACV;AAAA,IACJ,CAAC;AAAA,EACL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAqBA,MAAM,MAAM,MAAM;AACd,WAAO,MAAM,OAAO,mBAAmB;AAAA,MACnC,KAAK,KAAK;AAAA,MACV;AAAA,IACJ,CAAC;AAAA,EACL;AACJ;AAeA,eAAe,OAAO,MAAM,SAAS;AACjC,MAAI,gBAAgB,OAAO,KAAK,aAAa,SAAS;AAClD,UAAM,IAAI,UAAU,qBAAqB;AAAA,EAC7C;AACA,QAAM,MAAM,MAAM,OAAO,oBAAoB;AAAA,IACzC,MAAM,gBAAgB,MAAM,KAAK,SAAS,IAAI;AAAA,IAC9C;AAAA,EACJ,CAAC;AACD,SAAO,IAAI,WAAW,GAAG;AAC7B;AAiBA,eAAe,KAAK,MAAM,SAAS;AAC/B,MAAI,gBAAgB,OAAO,KAAK,aAAa,SAAS;AAClD,UAAM,IAAI,UAAU,qBAAqB;AAAA,EAC7C;AACA,QAAM,MAAM,MAAM,OAAO,kBAAkB;AAAA,IACvC,MAAM,gBAAgB,MAAM,KAAK,SAAS,IAAI;AAAA,IAC9C;AAAA,EACJ,CAAC;AACD,SAAO,IAAI,WAAW,GAAG;AAC7B;AAWA,eAAe,SAAS,UAAU,QAAQ,SAAS;AAC/C,MAAK,oBAAoB,OAAO,SAAS,aAAa,WAC9C,kBAAkB,OAAO,OAAO,aAAa,SAAU;AAC3D,UAAM,IAAI,UAAU,qBAAqB;AAAA,EAC7C;AACA,QAAM,OAAO,uBAAuB;AAAA,IAChC,UAAU,oBAAoB,MAAM,SAAS,SAAS,IAAI;AAAA,IAC1D,QAAQ,kBAAkB,MAAM,OAAO,SAAS,IAAI;AAAA,IACpD;AAAA,EACJ,CAAC;AACL;AAWA,eAAe,MAAM,MAAM,SAAS;AAChC,MAAI,gBAAgB,OAAO,KAAK,aAAa,SAAS;AAClD,UAAM,IAAI,UAAU,qBAAqB;AAAA,EAC7C;AACA,QAAM,OAAO,mBAAmB;AAAA,IAC5B,MAAM,gBAAgB,MAAM,KAAK,SAAS,IAAI;AAAA,IAC9C;AAAA,EACJ,CAAC;AACL;AAuBA,eAAe,QAAQ,MAAM,SAAS;AAClC,MAAI,gBAAgB,OAAO,KAAK,aAAa,SAAS;AAClD,UAAM,IAAI,UAAU,qBAAqB;AAAA,EAC7C;AACA,SAAO,MAAM,OAAO,sBAAsB;AAAA,IACtC,MAAM,gBAAgB,MAAM,KAAK,SAAS,IAAI;AAAA,IAC9C;AAAA,EACJ,CAAC;AACL;AAYA,eAAe,SAAS,MAAM,SAAS;AACnC,MAAI,gBAAgB,OAAO,KAAK,aAAa,SAAS;AAClD,UAAM,IAAI,UAAU,qBAAqB;AAAA,EAC7C;AACA,QAAM,MAAM,MAAM,OAAO,uBAAuB;AAAA,IAC5C,MAAM,gBAAgB,MAAM,KAAK,SAAS,IAAI;AAAA,IAC9C;AAAA,EACJ,CAAC;AACD,SAAO,eAAe,cAAc,IAAI,WAAW,GAAG,IAAI,WAAW,KAAK,GAAG;AACjF;AAWA,eAAe,aAAa,MAAM,SAAS;AACvC,MAAI,gBAAgB,OAAO,KAAK,aAAa,SAAS;AAClD,UAAM,IAAI,UAAU,qBAAqB;AAAA,EAC7C;AACA,QAAM,MAAM,MAAM,OAAO,4BAA4B;AAAA,IACjD,MAAM,gBAAgB,MAAM,KAAK,SAAS,IAAI;AAAA,IAC9C;AAAA,EACJ,CAAC;AACD,QAAM,QAAQ,eAAe,cAAc,MAAM,WAAW,KAAK,GAAG;AACpE,SAAO,IAAI,YAAY,EAAE,OAAO,KAAK;AACzC;AAgBA,eAAe,kBAAkB,MAAM,SAAS;AAC5C,MAAI,gBAAgB,OAAO,KAAK,aAAa,SAAS;AAClD,UAAM,IAAI,UAAU,qBAAqB;AAAA,EAC7C;AACA,QAAM,UAAU,gBAAgB,MAAM,KAAK,SAAS,IAAI;AACxD,SAAO,MAAM,QAAQ,QAAQ;AAAA,IACzB,MAAM;AAAA,IACN,KAAK;AAAA,IACL,MAAM,OAAO;AACT,UAAI,KAAK,QAAQ,MAAM;AACnB,aAAK,MAAM,MAAM,OAAO,kCAAkC;AAAA,UACtD,MAAM;AAAA,UACN;AAAA,QACJ,CAAC;AAAA,MACL;AACA,YAAM,MAAM,MAAM,OAAO,uCAAuC,EAAE,KAAK,KAAK,IAAI,CAAC;AACjF,YAAM,QAAQ,eAAe,cAAc,IAAI,WAAW,GAAG,IAAI,WAAW,KAAK,GAAG;AAMpF,YAAM,OAAO,MAAM,MAAM,aAAa,CAAC,MAAM;AAC7C,UAAI,MAAM;AAEN,aAAK,MAAM;AACX,eAAO,EAAE,OAAO,MAAM,KAAK;AAAA,MAC/B;AACA,YAAM,OAAO,IAAI,YAAY,EAAE,OAAO,MAAM,MAAM,GAAG,MAAM,UAAU,CAAC;AACtE,aAAO;AAAA,QACH,OAAO;AAAA,QACP;AAAA,MACJ;AAAA,IACJ;AAAA,IACA,CAAC,OAAO,aAAa,IAAI;AACrB,aAAO;AAAA,IACX;AAAA,EACJ,CAAC;AACL;AAaA,eAAe,OAAO,MAAM,SAAS;AACjC,MAAI,gBAAgB,OAAO,KAAK,aAAa,SAAS;AAClD,UAAM,IAAI,UAAU,qBAAqB;AAAA,EAC7C;AACA,QAAM,OAAO,oBAAoB;AAAA,IAC7B,MAAM,gBAAgB,MAAM,KAAK,SAAS,IAAI;AAAA,IAC9C;AAAA,EACJ,CAAC;AACL;AAgBA,eAAe,OAAO,SAAS,SAAS,SAAS;AAC7C,MAAK,mBAAmB,OAAO,QAAQ,aAAa,WAC5C,mBAAmB,OAAO,QAAQ,aAAa,SAAU;AAC7D,UAAM,IAAI,UAAU,qBAAqB;AAAA,EAC7C;AACA,QAAM,OAAO,oBAAoB;AAAA,IAC7B,SAAS,mBAAmB,MAAM,QAAQ,SAAS,IAAI;AAAA,IACvD,SAAS,mBAAmB,MAAM,QAAQ,SAAS,IAAI;AAAA,IACvD;AAAA,EACJ,CAAC;AACL;AAcA,eAAe,KAAK,MAAM,SAAS;AAC/B,QAAM,MAAM,MAAM,OAAO,kBAAkB;AAAA,IACvC,MAAM,gBAAgB,MAAM,KAAK,SAAS,IAAI;AAAA,IAC9C;AAAA,EACJ,CAAC;AACD,SAAO,cAAc,GAAG;AAC5B;AAeA,eAAe,MAAM,MAAM,SAAS;AAChC,QAAM,MAAM,MAAM,OAAO,mBAAmB;AAAA,IACxC,MAAM,gBAAgB,MAAM,KAAK,SAAS,IAAI;AAAA,IAC9C;AAAA,EACJ,CAAC;AACD,SAAO,cAAc,GAAG;AAC5B;AAqBA,eAAe,SAAS,MAAM,KAAK,SAAS;AACxC,MAAI,gBAAgB,OAAO,KAAK,aAAa,SAAS;AAClD,UAAM,IAAI,UAAU,qBAAqB;AAAA,EAC7C;AACA,QAAM,OAAO,sBAAsB;AAAA,IAC/B,MAAM,gBAAgB,MAAM,KAAK,SAAS,IAAI;AAAA,IAC9C;AAAA,IACA;AAAA,EACJ,CAAC;AACL;AAcA,eAAe,UAAU,MAAM,MAAM,SAAS;AAC1C,MAAI,gBAAgB,OAAO,KAAK,aAAa,SAAS;AAClD,UAAM,IAAI,UAAU,qBAAqB;AAAA,EAC7C;AACA,MAAI,gBAAgB,gBAAgB;AAChC,UAAM,OAAO,MAAM,KAAK,MAAM,EAAE,QAAQ,MAAM,GAAG,QAAQ,CAAC;AAC1D,UAAM,SAAS,KAAK,UAAU;AAC9B,QAAI;AACA,aAAO,MAAM;AACT,cAAM,EAAE,MAAM,MAAM,IAAI,MAAM,OAAO,KAAK;AAC1C,YAAI;AACA;AACJ,cAAM,KAAK,MAAM,KAAK;AAAA,MAC1B;AAAA,IACJ,UACA;AACI,aAAO,YAAY;AACnB,YAAM,KAAK,MAAM;AAAA,IACrB;AAAA,EACJ,OACK;AACD,UAAM,OAAO,wBAAwB,MAAM;AAAA,MACvC,SAAS;AAAA,QACL,MAAM,mBAAmB,gBAAgB,MAAM,KAAK,SAAS,IAAI,IAAI;AAAA,QACrE,SAAS,KAAK,UAAU,OAAO;AAAA,MACnC;AAAA,IACJ,CAAC;AAAA,EACL;AACJ;AAYA,eAAe,cAAc,MAAM,MAAM,SAAS;AAC9C,MAAI,gBAAgB,OAAO,KAAK,aAAa,SAAS;AAClD,UAAM,IAAI,UAAU,qBAAqB;AAAA,EAC7C;AACA,QAAM,UAAU,IAAI,YAAY;AAChC,QAAM,OAAO,6BAA6B,QAAQ,OAAO,IAAI,GAAG;AAAA,IAC5D,SAAS;AAAA,MACL,MAAM,mBAAmB,gBAAgB,MAAM,KAAK,SAAS,IAAI,IAAI;AAAA,MACrE,SAAS,KAAK,UAAU,OAAO;AAAA,IACnC;AAAA,EACJ,CAAC;AACL;AAYA,eAAe,OAAO,MAAM,SAAS;AACjC,MAAI,gBAAgB,OAAO,KAAK,aAAa,SAAS;AAClD,UAAM,IAAI,UAAU,qBAAqB;AAAA,EAC7C;AACA,SAAO,MAAM,OAAO,oBAAoB;AAAA,IACpC,MAAM,gBAAgB,MAAM,KAAK,SAAS,IAAI;AAAA,IAC9C;AAAA,EACJ,CAAC;AACL;AACA,IAAM,UAAN,cAAsB,SAAS;AAC/B;AACA,eAAe,cAAc,OAAO,IAAI,SAAS;AAC7C,QAAM,aAAa,MAAM,QAAQ,KAAK,IAAI,QAAQ,CAAC,KAAK;AACxD,aAAW,QAAQ,YAAY;AAC3B,QAAI,gBAAgB,OAAO,KAAK,aAAa,SAAS;AAClD,YAAM,IAAI,UAAU,qBAAqB;AAAA,IAC7C;AAAA,EACJ;AACA,QAAM,UAAU,IAAI,QAAQ;AAC5B,UAAQ,YAAY;AACpB,QAAM,MAAM,MAAM,OAAO,mBAAmB;AAAA,IACxC,OAAO,WAAW,IAAI,CAAC,MAAO,aAAa,MAAM,EAAE,SAAS,IAAI,CAAE;AAAA,IAClE;AAAA,IACA;AAAA,EACJ,CAAC;AACD,QAAM,UAAU,IAAI,QAAQ,GAAG;AAC/B,SAAO,MAAM;AACT,SAAK,QAAQ,MAAM;AAAA,EACvB;AACJ;AAOA,eAAe,MAAM,OAAO,IAAI,SAAS;AACrC,SAAO,MAAM,cAAc,OAAO,IAAI;AAAA,IAClC,SAAS;AAAA,IACT,GAAG;AAAA,EACP,CAAC;AACL;AAOA,eAAe,eAAe,OAAO,IAAI,SAAS;AAC9C,SAAO,MAAM,cAAc,OAAO,IAAI;AAAA,IAClC,GAAG;AAAA,IACH,SAAS;AAAA,EACb,CAAC;AACL;AAgBA,eAAe,KAAK,MAAM;AACtB,MAAI,gBAAgB,OAAO,KAAK,aAAa,SAAS;AAClD,UAAM,IAAI,UAAU,qBAAqB;AAAA,EAC7C;AACA,SAAO,MAAM,OAAO,kBAAkB;AAAA,IAClC,MAAM,gBAAgB,MAAM,KAAK,SAAS,IAAI;AAAA,EAClD,CAAC;AACL;", "names": ["BaseDirectory", "SeekMode", "size"]}