#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/d/ssh/learn/project-graph-origin/node_modules/.pnpm/openai@5.10.2_ws@8.18.3_zod@3.25.76/node_modules/openai/bin/node_modules:/mnt/d/ssh/learn/project-graph-origin/node_modules/.pnpm/openai@5.10.2_ws@8.18.3_zod@3.25.76/node_modules/openai/node_modules:/mnt/d/ssh/learn/project-graph-origin/node_modules/.pnpm/openai@5.10.2_ws@8.18.3_zod@3.25.76/node_modules:/mnt/d/ssh/learn/project-graph-origin/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/d/ssh/learn/project-graph-origin/node_modules/.pnpm/openai@5.10.2_ws@8.18.3_zod@3.25.76/node_modules/openai/bin/node_modules:/mnt/d/ssh/learn/project-graph-origin/node_modules/.pnpm/openai@5.10.2_ws@8.18.3_zod@3.25.76/node_modules/openai/node_modules:/mnt/d/ssh/learn/project-graph-origin/node_modules/.pnpm/openai@5.10.2_ws@8.18.3_zod@3.25.76/node_modules:/mnt/d/ssh/learn/project-graph-origin/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../openai/bin/cli" "$@"
else
  exec node  "$basedir/../openai/bin/cli" "$@"
fi
