{"version": 3, "sources": ["../../../../node_modules/.pnpm/@tauri-apps+plugin-dialog@2.3.1/node_modules/@tauri-apps/plugin-dialog/dist-js/index.js"], "sourcesContent": ["import { invoke } from '@tauri-apps/api/core';\n\n// Copyright 2019-2023 Tauri Programme within The Commons Conservancy\n// SPDX-License-Identifier: Apache-2.0\n// SPDX-License-Identifier: MIT\n/**\n * Open a file/directory selection dialog.\n *\n * The selected paths are added to the filesystem and asset protocol scopes.\n * When security is more important than the easy of use of this API,\n * prefer writing a dedicated command instead.\n *\n * Note that the scope change is not persisted, so the values are cleared when the application is restarted.\n * You can save it to the filesystem using [tauri-plugin-persisted-scope](https://github.com/tauri-apps/tauri-plugin-persisted-scope).\n * @example\n * ```typescript\n * import { open } from '@tauri-apps/plugin-dialog';\n * // Open a selection dialog for image files\n * const selected = await open({\n *   multiple: true,\n *   filters: [{\n *     name: 'Image',\n *     extensions: ['png', 'jpeg']\n *   }]\n * });\n * if (Array.isArray(selected)) {\n *   // user selected multiple files\n * } else if (selected === null) {\n *   // user cancelled the selection\n * } else {\n *   // user selected a single file\n * }\n * ```\n *\n * @example\n * ```typescript\n * import { open } from '@tauri-apps/plugin-dialog';\n * import { appDir } from '@tauri-apps/api/path';\n * // Open a selection dialog for directories\n * const selected = await open({\n *   directory: true,\n *   multiple: true,\n *   defaultPath: await appDir(),\n * });\n * if (Array.isArray(selected)) {\n *   // user selected multiple directories\n * } else if (selected === null) {\n *   // user cancelled the selection\n * } else {\n *   // user selected a single directory\n * }\n * ```\n *\n * @returns A promise resolving to the selected path(s)\n *\n * @since 2.0.0\n */\nasync function open(options = {}) {\n    if (typeof options === 'object') {\n        Object.freeze(options);\n    }\n    return await invoke('plugin:dialog|open', { options });\n}\n/**\n * Open a file/directory save dialog.\n *\n * The selected path is added to the filesystem and asset protocol scopes.\n * When security is more important than the easy of use of this API,\n * prefer writing a dedicated command instead.\n *\n * Note that the scope change is not persisted, so the values are cleared when the application is restarted.\n * You can save it to the filesystem using [tauri-plugin-persisted-scope](https://github.com/tauri-apps/tauri-plugin-persisted-scope).\n * @example\n * ```typescript\n * import { save } from '@tauri-apps/plugin-dialog';\n * const filePath = await save({\n *   filters: [{\n *     name: 'Image',\n *     extensions: ['png', 'jpeg']\n *   }]\n * });\n * ```\n *\n * @returns A promise resolving to the selected path.\n *\n * @since 2.0.0\n */\nasync function save(options = {}) {\n    if (typeof options === 'object') {\n        Object.freeze(options);\n    }\n    return await invoke('plugin:dialog|save', { options });\n}\n/**\n * Shows a message dialog with an `Ok` button.\n * @example\n * ```typescript\n * import { message } from '@tauri-apps/plugin-dialog';\n * await message('Tauri is awesome', 'Tauri');\n * await message('File not found', { title: 'Tauri', kind: 'error' });\n * ```\n *\n * @param message The message to show.\n * @param options The dialog's options. If a string, it represents the dialog title.\n *\n * @returns A promise indicating the success or failure of the operation.\n *\n * @since 2.0.0\n *\n */\nasync function message(message, options) {\n    const opts = typeof options === 'string' ? { title: options } : options;\n    await invoke('plugin:dialog|message', {\n        message: message.toString(),\n        title: opts?.title?.toString(),\n        kind: opts?.kind,\n        okButtonLabel: opts?.okLabel?.toString()\n    });\n}\n/**\n * Shows a question dialog with `Yes` and `No` buttons.\n * @example\n * ```typescript\n * import { ask } from '@tauri-apps/plugin-dialog';\n * const yes = await ask('Are you sure?', 'Tauri');\n * const yes2 = await ask('This action cannot be reverted. Are you sure?', { title: 'Tauri', kind: 'warning' });\n * ```\n *\n * @param message The message to show.\n * @param options The dialog's options. If a string, it represents the dialog title.\n *\n * @returns A promise resolving to a boolean indicating whether `Yes` was clicked or not.\n *\n * @since 2.0.0\n */\nasync function ask(message, options) {\n    const opts = typeof options === 'string' ? { title: options } : options;\n    return await invoke('plugin:dialog|ask', {\n        message: message.toString(),\n        title: opts?.title?.toString(),\n        kind: opts?.kind,\n        yesButtonLabel: opts?.okLabel?.toString(),\n        noButtonLabel: opts?.cancelLabel?.toString()\n    });\n}\n/**\n * Shows a question dialog with `Ok` and `Cancel` buttons.\n * @example\n * ```typescript\n * import { confirm } from '@tauri-apps/plugin-dialog';\n * const confirmed = await confirm('Are you sure?', 'Tauri');\n * const confirmed2 = await confirm('This action cannot be reverted. Are you sure?', { title: 'Tauri', kind: 'warning' });\n * ```\n *\n * @param message The message to show.\n * @param options The dialog's options. If a string, it represents the dialog title.\n *\n * @returns A promise resolving to a boolean indicating whether `Ok` was clicked or not.\n *\n * @since 2.0.0\n */\nasync function confirm(message, options) {\n    const opts = typeof options === 'string' ? { title: options } : options;\n    return await invoke('plugin:dialog|confirm', {\n        message: message.toString(),\n        title: opts?.title?.toString(),\n        kind: opts?.kind,\n        okButtonLabel: opts?.okLabel?.toString(),\n        cancelButtonLabel: opts?.cancelLabel?.toString()\n    });\n}\n\nexport { ask, confirm, message, open, save };\n"], "mappings": ";;;;;;AAyDA,eAAe,KAAK,UAAU,CAAC,GAAG;AAC9B,MAAI,OAAO,YAAY,UAAU;AAC7B,WAAO,OAAO,OAAO;AAAA,EACzB;AACA,SAAO,MAAM,OAAO,sBAAsB,EAAE,QAAQ,CAAC;AACzD;AAyBA,eAAe,KAAK,UAAU,CAAC,GAAG;AAC9B,MAAI,OAAO,YAAY,UAAU;AAC7B,WAAO,OAAO,OAAO;AAAA,EACzB;AACA,SAAO,MAAM,OAAO,sBAAsB,EAAE,QAAQ,CAAC;AACzD;AAkBA,eAAe,QAAQA,UAAS,SAAS;AA9GzC;AA+GI,QAAM,OAAO,OAAO,YAAY,WAAW,EAAE,OAAO,QAAQ,IAAI;AAChE,QAAM,OAAO,yBAAyB;AAAA,IAClC,SAASA,SAAQ,SAAS;AAAA,IAC1B,QAAO,kCAAM,UAAN,mBAAa;AAAA,IACpB,MAAM,6BAAM;AAAA,IACZ,gBAAe,kCAAM,YAAN,mBAAe;AAAA,EAClC,CAAC;AACL;AAiBA,eAAe,IAAIA,UAAS,SAAS;AAvIrC;AAwII,QAAM,OAAO,OAAO,YAAY,WAAW,EAAE,OAAO,QAAQ,IAAI;AAChE,SAAO,MAAM,OAAO,qBAAqB;AAAA,IACrC,SAASA,SAAQ,SAAS;AAAA,IAC1B,QAAO,kCAAM,UAAN,mBAAa;AAAA,IACpB,MAAM,6BAAM;AAAA,IACZ,iBAAgB,kCAAM,YAAN,mBAAe;AAAA,IAC/B,gBAAe,kCAAM,gBAAN,mBAAmB;AAAA,EACtC,CAAC;AACL;AAiBA,eAAe,QAAQA,UAAS,SAAS;AAjKzC;AAkKI,QAAM,OAAO,OAAO,YAAY,WAAW,EAAE,OAAO,QAAQ,IAAI;AAChE,SAAO,MAAM,OAAO,yBAAyB;AAAA,IACzC,SAASA,SAAQ,SAAS;AAAA,IAC1B,QAAO,kCAAM,UAAN,mBAAa;AAAA,IACpB,MAAM,6BAAM;AAAA,IACZ,gBAAe,kCAAM,YAAN,mBAAe;AAAA,IAC9B,oBAAmB,kCAAM,gBAAN,mBAAmB;AAAA,EAC1C,CAAC;AACL;", "names": ["message"]}