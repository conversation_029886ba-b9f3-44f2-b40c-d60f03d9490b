{"name": "@anthropic-ai/sdk", "version": "0.57.0", "description": "The official TypeScript library for the Anthropic API", "author": "Anthropic <<EMAIL>>", "types": "dist/index.d.ts", "main": "dist/index.js", "type": "commonjs", "repository": "github:anthropics/anthropic-sdk-typescript", "license": "MIT", "packageManager": "yarn@1.22.22", "files": ["**/*"], "private": false, "scripts": {"test": "./scripts/test", "build": "./scripts/build-all", "prepublishOnly": "echo 'to publish, run yarn build && (cd dist; yarn publish)' && exit 1", "format": "./scripts/format", "prepare": "if ./scripts/utils/check-is-in-git-install.sh; then ./scripts/build && ./scripts/utils/git-swap.sh; fi", "tsn": "ts-node -r tsconfig-paths/register", "lint": "./scripts/lint", "fix": "./scripts/format"}, "dependencies": {}, "devDependencies": {"@arethetypeswrong/cli": "^0.17.0", "@swc/core": "^1.3.102", "@swc/jest": "^0.2.29", "@types/jest": "^29.4.0", "@types/node": "^20.17.6", "@typescript-eslint/eslint-plugin": "8.31.1", "@typescript-eslint/parser": "8.31.1", "eslint": "^9.20.1", "eslint-plugin-prettier": "^5.4.1", "eslint-plugin-unused-imports": "^4.1.4", "iconv-lite": "^0.6.3", "jest": "^29.4.0", "prettier": "^3.0.0", "publint": "^0.2.12", "ts-jest": "^29.1.0", "ts-node": "^10.5.0", "tsc-multi": "https://github.com/stainless-api/tsc-multi/releases/download/v1.1.8/tsc-multi.tgz", "tsconfig-paths": "^4.0.0", "typescript": "5.8.3", "typescript-eslint": "8.31.1"}, "imports": {"@anthropic-ai/sdk": ".", "@anthropic-ai/sdk/*": "./src/*"}, "bin": {"anthropic-ai-sdk": "bin/cli"}, "exports": {".": {"import": "./dist/index.mjs", "require": "./dist/index.js"}, "./*.mjs": {"default": "./dist/*.mjs"}, "./*.js": {"default": "./dist/*.js"}, "./*": {"import": "./dist/*.mjs", "require": "./dist/*.js"}}}