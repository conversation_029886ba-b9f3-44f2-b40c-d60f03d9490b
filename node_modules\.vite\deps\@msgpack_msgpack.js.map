{"version": 3, "sources": ["../../../../node_modules/.pnpm/@msgpack+msgpack@3.1.2/node_modules/@msgpack/msgpack/src/utils/utf8.ts", "../../../../node_modules/.pnpm/@msgpack+msgpack@3.1.2/node_modules/@msgpack/msgpack/src/ExtData.ts", "../../../../node_modules/.pnpm/@msgpack+msgpack@3.1.2/node_modules/@msgpack/msgpack/src/DecodeError.ts", "../../../../node_modules/.pnpm/@msgpack+msgpack@3.1.2/node_modules/@msgpack/msgpack/src/utils/int.ts", "../../../../node_modules/.pnpm/@msgpack+msgpack@3.1.2/node_modules/@msgpack/msgpack/src/timestamp.ts", "../../../../node_modules/.pnpm/@msgpack+msgpack@3.1.2/node_modules/@msgpack/msgpack/src/ExtensionCodec.ts", "../../../../node_modules/.pnpm/@msgpack+msgpack@3.1.2/node_modules/@msgpack/msgpack/src/utils/typedArrays.ts", "../../../../node_modules/.pnpm/@msgpack+msgpack@3.1.2/node_modules/@msgpack/msgpack/src/Encoder.ts", "../../../../node_modules/.pnpm/@msgpack+msgpack@3.1.2/node_modules/@msgpack/msgpack/src/encode.ts", "../../../../node_modules/.pnpm/@msgpack+msgpack@3.1.2/node_modules/@msgpack/msgpack/src/utils/prettyByte.ts", "../../../../node_modules/.pnpm/@msgpack+msgpack@3.1.2/node_modules/@msgpack/msgpack/src/CachedKeyDecoder.ts", "../../../../node_modules/.pnpm/@msgpack+msgpack@3.1.2/node_modules/@msgpack/msgpack/src/Decoder.ts", "../../../../node_modules/.pnpm/@msgpack+msgpack@3.1.2/node_modules/@msgpack/msgpack/src/decode.ts", "../../../../node_modules/.pnpm/@msgpack+msgpack@3.1.2/node_modules/@msgpack/msgpack/src/utils/stream.ts", "../../../../node_modules/.pnpm/@msgpack+msgpack@3.1.2/node_modules/@msgpack/msgpack/src/decodeAsync.ts"], "sourcesContent": ["export function utf8Count(str: string): number {\n  const strLength = str.length;\n\n  let byteLength = 0;\n  let pos = 0;\n  while (pos < strLength) {\n    let value = str.charCodeAt(pos++);\n\n    if ((value & 0xffffff80) === 0) {\n      // 1-byte\n      byteLength++;\n      continue;\n    } else if ((value & 0xfffff800) === 0) {\n      // 2-bytes\n      byteLength += 2;\n    } else {\n      // handle surrogate pair\n      if (value >= 0xd800 && value <= 0xdbff) {\n        // high surrogate\n        if (pos < strLength) {\n          const extra = str.charCodeAt(pos);\n          if ((extra & 0xfc00) === 0xdc00) {\n            ++pos;\n            value = ((value & 0x3ff) << 10) + (extra & 0x3ff) + 0x10000;\n          }\n        }\n      }\n\n      if ((value & 0xffff0000) === 0) {\n        // 3-byte\n        byteLength += 3;\n      } else {\n        // 4-byte\n        byteLength += 4;\n      }\n    }\n  }\n  return byteLength;\n}\n\nexport function utf8EncodeJs(str: string, output: Uint8Array, outputOffset: number): void {\n  const strLength = str.length;\n  let offset = outputOffset;\n  let pos = 0;\n  while (pos < strLength) {\n    let value = str.charCodeAt(pos++);\n\n    if ((value & 0xffffff80) === 0) {\n      // 1-byte\n      output[offset++] = value;\n      continue;\n    } else if ((value & 0xfffff800) === 0) {\n      // 2-bytes\n      output[offset++] = ((value >> 6) & 0x1f) | 0xc0;\n    } else {\n      // handle surrogate pair\n      if (value >= 0xd800 && value <= 0xdbff) {\n        // high surrogate\n        if (pos < strLength) {\n          const extra = str.charCodeAt(pos);\n          if ((extra & 0xfc00) === 0xdc00) {\n            ++pos;\n            value = ((value & 0x3ff) << 10) + (extra & 0x3ff) + 0x10000;\n          }\n        }\n      }\n\n      if ((value & 0xffff0000) === 0) {\n        // 3-byte\n        output[offset++] = ((value >> 12) & 0x0f) | 0xe0;\n        output[offset++] = ((value >> 6) & 0x3f) | 0x80;\n      } else {\n        // 4-byte\n        output[offset++] = ((value >> 18) & 0x07) | 0xf0;\n        output[offset++] = ((value >> 12) & 0x3f) | 0x80;\n        output[offset++] = ((value >> 6) & 0x3f) | 0x80;\n      }\n    }\n\n    output[offset++] = (value & 0x3f) | 0x80;\n  }\n}\n\n// TextEncoder and TextDecoder are standardized in whatwg encoding:\n// https://encoding.spec.whatwg.org/\n// and available in all the modern browsers:\n// https://caniuse.com/textencoder\n// They are available in Node.js since v12 LTS as well:\n// https://nodejs.org/api/globals.html#textencoder\n\nconst sharedTextEncoder = new TextEncoder();\n\n// This threshold should be determined by benchmarking, which might vary in engines and input data.\n// Run `npx ts-node benchmark/encode-string.ts` for details.\nconst TEXT_ENCODER_THRESHOLD = 50;\n\nexport function utf8EncodeTE(str: string, output: Uint8Array, outputOffset: number): void {\n  sharedTextEncoder.encodeInto(str, output.subarray(outputOffset));\n}\n\nexport function utf8Encode(str: string, output: Uint8Array, outputOffset: number): void {\n  if (str.length > TEXT_ENCODER_THRESHOLD) {\n    utf8EncodeTE(str, output, outputOffset);\n  } else {\n    utf8EncodeJs(str, output, outputOffset);\n  }\n}\n\nconst CHUNK_SIZE = 0x1_000;\n\nexport function utf8DecodeJs(bytes: Uint8Array, inputOffset: number, byteLength: number): string {\n  let offset = inputOffset;\n  const end = offset + byteLength;\n\n  const units: Array<number> = [];\n  let result = \"\";\n  while (offset < end) {\n    const byte1 = bytes[offset++]!;\n    if ((byte1 & 0x80) === 0) {\n      // 1 byte\n      units.push(byte1);\n    } else if ((byte1 & 0xe0) === 0xc0) {\n      // 2 bytes\n      const byte2 = bytes[offset++]! & 0x3f;\n      units.push(((byte1 & 0x1f) << 6) | byte2);\n    } else if ((byte1 & 0xf0) === 0xe0) {\n      // 3 bytes\n      const byte2 = bytes[offset++]! & 0x3f;\n      const byte3 = bytes[offset++]! & 0x3f;\n      units.push(((byte1 & 0x1f) << 12) | (byte2 << 6) | byte3);\n    } else if ((byte1 & 0xf8) === 0xf0) {\n      // 4 bytes\n      const byte2 = bytes[offset++]! & 0x3f;\n      const byte3 = bytes[offset++]! & 0x3f;\n      const byte4 = bytes[offset++]! & 0x3f;\n      let unit = ((byte1 & 0x07) << 0x12) | (byte2 << 0x0c) | (byte3 << 0x06) | byte4;\n      if (unit > 0xffff) {\n        unit -= 0x10000;\n        units.push(((unit >>> 10) & 0x3ff) | 0xd800);\n        unit = 0xdc00 | (unit & 0x3ff);\n      }\n      units.push(unit);\n    } else {\n      units.push(byte1);\n    }\n\n    if (units.length >= CHUNK_SIZE) {\n      result += String.fromCharCode(...units);\n      units.length = 0;\n    }\n  }\n\n  if (units.length > 0) {\n    result += String.fromCharCode(...units);\n  }\n\n  return result;\n}\n\nconst sharedTextDecoder = new TextDecoder();\n\n// This threshold should be determined by benchmarking, which might vary in engines and input data.\n// Run `npx ts-node benchmark/decode-string.ts` for details.\nconst TEXT_DECODER_THRESHOLD = 200;\n\nexport function utf8DecodeTD(bytes: Uint8Array, inputOffset: number, byteLength: number): string {\n  const stringBytes = bytes.subarray(inputOffset, inputOffset + byteLength);\n  return sharedTextDecoder.decode(stringBytes);\n}\n\nexport function utf8Decode(bytes: Uint8Array, inputOffset: number, byteLength: number): string {\n  if (byteLength > TEXT_DECODER_THRESHOLD) {\n    return utf8DecodeTD(bytes, inputOffset, byteLength);\n  } else {\n    return utf8DecodeJs(bytes, inputOffset, byteLength);\n  }\n}\n", "/**\n * ExtData is used to handle Extension Types that are not registered to ExtensionCodec.\n */\nexport class ExtData {\n  readonly type: number;\n  readonly data: Uint8Array | ((pos: number) => Uint8Array);\n\n  constructor(type: number, data: Uint8Array | ((pos: number) => Uint8Array)) {\n    this.type = type;\n    this.data = data;\n  }\n}\n", "export class DecodeError extends Error {\n  constructor(message: string) {\n    super(message);\n\n    // fix the prototype chain in a cross-platform way\n    const proto: typeof DecodeError.prototype = Object.create(DecodeError.prototype);\n    Object.setPrototypeOf(this, proto);\n\n    Object.defineProperty(this, \"name\", {\n      configurable: true,\n      enumerable: false,\n      value: DecodeError.name,\n    });\n  }\n}\n", "// Integer Utility\n\nexport const UINT32_MAX = 0xffff_ffff;\n\n// DataView extension to handle int64 / uint64,\n// where the actual range is 53-bits integer (a.k.a. safe integer)\n\nexport function setUint64(view: DataView, offset: number, value: number): void {\n  const high = value / 0x1_0000_0000;\n  const low = value; // high bits are truncated by DataView\n  view.setUint32(offset, high);\n  view.setUint32(offset + 4, low);\n}\n\nexport function setInt64(view: DataView, offset: number, value: number): void {\n  const high = Math.floor(value / 0x1_0000_0000);\n  const low = value; // high bits are truncated by DataView\n  view.setUint32(offset, high);\n  view.setUint32(offset + 4, low);\n}\n\nexport function getInt64(view: DataView, offset: number): number {\n  const high = view.getInt32(offset);\n  const low = view.getUint32(offset + 4);\n  return high * 0x1_0000_0000 + low;\n}\n\nexport function getUint64(view: <PERSON>View, offset: number): number {\n  const high = view.getUint32(offset);\n  const low = view.getUint32(offset + 4);\n  return high * 0x1_0000_0000 + low;\n}\n", "// https://github.com/msgpack/msgpack/blob/master/spec.md#timestamp-extension-type\nimport { DecodeError } from \"./DecodeError.ts\";\nimport { getInt64, setInt64 } from \"./utils/int.ts\";\n\nexport const EXT_TIMESTAMP = -1;\n\nexport type TimeSpec = {\n  sec: number;\n  nsec: number;\n};\n\nconst TIMESTAMP32_MAX_SEC = 0x100000000 - 1; // 32-bit unsigned int\nconst TIMESTAMP64_MAX_SEC = 0x400000000 - 1; // 34-bit unsigned int\n\nexport function encodeTimeSpecToTimestamp({ sec, nsec }: TimeSpec): Uint8Array {\n  if (sec >= 0 && nsec >= 0 && sec <= TIMESTAMP64_MAX_SEC) {\n    // Here sec >= 0 && nsec >= 0\n    if (nsec === 0 && sec <= TIMESTAMP32_MAX_SEC) {\n      // timestamp 32 = { sec32 (unsigned) }\n      const rv = new Uint8Array(4);\n      const view = new DataView(rv.buffer);\n      view.setUint32(0, sec);\n      return rv;\n    } else {\n      // timestamp 64 = { nsec30 (unsigned), sec34 (unsigned) }\n      const secHigh = sec / 0x100000000;\n      const secLow = sec & 0xffffffff;\n      const rv = new Uint8Array(8);\n      const view = new DataView(rv.buffer);\n      // nsec30 | secHigh2\n      view.setUint32(0, (nsec << 2) | (secHigh & 0x3));\n      // secLow32\n      view.setUint32(4, secLow);\n      return rv;\n    }\n  } else {\n    // timestamp 96 = { nsec32 (unsigned), sec64 (signed) }\n    const rv = new Uint8Array(12);\n    const view = new DataView(rv.buffer);\n    view.setUint32(0, nsec);\n    setInt64(view, 4, sec);\n    return rv;\n  }\n}\n\nexport function encodeDateToTimeSpec(date: Date): TimeSpec {\n  const msec = date.getTime();\n  const sec = Math.floor(msec / 1e3);\n  const nsec = (msec - sec * 1e3) * 1e6;\n\n  // Normalizes { sec, nsec } to ensure nsec is unsigned.\n  const nsecInSec = Math.floor(nsec / 1e9);\n  return {\n    sec: sec + nsecInSec,\n    nsec: nsec - nsecInSec * 1e9,\n  };\n}\n\nexport function encodeTimestampExtension(object: unknown): Uint8Array | null {\n  if (object instanceof Date) {\n    const timeSpec = encodeDateToTimeSpec(object);\n    return encodeTimeSpecToTimestamp(timeSpec);\n  } else {\n    return null;\n  }\n}\n\nexport function decodeTimestampToTimeSpec(data: Uint8Array): TimeSpec {\n  const view = new DataView(data.buffer, data.byteOffset, data.byteLength);\n\n  // data may be 32, 64, or 96 bits\n  switch (data.byteLength) {\n    case 4: {\n      // timestamp 32 = { sec32 }\n      const sec = view.getUint32(0);\n      const nsec = 0;\n      return { sec, nsec };\n    }\n    case 8: {\n      // timestamp 64 = { nsec30, sec34 }\n      const nsec30AndSecHigh2 = view.getUint32(0);\n      const secLow32 = view.getUint32(4);\n      const sec = (nsec30AndSecHigh2 & 0x3) * 0x100000000 + secLow32;\n      const nsec = nsec30AndSecHigh2 >>> 2;\n      return { sec, nsec };\n    }\n    case 12: {\n      // timestamp 96 = { nsec32 (unsigned), sec64 (signed) }\n\n      const sec = getInt64(view, 4);\n      const nsec = view.getUint32(0);\n      return { sec, nsec };\n    }\n    default:\n      throw new DecodeError(`Unrecognized data size for timestamp (expected 4, 8, or 12): ${data.length}`);\n  }\n}\n\nexport function decodeTimestampExtension(data: Uint8Array): Date {\n  const timeSpec = decodeTimestampToTimeSpec(data);\n  return new Date(timeSpec.sec * 1e3 + timeSpec.nsec / 1e6);\n}\n\nexport const timestampExtension = {\n  type: EXT_TIMESTAMP,\n  encode: encodeTimestampExtension,\n  decode: decodeTimestampExtension,\n};\n", "// ExtensionCodec to handle MessagePack extensions\n\nimport { ExtData } from \"./ExtData.ts\";\nimport { timestampExtension } from \"./timestamp.ts\";\n\nexport type ExtensionDecoderType<ContextType> = (\n  data: Uint8Array,\n  extensionType: number,\n  context: ContextType,\n) => unknown;\n\nexport type ExtensionEncoderType<ContextType> = (\n  input: unknown,\n  context: ContextType,\n) => Uint8Array | ((dataPos: number) => Uint8Array) | null;\n\n// immutable interface to ExtensionCodec\nexport type ExtensionCodecType<ContextType> = {\n  // eslint-disable-next-line @typescript-eslint/naming-convention\n  __brand?: ContextType;\n  tryToEncode(object: unknown, context: ContextType): ExtData | null;\n  decode(data: Uint8Array, extType: number, context: ContextType): unknown;\n};\n\nexport class ExtensionCodec<ContextType = undefined> implements ExtensionCodecType<ContextType> {\n  public static readonly defaultCodec: ExtensionCodecType<undefined> = new ExtensionCodec();\n\n  // ensures ExtensionCodecType<X> matches ExtensionCodec<X>\n  // this will make type errors a lot more clear\n  // eslint-disable-next-line @typescript-eslint/naming-convention\n  __brand?: ContextType;\n\n  // built-in extensions\n  private readonly builtInEncoders: Array<ExtensionEncoderType<ContextType> | undefined | null> = [];\n  private readonly builtInDecoders: Array<ExtensionDecoderType<ContextType> | undefined | null> = [];\n\n  // custom extensions\n  private readonly encoders: Array<ExtensionEncoderType<ContextType> | undefined | null> = [];\n  private readonly decoders: Array<ExtensionDecoderType<ContextType> | undefined | null> = [];\n\n  public constructor() {\n    this.register(timestampExtension);\n  }\n\n  public register({\n    type,\n    encode,\n    decode,\n  }: {\n    type: number;\n    encode: ExtensionEncoderType<ContextType>;\n    decode: ExtensionDecoderType<ContextType>;\n  }): void {\n    if (type >= 0) {\n      // custom extensions\n      this.encoders[type] = encode;\n      this.decoders[type] = decode;\n    } else {\n      // built-in extensions\n      const index = -1 - type;\n      this.builtInEncoders[index] = encode;\n      this.builtInDecoders[index] = decode;\n    }\n  }\n\n  public tryToEncode(object: unknown, context: ContextType): ExtData | null {\n    // built-in extensions\n    for (let i = 0; i < this.builtInEncoders.length; i++) {\n      const encodeExt = this.builtInEncoders[i];\n      if (encodeExt != null) {\n        const data = encodeExt(object, context);\n        if (data != null) {\n          const type = -1 - i;\n          return new ExtData(type, data);\n        }\n      }\n    }\n\n    // custom extensions\n    for (let i = 0; i < this.encoders.length; i++) {\n      const encodeExt = this.encoders[i];\n      if (encodeExt != null) {\n        const data = encodeExt(object, context);\n        if (data != null) {\n          const type = i;\n          return new ExtData(type, data);\n        }\n      }\n    }\n\n    if (object instanceof ExtData) {\n      // to keep ExtData as is\n      return object;\n    }\n    return null;\n  }\n\n  public decode(data: Uint8Array, type: number, context: ContextType): unknown {\n    const decodeExt = type < 0 ? this.builtInDecoders[-1 - type] : this.decoders[type];\n    if (decodeExt) {\n      return decodeExt(data, type, context);\n    } else {\n      // decode() does not fail, returns ExtData instead.\n      return new ExtData(type, data);\n    }\n  }\n}\n", "function isArrayBufferLike(buffer: unknown): buffer is ArrayBuffer<PERSON>ike {\n  return (\n    buffer instanceof ArrayBuffer || (typeof SharedArrayBuffer !== \"undefined\" && buffer instanceof SharedArrayBuffer)\n  );\n}\n\nexport function ensureUint8Array(\n  buffer: ArrayLike<number> | Uint8Array<ArrayBufferLike> | ArrayBufferView | ArrayBufferLike,\n): Uint8Array<ArrayBufferLike> {\n  if (buffer instanceof Uint8Array) {\n    return buffer;\n  } else if (ArrayBuffer.isView(buffer)) {\n    return new Uint8Array(buffer.buffer, buffer.byteOffset, buffer.byteLength);\n  } else if (isArrayBuffer<PERSON>ike(buffer)) {\n    return new Uint8Array(buffer);\n  } else {\n    // ArrayLike<number>\n    return Uint8Array.from(buffer);\n  }\n}\n", "import { utf8Count, utf8Encode } from \"./utils/utf8.ts\";\nimport { ExtensionCodec } from \"./ExtensionCodec.ts\";\nimport { setInt64, setUint64 } from \"./utils/int.ts\";\nimport { ensureUint8Array } from \"./utils/typedArrays.ts\";\nimport type { ExtData } from \"./ExtData.ts\";\nimport type { ContextOf } from \"./context.ts\";\nimport type { ExtensionCodecType } from \"./ExtensionCodec.ts\";\n\nexport const DEFAULT_MAX_DEPTH = 100;\nexport const DEFAULT_INITIAL_BUFFER_SIZE = 2048;\n\nexport type EncoderOptions<ContextType = undefined> = Partial<\n  Readonly<{\n    extensionCodec: ExtensionCodecType<ContextType>;\n\n    /**\n     * Encodes bigint as Int64 or Uint64 if it's set to true.\n     * {@link forceIntegerToFloat} does not affect bigint.\n     * Depends on ES2020's {@link DataView#setBigInt64} and\n     * {@link DataView#setBigUint64}.\n     *\n     * Defaults to false.\n     */\n    useBigInt64: boolean;\n\n    /**\n     * The maximum depth in nested objects and arrays.\n     *\n     * Defaults to 100.\n     */\n    maxDepth: number;\n\n    /**\n     * The initial size of the internal buffer.\n     *\n     * Defaults to 2048.\n     */\n    initialBufferSize: number;\n\n    /**\n     * If `true`, the keys of an object is sorted. In other words, the encoded\n     * binary is canonical and thus comparable to another encoded binary.\n     *\n     * Defaults to `false`. If enabled, it spends more time in encoding objects.\n     */\n    sortKeys: boolean;\n    /**\n     * If `true`, non-integer numbers are encoded in float32, not in float64 (the default).\n     *\n     * Only use it if precisions don't matter.\n     *\n     * Defaults to `false`.\n     */\n    forceFloat32: boolean;\n\n    /**\n     * If `true`, an object property with `undefined` value are ignored.\n     * e.g. `{ foo: undefined }` will be encoded as `{}`, as `JSON.stringify()` does.\n     *\n     * Defaults to `false`. If enabled, it spends more time in encoding objects.\n     */\n    ignoreUndefined: boolean;\n\n    /**\n     * If `true`, integer numbers are encoded as floating point numbers,\n     * with the `forceFloat32` option taken into account.\n     *\n     * Defaults to `false`.\n     */\n    forceIntegerToFloat: boolean;\n  }>\n> &\n  ContextOf<ContextType>;\n\nexport class Encoder<ContextType = undefined> {\n  private readonly extensionCodec: ExtensionCodecType<ContextType>;\n  private readonly context: ContextType;\n  private readonly useBigInt64: boolean;\n  private readonly maxDepth: number;\n  private readonly initialBufferSize: number;\n  private readonly sortKeys: boolean;\n  private readonly forceFloat32: boolean;\n  private readonly ignoreUndefined: boolean;\n  private readonly forceIntegerToFloat: boolean;\n\n  private pos: number;\n  private view: DataView;\n  private bytes: Uint8Array;\n\n  private entered = false;\n\n  public constructor(options?: EncoderOptions<ContextType>) {\n    this.extensionCodec = options?.extensionCodec ?? (ExtensionCodec.defaultCodec as ExtensionCodecType<ContextType>);\n    this.context = (options as { context: ContextType } | undefined)?.context as ContextType; // needs a type assertion because EncoderOptions has no context property when ContextType is undefined\n\n    this.useBigInt64 = options?.useBigInt64 ?? false;\n    this.maxDepth = options?.maxDepth ?? DEFAULT_MAX_DEPTH;\n    this.initialBufferSize = options?.initialBufferSize ?? DEFAULT_INITIAL_BUFFER_SIZE;\n    this.sortKeys = options?.sortKeys ?? false;\n    this.forceFloat32 = options?.forceFloat32 ?? false;\n    this.ignoreUndefined = options?.ignoreUndefined ?? false;\n    this.forceIntegerToFloat = options?.forceIntegerToFloat ?? false;\n\n    this.pos = 0;\n    this.view = new DataView(new ArrayBuffer(this.initialBufferSize));\n    this.bytes = new Uint8Array(this.view.buffer);\n  }\n\n  private clone() {\n    // Because of slightly special argument `context`,\n    // type assertion is needed.\n    // eslint-disable-next-line @typescript-eslint/no-unsafe-argument\n    return new Encoder<ContextType>({\n      extensionCodec: this.extensionCodec,\n      context: this.context,\n      useBigInt64: this.useBigInt64,\n      maxDepth: this.maxDepth,\n      initialBufferSize: this.initialBufferSize,\n      sortKeys: this.sortKeys,\n      forceFloat32: this.forceFloat32,\n      ignoreUndefined: this.ignoreUndefined,\n      forceIntegerToFloat: this.forceIntegerToFloat,\n    } as any);\n  }\n\n  private reinitializeState() {\n    this.pos = 0;\n  }\n\n  /**\n   * This is almost equivalent to {@link Encoder#encode}, but it returns an reference of the encoder's internal buffer and thus much faster than {@link Encoder#encode}.\n   *\n   * @returns Encodes the object and returns a shared reference the encoder's internal buffer.\n   */\n  public encodeSharedRef(object: unknown): Uint8Array {\n    if (this.entered) {\n      const instance = this.clone();\n      return instance.encodeSharedRef(object);\n    }\n\n    try {\n      this.entered = true;\n\n      this.reinitializeState();\n      this.doEncode(object, 1);\n      return this.bytes.subarray(0, this.pos);\n    } finally {\n      this.entered = false;\n    }\n  }\n\n  /**\n   * @returns Encodes the object and returns a copy of the encoder's internal buffer.\n   */\n  public encode(object: unknown): Uint8Array {\n    if (this.entered) {\n      const instance = this.clone();\n      return instance.encode(object);\n    }\n\n    try {\n      this.entered = true;\n\n      this.reinitializeState();\n      this.doEncode(object, 1);\n      return this.bytes.slice(0, this.pos);\n    } finally {\n      this.entered = false;\n    }\n  }\n\n  private doEncode(object: unknown, depth: number): void {\n    if (depth > this.maxDepth) {\n      throw new Error(`Too deep objects in depth ${depth}`);\n    }\n\n    if (object == null) {\n      this.encodeNil();\n    } else if (typeof object === \"boolean\") {\n      this.encodeBoolean(object);\n    } else if (typeof object === \"number\") {\n      if (!this.forceIntegerToFloat) {\n        this.encodeNumber(object);\n      } else {\n        this.encodeNumberAsFloat(object);\n      }\n    } else if (typeof object === \"string\") {\n      this.encodeString(object);\n    } else if (this.useBigInt64 && typeof object === \"bigint\") {\n      this.encodeBigInt64(object);\n    } else {\n      this.encodeObject(object, depth);\n    }\n  }\n\n  private ensureBufferSizeToWrite(sizeToWrite: number) {\n    const requiredSize = this.pos + sizeToWrite;\n\n    if (this.view.byteLength < requiredSize) {\n      this.resizeBuffer(requiredSize * 2);\n    }\n  }\n\n  private resizeBuffer(newSize: number) {\n    const newBuffer = new ArrayBuffer(newSize);\n    const newBytes = new Uint8Array(newBuffer);\n    const newView = new DataView(newBuffer);\n\n    newBytes.set(this.bytes);\n\n    this.view = newView;\n    this.bytes = newBytes;\n  }\n\n  private encodeNil() {\n    this.writeU8(0xc0);\n  }\n\n  private encodeBoolean(object: boolean) {\n    if (object === false) {\n      this.writeU8(0xc2);\n    } else {\n      this.writeU8(0xc3);\n    }\n  }\n\n  private encodeNumber(object: number): void {\n    if (!this.forceIntegerToFloat && Number.isSafeInteger(object)) {\n      if (object >= 0) {\n        if (object < 0x80) {\n          // positive fixint\n          this.writeU8(object);\n        } else if (object < 0x100) {\n          // uint 8\n          this.writeU8(0xcc);\n          this.writeU8(object);\n        } else if (object < 0x10000) {\n          // uint 16\n          this.writeU8(0xcd);\n          this.writeU16(object);\n        } else if (object < 0x100000000) {\n          // uint 32\n          this.writeU8(0xce);\n          this.writeU32(object);\n        } else if (!this.useBigInt64) {\n          // uint 64\n          this.writeU8(0xcf);\n          this.writeU64(object);\n        } else {\n          this.encodeNumberAsFloat(object);\n        }\n      } else {\n        if (object >= -0x20) {\n          // negative fixint\n          this.writeU8(0xe0 | (object + 0x20));\n        } else if (object >= -0x80) {\n          // int 8\n          this.writeU8(0xd0);\n          this.writeI8(object);\n        } else if (object >= -0x8000) {\n          // int 16\n          this.writeU8(0xd1);\n          this.writeI16(object);\n        } else if (object >= -0x80000000) {\n          // int 32\n          this.writeU8(0xd2);\n          this.writeI32(object);\n        } else if (!this.useBigInt64) {\n          // int 64\n          this.writeU8(0xd3);\n          this.writeI64(object);\n        } else {\n          this.encodeNumberAsFloat(object);\n        }\n      }\n    } else {\n      this.encodeNumberAsFloat(object);\n    }\n  }\n\n  private encodeNumberAsFloat(object: number): void {\n    if (this.forceFloat32) {\n      // float 32\n      this.writeU8(0xca);\n      this.writeF32(object);\n    } else {\n      // float 64\n      this.writeU8(0xcb);\n      this.writeF64(object);\n    }\n  }\n\n  private encodeBigInt64(object: bigint): void {\n    if (object >= BigInt(0)) {\n      // uint 64\n      this.writeU8(0xcf);\n      this.writeBigUint64(object);\n    } else {\n      // int 64\n      this.writeU8(0xd3);\n      this.writeBigInt64(object);\n    }\n  }\n\n  private writeStringHeader(byteLength: number) {\n    if (byteLength < 32) {\n      // fixstr\n      this.writeU8(0xa0 + byteLength);\n    } else if (byteLength < 0x100) {\n      // str 8\n      this.writeU8(0xd9);\n      this.writeU8(byteLength);\n    } else if (byteLength < 0x10000) {\n      // str 16\n      this.writeU8(0xda);\n      this.writeU16(byteLength);\n    } else if (byteLength < 0x100000000) {\n      // str 32\n      this.writeU8(0xdb);\n      this.writeU32(byteLength);\n    } else {\n      throw new Error(`Too long string: ${byteLength} bytes in UTF-8`);\n    }\n  }\n\n  private encodeString(object: string) {\n    const maxHeaderSize = 1 + 4;\n\n    const byteLength = utf8Count(object);\n    this.ensureBufferSizeToWrite(maxHeaderSize + byteLength);\n    this.writeStringHeader(byteLength);\n    utf8Encode(object, this.bytes, this.pos);\n    this.pos += byteLength;\n  }\n\n  private encodeObject(object: unknown, depth: number) {\n    // try to encode objects with custom codec first of non-primitives\n    const ext = this.extensionCodec.tryToEncode(object, this.context);\n    if (ext != null) {\n      this.encodeExtension(ext);\n    } else if (Array.isArray(object)) {\n      this.encodeArray(object, depth);\n    } else if (ArrayBuffer.isView(object)) {\n      this.encodeBinary(object);\n    } else if (typeof object === \"object\") {\n      this.encodeMap(object as Record<string, unknown>, depth);\n    } else {\n      // symbol, function and other special object come here unless extensionCodec handles them.\n      throw new Error(`Unrecognized object: ${Object.prototype.toString.apply(object)}`);\n    }\n  }\n\n  private encodeBinary(object: ArrayBufferView) {\n    const size = object.byteLength;\n    if (size < 0x100) {\n      // bin 8\n      this.writeU8(0xc4);\n      this.writeU8(size);\n    } else if (size < 0x10000) {\n      // bin 16\n      this.writeU8(0xc5);\n      this.writeU16(size);\n    } else if (size < 0x100000000) {\n      // bin 32\n      this.writeU8(0xc6);\n      this.writeU32(size);\n    } else {\n      throw new Error(`Too large binary: ${size}`);\n    }\n    const bytes = ensureUint8Array(object);\n    this.writeU8a(bytes);\n  }\n\n  private encodeArray(object: Array<unknown>, depth: number) {\n    const size = object.length;\n    if (size < 16) {\n      // fixarray\n      this.writeU8(0x90 + size);\n    } else if (size < 0x10000) {\n      // array 16\n      this.writeU8(0xdc);\n      this.writeU16(size);\n    } else if (size < 0x100000000) {\n      // array 32\n      this.writeU8(0xdd);\n      this.writeU32(size);\n    } else {\n      throw new Error(`Too large array: ${size}`);\n    }\n    for (const item of object) {\n      this.doEncode(item, depth + 1);\n    }\n  }\n\n  private countWithoutUndefined(object: Record<string, unknown>, keys: ReadonlyArray<string>): number {\n    let count = 0;\n\n    for (const key of keys) {\n      if (object[key] !== undefined) {\n        count++;\n      }\n    }\n\n    return count;\n  }\n\n  private encodeMap(object: Record<string, unknown>, depth: number) {\n    const keys = Object.keys(object);\n    if (this.sortKeys) {\n      keys.sort();\n    }\n\n    const size = this.ignoreUndefined ? this.countWithoutUndefined(object, keys) : keys.length;\n\n    if (size < 16) {\n      // fixmap\n      this.writeU8(0x80 + size);\n    } else if (size < 0x10000) {\n      // map 16\n      this.writeU8(0xde);\n      this.writeU16(size);\n    } else if (size < 0x100000000) {\n      // map 32\n      this.writeU8(0xdf);\n      this.writeU32(size);\n    } else {\n      throw new Error(`Too large map object: ${size}`);\n    }\n\n    for (const key of keys) {\n      const value = object[key];\n\n      if (!(this.ignoreUndefined && value === undefined)) {\n        this.encodeString(key);\n        this.doEncode(value, depth + 1);\n      }\n    }\n  }\n\n  private encodeExtension(ext: ExtData) {\n    if (typeof ext.data === \"function\") {\n      const data = ext.data(this.pos + 6);\n      const size = data.length;\n\n      if (size >= 0x100000000) {\n        throw new Error(`Too large extension object: ${size}`);\n      }\n\n      this.writeU8(0xc9);\n      this.writeU32(size);\n      this.writeI8(ext.type);\n      this.writeU8a(data);\n      return;\n    }\n\n    const size = ext.data.length;\n    if (size === 1) {\n      // fixext 1\n      this.writeU8(0xd4);\n    } else if (size === 2) {\n      // fixext 2\n      this.writeU8(0xd5);\n    } else if (size === 4) {\n      // fixext 4\n      this.writeU8(0xd6);\n    } else if (size === 8) {\n      // fixext 8\n      this.writeU8(0xd7);\n    } else if (size === 16) {\n      // fixext 16\n      this.writeU8(0xd8);\n    } else if (size < 0x100) {\n      // ext 8\n      this.writeU8(0xc7);\n      this.writeU8(size);\n    } else if (size < 0x10000) {\n      // ext 16\n      this.writeU8(0xc8);\n      this.writeU16(size);\n    } else if (size < 0x100000000) {\n      // ext 32\n      this.writeU8(0xc9);\n      this.writeU32(size);\n    } else {\n      throw new Error(`Too large extension object: ${size}`);\n    }\n    this.writeI8(ext.type);\n    this.writeU8a(ext.data);\n  }\n\n  private writeU8(value: number) {\n    this.ensureBufferSizeToWrite(1);\n\n    this.view.setUint8(this.pos, value);\n    this.pos++;\n  }\n\n  private writeU8a(values: ArrayLike<number>) {\n    const size = values.length;\n    this.ensureBufferSizeToWrite(size);\n\n    this.bytes.set(values, this.pos);\n    this.pos += size;\n  }\n\n  private writeI8(value: number) {\n    this.ensureBufferSizeToWrite(1);\n\n    this.view.setInt8(this.pos, value);\n    this.pos++;\n  }\n\n  private writeU16(value: number) {\n    this.ensureBufferSizeToWrite(2);\n\n    this.view.setUint16(this.pos, value);\n    this.pos += 2;\n  }\n\n  private writeI16(value: number) {\n    this.ensureBufferSizeToWrite(2);\n\n    this.view.setInt16(this.pos, value);\n    this.pos += 2;\n  }\n\n  private writeU32(value: number) {\n    this.ensureBufferSizeToWrite(4);\n\n    this.view.setUint32(this.pos, value);\n    this.pos += 4;\n  }\n\n  private writeI32(value: number) {\n    this.ensureBufferSizeToWrite(4);\n\n    this.view.setInt32(this.pos, value);\n    this.pos += 4;\n  }\n\n  private writeF32(value: number) {\n    this.ensureBufferSizeToWrite(4);\n\n    this.view.setFloat32(this.pos, value);\n    this.pos += 4;\n  }\n\n  private writeF64(value: number) {\n    this.ensureBufferSizeToWrite(8);\n\n    this.view.setFloat64(this.pos, value);\n    this.pos += 8;\n  }\n\n  private writeU64(value: number) {\n    this.ensureBufferSizeToWrite(8);\n\n    setUint64(this.view, this.pos, value);\n    this.pos += 8;\n  }\n\n  private writeI64(value: number) {\n    this.ensureBufferSizeToWrite(8);\n\n    setInt64(this.view, this.pos, value);\n    this.pos += 8;\n  }\n\n  private writeBigUint64(value: bigint) {\n    this.ensureBufferSizeToWrite(8);\n\n    this.view.setBigUint64(this.pos, value);\n    this.pos += 8;\n  }\n\n  private writeBigInt64(value: bigint) {\n    this.ensureBufferSizeToWrite(8);\n\n    this.view.setBigInt64(this.pos, value);\n    this.pos += 8;\n  }\n}\n", "import { Encoder } from \"./Encoder.ts\";\nimport type { EncoderOptions } from \"./Encoder.ts\";\nimport type { SplitUndefined } from \"./context.ts\";\n\n/**\n * It encodes `value` in the MessagePack format and\n * returns a byte buffer.\n *\n * The returned buffer is a slice of a larger `ArrayBuffer`, so you have to use its `#byteOffset` and `#byteLength` in order to convert it to another typed arrays including NodeJS `Buffer`.\n */\nexport function encode<ContextType = undefined>(\n  value: unknown,\n  options?: EncoderOptions<SplitUndefined<ContextType>>,\n): Uint8Array {\n  const encoder = new Encoder(options);\n  return encoder.encodeSharedRef(value);\n}\n", "export function prettyByte(byte: number): string {\n  return `${byte < 0 ? \"-\" : \"\"}0x${Math.abs(byte).toString(16).padStart(2, \"0\")}`;\n}\n", "import { utf8DecodeJs } from \"./utils/utf8.ts\";\n\nconst DEFAULT_MAX_KEY_LENGTH = 16;\nconst DEFAULT_MAX_LENGTH_PER_KEY = 16;\n\nexport interface KeyDecoder {\n  canBeCached(byteLength: number): boolean;\n  decode(bytes: Uint8Array, inputOffset: number, byteLength: number): string;\n}\ninterface KeyCacheRecord {\n  readonly bytes: Uint8Array;\n  readonly str: string;\n}\n\nexport class CachedKeyDecoder implements KeyDecoder {\n  hit = 0;\n  miss = 0;\n  private readonly caches: Array<Array<KeyCacheRecord>>;\n  readonly maxKeyLength: number;\n  readonly maxLengthPerKey: number;\n\n  constructor(maxKeyLength = DEFAULT_MAX_KEY_LENGTH, maxLengthPerKey = DEFAULT_MAX_LENGTH_PER_KEY) {\n    this.maxKeyLength = maxKeyLength;\n    this.maxLengthPerKey = maxLengthPerKey;\n\n    // avoid `new Array(N)`, which makes a sparse array,\n    // because a sparse array is typically slower than a non-sparse array.\n    this.caches = [];\n    for (let i = 0; i < this.maxKeyLength; i++) {\n      this.caches.push([]);\n    }\n  }\n\n  public canBeCached(byteLength: number): boolean {\n    return byteLength > 0 && byteLength <= this.maxKeyLength;\n  }\n\n  private find(bytes: Uint8Array, inputOffset: number, byteLength: number): string | null {\n    const records = this.caches[byteLength - 1]!;\n\n    FIND_CHUNK: for (const record of records) {\n      const recordBytes = record.bytes;\n\n      for (let j = 0; j < byteLength; j++) {\n        if (recordBytes[j] !== bytes[inputOffset + j]) {\n          continue FIND_CHUNK;\n        }\n      }\n      return record.str;\n    }\n    return null;\n  }\n\n  private store(bytes: Uint8Array, value: string) {\n    const records = this.caches[bytes.length - 1]!;\n    const record: KeyCacheRecord = { bytes, str: value };\n\n    if (records.length >= this.maxLengthPerKey) {\n      // `records` are full!\n      // Set `record` to an arbitrary position.\n      records[(Math.random() * records.length) | 0] = record;\n    } else {\n      records.push(record);\n    }\n  }\n\n  public decode(bytes: Uint8Array, inputOffset: number, byteLength: number): string {\n    const cachedValue = this.find(bytes, inputOffset, byteLength);\n    if (cachedValue != null) {\n      this.hit++;\n      return cachedValue;\n    }\n    this.miss++;\n\n    const str = utf8DecodeJs(bytes, inputOffset, byteLength);\n    // Ensure to copy a slice of bytes because the bytes may be a NodeJS Buffer and Buffer#slice() returns a reference to its internal ArrayBuffer.\n    const slicedCopyOfBytes = Uint8Array.prototype.slice.call(bytes, inputOffset, inputOffset + byteLength);\n    this.store(slicedCopyOfBytes, str);\n    return str;\n  }\n}\n", "import { prettyByte } from \"./utils/prettyByte.ts\";\nimport { ExtensionCodec } from \"./ExtensionCodec.ts\";\nimport { getInt64, getUint64, UINT32_MAX } from \"./utils/int.ts\";\nimport { utf8Decode } from \"./utils/utf8.ts\";\nimport { ensureUint8Array } from \"./utils/typedArrays.ts\";\nimport { CachedKeyDecoder } from \"./CachedKeyDecoder.ts\";\nimport { DecodeError } from \"./DecodeError.ts\";\nimport type { ContextOf } from \"./context.ts\";\nimport type { ExtensionCodecType } from \"./ExtensionCodec.ts\";\nimport type { KeyDecoder } from \"./CachedKeyDecoder.ts\";\n\nexport type DecoderOptions<ContextType = undefined> = Readonly<\n  Partial<{\n    extensionCodec: ExtensionCodecType<ContextType>;\n\n    /**\n     * Decodes Int64 and Uint64 as bigint if it's set to true.\n     * Depends on ES2020's {@link DataView#getBigInt64} and\n     * {@link DataView#getBigUint64}.\n     *\n     * Defaults to false.\n     */\n    useBigInt64: boolean;\n\n    /**\n     * By default, string values will be decoded as UTF-8 strings. However, if this option is true,\n     * string values will be returned as Uint8Arrays without additional decoding.\n     *\n     * This is useful if the strings may contain invalid UTF-8 sequences.\n     *\n     * Note that this option only applies to string values, not map keys. Additionally, when\n     * enabled, raw string length is limited by the maxBinLength option.\n     */\n    rawStrings: boolean;\n\n    /**\n     * Maximum string length.\n     *\n     * Defaults to 4_294_967_295 (UINT32_MAX).\n     */\n    maxStrLength: number;\n    /**\n     * Maximum binary length.\n     *\n     * Defaults to 4_294_967_295 (UINT32_MAX).\n     */\n    maxBinLength: number;\n    /**\n     * Maximum array length.\n     *\n     * Defaults to 4_294_967_295 (UINT32_MAX).\n     */\n    maxArrayLength: number;\n    /**\n     * Maximum map length.\n     *\n     * Defaults to 4_294_967_295 (UINT32_MAX).\n     */\n    maxMapLength: number;\n    /**\n     * Maximum extension length.\n     *\n     * Defaults to 4_294_967_295 (UINT32_MAX).\n     */\n    maxExtLength: number;\n\n    /**\n     * An object key decoder. Defaults to the shared instance of {@link CachedKeyDecoder}.\n     * `null` is a special value to disable the use of the key decoder at all.\n     */\n    keyDecoder: KeyDecoder | null;\n\n    /**\n     * A function to convert decoded map key to a valid JS key type.\n     *\n     * Defaults to a function that throws an error if the key is not a string or a number.\n     */\n    mapKeyConverter: (key: unknown) => MapKeyType;\n  }>\n> &\n  ContextOf<ContextType>;\n\nconst STATE_ARRAY = \"array\";\nconst STATE_MAP_KEY = \"map_key\";\nconst STATE_MAP_VALUE = \"map_value\";\n\ntype MapKeyType = string | number;\n\nconst mapKeyConverter = (key: unknown): MapKeyType => {\n  if (typeof key === \"string\" || typeof key === \"number\") {\n    return key;\n  }\n  throw new DecodeError(\"The type of key must be string or number but \" + typeof key);\n};\n\ntype StackMapState = {\n  type: typeof STATE_MAP_KEY | typeof STATE_MAP_VALUE;\n  size: number;\n  key: MapKeyType | null;\n  readCount: number;\n  map: Record<string, unknown>;\n};\n\ntype StackArrayState = {\n  type: typeof STATE_ARRAY;\n  size: number;\n  array: Array<unknown>;\n  position: number;\n};\n\nclass StackPool {\n  private readonly stack: Array<StackState> = [];\n  private stackHeadPosition = -1;\n\n  public get length(): number {\n    return this.stackHeadPosition + 1;\n  }\n\n  public top(): StackState | undefined {\n    return this.stack[this.stackHeadPosition];\n  }\n\n  public pushArrayState(size: number) {\n    const state = this.getUninitializedStateFromPool() as StackArrayState;\n\n    state.type = STATE_ARRAY;\n    state.position = 0;\n    state.size = size;\n    state.array = new Array(size);\n  }\n\n  public pushMapState(size: number) {\n    const state = this.getUninitializedStateFromPool() as StackMapState;\n\n    state.type = STATE_MAP_KEY;\n    state.readCount = 0;\n    state.size = size;\n    state.map = {};\n  }\n\n  private getUninitializedStateFromPool() {\n    this.stackHeadPosition++;\n\n    if (this.stackHeadPosition === this.stack.length) {\n      const partialState: Partial<StackState> = {\n        type: undefined,\n        size: 0,\n        array: undefined,\n        position: 0,\n        readCount: 0,\n        map: undefined,\n        key: null,\n      };\n\n      this.stack.push(partialState as StackState);\n    }\n\n    return this.stack[this.stackHeadPosition];\n  }\n\n  public release(state: StackState): void {\n    const topStackState = this.stack[this.stackHeadPosition];\n\n    if (topStackState !== state) {\n      throw new Error(\"Invalid stack state. Released state is not on top of the stack.\");\n    }\n\n    if (state.type === STATE_ARRAY) {\n      const partialState = state as Partial<StackArrayState>;\n      partialState.size = 0;\n      partialState.array = undefined;\n      partialState.position = 0;\n      partialState.type = undefined;\n    }\n\n    if (state.type === STATE_MAP_KEY || state.type === STATE_MAP_VALUE) {\n      const partialState = state as Partial<StackMapState>;\n      partialState.size = 0;\n      partialState.map = undefined;\n      partialState.readCount = 0;\n      partialState.type = undefined;\n    }\n\n    this.stackHeadPosition--;\n  }\n\n  public reset(): void {\n    this.stack.length = 0;\n    this.stackHeadPosition = -1;\n  }\n}\n\ntype StackState = StackArrayState | StackMapState;\n\nconst HEAD_BYTE_REQUIRED = -1;\n\nconst EMPTY_VIEW = new DataView<ArrayBufferLike>(new ArrayBuffer(0));\nconst EMPTY_BYTES = new Uint8Array<ArrayBufferLike>(EMPTY_VIEW.buffer);\n\ntry {\n  // IE11: The spec says it should throw RangeError,\n  // IE11: but in IE11 it throws TypeError.\n  EMPTY_VIEW.getInt8(0);\n} catch (e) {\n  if (!(e instanceof RangeError)) {\n    throw new Error(\n      \"This module is not supported in the current JavaScript engine because DataView does not throw RangeError on out-of-bounds access\",\n    );\n  }\n}\n\nconst MORE_DATA = new RangeError(\"Insufficient data\");\n\nconst sharedCachedKeyDecoder = new CachedKeyDecoder();\n\nexport class Decoder<ContextType = undefined> {\n  private readonly extensionCodec: ExtensionCodecType<ContextType>;\n  private readonly context: ContextType;\n  private readonly useBigInt64: boolean;\n  private readonly rawStrings: boolean;\n  private readonly maxStrLength: number;\n  private readonly maxBinLength: number;\n  private readonly maxArrayLength: number;\n  private readonly maxMapLength: number;\n  private readonly maxExtLength: number;\n  private readonly keyDecoder: KeyDecoder | null;\n  private readonly mapKeyConverter: (key: unknown) => MapKeyType;\n\n  private totalPos = 0;\n  private pos = 0;\n\n  private view = EMPTY_VIEW;\n  private bytes = EMPTY_BYTES;\n  private headByte = HEAD_BYTE_REQUIRED;\n  private readonly stack = new StackPool();\n\n  private entered = false;\n\n  public constructor(options?: DecoderOptions<ContextType>) {\n    this.extensionCodec = options?.extensionCodec ?? (ExtensionCodec.defaultCodec as ExtensionCodecType<ContextType>);\n    this.context = (options as { context: ContextType } | undefined)?.context as ContextType; // needs a type assertion because EncoderOptions has no context property when ContextType is undefined\n\n    this.useBigInt64 = options?.useBigInt64 ?? false;\n    this.rawStrings = options?.rawStrings ?? false;\n    this.maxStrLength = options?.maxStrLength ?? UINT32_MAX;\n    this.maxBinLength = options?.maxBinLength ?? UINT32_MAX;\n    this.maxArrayLength = options?.maxArrayLength ?? UINT32_MAX;\n    this.maxMapLength = options?.maxMapLength ?? UINT32_MAX;\n    this.maxExtLength = options?.maxExtLength ?? UINT32_MAX;\n    this.keyDecoder = options?.keyDecoder !== undefined ? options.keyDecoder : sharedCachedKeyDecoder;\n    this.mapKeyConverter = options?.mapKeyConverter ?? mapKeyConverter;\n  }\n\n  private clone(): Decoder<ContextType> {\n    // eslint-disable-next-line @typescript-eslint/no-unsafe-argument\n    return new Decoder({\n      extensionCodec: this.extensionCodec,\n      context: this.context,\n      useBigInt64: this.useBigInt64,\n      rawStrings: this.rawStrings,\n      maxStrLength: this.maxStrLength,\n      maxBinLength: this.maxBinLength,\n      maxArrayLength: this.maxArrayLength,\n      maxMapLength: this.maxMapLength,\n      maxExtLength: this.maxExtLength,\n      keyDecoder: this.keyDecoder,\n    } as any);\n  }\n\n  private reinitializeState() {\n    this.totalPos = 0;\n    this.headByte = HEAD_BYTE_REQUIRED;\n    this.stack.reset();\n\n    // view, bytes, and pos will be re-initialized in setBuffer()\n  }\n\n  private setBuffer(buffer: ArrayLike<number> | ArrayBufferView | ArrayBufferLike): void {\n    const bytes = ensureUint8Array(buffer);\n    this.bytes = bytes;\n    this.view = new DataView(bytes.buffer, bytes.byteOffset, bytes.byteLength);\n    this.pos = 0;\n  }\n\n  private appendBuffer(buffer: ArrayLike<number> | ArrayBufferView | ArrayBufferLike): void {\n    if (this.headByte === HEAD_BYTE_REQUIRED && !this.hasRemaining(1)) {\n      this.setBuffer(buffer);\n    } else {\n      const remainingData = this.bytes.subarray(this.pos);\n      const newData = ensureUint8Array(buffer);\n\n      // concat remainingData + newData\n      const newBuffer = new Uint8Array(remainingData.length + newData.length);\n      newBuffer.set(remainingData);\n      newBuffer.set(newData, remainingData.length);\n      this.setBuffer(newBuffer);\n    }\n  }\n\n  private hasRemaining(size: number) {\n    return this.view.byteLength - this.pos >= size;\n  }\n\n  private createExtraByteError(posToShow: number): Error {\n    const { view, pos } = this;\n    return new RangeError(`Extra ${view.byteLength - pos} of ${view.byteLength} byte(s) found at buffer[${posToShow}]`);\n  }\n\n  /**\n   * @throws {@link DecodeError}\n   * @throws {@link RangeError}\n   */\n  public decode(buffer: ArrayLike<number> | ArrayBufferView | ArrayBufferLike): unknown {\n    if (this.entered) {\n      const instance = this.clone();\n      return instance.decode(buffer);\n    }\n\n    try {\n      this.entered = true;\n\n      this.reinitializeState();\n      this.setBuffer(buffer);\n\n      const object = this.doDecodeSync();\n      if (this.hasRemaining(1)) {\n        throw this.createExtraByteError(this.pos);\n      }\n      return object;\n    } finally {\n      this.entered = false;\n    }\n  }\n\n  public *decodeMulti(buffer: ArrayLike<number> | ArrayBufferView | ArrayBufferLike): Generator<unknown, void, unknown> {\n    if (this.entered) {\n      const instance = this.clone();\n      yield* instance.decodeMulti(buffer);\n      return;\n    }\n\n    try {\n      this.entered = true;\n\n      this.reinitializeState();\n      this.setBuffer(buffer);\n\n      while (this.hasRemaining(1)) {\n        yield this.doDecodeSync();\n      }\n    } finally {\n      this.entered = false;\n    }\n  }\n\n  public async decodeAsync(stream: AsyncIterable<ArrayLike<number> | ArrayBufferView | ArrayBufferLike>): Promise<unknown> {\n    if (this.entered) {\n      const instance = this.clone();\n      return instance.decodeAsync(stream);\n    }\n\n    try {\n      this.entered = true;\n\n      let decoded = false;\n      let object: unknown;\n      for await (const buffer of stream) {\n        if (decoded) {\n          this.entered = false;\n          throw this.createExtraByteError(this.totalPos);\n        }\n\n        this.appendBuffer(buffer);\n\n        try {\n          object = this.doDecodeSync();\n          decoded = true;\n        } catch (e) {\n          if (!(e instanceof RangeError)) {\n            throw e; // rethrow\n          }\n          // fallthrough\n        }\n        this.totalPos += this.pos;\n      }\n\n      if (decoded) {\n        if (this.hasRemaining(1)) {\n          throw this.createExtraByteError(this.totalPos);\n        }\n        return object;\n      }\n\n      const { headByte, pos, totalPos } = this;\n      throw new RangeError(\n        `Insufficient data in parsing ${prettyByte(headByte)} at ${totalPos} (${pos} in the current buffer)`,\n      );\n    } finally {\n      this.entered = false;\n    }\n  }\n\n  public decodeArrayStream(\n    stream: AsyncIterable<ArrayLike<number> | ArrayBufferView | ArrayBufferLike>,\n  ): AsyncGenerator<unknown, void, unknown> {\n    return this.decodeMultiAsync(stream, true);\n  }\n\n  public decodeStream(stream: AsyncIterable<ArrayLike<number> | ArrayBufferView | ArrayBufferLike>): AsyncGenerator<unknown, void, unknown> {\n    return this.decodeMultiAsync(stream, false);\n  }\n\n  private async *decodeMultiAsync(stream: AsyncIterable<ArrayLike<number> | ArrayBufferView | ArrayBufferLike>, isArray: boolean): AsyncGenerator<unknown, void, unknown> {\n    if (this.entered) {\n      const instance = this.clone();\n      yield* instance.decodeMultiAsync(stream, isArray);\n      return;\n    }\n\n    try {\n      this.entered = true;\n\n      let isArrayHeaderRequired = isArray;\n      let arrayItemsLeft = -1;\n\n      for await (const buffer of stream) {\n        if (isArray && arrayItemsLeft === 0) {\n          throw this.createExtraByteError(this.totalPos);\n        }\n\n        this.appendBuffer(buffer);\n\n        if (isArrayHeaderRequired) {\n          arrayItemsLeft = this.readArraySize();\n          isArrayHeaderRequired = false;\n          this.complete();\n        }\n\n        try {\n          while (true) {\n            yield this.doDecodeSync();\n            if (--arrayItemsLeft === 0) {\n              break;\n            }\n          }\n        } catch (e) {\n          if (!(e instanceof RangeError)) {\n            throw e; // rethrow\n          }\n          // fallthrough\n        }\n        this.totalPos += this.pos;\n      }\n    } finally {\n      this.entered = false;\n    }\n  }\n\n  private doDecodeSync(): unknown {\n    DECODE: while (true) {\n      const headByte = this.readHeadByte();\n      let object: unknown;\n\n      if (headByte >= 0xe0) {\n        // negative fixint (111x xxxx) 0xe0 - 0xff\n        object = headByte - 0x100;\n      } else if (headByte < 0xc0) {\n        if (headByte < 0x80) {\n          // positive fixint (0xxx xxxx) 0x00 - 0x7f\n          object = headByte;\n        } else if (headByte < 0x90) {\n          // fixmap (1000 xxxx) 0x80 - 0x8f\n          const size = headByte - 0x80;\n          if (size !== 0) {\n            this.pushMapState(size);\n            this.complete();\n            continue DECODE;\n          } else {\n            object = {};\n          }\n        } else if (headByte < 0xa0) {\n          // fixarray (1001 xxxx) 0x90 - 0x9f\n          const size = headByte - 0x90;\n          if (size !== 0) {\n            this.pushArrayState(size);\n            this.complete();\n            continue DECODE;\n          } else {\n            object = [];\n          }\n        } else {\n          // fixstr (101x xxxx) 0xa0 - 0xbf\n          const byteLength = headByte - 0xa0;\n          object = this.decodeString(byteLength, 0);\n        }\n      } else if (headByte === 0xc0) {\n        // nil\n        object = null;\n      } else if (headByte === 0xc2) {\n        // false\n        object = false;\n      } else if (headByte === 0xc3) {\n        // true\n        object = true;\n      } else if (headByte === 0xca) {\n        // float 32\n        object = this.readF32();\n      } else if (headByte === 0xcb) {\n        // float 64\n        object = this.readF64();\n      } else if (headByte === 0xcc) {\n        // uint 8\n        object = this.readU8();\n      } else if (headByte === 0xcd) {\n        // uint 16\n        object = this.readU16();\n      } else if (headByte === 0xce) {\n        // uint 32\n        object = this.readU32();\n      } else if (headByte === 0xcf) {\n        // uint 64\n        if (this.useBigInt64) {\n          object = this.readU64AsBigInt();\n        } else {\n          object = this.readU64();\n        }\n      } else if (headByte === 0xd0) {\n        // int 8\n        object = this.readI8();\n      } else if (headByte === 0xd1) {\n        // int 16\n        object = this.readI16();\n      } else if (headByte === 0xd2) {\n        // int 32\n        object = this.readI32();\n      } else if (headByte === 0xd3) {\n        // int 64\n        if (this.useBigInt64) {\n          object = this.readI64AsBigInt();\n        } else {\n          object = this.readI64();\n        }\n      } else if (headByte === 0xd9) {\n        // str 8\n        const byteLength = this.lookU8();\n        object = this.decodeString(byteLength, 1);\n      } else if (headByte === 0xda) {\n        // str 16\n        const byteLength = this.lookU16();\n        object = this.decodeString(byteLength, 2);\n      } else if (headByte === 0xdb) {\n        // str 32\n        const byteLength = this.lookU32();\n        object = this.decodeString(byteLength, 4);\n      } else if (headByte === 0xdc) {\n        // array 16\n        const size = this.readU16();\n        if (size !== 0) {\n          this.pushArrayState(size);\n          this.complete();\n          continue DECODE;\n        } else {\n          object = [];\n        }\n      } else if (headByte === 0xdd) {\n        // array 32\n        const size = this.readU32();\n        if (size !== 0) {\n          this.pushArrayState(size);\n          this.complete();\n          continue DECODE;\n        } else {\n          object = [];\n        }\n      } else if (headByte === 0xde) {\n        // map 16\n        const size = this.readU16();\n        if (size !== 0) {\n          this.pushMapState(size);\n          this.complete();\n          continue DECODE;\n        } else {\n          object = {};\n        }\n      } else if (headByte === 0xdf) {\n        // map 32\n        const size = this.readU32();\n        if (size !== 0) {\n          this.pushMapState(size);\n          this.complete();\n          continue DECODE;\n        } else {\n          object = {};\n        }\n      } else if (headByte === 0xc4) {\n        // bin 8\n        const size = this.lookU8();\n        object = this.decodeBinary(size, 1);\n      } else if (headByte === 0xc5) {\n        // bin 16\n        const size = this.lookU16();\n        object = this.decodeBinary(size, 2);\n      } else if (headByte === 0xc6) {\n        // bin 32\n        const size = this.lookU32();\n        object = this.decodeBinary(size, 4);\n      } else if (headByte === 0xd4) {\n        // fixext 1\n        object = this.decodeExtension(1, 0);\n      } else if (headByte === 0xd5) {\n        // fixext 2\n        object = this.decodeExtension(2, 0);\n      } else if (headByte === 0xd6) {\n        // fixext 4\n        object = this.decodeExtension(4, 0);\n      } else if (headByte === 0xd7) {\n        // fixext 8\n        object = this.decodeExtension(8, 0);\n      } else if (headByte === 0xd8) {\n        // fixext 16\n        object = this.decodeExtension(16, 0);\n      } else if (headByte === 0xc7) {\n        // ext 8\n        const size = this.lookU8();\n        object = this.decodeExtension(size, 1);\n      } else if (headByte === 0xc8) {\n        // ext 16\n        const size = this.lookU16();\n        object = this.decodeExtension(size, 2);\n      } else if (headByte === 0xc9) {\n        // ext 32\n        const size = this.lookU32();\n        object = this.decodeExtension(size, 4);\n      } else {\n        throw new DecodeError(`Unrecognized type byte: ${prettyByte(headByte)}`);\n      }\n\n      this.complete();\n\n      const stack = this.stack;\n      while (stack.length > 0) {\n        // arrays and maps\n        const state = stack.top()!;\n        if (state.type === STATE_ARRAY) {\n          state.array[state.position] = object;\n          state.position++;\n          if (state.position === state.size) {\n            object = state.array;\n            stack.release(state);\n          } else {\n            continue DECODE;\n          }\n        } else if (state.type === STATE_MAP_KEY) {\n          if (object === \"__proto__\") {\n            throw new DecodeError(\"The key __proto__ is not allowed\");\n          }\n\n          state.key = this.mapKeyConverter(object);\n          state.type = STATE_MAP_VALUE;\n          continue DECODE;\n        } else {\n          // it must be `state.type === State.MAP_VALUE` here\n\n          state.map[state.key!] = object;\n          state.readCount++;\n\n          if (state.readCount === state.size) {\n            object = state.map;\n            stack.release(state);\n          } else {\n            state.key = null;\n            state.type = STATE_MAP_KEY;\n            continue DECODE;\n          }\n        }\n      }\n\n      return object;\n    }\n  }\n\n  private readHeadByte(): number {\n    if (this.headByte === HEAD_BYTE_REQUIRED) {\n      this.headByte = this.readU8();\n      // console.log(\"headByte\", prettyByte(this.headByte));\n    }\n\n    return this.headByte;\n  }\n\n  private complete(): void {\n    this.headByte = HEAD_BYTE_REQUIRED;\n  }\n\n  private readArraySize(): number {\n    const headByte = this.readHeadByte();\n\n    switch (headByte) {\n      case 0xdc:\n        return this.readU16();\n      case 0xdd:\n        return this.readU32();\n      default: {\n        if (headByte < 0xa0) {\n          return headByte - 0x90;\n        } else {\n          throw new DecodeError(`Unrecognized array type byte: ${prettyByte(headByte)}`);\n        }\n      }\n    }\n  }\n\n  private pushMapState(size: number) {\n    if (size > this.maxMapLength) {\n      throw new DecodeError(`Max length exceeded: map length (${size}) > maxMapLengthLength (${this.maxMapLength})`);\n    }\n\n    this.stack.pushMapState(size);\n  }\n\n  private pushArrayState(size: number) {\n    if (size > this.maxArrayLength) {\n      throw new DecodeError(`Max length exceeded: array length (${size}) > maxArrayLength (${this.maxArrayLength})`);\n    }\n\n    this.stack.pushArrayState(size);\n  }\n\n  private decodeString(byteLength: number, headerOffset: number): string | Uint8Array {\n    if (!this.rawStrings || this.stateIsMapKey()) {\n      return this.decodeUtf8String(byteLength, headerOffset);\n    }\n    return this.decodeBinary(byteLength, headerOffset);\n  }\n\n  /**\n   * @throws {@link RangeError}\n   */\n  private decodeUtf8String(byteLength: number, headerOffset: number): string {\n    if (byteLength > this.maxStrLength) {\n      throw new DecodeError(\n        `Max length exceeded: UTF-8 byte length (${byteLength}) > maxStrLength (${this.maxStrLength})`,\n      );\n    }\n\n    if (this.bytes.byteLength < this.pos + headerOffset + byteLength) {\n      throw MORE_DATA;\n    }\n\n    const offset = this.pos + headerOffset;\n    let object: string;\n    if (this.stateIsMapKey() && this.keyDecoder?.canBeCached(byteLength)) {\n      object = this.keyDecoder.decode(this.bytes, offset, byteLength);\n    } else {\n      object = utf8Decode(this.bytes, offset, byteLength);\n    }\n    this.pos += headerOffset + byteLength;\n    return object;\n  }\n\n  private stateIsMapKey(): boolean {\n    if (this.stack.length > 0) {\n      const state = this.stack.top()!;\n      return state.type === STATE_MAP_KEY;\n    }\n    return false;\n  }\n\n  /**\n   * @throws {@link RangeError}\n   */\n  private decodeBinary(byteLength: number, headOffset: number): Uint8Array {\n    if (byteLength > this.maxBinLength) {\n      throw new DecodeError(`Max length exceeded: bin length (${byteLength}) > maxBinLength (${this.maxBinLength})`);\n    }\n\n    if (!this.hasRemaining(byteLength + headOffset)) {\n      throw MORE_DATA;\n    }\n\n    const offset = this.pos + headOffset;\n    const object = this.bytes.subarray(offset, offset + byteLength);\n    this.pos += headOffset + byteLength;\n    return object;\n  }\n\n  private decodeExtension(size: number, headOffset: number): unknown {\n    if (size > this.maxExtLength) {\n      throw new DecodeError(`Max length exceeded: ext length (${size}) > maxExtLength (${this.maxExtLength})`);\n    }\n\n    const extType = this.view.getInt8(this.pos + headOffset);\n    const data = this.decodeBinary(size, headOffset + 1 /* extType */);\n    return this.extensionCodec.decode(data, extType, this.context);\n  }\n\n  private lookU8() {\n    return this.view.getUint8(this.pos);\n  }\n\n  private lookU16() {\n    return this.view.getUint16(this.pos);\n  }\n\n  private lookU32() {\n    return this.view.getUint32(this.pos);\n  }\n\n  private readU8(): number {\n    const value = this.view.getUint8(this.pos);\n    this.pos++;\n    return value;\n  }\n\n  private readI8(): number {\n    const value = this.view.getInt8(this.pos);\n    this.pos++;\n    return value;\n  }\n\n  private readU16(): number {\n    const value = this.view.getUint16(this.pos);\n    this.pos += 2;\n    return value;\n  }\n\n  private readI16(): number {\n    const value = this.view.getInt16(this.pos);\n    this.pos += 2;\n    return value;\n  }\n\n  private readU32(): number {\n    const value = this.view.getUint32(this.pos);\n    this.pos += 4;\n    return value;\n  }\n\n  private readI32(): number {\n    const value = this.view.getInt32(this.pos);\n    this.pos += 4;\n    return value;\n  }\n\n  private readU64(): number {\n    const value = getUint64(this.view, this.pos);\n    this.pos += 8;\n    return value;\n  }\n\n  private readI64(): number {\n    const value = getInt64(this.view, this.pos);\n    this.pos += 8;\n    return value;\n  }\n\n  private readU64AsBigInt(): bigint {\n    const value = this.view.getBigUint64(this.pos);\n    this.pos += 8;\n    return value;\n  }\n\n  private readI64AsBigInt(): bigint {\n    const value = this.view.getBigInt64(this.pos);\n    this.pos += 8;\n    return value;\n  }\n\n  private readF32() {\n    const value = this.view.getFloat32(this.pos);\n    this.pos += 4;\n    return value;\n  }\n\n  private readF64() {\n    const value = this.view.getFloat64(this.pos);\n    this.pos += 8;\n    return value;\n  }\n}\n", "import { Decoder } from \"./Decoder.ts\";\nimport type { DecoderOptions } from \"./Decoder.ts\";\nimport type { SplitUndefined } from \"./context.ts\";\n\n/**\n * It decodes a single MessagePack object in a buffer.\n *\n * This is a synchronous decoding function.\n * See other variants for asynchronous decoding: {@link decodeAsync}, {@link decodeMultiStream}, or {@link decodeArrayStream}.\n *\n * @throws {@link RangeError} if the buffer is incomplete, including the case where the buffer is empty.\n * @throws {@link DecodeError} if the buffer contains invalid data.\n */\nexport function decode<ContextType = undefined>(\n  buffer: ArrayLike<number> | ArrayBufferView | ArrayBufferLike,\n  options?: DecoderOptions<SplitUndefined<ContextType>>,\n): unknown {\n  const decoder = new Decoder(options);\n  return decoder.decode(buffer);\n}\n\n/**\n * It decodes multiple MessagePack objects in a buffer.\n * This is corresponding to {@link decodeMultiStream}.\n *\n * @throws {@link RangeError} if the buffer is incomplete, including the case where the buffer is empty.\n * @throws {@link DecodeError} if the buffer contains invalid data.\n */\nexport function decodeMulti<ContextType = undefined>(\n  buffer: ArrayLike<number> | BufferSource,\n  options?: DecoderOptions<SplitUndefined<ContextType>>,\n): Generator<unknown, void, unknown> {\n  const decoder = new Decoder(options);\n  return decoder.decodeMulti(buffer);\n}\n", "// utility for whatwg streams\n\n// The living standard of whatwg streams says\n// ReadableStream is also AsyncIterable, but\n// as of June 2019, no browser implements it.\n// See https://streams.spec.whatwg.org/ for details\nexport type ReadableStreamLike<T> = AsyncIterable<T> | ReadableStream<T>;\n\nexport function isAsyncIterable<T>(object: ReadableStreamLike<T>): object is AsyncIterable<T> {\n  return (object as any)[Symbol.asyncIterator] != null;\n}\n\nexport async function* asyncIterableFromStream<T>(stream: ReadableStream<T>): AsyncIterable<T> {\n  const reader = stream.getReader();\n\n  try {\n    while (true) {\n      const { done, value } = await reader.read();\n      if (done) {\n        return;\n      }\n      yield value;\n    }\n  } finally {\n    reader.releaseLock();\n  }\n}\n\nexport function ensureAsyncIterable<T>(streamLike: ReadableStreamLike<T>): AsyncIterable<T> {\n  if (isAsyncIterable(streamLike)) {\n    return streamLike;\n  } else {\n    return asyncIterableFromStream(streamLike);\n  }\n}\n", "import { Decoder } from \"./Decoder.ts\";\nimport { ensureAsyncIterable } from \"./utils/stream.ts\";\nimport type { DecoderOptions } from \"./Decoder.ts\";\nimport type { ReadableStreamLike } from \"./utils/stream.ts\";\nimport type { SplitUndefined } from \"./context.ts\";\n\n/**\n * @throws {@link RangeError} if the buffer is incomplete, including the case where the buffer is empty.\n * @throws {@link DecodeError} if the buffer contains invalid data.\n */\nexport async function decodeAsync<ContextType = undefined>(\n  streamLike: ReadableStreamLike<ArrayLike<number> | BufferSource>,\n  options?: DecoderOptions<SplitUndefined<ContextType>>,\n): Promise<unknown> {\n  const stream = ensureAsyncIterable(streamLike);\n  const decoder = new Decoder(options);\n  return decoder.decodeAsync(stream);\n}\n\n/**\n * @throws {@link RangeError} if the buffer is incomplete, including the case where the buffer is empty.\n * @throws {@link DecodeError} if the buffer contains invalid data.\n */\nexport function decodeArrayStream<ContextType>(\n  streamLike: ReadableStreamLike<ArrayLike<number> | BufferSource>,\n  options?: DecoderOptions<SplitUndefined<ContextType>>,\n): AsyncGenerator<unknown, void, unknown> {\n  const stream = ensureAsyncIterable(streamLike);\n  const decoder = new Decoder(options);\n  return decoder.decodeArrayStream(stream);\n}\n\n/**\n * @throws {@link RangeError} if the buffer is incomplete, including the case where the buffer is empty.\n * @throws {@link DecodeError} if the buffer contains invalid data.\n */\nexport function decodeMultiStream<ContextType>(\n  streamLike: ReadableStreamLike<ArrayLike<number> | BufferSource>,\n  options?: DecoderOptions<SplitUndefined<ContextType>>,\n): AsyncGenerator<unknown, void, unknown> {\n  const stream = ensureAsyncIterable(streamLike);\n  const decoder = new Decoder(options);\n  return decoder.decodeStream(stream);\n}\n"], "mappings": ";;;AAAM,SAAU,UAAU,KAAW;AACnC,QAAM,YAAY,IAAI;AAEtB,MAAI,aAAa;AACjB,MAAI,MAAM;AACV,SAAO,MAAM,WAAW;AACtB,QAAI,QAAQ,IAAI,WAAW,KAAK;AAEhC,SAAK,QAAQ,gBAAgB,GAAG;AAE9B;AACA;IACF,YAAY,QAAQ,gBAAgB,GAAG;AAErC,oBAAc;IAChB,OAAO;AAEL,UAAI,SAAS,SAAU,SAAS,OAAQ;AAEtC,YAAI,MAAM,WAAW;AACnB,gBAAM,QAAQ,IAAI,WAAW,GAAG;AAChC,eAAK,QAAQ,WAAY,OAAQ;AAC/B,cAAE;AACF,sBAAU,QAAQ,SAAU,OAAO,QAAQ,QAAS;UACtD;QACF;MACF;AAEA,WAAK,QAAQ,gBAAgB,GAAG;AAE9B,sBAAc;MAChB,OAAO;AAEL,sBAAc;MAChB;IACF;EACF;AACA,SAAO;AACT;AAEM,SAAU,aAAa,KAAa,QAAoB,cAAoB;AAChF,QAAM,YAAY,IAAI;AACtB,MAAI,SAAS;AACb,MAAI,MAAM;AACV,SAAO,MAAM,WAAW;AACtB,QAAI,QAAQ,IAAI,WAAW,KAAK;AAEhC,SAAK,QAAQ,gBAAgB,GAAG;AAE9B,aAAO,QAAQ,IAAI;AACnB;IACF,YAAY,QAAQ,gBAAgB,GAAG;AAErC,aAAO,QAAQ,IAAM,SAAS,IAAK,KAAQ;IAC7C,OAAO;AAEL,UAAI,SAAS,SAAU,SAAS,OAAQ;AAEtC,YAAI,MAAM,WAAW;AACnB,gBAAM,QAAQ,IAAI,WAAW,GAAG;AAChC,eAAK,QAAQ,WAAY,OAAQ;AAC/B,cAAE;AACF,sBAAU,QAAQ,SAAU,OAAO,QAAQ,QAAS;UACtD;QACF;MACF;AAEA,WAAK,QAAQ,gBAAgB,GAAG;AAE9B,eAAO,QAAQ,IAAM,SAAS,KAAM,KAAQ;AAC5C,eAAO,QAAQ,IAAM,SAAS,IAAK,KAAQ;MAC7C,OAAO;AAEL,eAAO,QAAQ,IAAM,SAAS,KAAM,IAAQ;AAC5C,eAAO,QAAQ,IAAM,SAAS,KAAM,KAAQ;AAC5C,eAAO,QAAQ,IAAM,SAAS,IAAK,KAAQ;MAC7C;IACF;AAEA,WAAO,QAAQ,IAAK,QAAQ,KAAQ;EACtC;AACF;AASA,IAAM,oBAAoB,IAAI,YAAW;AAIzC,IAAM,yBAAyB;AAEzB,SAAU,aAAa,KAAa,QAAoB,cAAoB;AAChF,oBAAkB,WAAW,KAAK,OAAO,SAAS,YAAY,CAAC;AACjE;AAEM,SAAU,WAAW,KAAa,QAAoB,cAAoB;AAC9E,MAAI,IAAI,SAAS,wBAAwB;AACvC,iBAAa,KAAK,QAAQ,YAAY;EACxC,OAAO;AACL,iBAAa,KAAK,QAAQ,YAAY;EACxC;AACF;AAEA,IAAM,aAAa;AAEb,SAAU,aAAa,OAAmB,aAAqB,YAAkB;AACrF,MAAI,SAAS;AACb,QAAM,MAAM,SAAS;AAErB,QAAM,QAAuB,CAAA;AAC7B,MAAI,SAAS;AACb,SAAO,SAAS,KAAK;AACnB,UAAM,QAAQ,MAAM,QAAQ;AAC5B,SAAK,QAAQ,SAAU,GAAG;AAExB,YAAM,KAAK,KAAK;IAClB,YAAY,QAAQ,SAAU,KAAM;AAElC,YAAM,QAAQ,MAAM,QAAQ,IAAK;AACjC,YAAM,MAAO,QAAQ,OAAS,IAAK,KAAK;IAC1C,YAAY,QAAQ,SAAU,KAAM;AAElC,YAAM,QAAQ,MAAM,QAAQ,IAAK;AACjC,YAAM,QAAQ,MAAM,QAAQ,IAAK;AACjC,YAAM,MAAO,QAAQ,OAAS,KAAO,SAAS,IAAK,KAAK;IAC1D,YAAY,QAAQ,SAAU,KAAM;AAElC,YAAM,QAAQ,MAAM,QAAQ,IAAK;AACjC,YAAM,QAAQ,MAAM,QAAQ,IAAK;AACjC,YAAM,QAAQ,MAAM,QAAQ,IAAK;AACjC,UAAI,QAAS,QAAQ,MAAS,KAAS,SAAS,KAAS,SAAS,IAAQ;AAC1E,UAAI,OAAO,OAAQ;AACjB,gBAAQ;AACR,cAAM,KAAO,SAAS,KAAM,OAAS,KAAM;AAC3C,eAAO,QAAU,OAAO;MAC1B;AACA,YAAM,KAAK,IAAI;IACjB,OAAO;AACL,YAAM,KAAK,KAAK;IAClB;AAEA,QAAI,MAAM,UAAU,YAAY;AAC9B,gBAAU,OAAO,aAAa,GAAG,KAAK;AACtC,YAAM,SAAS;IACjB;EACF;AAEA,MAAI,MAAM,SAAS,GAAG;AACpB,cAAU,OAAO,aAAa,GAAG,KAAK;EACxC;AAEA,SAAO;AACT;AAEA,IAAM,oBAAoB,IAAI,YAAW;AAIzC,IAAM,yBAAyB;AAEzB,SAAU,aAAa,OAAmB,aAAqB,YAAkB;AACrF,QAAM,cAAc,MAAM,SAAS,aAAa,cAAc,UAAU;AACxE,SAAO,kBAAkB,OAAO,WAAW;AAC7C;AAEM,SAAU,WAAW,OAAmB,aAAqB,YAAkB;AACnF,MAAI,aAAa,wBAAwB;AACvC,WAAO,aAAa,OAAO,aAAa,UAAU;EACpD,OAAO;AACL,WAAO,aAAa,OAAO,aAAa,UAAU;EACpD;AACF;;;AC7KM,IAAO,UAAP,MAAc;EAIlB,YAAY,MAAc,MAAgD;AACxE,SAAK,OAAO;AACZ,SAAK,OAAO;EACd;;;;ACVI,IAAO,cAAP,MAAO,qBAAoB,MAAK;EACpC,YAAY,SAAe;AACzB,UAAM,OAAO;AAGb,UAAM,QAAsC,OAAO,OAAO,aAAY,SAAS;AAC/E,WAAO,eAAe,MAAM,KAAK;AAEjC,WAAO,eAAe,MAAM,QAAQ;MAClC,cAAc;MACd,YAAY;MACZ,OAAO,aAAY;KACpB;EACH;;;;ACXK,IAAM,aAAa;AAKpB,SAAU,UAAU,MAAgB,QAAgB,OAAa;AACrE,QAAM,OAAO,QAAQ;AACrB,QAAM,MAAM;AACZ,OAAK,UAAU,QAAQ,IAAI;AAC3B,OAAK,UAAU,SAAS,GAAG,GAAG;AAChC;AAEM,SAAU,SAAS,MAAgB,QAAgB,OAAa;AACpE,QAAM,OAAO,KAAK,MAAM,QAAQ,UAAa;AAC7C,QAAM,MAAM;AACZ,OAAK,UAAU,QAAQ,IAAI;AAC3B,OAAK,UAAU,SAAS,GAAG,GAAG;AAChC;AAEM,SAAU,SAAS,MAAgB,QAAc;AACrD,QAAM,OAAO,KAAK,SAAS,MAAM;AACjC,QAAM,MAAM,KAAK,UAAU,SAAS,CAAC;AACrC,SAAO,OAAO,aAAgB;AAChC;AAEM,SAAU,UAAU,MAAgB,QAAc;AACtD,QAAM,OAAO,KAAK,UAAU,MAAM;AAClC,QAAM,MAAM,KAAK,UAAU,SAAS,CAAC;AACrC,SAAO,OAAO,aAAgB;AAChC;;;AC3BO,IAAM,gBAAgB;AAO7B,IAAM,sBAAsB,aAAc;AAC1C,IAAM,sBAAsB,cAAc;AAEpC,SAAU,0BAA0B,EAAE,KAAK,KAAI,GAAY;AAC/D,MAAI,OAAO,KAAK,QAAQ,KAAK,OAAO,qBAAqB;AAEvD,QAAI,SAAS,KAAK,OAAO,qBAAqB;AAE5C,YAAM,KAAK,IAAI,WAAW,CAAC;AAC3B,YAAM,OAAO,IAAI,SAAS,GAAG,MAAM;AACnC,WAAK,UAAU,GAAG,GAAG;AACrB,aAAO;IACT,OAAO;AAEL,YAAM,UAAU,MAAM;AACtB,YAAM,SAAS,MAAM;AACrB,YAAM,KAAK,IAAI,WAAW,CAAC;AAC3B,YAAM,OAAO,IAAI,SAAS,GAAG,MAAM;AAEnC,WAAK,UAAU,GAAI,QAAQ,IAAM,UAAU,CAAI;AAE/C,WAAK,UAAU,GAAG,MAAM;AACxB,aAAO;IACT;EACF,OAAO;AAEL,UAAM,KAAK,IAAI,WAAW,EAAE;AAC5B,UAAM,OAAO,IAAI,SAAS,GAAG,MAAM;AACnC,SAAK,UAAU,GAAG,IAAI;AACtB,aAAS,MAAM,GAAG,GAAG;AACrB,WAAO;EACT;AACF;AAEM,SAAU,qBAAqB,MAAU;AAC7C,QAAM,OAAO,KAAK,QAAO;AACzB,QAAM,MAAM,KAAK,MAAM,OAAO,GAAG;AACjC,QAAM,QAAQ,OAAO,MAAM,OAAO;AAGlC,QAAM,YAAY,KAAK,MAAM,OAAO,GAAG;AACvC,SAAO;IACL,KAAK,MAAM;IACX,MAAM,OAAO,YAAY;;AAE7B;AAEM,SAAU,yBAAyB,QAAe;AACtD,MAAI,kBAAkB,MAAM;AAC1B,UAAM,WAAW,qBAAqB,MAAM;AAC5C,WAAO,0BAA0B,QAAQ;EAC3C,OAAO;AACL,WAAO;EACT;AACF;AAEM,SAAU,0BAA0B,MAAgB;AACxD,QAAM,OAAO,IAAI,SAAS,KAAK,QAAQ,KAAK,YAAY,KAAK,UAAU;AAGvE,UAAQ,KAAK,YAAY;IACvB,KAAK,GAAG;AAEN,YAAM,MAAM,KAAK,UAAU,CAAC;AAC5B,YAAM,OAAO;AACb,aAAO,EAAE,KAAK,KAAI;IACpB;IACA,KAAK,GAAG;AAEN,YAAM,oBAAoB,KAAK,UAAU,CAAC;AAC1C,YAAM,WAAW,KAAK,UAAU,CAAC;AACjC,YAAM,OAAO,oBAAoB,KAAO,aAAc;AACtD,YAAM,OAAO,sBAAsB;AACnC,aAAO,EAAE,KAAK,KAAI;IACpB;IACA,KAAK,IAAI;AAGP,YAAM,MAAM,SAAS,MAAM,CAAC;AAC5B,YAAM,OAAO,KAAK,UAAU,CAAC;AAC7B,aAAO,EAAE,KAAK,KAAI;IACpB;IACA;AACE,YAAM,IAAI,YAAY,gEAAgE,KAAK,MAAM,EAAE;EACvG;AACF;AAEM,SAAU,yBAAyB,MAAgB;AACvD,QAAM,WAAW,0BAA0B,IAAI;AAC/C,SAAO,IAAI,KAAK,SAAS,MAAM,MAAM,SAAS,OAAO,GAAG;AAC1D;AAEO,IAAM,qBAAqB;EAChC,MAAM;EACN,QAAQ;EACR,QAAQ;;;;AClFJ,IAAO,iBAAP,MAAqB;EAgBzB,cAAA;AAPiB,SAAA,kBAA+E,CAAA;AAC/E,SAAA,kBAA+E,CAAA;AAG/E,SAAA,WAAwE,CAAA;AACxE,SAAA,WAAwE,CAAA;AAGvF,SAAK,SAAS,kBAAkB;EAClC;EAEO,SAAS,EACd,MACA,QAAAA,SACA,QAAAC,QAAM,GAKP;AACC,QAAI,QAAQ,GAAG;AAEb,WAAK,SAAS,IAAI,IAAID;AACtB,WAAK,SAAS,IAAI,IAAIC;IACxB,OAAO;AAEL,YAAM,QAAQ,KAAK;AACnB,WAAK,gBAAgB,KAAK,IAAID;AAC9B,WAAK,gBAAgB,KAAK,IAAIC;IAChC;EACF;EAEO,YAAY,QAAiB,SAAoB;AAEtD,aAAS,IAAI,GAAG,IAAI,KAAK,gBAAgB,QAAQ,KAAK;AACpD,YAAM,YAAY,KAAK,gBAAgB,CAAC;AACxC,UAAI,aAAa,MAAM;AACrB,cAAM,OAAO,UAAU,QAAQ,OAAO;AACtC,YAAI,QAAQ,MAAM;AAChB,gBAAM,OAAO,KAAK;AAClB,iBAAO,IAAI,QAAQ,MAAM,IAAI;QAC/B;MACF;IACF;AAGA,aAAS,IAAI,GAAG,IAAI,KAAK,SAAS,QAAQ,KAAK;AAC7C,YAAM,YAAY,KAAK,SAAS,CAAC;AACjC,UAAI,aAAa,MAAM;AACrB,cAAM,OAAO,UAAU,QAAQ,OAAO;AACtC,YAAI,QAAQ,MAAM;AAChB,gBAAM,OAAO;AACb,iBAAO,IAAI,QAAQ,MAAM,IAAI;QAC/B;MACF;IACF;AAEA,QAAI,kBAAkB,SAAS;AAE7B,aAAO;IACT;AACA,WAAO;EACT;EAEO,OAAO,MAAkB,MAAc,SAAoB;AAChE,UAAM,YAAY,OAAO,IAAI,KAAK,gBAAgB,KAAK,IAAI,IAAI,KAAK,SAAS,IAAI;AACjF,QAAI,WAAW;AACb,aAAO,UAAU,MAAM,MAAM,OAAO;IACtC,OAAO;AAEL,aAAO,IAAI,QAAQ,MAAM,IAAI;IAC/B;EACF;;AAhFuB,eAAA,eAA8C,IAAI,eAAc;;;ACzBzF,SAAS,kBAAkB,QAAe;AACxC,SACE,kBAAkB,eAAgB,OAAO,sBAAsB,eAAe,kBAAkB;AAEpG;AAEM,SAAU,iBACd,QAA2F;AAE3F,MAAI,kBAAkB,YAAY;AAChC,WAAO;EACT,WAAW,YAAY,OAAO,MAAM,GAAG;AACrC,WAAO,IAAI,WAAW,OAAO,QAAQ,OAAO,YAAY,OAAO,UAAU;EAC3E,WAAW,kBAAkB,MAAM,GAAG;AACpC,WAAO,IAAI,WAAW,MAAM;EAC9B,OAAO;AAEL,WAAO,WAAW,KAAK,MAAM;EAC/B;AACF;;;ACXO,IAAM,oBAAoB;AAC1B,IAAM,8BAA8B;AAiErC,IAAO,UAAP,MAAO,SAAO;EAiBlB,YAAmB,SAAqC;AAFhD,SAAA,UAAU;AAGhB,SAAK,kBAAiB,mCAAS,mBAAmB,eAAe;AACjE,SAAK,UAAW,mCAAkD;AAElE,SAAK,eAAc,mCAAS,gBAAe;AAC3C,SAAK,YAAW,mCAAS,aAAY;AACrC,SAAK,qBAAoB,mCAAS,sBAAqB;AACvD,SAAK,YAAW,mCAAS,aAAY;AACrC,SAAK,gBAAe,mCAAS,iBAAgB;AAC7C,SAAK,mBAAkB,mCAAS,oBAAmB;AACnD,SAAK,uBAAsB,mCAAS,wBAAuB;AAE3D,SAAK,MAAM;AACX,SAAK,OAAO,IAAI,SAAS,IAAI,YAAY,KAAK,iBAAiB,CAAC;AAChE,SAAK,QAAQ,IAAI,WAAW,KAAK,KAAK,MAAM;EAC9C;EAEQ,QAAK;AAIX,WAAO,IAAI,SAAqB;MAC9B,gBAAgB,KAAK;MACrB,SAAS,KAAK;MACd,aAAa,KAAK;MAClB,UAAU,KAAK;MACf,mBAAmB,KAAK;MACxB,UAAU,KAAK;MACf,cAAc,KAAK;MACnB,iBAAiB,KAAK;MACtB,qBAAqB,KAAK;KACpB;EACV;EAEQ,oBAAiB;AACvB,SAAK,MAAM;EACb;;;;;;EAOO,gBAAgB,QAAe;AACpC,QAAI,KAAK,SAAS;AAChB,YAAM,WAAW,KAAK,MAAK;AAC3B,aAAO,SAAS,gBAAgB,MAAM;IACxC;AAEA,QAAI;AACF,WAAK,UAAU;AAEf,WAAK,kBAAiB;AACtB,WAAK,SAAS,QAAQ,CAAC;AACvB,aAAO,KAAK,MAAM,SAAS,GAAG,KAAK,GAAG;IACxC;AACE,WAAK,UAAU;IACjB;EACF;;;;EAKO,OAAO,QAAe;AAC3B,QAAI,KAAK,SAAS;AAChB,YAAM,WAAW,KAAK,MAAK;AAC3B,aAAO,SAAS,OAAO,MAAM;IAC/B;AAEA,QAAI;AACF,WAAK,UAAU;AAEf,WAAK,kBAAiB;AACtB,WAAK,SAAS,QAAQ,CAAC;AACvB,aAAO,KAAK,MAAM,MAAM,GAAG,KAAK,GAAG;IACrC;AACE,WAAK,UAAU;IACjB;EACF;EAEQ,SAAS,QAAiB,OAAa;AAC7C,QAAI,QAAQ,KAAK,UAAU;AACzB,YAAM,IAAI,MAAM,6BAA6B,KAAK,EAAE;IACtD;AAEA,QAAI,UAAU,MAAM;AAClB,WAAK,UAAS;IAChB,WAAW,OAAO,WAAW,WAAW;AACtC,WAAK,cAAc,MAAM;IAC3B,WAAW,OAAO,WAAW,UAAU;AACrC,UAAI,CAAC,KAAK,qBAAqB;AAC7B,aAAK,aAAa,MAAM;MAC1B,OAAO;AACL,aAAK,oBAAoB,MAAM;MACjC;IACF,WAAW,OAAO,WAAW,UAAU;AACrC,WAAK,aAAa,MAAM;IAC1B,WAAW,KAAK,eAAe,OAAO,WAAW,UAAU;AACzD,WAAK,eAAe,MAAM;IAC5B,OAAO;AACL,WAAK,aAAa,QAAQ,KAAK;IACjC;EACF;EAEQ,wBAAwB,aAAmB;AACjD,UAAM,eAAe,KAAK,MAAM;AAEhC,QAAI,KAAK,KAAK,aAAa,cAAc;AACvC,WAAK,aAAa,eAAe,CAAC;IACpC;EACF;EAEQ,aAAa,SAAe;AAClC,UAAM,YAAY,IAAI,YAAY,OAAO;AACzC,UAAM,WAAW,IAAI,WAAW,SAAS;AACzC,UAAM,UAAU,IAAI,SAAS,SAAS;AAEtC,aAAS,IAAI,KAAK,KAAK;AAEvB,SAAK,OAAO;AACZ,SAAK,QAAQ;EACf;EAEQ,YAAS;AACf,SAAK,QAAQ,GAAI;EACnB;EAEQ,cAAc,QAAe;AACnC,QAAI,WAAW,OAAO;AACpB,WAAK,QAAQ,GAAI;IACnB,OAAO;AACL,WAAK,QAAQ,GAAI;IACnB;EACF;EAEQ,aAAa,QAAc;AACjC,QAAI,CAAC,KAAK,uBAAuB,OAAO,cAAc,MAAM,GAAG;AAC7D,UAAI,UAAU,GAAG;AACf,YAAI,SAAS,KAAM;AAEjB,eAAK,QAAQ,MAAM;QACrB,WAAW,SAAS,KAAO;AAEzB,eAAK,QAAQ,GAAI;AACjB,eAAK,QAAQ,MAAM;QACrB,WAAW,SAAS,OAAS;AAE3B,eAAK,QAAQ,GAAI;AACjB,eAAK,SAAS,MAAM;QACtB,WAAW,SAAS,YAAa;AAE/B,eAAK,QAAQ,GAAI;AACjB,eAAK,SAAS,MAAM;QACtB,WAAW,CAAC,KAAK,aAAa;AAE5B,eAAK,QAAQ,GAAI;AACjB,eAAK,SAAS,MAAM;QACtB,OAAO;AACL,eAAK,oBAAoB,MAAM;QACjC;MACF,OAAO;AACL,YAAI,UAAU,KAAO;AAEnB,eAAK,QAAQ,MAAQ,SAAS,EAAK;QACrC,WAAW,UAAU,MAAO;AAE1B,eAAK,QAAQ,GAAI;AACjB,eAAK,QAAQ,MAAM;QACrB,WAAW,UAAU,QAAS;AAE5B,eAAK,QAAQ,GAAI;AACjB,eAAK,SAAS,MAAM;QACtB,WAAW,UAAU,aAAa;AAEhC,eAAK,QAAQ,GAAI;AACjB,eAAK,SAAS,MAAM;QACtB,WAAW,CAAC,KAAK,aAAa;AAE5B,eAAK,QAAQ,GAAI;AACjB,eAAK,SAAS,MAAM;QACtB,OAAO;AACL,eAAK,oBAAoB,MAAM;QACjC;MACF;IACF,OAAO;AACL,WAAK,oBAAoB,MAAM;IACjC;EACF;EAEQ,oBAAoB,QAAc;AACxC,QAAI,KAAK,cAAc;AAErB,WAAK,QAAQ,GAAI;AACjB,WAAK,SAAS,MAAM;IACtB,OAAO;AAEL,WAAK,QAAQ,GAAI;AACjB,WAAK,SAAS,MAAM;IACtB;EACF;EAEQ,eAAe,QAAc;AACnC,QAAI,UAAU,OAAO,CAAC,GAAG;AAEvB,WAAK,QAAQ,GAAI;AACjB,WAAK,eAAe,MAAM;IAC5B,OAAO;AAEL,WAAK,QAAQ,GAAI;AACjB,WAAK,cAAc,MAAM;IAC3B;EACF;EAEQ,kBAAkB,YAAkB;AAC1C,QAAI,aAAa,IAAI;AAEnB,WAAK,QAAQ,MAAO,UAAU;IAChC,WAAW,aAAa,KAAO;AAE7B,WAAK,QAAQ,GAAI;AACjB,WAAK,QAAQ,UAAU;IACzB,WAAW,aAAa,OAAS;AAE/B,WAAK,QAAQ,GAAI;AACjB,WAAK,SAAS,UAAU;IAC1B,WAAW,aAAa,YAAa;AAEnC,WAAK,QAAQ,GAAI;AACjB,WAAK,SAAS,UAAU;IAC1B,OAAO;AACL,YAAM,IAAI,MAAM,oBAAoB,UAAU,iBAAiB;IACjE;EACF;EAEQ,aAAa,QAAc;AACjC,UAAM,gBAAgB,IAAI;AAE1B,UAAM,aAAa,UAAU,MAAM;AACnC,SAAK,wBAAwB,gBAAgB,UAAU;AACvD,SAAK,kBAAkB,UAAU;AACjC,eAAW,QAAQ,KAAK,OAAO,KAAK,GAAG;AACvC,SAAK,OAAO;EACd;EAEQ,aAAa,QAAiB,OAAa;AAEjD,UAAM,MAAM,KAAK,eAAe,YAAY,QAAQ,KAAK,OAAO;AAChE,QAAI,OAAO,MAAM;AACf,WAAK,gBAAgB,GAAG;IAC1B,WAAW,MAAM,QAAQ,MAAM,GAAG;AAChC,WAAK,YAAY,QAAQ,KAAK;IAChC,WAAW,YAAY,OAAO,MAAM,GAAG;AACrC,WAAK,aAAa,MAAM;IAC1B,WAAW,OAAO,WAAW,UAAU;AACrC,WAAK,UAAU,QAAmC,KAAK;IACzD,OAAO;AAEL,YAAM,IAAI,MAAM,wBAAwB,OAAO,UAAU,SAAS,MAAM,MAAM,CAAC,EAAE;IACnF;EACF;EAEQ,aAAa,QAAuB;AAC1C,UAAM,OAAO,OAAO;AACpB,QAAI,OAAO,KAAO;AAEhB,WAAK,QAAQ,GAAI;AACjB,WAAK,QAAQ,IAAI;IACnB,WAAW,OAAO,OAAS;AAEzB,WAAK,QAAQ,GAAI;AACjB,WAAK,SAAS,IAAI;IACpB,WAAW,OAAO,YAAa;AAE7B,WAAK,QAAQ,GAAI;AACjB,WAAK,SAAS,IAAI;IACpB,OAAO;AACL,YAAM,IAAI,MAAM,qBAAqB,IAAI,EAAE;IAC7C;AACA,UAAM,QAAQ,iBAAiB,MAAM;AACrC,SAAK,SAAS,KAAK;EACrB;EAEQ,YAAY,QAAwB,OAAa;AACvD,UAAM,OAAO,OAAO;AACpB,QAAI,OAAO,IAAI;AAEb,WAAK,QAAQ,MAAO,IAAI;IAC1B,WAAW,OAAO,OAAS;AAEzB,WAAK,QAAQ,GAAI;AACjB,WAAK,SAAS,IAAI;IACpB,WAAW,OAAO,YAAa;AAE7B,WAAK,QAAQ,GAAI;AACjB,WAAK,SAAS,IAAI;IACpB,OAAO;AACL,YAAM,IAAI,MAAM,oBAAoB,IAAI,EAAE;IAC5C;AACA,eAAW,QAAQ,QAAQ;AACzB,WAAK,SAAS,MAAM,QAAQ,CAAC;IAC/B;EACF;EAEQ,sBAAsB,QAAiC,MAA2B;AACxF,QAAI,QAAQ;AAEZ,eAAW,OAAO,MAAM;AACtB,UAAI,OAAO,GAAG,MAAM,QAAW;AAC7B;MACF;IACF;AAEA,WAAO;EACT;EAEQ,UAAU,QAAiC,OAAa;AAC9D,UAAM,OAAO,OAAO,KAAK,MAAM;AAC/B,QAAI,KAAK,UAAU;AACjB,WAAK,KAAI;IACX;AAEA,UAAM,OAAO,KAAK,kBAAkB,KAAK,sBAAsB,QAAQ,IAAI,IAAI,KAAK;AAEpF,QAAI,OAAO,IAAI;AAEb,WAAK,QAAQ,MAAO,IAAI;IAC1B,WAAW,OAAO,OAAS;AAEzB,WAAK,QAAQ,GAAI;AACjB,WAAK,SAAS,IAAI;IACpB,WAAW,OAAO,YAAa;AAE7B,WAAK,QAAQ,GAAI;AACjB,WAAK,SAAS,IAAI;IACpB,OAAO;AACL,YAAM,IAAI,MAAM,yBAAyB,IAAI,EAAE;IACjD;AAEA,eAAW,OAAO,MAAM;AACtB,YAAM,QAAQ,OAAO,GAAG;AAExB,UAAI,EAAE,KAAK,mBAAmB,UAAU,SAAY;AAClD,aAAK,aAAa,GAAG;AACrB,aAAK,SAAS,OAAO,QAAQ,CAAC;MAChC;IACF;EACF;EAEQ,gBAAgB,KAAY;AAClC,QAAI,OAAO,IAAI,SAAS,YAAY;AAClC,YAAM,OAAO,IAAI,KAAK,KAAK,MAAM,CAAC;AAClC,YAAMC,QAAO,KAAK;AAElB,UAAIA,SAAQ,YAAa;AACvB,cAAM,IAAI,MAAM,+BAA+BA,KAAI,EAAE;MACvD;AAEA,WAAK,QAAQ,GAAI;AACjB,WAAK,SAASA,KAAI;AAClB,WAAK,QAAQ,IAAI,IAAI;AACrB,WAAK,SAAS,IAAI;AAClB;IACF;AAEA,UAAM,OAAO,IAAI,KAAK;AACtB,QAAI,SAAS,GAAG;AAEd,WAAK,QAAQ,GAAI;IACnB,WAAW,SAAS,GAAG;AAErB,WAAK,QAAQ,GAAI;IACnB,WAAW,SAAS,GAAG;AAErB,WAAK,QAAQ,GAAI;IACnB,WAAW,SAAS,GAAG;AAErB,WAAK,QAAQ,GAAI;IACnB,WAAW,SAAS,IAAI;AAEtB,WAAK,QAAQ,GAAI;IACnB,WAAW,OAAO,KAAO;AAEvB,WAAK,QAAQ,GAAI;AACjB,WAAK,QAAQ,IAAI;IACnB,WAAW,OAAO,OAAS;AAEzB,WAAK,QAAQ,GAAI;AACjB,WAAK,SAAS,IAAI;IACpB,WAAW,OAAO,YAAa;AAE7B,WAAK,QAAQ,GAAI;AACjB,WAAK,SAAS,IAAI;IACpB,OAAO;AACL,YAAM,IAAI,MAAM,+BAA+B,IAAI,EAAE;IACvD;AACA,SAAK,QAAQ,IAAI,IAAI;AACrB,SAAK,SAAS,IAAI,IAAI;EACxB;EAEQ,QAAQ,OAAa;AAC3B,SAAK,wBAAwB,CAAC;AAE9B,SAAK,KAAK,SAAS,KAAK,KAAK,KAAK;AAClC,SAAK;EACP;EAEQ,SAAS,QAAyB;AACxC,UAAM,OAAO,OAAO;AACpB,SAAK,wBAAwB,IAAI;AAEjC,SAAK,MAAM,IAAI,QAAQ,KAAK,GAAG;AAC/B,SAAK,OAAO;EACd;EAEQ,QAAQ,OAAa;AAC3B,SAAK,wBAAwB,CAAC;AAE9B,SAAK,KAAK,QAAQ,KAAK,KAAK,KAAK;AACjC,SAAK;EACP;EAEQ,SAAS,OAAa;AAC5B,SAAK,wBAAwB,CAAC;AAE9B,SAAK,KAAK,UAAU,KAAK,KAAK,KAAK;AACnC,SAAK,OAAO;EACd;EAEQ,SAAS,OAAa;AAC5B,SAAK,wBAAwB,CAAC;AAE9B,SAAK,KAAK,SAAS,KAAK,KAAK,KAAK;AAClC,SAAK,OAAO;EACd;EAEQ,SAAS,OAAa;AAC5B,SAAK,wBAAwB,CAAC;AAE9B,SAAK,KAAK,UAAU,KAAK,KAAK,KAAK;AACnC,SAAK,OAAO;EACd;EAEQ,SAAS,OAAa;AAC5B,SAAK,wBAAwB,CAAC;AAE9B,SAAK,KAAK,SAAS,KAAK,KAAK,KAAK;AAClC,SAAK,OAAO;EACd;EAEQ,SAAS,OAAa;AAC5B,SAAK,wBAAwB,CAAC;AAE9B,SAAK,KAAK,WAAW,KAAK,KAAK,KAAK;AACpC,SAAK,OAAO;EACd;EAEQ,SAAS,OAAa;AAC5B,SAAK,wBAAwB,CAAC;AAE9B,SAAK,KAAK,WAAW,KAAK,KAAK,KAAK;AACpC,SAAK,OAAO;EACd;EAEQ,SAAS,OAAa;AAC5B,SAAK,wBAAwB,CAAC;AAE9B,cAAU,KAAK,MAAM,KAAK,KAAK,KAAK;AACpC,SAAK,OAAO;EACd;EAEQ,SAAS,OAAa;AAC5B,SAAK,wBAAwB,CAAC;AAE9B,aAAS,KAAK,MAAM,KAAK,KAAK,KAAK;AACnC,SAAK,OAAO;EACd;EAEQ,eAAe,OAAa;AAClC,SAAK,wBAAwB,CAAC;AAE9B,SAAK,KAAK,aAAa,KAAK,KAAK,KAAK;AACtC,SAAK,OAAO;EACd;EAEQ,cAAc,OAAa;AACjC,SAAK,wBAAwB,CAAC;AAE9B,SAAK,KAAK,YAAY,KAAK,KAAK,KAAK;AACrC,SAAK,OAAO;EACd;;;;AC1jBI,SAAU,OACd,OACA,SAAqD;AAErD,QAAM,UAAU,IAAI,QAAQ,OAAO;AACnC,SAAO,QAAQ,gBAAgB,KAAK;AACtC;;;AChBM,SAAU,WAAW,MAAY;AACrC,SAAO,GAAG,OAAO,IAAI,MAAM,EAAE,KAAK,KAAK,IAAI,IAAI,EAAE,SAAS,EAAE,EAAE,SAAS,GAAG,GAAG,CAAC;AAChF;;;ACAA,IAAM,yBAAyB;AAC/B,IAAM,6BAA6B;AAW7B,IAAO,mBAAP,MAAuB;EAO3B,YAAY,eAAe,wBAAwB,kBAAkB,4BAA0B;AAN/F,SAAA,MAAM;AACN,SAAA,OAAO;AAML,SAAK,eAAe;AACpB,SAAK,kBAAkB;AAIvB,SAAK,SAAS,CAAA;AACd,aAAS,IAAI,GAAG,IAAI,KAAK,cAAc,KAAK;AAC1C,WAAK,OAAO,KAAK,CAAA,CAAE;IACrB;EACF;EAEO,YAAY,YAAkB;AACnC,WAAO,aAAa,KAAK,cAAc,KAAK;EAC9C;EAEQ,KAAK,OAAmB,aAAqB,YAAkB;AACrE,UAAM,UAAU,KAAK,OAAO,aAAa,CAAC;AAE1C,eAAY,YAAW,UAAU,SAAS;AACxC,YAAM,cAAc,OAAO;AAE3B,eAAS,IAAI,GAAG,IAAI,YAAY,KAAK;AACnC,YAAI,YAAY,CAAC,MAAM,MAAM,cAAc,CAAC,GAAG;AAC7C,mBAAS;QACX;MACF;AACA,aAAO,OAAO;IAChB;AACA,WAAO;EACT;EAEQ,MAAM,OAAmB,OAAa;AAC5C,UAAM,UAAU,KAAK,OAAO,MAAM,SAAS,CAAC;AAC5C,UAAM,SAAyB,EAAE,OAAO,KAAK,MAAK;AAElD,QAAI,QAAQ,UAAU,KAAK,iBAAiB;AAG1C,cAAS,KAAK,OAAM,IAAK,QAAQ,SAAU,CAAC,IAAI;IAClD,OAAO;AACL,cAAQ,KAAK,MAAM;IACrB;EACF;EAEO,OAAO,OAAmB,aAAqB,YAAkB;AACtE,UAAM,cAAc,KAAK,KAAK,OAAO,aAAa,UAAU;AAC5D,QAAI,eAAe,MAAM;AACvB,WAAK;AACL,aAAO;IACT;AACA,SAAK;AAEL,UAAM,MAAM,aAAa,OAAO,aAAa,UAAU;AAEvD,UAAM,oBAAoB,WAAW,UAAU,MAAM,KAAK,OAAO,aAAa,cAAc,UAAU;AACtG,SAAK,MAAM,mBAAmB,GAAG;AACjC,WAAO;EACT;;;;ACGF,IAAM,cAAc;AACpB,IAAM,gBAAgB;AACtB,IAAM,kBAAkB;AAIxB,IAAM,kBAAkB,CAAC,QAA4B;AACnD,MAAI,OAAO,QAAQ,YAAY,OAAO,QAAQ,UAAU;AACtD,WAAO;EACT;AACA,QAAM,IAAI,YAAY,kDAAkD,OAAO,GAAG;AACpF;AAiBA,IAAM,YAAN,MAAe;EAAf,cAAA;AACmB,SAAA,QAA2B,CAAA;AACpC,SAAA,oBAAoB;EA8E9B;EA5EE,IAAW,SAAM;AACf,WAAO,KAAK,oBAAoB;EAClC;EAEO,MAAG;AACR,WAAO,KAAK,MAAM,KAAK,iBAAiB;EAC1C;EAEO,eAAe,MAAY;AAChC,UAAM,QAAQ,KAAK,8BAA6B;AAEhD,UAAM,OAAO;AACb,UAAM,WAAW;AACjB,UAAM,OAAO;AACb,UAAM,QAAQ,IAAI,MAAM,IAAI;EAC9B;EAEO,aAAa,MAAY;AAC9B,UAAM,QAAQ,KAAK,8BAA6B;AAEhD,UAAM,OAAO;AACb,UAAM,YAAY;AAClB,UAAM,OAAO;AACb,UAAM,MAAM,CAAA;EACd;EAEQ,gCAA6B;AACnC,SAAK;AAEL,QAAI,KAAK,sBAAsB,KAAK,MAAM,QAAQ;AAChD,YAAM,eAAoC;QACxC,MAAM;QACN,MAAM;QACN,OAAO;QACP,UAAU;QACV,WAAW;QACX,KAAK;QACL,KAAK;;AAGP,WAAK,MAAM,KAAK,YAA0B;IAC5C;AAEA,WAAO,KAAK,MAAM,KAAK,iBAAiB;EAC1C;EAEO,QAAQ,OAAiB;AAC9B,UAAM,gBAAgB,KAAK,MAAM,KAAK,iBAAiB;AAEvD,QAAI,kBAAkB,OAAO;AAC3B,YAAM,IAAI,MAAM,iEAAiE;IACnF;AAEA,QAAI,MAAM,SAAS,aAAa;AAC9B,YAAM,eAAe;AACrB,mBAAa,OAAO;AACpB,mBAAa,QAAQ;AACrB,mBAAa,WAAW;AACxB,mBAAa,OAAO;IACtB;AAEA,QAAI,MAAM,SAAS,iBAAiB,MAAM,SAAS,iBAAiB;AAClE,YAAM,eAAe;AACrB,mBAAa,OAAO;AACpB,mBAAa,MAAM;AACnB,mBAAa,YAAY;AACzB,mBAAa,OAAO;IACtB;AAEA,SAAK;EACP;EAEO,QAAK;AACV,SAAK,MAAM,SAAS;AACpB,SAAK,oBAAoB;EAC3B;;AAKF,IAAM,qBAAqB;AAE3B,IAAM,aAAa,IAAI,SAA0B,IAAI,YAAY,CAAC,CAAC;AACnE,IAAM,cAAc,IAAI,WAA4B,WAAW,MAAM;AAErE,IAAI;AAGF,aAAW,QAAQ,CAAC;AACtB,SAAS,GAAG;AACV,MAAI,EAAE,aAAa,aAAa;AAC9B,UAAM,IAAI,MACR,kIAAkI;EAEtI;AACF;AAEA,IAAM,YAAY,IAAI,WAAW,mBAAmB;AAEpD,IAAM,yBAAyB,IAAI,iBAAgB;AAE7C,IAAO,UAAP,MAAO,SAAO;EAuBlB,YAAmB,SAAqC;AAVhD,SAAA,WAAW;AACX,SAAA,MAAM;AAEN,SAAA,OAAO;AACP,SAAA,QAAQ;AACR,SAAA,WAAW;AACF,SAAA,QAAQ,IAAI,UAAS;AAE9B,SAAA,UAAU;AAGhB,SAAK,kBAAiB,mCAAS,mBAAmB,eAAe;AACjE,SAAK,UAAW,mCAAkD;AAElE,SAAK,eAAc,mCAAS,gBAAe;AAC3C,SAAK,cAAa,mCAAS,eAAc;AACzC,SAAK,gBAAe,mCAAS,iBAAgB;AAC7C,SAAK,gBAAe,mCAAS,iBAAgB;AAC7C,SAAK,kBAAiB,mCAAS,mBAAkB;AACjD,SAAK,gBAAe,mCAAS,iBAAgB;AAC7C,SAAK,gBAAe,mCAAS,iBAAgB;AAC7C,SAAK,cAAa,mCAAS,gBAAe,SAAY,QAAQ,aAAa;AAC3E,SAAK,mBAAkB,mCAAS,oBAAmB;EACrD;EAEQ,QAAK;AAEX,WAAO,IAAI,SAAQ;MACjB,gBAAgB,KAAK;MACrB,SAAS,KAAK;MACd,aAAa,KAAK;MAClB,YAAY,KAAK;MACjB,cAAc,KAAK;MACnB,cAAc,KAAK;MACnB,gBAAgB,KAAK;MACrB,cAAc,KAAK;MACnB,cAAc,KAAK;MACnB,YAAY,KAAK;KACX;EACV;EAEQ,oBAAiB;AACvB,SAAK,WAAW;AAChB,SAAK,WAAW;AAChB,SAAK,MAAM,MAAK;EAGlB;EAEQ,UAAU,QAA6D;AAC7E,UAAM,QAAQ,iBAAiB,MAAM;AACrC,SAAK,QAAQ;AACb,SAAK,OAAO,IAAI,SAAS,MAAM,QAAQ,MAAM,YAAY,MAAM,UAAU;AACzE,SAAK,MAAM;EACb;EAEQ,aAAa,QAA6D;AAChF,QAAI,KAAK,aAAa,sBAAsB,CAAC,KAAK,aAAa,CAAC,GAAG;AACjE,WAAK,UAAU,MAAM;IACvB,OAAO;AACL,YAAM,gBAAgB,KAAK,MAAM,SAAS,KAAK,GAAG;AAClD,YAAM,UAAU,iBAAiB,MAAM;AAGvC,YAAM,YAAY,IAAI,WAAW,cAAc,SAAS,QAAQ,MAAM;AACtE,gBAAU,IAAI,aAAa;AAC3B,gBAAU,IAAI,SAAS,cAAc,MAAM;AAC3C,WAAK,UAAU,SAAS;IAC1B;EACF;EAEQ,aAAa,MAAY;AAC/B,WAAO,KAAK,KAAK,aAAa,KAAK,OAAO;EAC5C;EAEQ,qBAAqB,WAAiB;AAC5C,UAAM,EAAE,MAAM,IAAG,IAAK;AACtB,WAAO,IAAI,WAAW,SAAS,KAAK,aAAa,GAAG,OAAO,KAAK,UAAU,4BAA4B,SAAS,GAAG;EACpH;;;;;EAMO,OAAO,QAA6D;AACzE,QAAI,KAAK,SAAS;AAChB,YAAM,WAAW,KAAK,MAAK;AAC3B,aAAO,SAAS,OAAO,MAAM;IAC/B;AAEA,QAAI;AACF,WAAK,UAAU;AAEf,WAAK,kBAAiB;AACtB,WAAK,UAAU,MAAM;AAErB,YAAM,SAAS,KAAK,aAAY;AAChC,UAAI,KAAK,aAAa,CAAC,GAAG;AACxB,cAAM,KAAK,qBAAqB,KAAK,GAAG;MAC1C;AACA,aAAO;IACT;AACE,WAAK,UAAU;IACjB;EACF;EAEO,CAAC,YAAY,QAA6D;AAC/E,QAAI,KAAK,SAAS;AAChB,YAAM,WAAW,KAAK,MAAK;AAC3B,aAAO,SAAS,YAAY,MAAM;AAClC;IACF;AAEA,QAAI;AACF,WAAK,UAAU;AAEf,WAAK,kBAAiB;AACtB,WAAK,UAAU,MAAM;AAErB,aAAO,KAAK,aAAa,CAAC,GAAG;AAC3B,cAAM,KAAK,aAAY;MACzB;IACF;AACE,WAAK,UAAU;IACjB;EACF;EAEO,MAAM,YAAY,QAA4E;AACnG,QAAI,KAAK,SAAS;AAChB,YAAM,WAAW,KAAK,MAAK;AAC3B,aAAO,SAAS,YAAY,MAAM;IACpC;AAEA,QAAI;AACF,WAAK,UAAU;AAEf,UAAI,UAAU;AACd,UAAI;AACJ,uBAAiB,UAAU,QAAQ;AACjC,YAAI,SAAS;AACX,eAAK,UAAU;AACf,gBAAM,KAAK,qBAAqB,KAAK,QAAQ;QAC/C;AAEA,aAAK,aAAa,MAAM;AAExB,YAAI;AACF,mBAAS,KAAK,aAAY;AAC1B,oBAAU;QACZ,SAAS,GAAG;AACV,cAAI,EAAE,aAAa,aAAa;AAC9B,kBAAM;UACR;QAEF;AACA,aAAK,YAAY,KAAK;MACxB;AAEA,UAAI,SAAS;AACX,YAAI,KAAK,aAAa,CAAC,GAAG;AACxB,gBAAM,KAAK,qBAAqB,KAAK,QAAQ;QAC/C;AACA,eAAO;MACT;AAEA,YAAM,EAAE,UAAU,KAAK,SAAQ,IAAK;AACpC,YAAM,IAAI,WACR,gCAAgC,WAAW,QAAQ,CAAC,OAAO,QAAQ,KAAK,GAAG,yBAAyB;IAExG;AACE,WAAK,UAAU;IACjB;EACF;EAEO,kBACL,QAA4E;AAE5E,WAAO,KAAK,iBAAiB,QAAQ,IAAI;EAC3C;EAEO,aAAa,QAA4E;AAC9F,WAAO,KAAK,iBAAiB,QAAQ,KAAK;EAC5C;EAEQ,OAAO,iBAAiB,QAA8E,SAAgB;AAC5H,QAAI,KAAK,SAAS;AAChB,YAAM,WAAW,KAAK,MAAK;AAC3B,aAAO,SAAS,iBAAiB,QAAQ,OAAO;AAChD;IACF;AAEA,QAAI;AACF,WAAK,UAAU;AAEf,UAAI,wBAAwB;AAC5B,UAAI,iBAAiB;AAErB,uBAAiB,UAAU,QAAQ;AACjC,YAAI,WAAW,mBAAmB,GAAG;AACnC,gBAAM,KAAK,qBAAqB,KAAK,QAAQ;QAC/C;AAEA,aAAK,aAAa,MAAM;AAExB,YAAI,uBAAuB;AACzB,2BAAiB,KAAK,cAAa;AACnC,kCAAwB;AACxB,eAAK,SAAQ;QACf;AAEA,YAAI;AACF,iBAAO,MAAM;AACX,kBAAM,KAAK,aAAY;AACvB,gBAAI,EAAE,mBAAmB,GAAG;AAC1B;YACF;UACF;QACF,SAAS,GAAG;AACV,cAAI,EAAE,aAAa,aAAa;AAC9B,kBAAM;UACR;QAEF;AACA,aAAK,YAAY,KAAK;MACxB;IACF;AACE,WAAK,UAAU;IACjB;EACF;EAEQ,eAAY;AAClB,WAAQ,QAAO,MAAM;AACnB,YAAM,WAAW,KAAK,aAAY;AAClC,UAAI;AAEJ,UAAI,YAAY,KAAM;AAEpB,iBAAS,WAAW;MACtB,WAAW,WAAW,KAAM;AAC1B,YAAI,WAAW,KAAM;AAEnB,mBAAS;QACX,WAAW,WAAW,KAAM;AAE1B,gBAAM,OAAO,WAAW;AACxB,cAAI,SAAS,GAAG;AACd,iBAAK,aAAa,IAAI;AACtB,iBAAK,SAAQ;AACb,qBAAS;UACX,OAAO;AACL,qBAAS,CAAA;UACX;QACF,WAAW,WAAW,KAAM;AAE1B,gBAAM,OAAO,WAAW;AACxB,cAAI,SAAS,GAAG;AACd,iBAAK,eAAe,IAAI;AACxB,iBAAK,SAAQ;AACb,qBAAS;UACX,OAAO;AACL,qBAAS,CAAA;UACX;QACF,OAAO;AAEL,gBAAM,aAAa,WAAW;AAC9B,mBAAS,KAAK,aAAa,YAAY,CAAC;QAC1C;MACF,WAAW,aAAa,KAAM;AAE5B,iBAAS;MACX,WAAW,aAAa,KAAM;AAE5B,iBAAS;MACX,WAAW,aAAa,KAAM;AAE5B,iBAAS;MACX,WAAW,aAAa,KAAM;AAE5B,iBAAS,KAAK,QAAO;MACvB,WAAW,aAAa,KAAM;AAE5B,iBAAS,KAAK,QAAO;MACvB,WAAW,aAAa,KAAM;AAE5B,iBAAS,KAAK,OAAM;MACtB,WAAW,aAAa,KAAM;AAE5B,iBAAS,KAAK,QAAO;MACvB,WAAW,aAAa,KAAM;AAE5B,iBAAS,KAAK,QAAO;MACvB,WAAW,aAAa,KAAM;AAE5B,YAAI,KAAK,aAAa;AACpB,mBAAS,KAAK,gBAAe;QAC/B,OAAO;AACL,mBAAS,KAAK,QAAO;QACvB;MACF,WAAW,aAAa,KAAM;AAE5B,iBAAS,KAAK,OAAM;MACtB,WAAW,aAAa,KAAM;AAE5B,iBAAS,KAAK,QAAO;MACvB,WAAW,aAAa,KAAM;AAE5B,iBAAS,KAAK,QAAO;MACvB,WAAW,aAAa,KAAM;AAE5B,YAAI,KAAK,aAAa;AACpB,mBAAS,KAAK,gBAAe;QAC/B,OAAO;AACL,mBAAS,KAAK,QAAO;QACvB;MACF,WAAW,aAAa,KAAM;AAE5B,cAAM,aAAa,KAAK,OAAM;AAC9B,iBAAS,KAAK,aAAa,YAAY,CAAC;MAC1C,WAAW,aAAa,KAAM;AAE5B,cAAM,aAAa,KAAK,QAAO;AAC/B,iBAAS,KAAK,aAAa,YAAY,CAAC;MAC1C,WAAW,aAAa,KAAM;AAE5B,cAAM,aAAa,KAAK,QAAO;AAC/B,iBAAS,KAAK,aAAa,YAAY,CAAC;MAC1C,WAAW,aAAa,KAAM;AAE5B,cAAM,OAAO,KAAK,QAAO;AACzB,YAAI,SAAS,GAAG;AACd,eAAK,eAAe,IAAI;AACxB,eAAK,SAAQ;AACb,mBAAS;QACX,OAAO;AACL,mBAAS,CAAA;QACX;MACF,WAAW,aAAa,KAAM;AAE5B,cAAM,OAAO,KAAK,QAAO;AACzB,YAAI,SAAS,GAAG;AACd,eAAK,eAAe,IAAI;AACxB,eAAK,SAAQ;AACb,mBAAS;QACX,OAAO;AACL,mBAAS,CAAA;QACX;MACF,WAAW,aAAa,KAAM;AAE5B,cAAM,OAAO,KAAK,QAAO;AACzB,YAAI,SAAS,GAAG;AACd,eAAK,aAAa,IAAI;AACtB,eAAK,SAAQ;AACb,mBAAS;QACX,OAAO;AACL,mBAAS,CAAA;QACX;MACF,WAAW,aAAa,KAAM;AAE5B,cAAM,OAAO,KAAK,QAAO;AACzB,YAAI,SAAS,GAAG;AACd,eAAK,aAAa,IAAI;AACtB,eAAK,SAAQ;AACb,mBAAS;QACX,OAAO;AACL,mBAAS,CAAA;QACX;MACF,WAAW,aAAa,KAAM;AAE5B,cAAM,OAAO,KAAK,OAAM;AACxB,iBAAS,KAAK,aAAa,MAAM,CAAC;MACpC,WAAW,aAAa,KAAM;AAE5B,cAAM,OAAO,KAAK,QAAO;AACzB,iBAAS,KAAK,aAAa,MAAM,CAAC;MACpC,WAAW,aAAa,KAAM;AAE5B,cAAM,OAAO,KAAK,QAAO;AACzB,iBAAS,KAAK,aAAa,MAAM,CAAC;MACpC,WAAW,aAAa,KAAM;AAE5B,iBAAS,KAAK,gBAAgB,GAAG,CAAC;MACpC,WAAW,aAAa,KAAM;AAE5B,iBAAS,KAAK,gBAAgB,GAAG,CAAC;MACpC,WAAW,aAAa,KAAM;AAE5B,iBAAS,KAAK,gBAAgB,GAAG,CAAC;MACpC,WAAW,aAAa,KAAM;AAE5B,iBAAS,KAAK,gBAAgB,GAAG,CAAC;MACpC,WAAW,aAAa,KAAM;AAE5B,iBAAS,KAAK,gBAAgB,IAAI,CAAC;MACrC,WAAW,aAAa,KAAM;AAE5B,cAAM,OAAO,KAAK,OAAM;AACxB,iBAAS,KAAK,gBAAgB,MAAM,CAAC;MACvC,WAAW,aAAa,KAAM;AAE5B,cAAM,OAAO,KAAK,QAAO;AACzB,iBAAS,KAAK,gBAAgB,MAAM,CAAC;MACvC,WAAW,aAAa,KAAM;AAE5B,cAAM,OAAO,KAAK,QAAO;AACzB,iBAAS,KAAK,gBAAgB,MAAM,CAAC;MACvC,OAAO;AACL,cAAM,IAAI,YAAY,2BAA2B,WAAW,QAAQ,CAAC,EAAE;MACzE;AAEA,WAAK,SAAQ;AAEb,YAAM,QAAQ,KAAK;AACnB,aAAO,MAAM,SAAS,GAAG;AAEvB,cAAM,QAAQ,MAAM,IAAG;AACvB,YAAI,MAAM,SAAS,aAAa;AAC9B,gBAAM,MAAM,MAAM,QAAQ,IAAI;AAC9B,gBAAM;AACN,cAAI,MAAM,aAAa,MAAM,MAAM;AACjC,qBAAS,MAAM;AACf,kBAAM,QAAQ,KAAK;UACrB,OAAO;AACL,qBAAS;UACX;QACF,WAAW,MAAM,SAAS,eAAe;AACvC,cAAI,WAAW,aAAa;AAC1B,kBAAM,IAAI,YAAY,kCAAkC;UAC1D;AAEA,gBAAM,MAAM,KAAK,gBAAgB,MAAM;AACvC,gBAAM,OAAO;AACb,mBAAS;QACX,OAAO;AAGL,gBAAM,IAAI,MAAM,GAAI,IAAI;AACxB,gBAAM;AAEN,cAAI,MAAM,cAAc,MAAM,MAAM;AAClC,qBAAS,MAAM;AACf,kBAAM,QAAQ,KAAK;UACrB,OAAO;AACL,kBAAM,MAAM;AACZ,kBAAM,OAAO;AACb,qBAAS;UACX;QACF;MACF;AAEA,aAAO;IACT;EACF;EAEQ,eAAY;AAClB,QAAI,KAAK,aAAa,oBAAoB;AACxC,WAAK,WAAW,KAAK,OAAM;IAE7B;AAEA,WAAO,KAAK;EACd;EAEQ,WAAQ;AACd,SAAK,WAAW;EAClB;EAEQ,gBAAa;AACnB,UAAM,WAAW,KAAK,aAAY;AAElC,YAAQ,UAAU;MAChB,KAAK;AACH,eAAO,KAAK,QAAO;MACrB,KAAK;AACH,eAAO,KAAK,QAAO;MACrB,SAAS;AACP,YAAI,WAAW,KAAM;AACnB,iBAAO,WAAW;QACpB,OAAO;AACL,gBAAM,IAAI,YAAY,iCAAiC,WAAW,QAAQ,CAAC,EAAE;QAC/E;MACF;IACF;EACF;EAEQ,aAAa,MAAY;AAC/B,QAAI,OAAO,KAAK,cAAc;AAC5B,YAAM,IAAI,YAAY,oCAAoC,IAAI,2BAA2B,KAAK,YAAY,GAAG;IAC/G;AAEA,SAAK,MAAM,aAAa,IAAI;EAC9B;EAEQ,eAAe,MAAY;AACjC,QAAI,OAAO,KAAK,gBAAgB;AAC9B,YAAM,IAAI,YAAY,sCAAsC,IAAI,uBAAuB,KAAK,cAAc,GAAG;IAC/G;AAEA,SAAK,MAAM,eAAe,IAAI;EAChC;EAEQ,aAAa,YAAoB,cAAoB;AAC3D,QAAI,CAAC,KAAK,cAAc,KAAK,cAAa,GAAI;AAC5C,aAAO,KAAK,iBAAiB,YAAY,YAAY;IACvD;AACA,WAAO,KAAK,aAAa,YAAY,YAAY;EACnD;;;;EAKQ,iBAAiB,YAAoB,cAAoB;AAluBnE;AAmuBI,QAAI,aAAa,KAAK,cAAc;AAClC,YAAM,IAAI,YACR,2CAA2C,UAAU,qBAAqB,KAAK,YAAY,GAAG;IAElG;AAEA,QAAI,KAAK,MAAM,aAAa,KAAK,MAAM,eAAe,YAAY;AAChE,YAAM;IACR;AAEA,UAAM,SAAS,KAAK,MAAM;AAC1B,QAAI;AACJ,QAAI,KAAK,cAAa,OAAM,UAAK,eAAL,mBAAiB,YAAY,cAAa;AACpE,eAAS,KAAK,WAAW,OAAO,KAAK,OAAO,QAAQ,UAAU;IAChE,OAAO;AACL,eAAS,WAAW,KAAK,OAAO,QAAQ,UAAU;IACpD;AACA,SAAK,OAAO,eAAe;AAC3B,WAAO;EACT;EAEQ,gBAAa;AACnB,QAAI,KAAK,MAAM,SAAS,GAAG;AACzB,YAAM,QAAQ,KAAK,MAAM,IAAG;AAC5B,aAAO,MAAM,SAAS;IACxB;AACA,WAAO;EACT;;;;EAKQ,aAAa,YAAoB,YAAkB;AACzD,QAAI,aAAa,KAAK,cAAc;AAClC,YAAM,IAAI,YAAY,oCAAoC,UAAU,qBAAqB,KAAK,YAAY,GAAG;IAC/G;AAEA,QAAI,CAAC,KAAK,aAAa,aAAa,UAAU,GAAG;AAC/C,YAAM;IACR;AAEA,UAAM,SAAS,KAAK,MAAM;AAC1B,UAAM,SAAS,KAAK,MAAM,SAAS,QAAQ,SAAS,UAAU;AAC9D,SAAK,OAAO,aAAa;AACzB,WAAO;EACT;EAEQ,gBAAgB,MAAc,YAAkB;AACtD,QAAI,OAAO,KAAK,cAAc;AAC5B,YAAM,IAAI,YAAY,oCAAoC,IAAI,qBAAqB,KAAK,YAAY,GAAG;IACzG;AAEA,UAAM,UAAU,KAAK,KAAK,QAAQ,KAAK,MAAM,UAAU;AACvD,UAAM,OAAO,KAAK;MAAa;MAAM,aAAa;;IAAe;AACjE,WAAO,KAAK,eAAe,OAAO,MAAM,SAAS,KAAK,OAAO;EAC/D;EAEQ,SAAM;AACZ,WAAO,KAAK,KAAK,SAAS,KAAK,GAAG;EACpC;EAEQ,UAAO;AACb,WAAO,KAAK,KAAK,UAAU,KAAK,GAAG;EACrC;EAEQ,UAAO;AACb,WAAO,KAAK,KAAK,UAAU,KAAK,GAAG;EACrC;EAEQ,SAAM;AACZ,UAAM,QAAQ,KAAK,KAAK,SAAS,KAAK,GAAG;AACzC,SAAK;AACL,WAAO;EACT;EAEQ,SAAM;AACZ,UAAM,QAAQ,KAAK,KAAK,QAAQ,KAAK,GAAG;AACxC,SAAK;AACL,WAAO;EACT;EAEQ,UAAO;AACb,UAAM,QAAQ,KAAK,KAAK,UAAU,KAAK,GAAG;AAC1C,SAAK,OAAO;AACZ,WAAO;EACT;EAEQ,UAAO;AACb,UAAM,QAAQ,KAAK,KAAK,SAAS,KAAK,GAAG;AACzC,SAAK,OAAO;AACZ,WAAO;EACT;EAEQ,UAAO;AACb,UAAM,QAAQ,KAAK,KAAK,UAAU,KAAK,GAAG;AAC1C,SAAK,OAAO;AACZ,WAAO;EACT;EAEQ,UAAO;AACb,UAAM,QAAQ,KAAK,KAAK,SAAS,KAAK,GAAG;AACzC,SAAK,OAAO;AACZ,WAAO;EACT;EAEQ,UAAO;AACb,UAAM,QAAQ,UAAU,KAAK,MAAM,KAAK,GAAG;AAC3C,SAAK,OAAO;AACZ,WAAO;EACT;EAEQ,UAAO;AACb,UAAM,QAAQ,SAAS,KAAK,MAAM,KAAK,GAAG;AAC1C,SAAK,OAAO;AACZ,WAAO;EACT;EAEQ,kBAAe;AACrB,UAAM,QAAQ,KAAK,KAAK,aAAa,KAAK,GAAG;AAC7C,SAAK,OAAO;AACZ,WAAO;EACT;EAEQ,kBAAe;AACrB,UAAM,QAAQ,KAAK,KAAK,YAAY,KAAK,GAAG;AAC5C,SAAK,OAAO;AACZ,WAAO;EACT;EAEQ,UAAO;AACb,UAAM,QAAQ,KAAK,KAAK,WAAW,KAAK,GAAG;AAC3C,SAAK,OAAO;AACZ,WAAO;EACT;EAEQ,UAAO;AACb,UAAM,QAAQ,KAAK,KAAK,WAAW,KAAK,GAAG;AAC3C,SAAK,OAAO;AACZ,WAAO;EACT;;;;ACj2BI,SAAU,OACd,QACA,SAAqD;AAErD,QAAM,UAAU,IAAI,QAAQ,OAAO;AACnC,SAAO,QAAQ,OAAO,MAAM;AAC9B;AASM,SAAU,YACd,QACA,SAAqD;AAErD,QAAM,UAAU,IAAI,QAAQ,OAAO;AACnC,SAAO,QAAQ,YAAY,MAAM;AACnC;;;AC1BM,SAAU,gBAAmB,QAA6B;AAC9D,SAAQ,OAAe,OAAO,aAAa,KAAK;AAClD;AAEA,gBAAuB,wBAA2B,QAAyB;AACzE,QAAM,SAAS,OAAO,UAAS;AAE/B,MAAI;AACF,WAAO,MAAM;AACX,YAAM,EAAE,MAAM,MAAK,IAAK,MAAM,OAAO,KAAI;AACzC,UAAI,MAAM;AACR;MACF;AACA,YAAM;IACR;EACF;AACE,WAAO,YAAW;EACpB;AACF;AAEM,SAAU,oBAAuB,YAAiC;AACtE,MAAI,gBAAgB,UAAU,GAAG;AAC/B,WAAO;EACT,OAAO;AACL,WAAO,wBAAwB,UAAU;EAC3C;AACF;;;ACxBA,eAAsB,YACpB,YACA,SAAqD;AAErD,QAAM,SAAS,oBAAoB,UAAU;AAC7C,QAAM,UAAU,IAAI,QAAQ,OAAO;AACnC,SAAO,QAAQ,YAAY,MAAM;AACnC;AAMM,SAAU,kBACd,YACA,SAAqD;AAErD,QAAM,SAAS,oBAAoB,UAAU;AAC7C,QAAM,UAAU,IAAI,QAAQ,OAAO;AACnC,SAAO,QAAQ,kBAAkB,MAAM;AACzC;AAMM,SAAU,kBACd,YACA,SAAqD;AAErD,QAAM,SAAS,oBAAoB,UAAU;AAC7C,QAAM,UAAU,IAAI,QAAQ,OAAO;AACnC,SAAO,QAAQ,aAAa,MAAM;AACpC;", "names": ["encode", "decode", "size"]}