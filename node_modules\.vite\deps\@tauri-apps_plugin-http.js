import {
  Channel,
  invoke
} from "./chunk-Y3KOJBAE.js";
import "./chunk-DC5AMYBS.js";

// ../node_modules/.pnpm/@tauri-apps+plugin-http@2.5.1/node_modules/@tauri-apps/plugin-http/dist-js/index.js
var ERROR_REQUEST_CANCELLED = "Request cancelled";
async function fetch(input, init) {
  const signal = init == null ? void 0 : init.signal;
  if (signal == null ? void 0 : signal.aborted) {
    throw new Error(ERROR_REQUEST_CANCELLED);
  }
  const maxRedirections = init == null ? void 0 : init.maxRedirections;
  const connectTimeout = init == null ? void 0 : init.connectTimeout;
  const proxy = init == null ? void 0 : init.proxy;
  const danger = init == null ? void 0 : init.danger;
  if (init) {
    delete init.maxRedirections;
    delete init.connectTimeout;
    delete init.proxy;
    delete init.danger;
  }
  const headers = (init == null ? void 0 : init.headers) ? init.headers instanceof Headers ? init.headers : new Headers(init.headers) : new Headers();
  const req = new Request(input, init);
  const buffer = await req.arrayBuffer();
  const data = buffer.byteLength !== 0 ? Array.from(new Uint8Array(buffer)) : null;
  for (const [key, value] of req.headers) {
    if (!headers.get(key)) {
      headers.set(key, value);
    }
  }
  const headersArray = headers instanceof Headers ? Array.from(headers.entries()) : Array.isArray(headers) ? headers : Object.entries(headers);
  const mappedHeaders = headersArray.map(([name, val]) => [
    name,
    // we need to ensure we have all header values as strings
    // eslint-disable-next-line
    typeof val === "string" ? val : val.toString()
  ]);
  if (signal == null ? void 0 : signal.aborted) {
    throw new Error(ERROR_REQUEST_CANCELLED);
  }
  const rid = await invoke("plugin:http|fetch", {
    clientConfig: {
      method: req.method,
      url: req.url,
      headers: mappedHeaders,
      data,
      maxRedirections,
      connectTimeout,
      proxy,
      danger
    }
  });
  const abort = () => invoke("plugin:http|fetch_cancel", { rid });
  if (signal == null ? void 0 : signal.aborted) {
    abort();
    throw new Error(ERROR_REQUEST_CANCELLED);
  }
  signal == null ? void 0 : signal.addEventListener("abort", () => void abort());
  const { status, statusText, url, headers: responseHeaders, rid: responseRid } = await invoke("plugin:http|fetch_send", {
    rid
  });
  const body = [101, 103, 204, 205, 304].includes(status) ? null : new ReadableStream({
    start: (controller) => {
      const streamChannel = new Channel();
      streamChannel.onmessage = (res2) => {
        if (signal == null ? void 0 : signal.aborted) {
          controller.error(ERROR_REQUEST_CANCELLED);
          return;
        }
        const resUint8 = new Uint8Array(res2);
        const lastByte = resUint8[resUint8.byteLength - 1];
        const actualRes = resUint8.slice(0, resUint8.byteLength - 1);
        if (lastByte == 1) {
          controller.close();
          return;
        }
        controller.enqueue(actualRes);
      };
      invoke("plugin:http|fetch_read_body", {
        rid: responseRid,
        streamChannel
      }).catch((e) => {
        controller.error(e);
      });
    }
  });
  const res = new Response(body, {
    status,
    statusText
  });
  Object.defineProperty(res, "url", { value: url });
  Object.defineProperty(res, "headers", {
    value: new Headers(responseHeaders)
  });
  return res;
}
export {
  fetch
};
//# sourceMappingURL=@tauri-apps_plugin-http.js.map
