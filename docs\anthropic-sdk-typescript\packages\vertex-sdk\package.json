{"name": "@anthropic-ai/vertex-sdk", "version": "0.12.1", "description": "The official TypeScript library for the Anthropic Vertex API", "author": "Anthropic <<EMAIL>>", "types": "dist/index.d.ts", "main": "dist/index.js", "type": "commonjs", "repository": "github:anthropics/anthropic-sdk-typescript", "license": "MIT", "packageManager": "yarn@1.22.21", "private": false, "scripts": {"test": "echo 'no tests defined yet' && exit 1", "build": "bash ./build", "prepack": "echo 'to pack, run yarn build && (cd dist; yarn pack)' && exit 1", "prepublishOnly": "echo 'to publish, run yarn build && (cd dist; yarn publish)' && exit 1", "format": "prettier --write --cache --cache-strategy metadata . !dist", "prepare": "if [ $(basename $(dirname $PWD)) = 'node_modules' ]; then npm run build; fi", "tsn": "ts-node -r tsconfig-paths/register", "lint": "eslint --ext ts,js .", "fix": "eslint --fix --ext ts,js ."}, "dependencies": {"@anthropic-ai/sdk": "file:../../dist/", "google-auth-library": "^9.4.2"}, "devDependencies": {"@types/node": "^20.17.6", "@types/jest": "^29.4.0", "@typescript-eslint/eslint-plugin": "^6.7.0", "@typescript-eslint/parser": "^6.7.0", "eslint": "^8.49.0", "eslint-plugin-prettier": "^5.0.1", "eslint-plugin-unused-imports": "^3.0.0", "jest": "^29.4.0", "prettier": "^3.0.0", "ts-jest": "^29.1.0", "ts-morph": "^19.0.0", "ts-node": "^10.5.0", "tsc-multi": "https://github.com/stainless-api/tsc-multi/releases/download/v1.1.3/tsc-multi.tgz", "tsconfig-paths": "^4.0.0", "typescript": "^4.8.2"}, "imports": {"@anthropic-ai/vertex-sdk": ".", "@anthropic-ai/vertex-sdk/*": "./src/*"}, "exports": {".": {"require": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "types": "./dist/index.d.mts", "default": "./dist/index.mjs"}, "./*.mjs": {"types": "./dist/*.d.ts", "default": "./dist/*.mjs"}, "./*.js": {"types": "./dist/*.d.ts", "default": "./dist/*.js"}, "./*": {"types": "./dist/*.d.ts", "require": "./dist/*.js", "default": "./dist/*.mjs"}}}