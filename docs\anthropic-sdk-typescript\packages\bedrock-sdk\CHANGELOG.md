# Changelog

## 0.23.0 (2025-07-21)

Full Changelog: [bedrock-sdk-v0.22.4...bedrock-sdk-v0.23.0](https://github.com/anthropics/anthropic-sdk-typescript/compare/bedrock-sdk-v0.22.4...bedrock-sdk-v0.23.0)

### Features

* **bedrock:** better edge runtime support ([#462](https://github.com/anthropics/anthropic-sdk-typescript/issues/462)) ([5f8d1bb](https://github.com/anthropics/anthropic-sdk-typescript/commit/5f8d1bb6a1e7a71acf7a89a090bb2296c050e1d2))


### Bug Fixes

* **bedrock:** fix lint errors ([aa40e9c](https://github.com/anthropics/anthropic-sdk-typescript/commit/aa40e9c6987a23ca964683a72fb89c9f570528bd))

## 0.22.4 (2025-07-03)

Full Changelog: [bedrock-sdk-v0.22.3...bedrock-sdk-v0.22.4](https://github.com/anthropics/anthropic-sdk-typescript/compare/bedrock-sdk-v0.22.3...bedrock-sdk-v0.22.4)

### Bug Fixes

* **bedrock:** fix bedrock logger ([f183bc9](https://github.com/anthropics/anthropic-sdk-typescript/commit/f183bc97bc5a83b0e00053740caa387a1c98c322))

## 0.22.3 (2025-06-30)

Full Changelog: [bedrock-sdk-v0.22.2...bedrock-sdk-v0.22.3](https://github.com/anthropics/anthropic-sdk-typescript/compare/bedrock-sdk-v0.22.2...bedrock-sdk-v0.22.3)

### Bug Fixes

* **client:** use proxy in bedrock when requesting credentials from AWS ([8cfd227](https://github.com/anthropics/anthropic-sdk-typescript/commit/8cfd227dd82686101ea54d5d32a0c1d6ad04d336))

## 0.22.2 (2025-06-24)

Full Changelog: [bedrock-sdk-v0.22.1...bedrock-sdk-v0.22.2](https://github.com/anthropics/anthropic-sdk-typescript/compare/bedrock-sdk-v0.22.1...bedrock-sdk-v0.22.2)

## 0.22.1 (2025-05-22)

Full Changelog: [bedrock-sdk-v0.22.0...bedrock-sdk-v0.22.1](https://github.com/anthropics/anthropic-sdk-typescript/compare/bedrock-sdk-v0.22.0...bedrock-sdk-v0.22.1)

### Chores

* **internal:** version bump ([8ebaf61](https://github.com/anthropics/anthropic-sdk-typescript/commit/8ebaf616d2e5c6aebc153f19a403dde41ab5a9f1))

## 0.22.0 (2025-05-15)

Full Changelog: [bedrock-sdk-v0.21.2...bedrock-sdk-v0.22.0](https://github.com/anthropics/anthropic-sdk-typescript/compare/bedrock-sdk-v0.21.2...bedrock-sdk-v0.22.0)

### Features

* **bedrock:** support skipAuth on Bedrock client to bypass local auth requirements ([b661c5f](https://github.com/anthropics/anthropic-sdk-typescript/commit/b661c5fe4d93fa749de5b7cbbce98dc224a68adc))


### Bug Fixes

* **bedrock:** support model names with slashes ([cb5fa8a](https://github.com/anthropics/anthropic-sdk-typescript/commit/cb5fa8a8f55ed12382aeb5f09110b0d5fefc46bb))

## 0.21.2 (2025-05-09)

Full Changelog: [bedrock-sdk-v0.21.1...bedrock-sdk-v0.21.2](https://github.com/anthropics/anthropic-sdk-typescript/compare/bedrock-sdk-v0.21.1...bedrock-sdk-v0.21.2)

### Bug Fixes

* **client:** always overwrite when merging headers ([657912a](https://github.com/anthropics/anthropic-sdk-typescript/commit/657912ad66f86e878291a4cab5436844efbb633b))

## 0.21.1 (2025-05-09)

Full Changelog: [bedrock-sdk-v0.21.0...bedrock-sdk-v0.21.1](https://github.com/anthropics/anthropic-sdk-typescript/compare/bedrock-sdk-v0.21.0...bedrock-sdk-v0.21.1)

### Chores

* sync repo ([508e385](https://github.com/anthropics/anthropic-sdk-typescript/commit/508e38511c13ba8842065d0dafbc7f462abe0322))

## 0.21.0 (2025-05-09)

Full Changelog: [bedrock-sdk-v0.20.0...bedrock-sdk-v0.21.0](https://github.com/anthropics/anthropic-sdk-typescript/compare/bedrock-sdk-v0.20.0...bedrock-sdk-v0.21.0)

### Features

* **bedrock:** add beta.messages.create() method ([faf8484](https://github.com/anthropics/anthropic-sdk-typescript/commit/faf84848e42ec02994003283bf6178aa55233531))


### Bug Fixes

* **bedrock,vertex:** update to new SDK version ([cb620bb](https://github.com/anthropics/anthropic-sdk-typescript/commit/cb620bbb60e64f27168f74db7a48ad8c2d5f953e))
* **bedrock:** correct messages beta handling ([f34d67a](https://github.com/anthropics/anthropic-sdk-typescript/commit/f34d67abca1270f04f9bbf6ce430ce67b36d4932))
* **bedrock:** don't mutate request body inputs ([4523ca9](https://github.com/anthropics/anthropic-sdk-typescript/commit/4523ca92b0fa194c2de0ac8bbc7e66873ec5e817))
* **bedrock:** update streaming util import ([01d03bf](https://github.com/anthropics/anthropic-sdk-typescript/commit/01d03bf79615de601f8bb9284df818e7d066aafa))
* **streaming:** handle more AbortError cases ([521d6cd](https://github.com/anthropics/anthropic-sdk-typescript/commit/521d6cda1c43bad9b0ef110809e0d3e7cd411a0d))
* **vertex,bedrock:** correct build script ([df895a7](https://github.com/anthropics/anthropic-sdk-typescript/commit/df895a7d7cbd51422343fca95ab9f1d58918b2a1))


### Chores

* **bedrock,vertex:** remove unsupported countTokens method ([#597](https://github.com/anthropics/anthropic-sdk-typescript/issues/597)) ([6f6db16](https://github.com/anthropics/anthropic-sdk-typescript/commit/6f6db164d2526c2fb272151f4d68140da27ce1ea))
* **bedrock:** add `skipAuth` option to allow users to let authorization be handled elsewhere ([ee58772](https://github.com/anthropics/anthropic-sdk-typescript/commit/ee587723718109797efa80b020076c43a300f1b9))
* **bedrock:** bump [@aws-sdk](https://github.com/aws-sdk) dependencies ([ff925db](https://github.com/anthropics/anthropic-sdk-typescript/commit/ff925db987a66950c997ec50b9c55e67152d1945))
* **bedrock:** bump [@aws-sdk](https://github.com/aws-sdk) dependencies ([9891107](https://github.com/anthropics/anthropic-sdk-typescript/commit/98911074a745bfefe29f26c725bf54b6e228c93c))
* **bedrock:** bump @aws-sdk/credential-providers ([9f611d6](https://github.com/anthropics/anthropic-sdk-typescript/commit/9f611d6ba8c6b3de02c3183e4f2f42d1540525f0))
* **bedrock:** bump @aws-sdk/credential-providers ([491646e](https://github.com/anthropics/anthropic-sdk-typescript/commit/491646e10a48a09c0828d0fb23908f43f38d8e6a))
* **bedrock:** bump dependency on @anthropic-ai/sdk ([2d0d4b6](https://github.com/anthropics/anthropic-sdk-typescript/commit/2d0d4b61368286581e281b604e005fc81139b6b9))
* **bedrock:** remove unsupported methods ([8bb04ed](https://github.com/anthropics/anthropic-sdk-typescript/commit/8bb04ed2370c894b080d56d37e960ad4c3dc5925))
* **internal:** migrate to eslint v9 ([1141664](https://github.com/anthropics/anthropic-sdk-typescript/commit/114166498a54e0b62cd9ea071fc736d714cefde6))


### Documentation

* use latest sonnet in example snippets ([#625](https://github.com/anthropics/anthropic-sdk-typescript/issues/625)) ([a965791](https://github.com/anthropics/anthropic-sdk-typescript/commit/a9657918aaf1246609105cbafaf4bb043b146356))

## 0.20.0 (2025-05-09)

Full Changelog: [bedrock-sdk-v0.12.6...bedrock-sdk-v0.12.7](https://github.com/anthropics/anthropic-sdk-typescript/compare/bedrock-sdk-v0.12.6...bedrock-sdk-v0.12.7)

### Bug Fixes

* **bedrock,vertex:** update to new SDK version ([cb620bb](https://github.com/anthropics/anthropic-sdk-typescript/commit/cb620bbb60e64f27168f74db7a48ad8c2d5f953e))
* **vertex,bedrock:** correct build script ([df895a7](https://github.com/anthropics/anthropic-sdk-typescript/commit/df895a7d7cbd51422343fca95ab9f1d58918b2a1))


### Chores

* **bedrock:** add `skipAuth` option to allow users to let authorization be handled elsewhere ([ee58772](https://github.com/anthropics/anthropic-sdk-typescript/commit/ee587723718109797efa80b020076c43a300f1b9))
* **bedrock:** bump [@aws-sdk](https://github.com/aws-sdk) dependencies ([ff925db](https://github.com/anthropics/anthropic-sdk-typescript/commit/ff925db987a66950c997ec50b9c55e67152d1945))
* **bedrock:** bump @aws-sdk/credential-providers ([9f611d6](https://github.com/anthropics/anthropic-sdk-typescript/commit/9f611d6ba8c6b3de02c3183e4f2f42d1540525f0))

## 0.12.6 (2025-04-28)

Full Changelog: [bedrock-sdk-v0.12.5...bedrock-sdk-v0.12.6](https://github.com/anthropics/anthropic-sdk-typescript/compare/bedrock-sdk-v0.12.5...bedrock-sdk-v0.12.6)

### Chores

* **bedrock:** bump [@aws-sdk](https://github.com/aws-sdk) dependencies ([6440e1d](https://github.com/anthropics/anthropic-sdk-typescript/commit/6440e1db992779aeead231b420a55002ec0991d1))

## 0.12.5 (2025-04-25)

Full Changelog: [bedrock-sdk-v0.12.4...bedrock-sdk-v0.12.5](https://github.com/anthropics/anthropic-sdk-typescript/compare/bedrock-sdk-v0.12.4...bedrock-sdk-v0.12.5)

### Chores

* **bedrock:** bump @aws-sdk/credential-providers ([a4d88d7](https://github.com/anthropics/anthropic-sdk-typescript/commit/a4d88d7807e2cf1a0836dc05d9deff5bfc0c665f))

## 0.12.4 (2025-01-23)

Full Changelog: [bedrock-sdk-v0.12.3...bedrock-sdk-v0.12.4](https://github.com/anthropics/anthropic-sdk-typescript/compare/bedrock-sdk-v0.12.3...bedrock-sdk-v0.12.4)

### Bug Fixes

* **bedrock:** update streaming util import ([255c059](https://github.com/anthropics/anthropic-sdk-typescript/commit/255c0599e333e6fae582255e3b0631538b168c69))

## 0.12.3 (2025-01-23)

Full Changelog: [bedrock-sdk-v0.12.2...bedrock-sdk-v0.12.3](https://github.com/anthropics/anthropic-sdk-typescript/compare/bedrock-sdk-v0.12.2...bedrock-sdk-v0.12.3)

### Chores

* **bedrock:** bump dependency on @anthropic-ai/sdk ([8745ca2](https://github.com/anthropics/anthropic-sdk-typescript/commit/8745ca2160f5af4d89e5732a44e560c5e2787aa8))

## 0.12.2 (2025-01-21)

Full Changelog: [bedrock-sdk-v0.12.1...bedrock-sdk-v0.12.2](https://github.com/anthropics/anthropic-sdk-typescript/compare/bedrock-sdk-v0.12.1...bedrock-sdk-v0.12.2)

### Chores

* **internal:** temporary revert commit ([#643](https://github.com/anthropics/anthropic-sdk-typescript/issues/643)) ([43dd43c](https://github.com/anthropics/anthropic-sdk-typescript/commit/43dd43c4c8ab69d5a60e59473af7dff5f7799048))

## 0.12.1 (2024-12-20)

Full Changelog: [bedrock-sdk-v0.12.0...bedrock-sdk-v0.12.1](https://github.com/anthropics/anthropic-sdk-typescript/compare/bedrock-sdk-v0.12.0...bedrock-sdk-v0.12.1)

### Chores

* **internal:** temporary revert commit ([#643](https://github.com/anthropics/anthropic-sdk-typescript/issues/643)) ([8057b1e](https://github.com/anthropics/anthropic-sdk-typescript/commit/8057b1eb67ccccee042a45f2efe53cccced15682))

## 0.12.0 (2024-12-17)

Full Changelog: [bedrock-sdk-v0.11.2...bedrock-sdk-v0.12.0](https://github.com/anthropics/anthropic-sdk-typescript/compare/bedrock-sdk-v0.11.2...bedrock-sdk-v0.12.0)

### Features

* **api:** general availability updates ([#631](https://github.com/anthropics/anthropic-sdk-typescript/issues/631)) ([b5c92e5](https://github.com/anthropics/anthropic-sdk-typescript/commit/b5c92e5b74c370ac3f9ba28e915bd54588a42be0))


### Chores

* **bedrock,vertex:** remove unsupported countTokens method ([#597](https://github.com/anthropics/anthropic-sdk-typescript/issues/597)) ([17b7da5](https://github.com/anthropics/anthropic-sdk-typescript/commit/****************************************))
* **bedrock:** remove unsupported methods ([6458dc1](https://github.com/anthropics/anthropic-sdk-typescript/commit/6458dc14544c16240a6580a21a36fcf5bde594b2))


### Documentation

* use latest sonnet in example snippets ([#625](https://github.com/anthropics/anthropic-sdk-typescript/issues/625)) ([f70882b](https://github.com/anthropics/anthropic-sdk-typescript/commit/f70882b0e8119a414b01b9f0b85fbe1ccb06f122))

## 0.11.2 (2024-11-05)

Full Changelog: [bedrock-sdk-v0.11.1...bedrock-sdk-v0.11.2](https://github.com/anthropics/anthropic-sdk-typescript/compare/bedrock-sdk-v0.11.1...bedrock-sdk-v0.11.2)

### Bug Fixes

* **bedrock:** don't mutate request body inputs ([f83b535](https://github.com/anthropics/anthropic-sdk-typescript/commit/f83b53520262219229cecc388f95d92be83c09d5))

## 0.11.1 (2024-10-23)

Full Changelog: [bedrock-sdk-v0.11.0...bedrock-sdk-v0.11.1](https://github.com/anthropics/anthropic-sdk-typescript/compare/bedrock-sdk-v0.11.0...bedrock-sdk-v0.11.1)

### Bug Fixes

* **bedrock:** correct messages beta handling ([9b57586](https://github.com/anthropics/anthropic-sdk-typescript/commit/9b57586456221f8900902b8e85c7c017959c150a))

## 0.11.0 (2024-10-22)

Full Changelog: [bedrock-sdk-v0.10.4...bedrock-sdk-v0.11.0](https://github.com/anthropics/anthropic-sdk-typescript/compare/bedrock-sdk-v0.10.4...bedrock-sdk-v0.11.0)

### Features

* **bedrock:** add beta.messages.create() method ([6317592](https://github.com/anthropics/anthropic-sdk-typescript/commit/63175920a016a2ad187dd1127d263357cf6c007e))

## 0.10.4 (2024-10-08)

Full Changelog: [bedrock-sdk-v0.10.3...bedrock-sdk-v0.10.4](https://github.com/anthropics/anthropic-sdk-typescript/compare/bedrock-sdk-v0.10.3...bedrock-sdk-v0.10.4)

### Refactors

* **types:** improve metadata type names ([#547](https://github.com/anthropics/anthropic-sdk-typescript/issues/547)) ([cef499c](https://github.com/anthropics/anthropic-sdk-typescript/commit/cef499cf3b01643f7e5e3c09524f49e198b940be))

## 0.10.3 (2024-10-04)

Full Changelog: [bedrock-sdk-v0.10.2...bedrock-sdk-v0.10.3](https://github.com/anthropics/anthropic-sdk-typescript/compare/bedrock-sdk-v0.10.2...bedrock-sdk-v0.10.3)

### Chores

* better object fallback behaviour for casting errors ([#526](https://github.com/anthropics/anthropic-sdk-typescript/issues/526)) ([4ffb2e4](https://github.com/anthropics/anthropic-sdk-typescript/commit/4ffb2e4e1f5fef3ae58d9f4c99a63e75dd459c5b))

## 0.10.2 (2024-07-29)

Full Changelog: [bedrock-sdk-v0.10.1...bedrock-sdk-v0.10.2](https://github.com/anthropics/anthropic-sdk-typescript/compare/bedrock-sdk-v0.10.1...bedrock-sdk-v0.10.2)

### Chores

* **bedrock:** use `chunk` for internal SSE parsing instead of `completion` ([#472](https://github.com/anthropics/anthropic-sdk-typescript/issues/472)) ([0f6190a](https://github.com/anthropics/anthropic-sdk-typescript/commit/0f6190a69d8986ac3779441eba43d345ec3fb342))
* **docs:** rename anthropic const to client ([#471](https://github.com/anthropics/anthropic-sdk-typescript/issues/471)) ([e1a7f9f](https://github.com/anthropics/anthropic-sdk-typescript/commit/e1a7f9f813077fb033c732c004c7bda85738a321))
* **internal:** remove old reference to check-test-server ([8dc9afc](https://github.com/anthropics/anthropic-sdk-typescript/commit/8dc9afcf00c4a38c2d85171ebceafc5f6a47c117))

## 0.10.1 (2024-06-25)

Full Changelog: [bedrock-sdk-v0.10.0...bedrock-sdk-v0.10.1](https://github.com/anthropics/anthropic-sdk-typescript/compare/bedrock-sdk-v0.10.0...bedrock-sdk-v0.10.1)

### Chores

* **internal:** replace deprecated aws-sdk packages with [@smithy](https://github.com/smithy) ([#447](https://github.com/anthropics/anthropic-sdk-typescript/issues/447)) ([4328cbf](https://github.com/anthropics/anthropic-sdk-typescript/commit/4328cbf9e64f8bfc9b95a9048b18729c9a938ba5))

## 0.10.0 (2024-05-30)

Full Changelog: [bedrock-sdk-v0.9.8...bedrock-sdk-v0.10.0](https://github.com/anthropics/anthropic-sdk-typescript/compare/bedrock-sdk-v0.9.8...bedrock-sdk-v0.10.0)

### Features

* **bedrock:** support tools ([91fc61a](https://github.com/anthropics/anthropic-sdk-typescript/commit/91fc61ae7246705d26e96a95dae38b46e9ad9290))

## 0.9.8 (2024-05-16)

Full Changelog: [bedrock-sdk-v0.9.7...bedrock-sdk-v0.9.8](https://github.com/anthropics/anthropic-sdk-typescript/compare/bedrock-sdk-v0.9.7...bedrock-sdk-v0.9.8)

### Chores

* **internal:** fix generated version numbers ([#413](https://github.com/anthropics/anthropic-sdk-typescript/issues/413)) ([ea77063](https://github.com/anthropics/anthropic-sdk-typescript/commit/ea770630897bb85caaecd39bccf478e4dd3f169c))

## 0.9.7 (2024-05-07)

Full Changelog: [bedrock-sdk-v0.9.6...bedrock-sdk-v0.9.7](https://github.com/anthropics/anthropic-sdk-typescript/compare/bedrock-sdk-v0.9.6...bedrock-sdk-v0.9.7)

### Chores

* **internal:** refactor scripts ([#404](https://github.com/anthropics/anthropic-sdk-typescript/issues/404)) ([f60e2d8](https://github.com/anthropics/anthropic-sdk-typescript/commit/f60e2d81bb241063507d2d7e728c78e78c1c5e51))

## 0.9.6 (2024-04-09)

Full Changelog: [bedrock-sdk-v0.9.5...bedrock-sdk-v0.9.6](https://github.com/anthropics/anthropic-sdk-typescript/compare/bedrock-sdk-v0.9.5...bedrock-sdk-v0.9.6)

### Chores

* **internal:** update lock files ([#377](https://github.com/anthropics/anthropic-sdk-typescript/issues/377)) ([6d239ef](https://github.com/anthropics/anthropic-sdk-typescript/commit/6d239efaca730baba374a1b49f6b1a4037b3e163))

## 0.9.5 (2024-04-04)

Full Changelog: [bedrock-sdk-v0.9.4...bedrock-sdk-v0.9.5](https://github.com/anthropics/anthropic-sdk-typescript/compare/bedrock-sdk-v0.9.4...bedrock-sdk-v0.9.5)

### Documentation

* **readme:** mention tool use ([#375](https://github.com/anthropics/anthropic-sdk-typescript/issues/375)) ([72356dd](https://github.com/anthropics/anthropic-sdk-typescript/commit/72356dd9c498344074c292ffdab602d54c4fa13e))

## 0.9.4 (2024-04-04)

Full Changelog: [bedrock-sdk-v0.9.3...bedrock-sdk-v0.9.4](https://github.com/anthropics/anthropic-sdk-typescript/compare/bedrock-sdk-v0.9.3...bedrock-sdk-v0.9.4)

### Bug Fixes

* **types:** correctly mark type as a required property in requests ([#371](https://github.com/anthropics/anthropic-sdk-typescript/issues/371)) ([a04edd8](https://github.com/anthropics/anthropic-sdk-typescript/commit/a04edd8d7f4c552281b37a44099edf432d7fcb27))

## 0.9.3 (2024-04-04)

Full Changelog: [bedrock-sdk-v0.9.2...bedrock-sdk-v0.9.3](https://github.com/anthropics/anthropic-sdk-typescript/compare/bedrock-sdk-v0.9.2...bedrock-sdk-v0.9.3)

### Chores

* **deps:** remove unused dependency digest-fetch ([#368](https://github.com/anthropics/anthropic-sdk-typescript/issues/368)) ([df1df0f](https://github.com/anthropics/anthropic-sdk-typescript/commit/df1df0f509682841c703fa1ea5062a796cfe2091))

## 0.9.2 (2024-03-29)

Full Changelog: [bedrock-sdk-v0.9.1...bedrock-sdk-v0.9.2](https://github.com/anthropics/anthropic-sdk-typescript/compare/bedrock-sdk-v0.9.1...bedrock-sdk-v0.9.2)

### Documentation

* **bedrock:** fix dead link ([#356](https://github.com/anthropics/anthropic-sdk-typescript/issues/356)) ([a953e00](https://github.com/anthropics/anthropic-sdk-typescript/commit/a953e0070698f3238b728ffe06a056a9f2d6b7ff))

## 0.9.1 (2024-03-06)

Full Changelog: [bedrock-sdk-v0.9.0...bedrock-sdk-v0.9.1](https://github.com/anthropics/anthropic-sdk-typescript/compare/bedrock-sdk-v0.9.0...bedrock-sdk-v0.9.1)

### Documentation

* remove extraneous --save and yarn install instructions ([#323](https://github.com/anthropics/anthropic-sdk-typescript/issues/323)) ([775ecb9](https://github.com/anthropics/anthropic-sdk-typescript/commit/775ecb9ef3ab17e88dabc149faa0876cd6ab5f0b))

## 0.9.0 (2024-03-04)

Full Changelog: [bedrock-sdk-v0.8.0...bedrock-sdk-v0.9.0](https://github.com/anthropics/anthropic-sdk-typescript/compare/bedrock-sdk-v0.8.0...bedrock-sdk-v0.9.0)

### Features

* **bedrock:** add messages API ([#305](https://github.com/anthropics/anthropic-sdk-typescript/issues/305)) ([8b7f89e](https://github.com/anthropics/anthropic-sdk-typescript/commit/8b7f89e1e60416f9ad5b575d43238a4259654395))

## 0.8.0 (2024-03-04)

Full Changelog: [bedrock-sdk-v0.7.1...bedrock-sdk-v0.8.0](https://github.com/anthropics/anthropic-sdk-typescript/compare/bedrock-sdk-v0.7.1...bedrock-sdk-v0.8.0)

### Features

* **messages:** add support for image inputs ([#303](https://github.com/anthropics/anthropic-sdk-typescript/issues/303)) ([7663bd6](https://github.com/anthropics/anthropic-sdk-typescript/commit/7663bd6e1a4427483cf5f13889bc5c63314e5bae))

## 0.7.1 (2024-01-31)

Full Changelog: [bedrock-sdk-v0.7.0...bedrock-sdk-v0.7.1](https://github.com/anthropics/anthropic-sdk-typescript/compare/bedrock-sdk-v0.7.0...bedrock-sdk-v0.7.1)

### Chores

* **bedrock:** move bedrock SDK to the main repo ([#274](https://github.com/anthropics/anthropic-sdk-typescript/issues/274)) ([1a565fe](https://github.com/anthropics/anthropic-sdk-typescript/commit/1a565feddd19c3dbe62f087fc9f13520bb69fc0e))
* release main ([6679340](https://github.com/anthropics/anthropic-sdk-typescript/commit/6679340c68b7f1599e5a9a543371f7426f96307a))

## 0.7.0 (2024-01-31)

This release restructures the SDK so that it relies on the main `@anthropic-ai/sdk` instead of duplicating everything.

- All subpath imports are broken, e.g. `import { Completion } from '@anthropic-ai/bedrock-sdk/resources/completions'`
- Types are no longer exported through the default import, e.g. `AnthropicBedrock.Completion`
  - e.g. AnthropicBedrock.APIError, AnthropicBedrock.HUMAN_PROMPT

However, these are all an straightforward fixes, you just have to replace `@anthropic-ai/bedrock-sdk` with `@anthropic-ai/sdk`, e.g.

```diff
- import AnthropicBedrock from '@anthropic-ai/bedrock-sdk';
+ import Anthropic from '@anthropic-ai/sdk';

- type Completion = AnthropicBedrock.Completion;
+ type Completion = Anthropic.Completion;
```

## 0.6.5 (2024-01-30)

Full Changelog: [v0.6.4...v0.6.5](https://github.com/anthropics/anthropic-bedrock-typescript/compare/v0.6.4...v0.6.5)

### Chores

* **internal:** support pre-release versioning ([#77](https://github.com/anthropics/anthropic-bedrock-typescript/issues/77)) ([b96f745](https://github.com/anthropics/anthropic-bedrock-typescript/commit/b96f745cf406677a552c863b2c5ee967f3353919))

## 0.6.4 (2024-01-25)

Full Changelog: [v0.6.3...v0.6.4](https://github.com/anthropics/anthropic-bedrock-typescript/compare/v0.6.3...v0.6.4)

### Chores

* **internal:** add internal helpers & improve build scripts ([#73](https://github.com/anthropics/anthropic-bedrock-typescript/issues/73)) ([d12b655](https://github.com/anthropics/anthropic-bedrock-typescript/commit/d12b655286e3677e4a24a5616bb633b553fa5784))
* **internal:** don't re-export streaming type ([#76](https://github.com/anthropics/anthropic-bedrock-typescript/issues/76)) ([92fb967](https://github.com/anthropics/anthropic-bedrock-typescript/commit/92fb967c40d8c730441fc3b42bef92bc478436aa))
* **internal:** minor streaming updates ([#75](https://github.com/anthropics/anthropic-bedrock-typescript/issues/75)) ([73bac4c](https://github.com/anthropics/anthropic-bedrock-typescript/commit/73bac4c4f2f6edc4b7a72274cd3146c5821b53a8))

## 0.6.3 (2024-01-19)

Full Changelog: [v0.6.2...v0.6.3](https://github.com/anthropics/anthropic-bedrock-typescript/compare/v0.6.2...v0.6.3)

### Bug Fixes

* allow body type in RequestOptions to be null ([#71](https://github.com/anthropics/anthropic-bedrock-typescript/issues/71)) ([a04f753](https://github.com/anthropics/anthropic-bedrock-typescript/commit/a04f7538e789324cebcea61476c1d745bbfc30cf))

## 0.6.2 (2024-01-18)

Full Changelog: [v0.6.1...v0.6.2](https://github.com/anthropics/anthropic-bedrock-typescript/compare/v0.6.1...v0.6.2)

### Bug Fixes

* **ci:** ignore stainless-app edits to release PR title ([#70](https://github.com/anthropics/anthropic-bedrock-typescript/issues/70)) ([c3a058c](https://github.com/anthropics/anthropic-bedrock-typescript/commit/c3a058cccfdc0f0db1fbf06c57c13374bad58015))
* **headers:** always send lowercase headers and strip undefined (BREAKING in rare cases) ([#60](https://github.com/anthropics/anthropic-bedrock-typescript/issues/60)) ([9cc4518](https://github.com/anthropics/anthropic-bedrock-typescript/commit/9cc4518ebd41d6ee438e686f4d0da2629e511796))
* **types:** accept undefined for optional client options ([#69](https://github.com/anthropics/anthropic-bedrock-typescript/issues/69)) ([cf597f6](https://github.com/anthropics/anthropic-bedrock-typescript/commit/cf597f6384337109011cd7d920b6a6530cdef74f))
* use default base url if BASE_URL env var is blank ([#64](https://github.com/anthropics/anthropic-bedrock-typescript/issues/64)) ([134bf8f](https://github.com/anthropics/anthropic-bedrock-typescript/commit/134bf8f35d6071224280c9bed6151e107fee3c93))


### Chores

* add .keep files for examples and custom code directories ([#63](https://github.com/anthropics/anthropic-bedrock-typescript/issues/63)) ([0064f30](https://github.com/anthropics/anthropic-bedrock-typescript/commit/0064f3035cf4a11645c632abf1e026ee27ac92a2))
* **internal:** debug logging for retries; speculative retry-after-ms support ([#68](https://github.com/anthropics/anthropic-bedrock-typescript/issues/68)) ([e6a95f6](https://github.com/anthropics/anthropic-bedrock-typescript/commit/e6a95f644a300c5e3d5856edcaf98c60c953e461))
* **internal:** improve type signatures ([#62](https://github.com/anthropics/anthropic-bedrock-typescript/issues/62)) ([6e24bdc](https://github.com/anthropics/anthropic-bedrock-typescript/commit/6e24bdc7ee82252243c0f83c68c5b6a363d756fc))
* **internal:** narrow type into stringifyQuery ([#65](https://github.com/anthropics/anthropic-bedrock-typescript/issues/65)) ([443febf](https://github.com/anthropics/anthropic-bedrock-typescript/commit/443febf02c4c71b28934a776da0198fdd094de8f))


### Documentation

* fix missing async in readme code sample ([#67](https://github.com/anthropics/anthropic-bedrock-typescript/issues/67)) ([a6d20eb](https://github.com/anthropics/anthropic-bedrock-typescript/commit/a6d20eb8d3d4c6be9d92afd4eb7d93ea5c661094))
* **readme:** improve api reference ([#66](https://github.com/anthropics/anthropic-bedrock-typescript/issues/66)) ([f90bbaf](https://github.com/anthropics/anthropic-bedrock-typescript/commit/f90bbaf631e9ac34fb613a43ece1f489d82f04b8))

## 0.6.1 (2023-12-20)

Full Changelog: [v0.6.0...v0.6.1](https://github.com/anthropics/anthropic-bedrock-typescript/compare/v0.6.0...v0.6.1)

### Chores

* **ci:** run release workflow once per day ([#54](https://github.com/anthropics/anthropic-bedrock-typescript/issues/54)) ([b5072dd](https://github.com/anthropics/anthropic-bedrock-typescript/commit/b5072ddc0c068737cdca2dd6ecfcb231ac7edc1b))
* **deps:** update dependency ts-jest to v29.1.1 ([#55](https://github.com/anthropics/anthropic-bedrock-typescript/issues/55)) ([946a832](https://github.com/anthropics/anthropic-bedrock-typescript/commit/946a8326410b47143f2c9e611e6b62866d5d9734))
* **deps:** update jest ([#56](https://github.com/anthropics/anthropic-bedrock-typescript/issues/56)) ([492232d](https://github.com/anthropics/anthropic-bedrock-typescript/commit/492232de92f7d79b42869ca619cc85ae4a1b7966))
* **internal:** update test examples ([#50](https://github.com/anthropics/anthropic-bedrock-typescript/issues/50)) ([33132ea](https://github.com/anthropics/anthropic-bedrock-typescript/commit/33132eaab93f2fa39cc9e975df0b0323bb486395))
* update dependencies ([#53](https://github.com/anthropics/anthropic-bedrock-typescript/issues/53)) ([4a72bd7](https://github.com/anthropics/anthropic-bedrock-typescript/commit/4a72bd7f857a7024ff0e8caf574e92998431f4f3))
* update prettier ([#52](https://github.com/anthropics/anthropic-bedrock-typescript/issues/52)) ([acfe9e4](https://github.com/anthropics/anthropic-bedrock-typescript/commit/acfe9e46299275fc14c9a704c7c048c87e0e33db))


### Documentation

* reformat README.md ([#58](https://github.com/anthropics/anthropic-bedrock-typescript/issues/58)) ([050b328](https://github.com/anthropics/anthropic-bedrock-typescript/commit/050b328725d37f5a0876704b5754953967495ada))


### Refactors

* write jest config in typescript ([#57](https://github.com/anthropics/anthropic-bedrock-typescript/issues/57)) ([bbaa155](https://github.com/anthropics/anthropic-bedrock-typescript/commit/bbaa155e6377283a12258a470d4d10da3d9ebe54))


### Build System

* specify `packageManager: yarn` ([#51](https://github.com/anthropics/anthropic-bedrock-typescript/issues/51)) ([59453e5](https://github.com/anthropics/anthropic-bedrock-typescript/commit/59453e581ad770fb1d12a7458774d7427d0b90de))

## 0.6.0 (2023-12-06)

Full Changelog: [v0.5.2...v0.6.0](https://github.com/anthropics/anthropic-bedrock-typescript/compare/v0.5.2...v0.6.0)

### Features

* **client:** support reading the base url from an env variable ([#43](https://github.com/anthropics/anthropic-bedrock-typescript/issues/43)) ([783e9a1](https://github.com/anthropics/anthropic-bedrock-typescript/commit/783e9a1c6bacbc18028ee5e052758103e7c89453))


### Bug Fixes

* bump default request timeout to 10min to match documentation ([#47](https://github.com/anthropics/anthropic-bedrock-typescript/issues/47)) ([16d2d96](https://github.com/anthropics/anthropic-bedrock-typescript/commit/16d2d960dfb8076bb41d769a35aeaec564177238))

## 0.5.2 (2023-11-28)

Full Changelog: [v0.5.1...v0.5.2](https://github.com/anthropics/anthropic-bedrock-typescript/compare/v0.5.1...v0.5.2)

## 0.5.1 (2023-11-24)

Full Changelog: [v0.5.0...v0.5.1](https://github.com/anthropics/anthropic-bedrock-typescript/compare/v0.5.0...v0.5.1)

### Chores

* **internal:** remove file import and conditionally run prepare ([#39](https://github.com/anthropics/anthropic-bedrock-typescript/issues/39)) ([546295e](https://github.com/anthropics/anthropic-bedrock-typescript/commit/546295e63e5d0c373f7f84d36b98cf8094c2c5c8))

## 0.5.0 (2023-11-21)

Full Changelog: [v0.4.1...v0.5.0](https://github.com/anthropics/anthropic-bedrock-typescript/compare/v0.4.1...v0.5.0)

### Features

* allow installing package directly from github ([#37](https://github.com/anthropics/anthropic-bedrock-typescript/issues/37)) ([758b62f](https://github.com/anthropics/anthropic-bedrock-typescript/commit/758b62f86b7d62229f9f41c931c03eebc16d03fc))


### Chores

* **ci:** fix publish-npm ([#35](https://github.com/anthropics/anthropic-bedrock-typescript/issues/35)) ([03ca66d](https://github.com/anthropics/anthropic-bedrock-typescript/commit/03ca66d6a9b9d7fcc7f930c62535d162e46917ea))
* **internal:** don't call prepare in dist ([#38](https://github.com/anthropics/anthropic-bedrock-typescript/issues/38)) ([21038f6](https://github.com/anthropics/anthropic-bedrock-typescript/commit/21038f62cf3dafda7cf9f79d5694bd2e89392bc6))

## 0.4.1 (2023-11-14)

Full Changelog: [v0.4.0...v0.4.1](https://github.com/anthropics/anthropic-bedrock-typescript/compare/v0.4.0...v0.4.1)

### Chores

* **ci:** update release-please config ([#29](https://github.com/anthropics/anthropic-bedrock-typescript/issues/29)) ([9f932f7](https://github.com/anthropics/anthropic-bedrock-typescript/commit/9f932f7091d3c0a31c650d326c7669ee90c534ee))
* **docs:** fix github links ([#31](https://github.com/anthropics/anthropic-bedrock-typescript/issues/31)) ([8c433fd](https://github.com/anthropics/anthropic-bedrock-typescript/commit/8c433fdde7c618afc7b5ecd32c85eb06dd0f048b))
* **internal:** update APIResource structure ([#34](https://github.com/anthropics/anthropic-bedrock-typescript/issues/34)) ([c85a2e3](https://github.com/anthropics/anthropic-bedrock-typescript/commit/c85a2e3bfa8d95ea2d7444d32ba884984b7e61e7))
* **internal:** update jest config ([#33](https://github.com/anthropics/anthropic-bedrock-typescript/issues/33)) ([a46da67](https://github.com/anthropics/anthropic-bedrock-typescript/commit/a46da679e8f40600fa37c0de3a90c633b78356eb))
* **internal:** update tsconfig ([#32](https://github.com/anthropics/anthropic-bedrock-typescript/issues/32)) ([b9295df](https://github.com/anthropics/anthropic-bedrock-typescript/commit/b9295dff2a3aa721f057d64dfc41eaf7d6bd0f6c))

## 0.4.0 (2023-11-04)

Full Changelog: [v0.3.0...v0.4.0](https://github.com/anthropics/anthropic-bedrock-typescript/compare/v0.3.0...v0.4.0)

### Features

* **client:** allow binary returns ([#27](https://github.com/anthropics/anthropic-bedrock-typescript/issues/27)) ([d9e84a1](https://github.com/anthropics/anthropic-bedrock-typescript/commit/d9e84a1f8d25d43f3eb256723bfb89cf0d354453))
* **github:** include a devcontainer setup ([#26](https://github.com/anthropics/anthropic-bedrock-typescript/issues/26)) ([c37cf14](https://github.com/anthropics/anthropic-bedrock-typescript/commit/c37cf14409464bceeacb97cb6f8e098a1bfefd2d))


### Chores

* **internal:** update gitignore ([#22](https://github.com/anthropics/anthropic-bedrock-typescript/issues/22)) ([d448991](https://github.com/anthropics/anthropic-bedrock-typescript/commit/d448991a6f24c0a50f686380957473bfa2cd13b7))
* small cleanups ([#25](https://github.com/anthropics/anthropic-bedrock-typescript/issues/25)) ([d18cfcb](https://github.com/anthropics/anthropic-bedrock-typescript/commit/d18cfcb28428ca944b423ea515d046720553c28d))


### Documentation

* document customizing fetch ([#28](https://github.com/anthropics/anthropic-bedrock-typescript/issues/28)) ([878bd1b](https://github.com/anthropics/anthropic-bedrock-typescript/commit/878bd1b240dc319e3ab37b7e8b4fe96eb155688a))
* fix github links ([#24](https://github.com/anthropics/anthropic-bedrock-typescript/issues/24)) ([9560ba7](https://github.com/anthropics/anthropic-bedrock-typescript/commit/9560ba7c165d373a23d77d8d14a864845e2dc721))

## 0.3.0 (2023-10-25)

Full Changelog: [v0.2.0...v0.3.0](https://github.com/anthropics/anthropic-bedrock-typescript/compare/v0.2.0...v0.3.0)

### Features

* **client:** adjust retry behavior to be exponential backoff ([#18](https://github.com/anthropics/anthropic-bedrock-typescript/issues/18)) ([51d3a6e](https://github.com/anthropics/anthropic-bedrock-typescript/commit/51d3a6e8ab71ba935bd71b497d83df1896835199))


### Bug Fixes

* typo in build script ([#21](https://github.com/anthropics/anthropic-bedrock-typescript/issues/21)) ([b86502d](https://github.com/anthropics/anthropic-bedrock-typescript/commit/b86502d913b2d607c49db9fc1c5656d2a089e7a9))

## 0.2.0 (2023-10-19)

Full Changelog: [v0.1.2...v0.2.0](https://github.com/anthropics/anthropic-bedrock-typescript/compare/v0.1.2...v0.2.0)

### Features

* handle 204 No Content gracefully ([#17](https://github.com/anthropics/anthropic-bedrock-typescript/issues/17)) ([f11420b](https://github.com/anthropics/anthropic-bedrock-typescript/commit/f11420b2a9e2b9a127194bd811708f9f010447b5))

## 0.1.2 (2023-10-17)

Full Changelog: [v0.1.1...v0.1.2](https://github.com/anthropics/anthropic-bedrock-typescript/compare/v0.1.1...v0.1.2)

### Bug Fixes

* import web-streams-polyfill without overriding globals ([#13](https://github.com/anthropics/anthropic-bedrock-typescript/issues/13)) ([30db709](https://github.com/anthropics/anthropic-bedrock-typescript/commit/30db7098fea0154c8dcb484bfee2ed5c4ec946aa))

## 0.1.1 (2023-10-16)

Full Changelog: [v0.1.0...v0.1.1](https://github.com/anthropics/anthropic-bedrock-typescript/compare/v0.1.0...v0.1.1)

### Bug Fixes

* improve status code in error messages ([#9](https://github.com/anthropics/anthropic-bedrock-typescript/issues/9)) ([aa3f1b0](https://github.com/anthropics/anthropic-bedrock-typescript/commit/aa3f1b01ee5d9161c793f3f263fc5b297d1d1258))


### Chores

* add case insensitive get header function ([#4](https://github.com/anthropics/anthropic-bedrock-typescript/issues/4)) ([b7309b1](https://github.com/anthropics/anthropic-bedrock-typescript/commit/b7309b10201e048f726993c70bbf075f6927cabe))
* **internal:** add debug logs for stream responses ([#8](https://github.com/anthropics/anthropic-bedrock-typescript/issues/8)) ([b8763a7](https://github.com/anthropics/anthropic-bedrock-typescript/commit/b8763a73376e2a1ddb9073b649d4aacfad27bf69))
* update comment ([#6](https://github.com/anthropics/anthropic-bedrock-typescript/issues/6)) ([7361f09](https://github.com/anthropics/anthropic-bedrock-typescript/commit/7361f09aa5430d8dcd8193dc599ea9fa75d17e4e))


### Documentation

* organisation -&gt; organization (UK to US English) ([#11](https://github.com/anthropics/anthropic-bedrock-typescript/issues/11)) ([5cbea8e](https://github.com/anthropics/anthropic-bedrock-typescript/commit/5cbea8e9ef936b314617765d7bc2dbb2c3d98eac))

## 0.1.0 (2023-10-12)

Full Changelog: [v0.0.1...v0.1.0](https://github.com/anthropics/anthropic-bedrock-typescript/compare/v0.0.1...v0.1.0)

### Features

* **init:** initial commit ([#1](https://github.com/anthropics/anthropic-bedrock-typescript/issues/1)) ([17f9073](https://github.com/anthropics/anthropic-bedrock-typescript/commit/17f9073f1545f9f578e67c56f827322a7691ca21))
