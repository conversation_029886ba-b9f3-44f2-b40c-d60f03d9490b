/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */

// Code generated by the Google Gen AI SDK generator DO NOT EDIT.

import {ApiClient} from './_api_client.js';
import * as common from './_common.js';
import {BaseModule} from './_common.js';
import * as converters from './converters/_files_converters.js';
import {PagedItem, Pager} from './pagers.js';
import * as types from './types.js';

export class Files extends BaseModule {
  constructor(private readonly apiClient: ApiClient) {
    super();
  }

  /**
   * Lists all current project files from the service.
   *
   * @param params - The parameters for the list request
   * @return The paginated results of the list of files
   *
   * @example
   * The following code prints the names of all files from the service, the
   * size of each page is 10.
   *
   * ```ts
   * const listResponse = await ai.files.list({config: {'pageSize': 10}});
   * for await (const file of listResponse) {
   *   console.log(file.name);
   * }
   * ```
   */
  list = async (
    params: types.ListFilesParameters = {},
  ): Promise<Pager<types.File>> => {
    return new Pager<types.File>(
      PagedItem.PAGED_ITEM_FILES,
      (x: types.ListFilesParameters) => this.listInternal(x),
      await this.listInternal(params),
      params,
    );
  };

  /**
   * Uploads a file asynchronously to the Gemini API.
   * This method is not available in Vertex AI.
   * Supported upload sources:
   * - Node.js: File path (string) or Blob object.
   * - Browser: Blob object (e.g., File).
   *
   * @remarks
   * The `mimeType` can be specified in the `config` parameter. If omitted:
   *  - For file path (string) inputs, the `mimeType` will be inferred from the
   *     file extension.
   *  - For Blob object inputs, the `mimeType` will be set to the Blob's `type`
   *     property.
   * Somex eamples for file extension to mimeType mapping:
   * .txt -> text/plain
   * .json -> application/json
   * .jpg  -> image/jpeg
   * .png -> image/png
   * .mp3 -> audio/mpeg
   * .mp4 -> video/mp4
   *
   * This section can contain multiple paragraphs and code examples.
   *
   * @param params - Optional parameters specified in the
   *        `types.UploadFileParameters` interface.
   *         @see {@link types.UploadFileParameters#config} for the optional
   *         config in the parameters.
   * @return A promise that resolves to a `types.File` object.
   * @throws An error if called on a Vertex AI client.
   * @throws An error if the `mimeType` is not provided and can not be inferred,
   * the `mimeType` can be provided in the `params.config` parameter.
   * @throws An error occurs if a suitable upload location cannot be established.
   *
   * @example
   * The following code uploads a file to Gemini API.
   *
   * ```ts
   * const file = await ai.files.upload({file: 'file.txt', config: {
   *   mimeType: 'text/plain',
   * }});
   * console.log(file.name);
   * ```
   */
  async upload(params: types.UploadFileParameters): Promise<types.File> {
    if (this.apiClient.isVertexAI()) {
      throw new Error(
        'Vertex AI does not support uploading files. You can share files through a GCS bucket.',
      );
    }

    return this.apiClient
      .uploadFile(params.file, params.config)
      .then((response) => {
        const file = converters.fileFromMldev(response);
        return file as types.File;
      });
  }

  /**
   * Downloads a remotely stored file asynchronously to a location specified in
   * the `params` object. This method only works on Node environment, to
   * download files in the browser, use a browser compliant method like an <a>
   * tag.
   *
   * @param params - The parameters for the download request.
   *
   * @example
   * The following code downloads an example file named "files/mehozpxf877d" as
   * "file.txt".
   *
   * ```ts
   * await ai.files.download({file: file.name, downloadPath: 'file.txt'});
   * ```
   */

  async download(params: types.DownloadFileParameters): Promise<void> {
    await this.apiClient.downloadFile(params);
  }

  private async listInternal(
    params: types.ListFilesParameters,
  ): Promise<types.ListFilesResponse> {
    let response: Promise<types.ListFilesResponse>;

    let path: string = '';
    let queryParams: Record<string, string> = {};
    if (this.apiClient.isVertexAI()) {
      throw new Error(
        'This method is only supported by the Gemini Developer API.',
      );
    } else {
      const body = converters.listFilesParametersToMldev(params);
      path = common.formatMap('files', body['_url'] as Record<string, unknown>);
      queryParams = body['_query'] as Record<string, string>;
      delete body['config'];
      delete body['_url'];
      delete body['_query'];

      response = this.apiClient
        .request({
          path: path,
          queryParams: queryParams,
          body: JSON.stringify(body),
          httpMethod: 'GET',
          httpOptions: params.config?.httpOptions,
          abortSignal: params.config?.abortSignal,
        })
        .then((httpResponse) => {
          return httpResponse.json().then((jsonResponse) => {
            const response = jsonResponse as types.ListFilesResponse;
            response.sdkHttpResponse = {
              headers: httpResponse.headers,
            } as types.HttpResponse;
            return response;
          });
        }) as Promise<types.ListFilesResponse>;

      return response.then((apiResponse) => {
        const resp = converters.listFilesResponseFromMldev(apiResponse);
        const typedResp = new types.ListFilesResponse();
        Object.assign(typedResp, resp);
        return typedResp;
      });
    }
  }

  private async createInternal(
    params: types.CreateFileParameters,
  ): Promise<types.CreateFileResponse> {
    let response: Promise<types.CreateFileResponse>;

    let path: string = '';
    let queryParams: Record<string, string> = {};
    if (this.apiClient.isVertexAI()) {
      throw new Error(
        'This method is only supported by the Gemini Developer API.',
      );
    } else {
      const body = converters.createFileParametersToMldev(params);
      path = common.formatMap(
        'upload/v1beta/files',
        body['_url'] as Record<string, unknown>,
      );
      queryParams = body['_query'] as Record<string, string>;
      delete body['config'];
      delete body['_url'];
      delete body['_query'];

      response = this.apiClient
        .request({
          path: path,
          queryParams: queryParams,
          body: JSON.stringify(body),
          httpMethod: 'POST',
          httpOptions: params.config?.httpOptions,
          abortSignal: params.config?.abortSignal,
        })
        .then((httpResponse) => {
          return httpResponse.json();
        }) as Promise<types.CreateFileResponse>;

      return response.then((apiResponse) => {
        const resp = converters.createFileResponseFromMldev(apiResponse);
        const typedResp = new types.CreateFileResponse();
        Object.assign(typedResp, resp);
        return typedResp;
      });
    }
  }

  /**
   * Retrieves the file information from the service.
   *
   * @param params - The parameters for the get request
   * @return The Promise that resolves to the types.File object requested.
   *
   * @example
   * ```ts
   * const config: GetFileParameters = {
   *   name: fileName,
   * };
   * file = await ai.files.get(config);
   * console.log(file.name);
   * ```
   */
  async get(params: types.GetFileParameters): Promise<types.File> {
    let response: Promise<types.File>;

    let path: string = '';
    let queryParams: Record<string, string> = {};
    if (this.apiClient.isVertexAI()) {
      throw new Error(
        'This method is only supported by the Gemini Developer API.',
      );
    } else {
      const body = converters.getFileParametersToMldev(params);
      path = common.formatMap(
        'files/{file}',
        body['_url'] as Record<string, unknown>,
      );
      queryParams = body['_query'] as Record<string, string>;
      delete body['config'];
      delete body['_url'];
      delete body['_query'];

      response = this.apiClient
        .request({
          path: path,
          queryParams: queryParams,
          body: JSON.stringify(body),
          httpMethod: 'GET',
          httpOptions: params.config?.httpOptions,
          abortSignal: params.config?.abortSignal,
        })
        .then((httpResponse) => {
          return httpResponse.json();
        }) as Promise<types.File>;

      return response.then((apiResponse) => {
        const resp = converters.fileFromMldev(apiResponse);

        return resp as types.File;
      });
    }
  }

  /**
   * Deletes a remotely stored file.
   *
   * @param params - The parameters for the delete request.
   * @return The DeleteFileResponse, the response for the delete method.
   *
   * @example
   * The following code deletes an example file named "files/mehozpxf877d".
   *
   * ```ts
   * await ai.files.delete({name: file.name});
   * ```
   */
  async delete(
    params: types.DeleteFileParameters,
  ): Promise<types.DeleteFileResponse> {
    let response: Promise<types.DeleteFileResponse>;

    let path: string = '';
    let queryParams: Record<string, string> = {};
    if (this.apiClient.isVertexAI()) {
      throw new Error(
        'This method is only supported by the Gemini Developer API.',
      );
    } else {
      const body = converters.deleteFileParametersToMldev(params);
      path = common.formatMap(
        'files/{file}',
        body['_url'] as Record<string, unknown>,
      );
      queryParams = body['_query'] as Record<string, string>;
      delete body['config'];
      delete body['_url'];
      delete body['_query'];

      response = this.apiClient
        .request({
          path: path,
          queryParams: queryParams,
          body: JSON.stringify(body),
          httpMethod: 'DELETE',
          httpOptions: params.config?.httpOptions,
          abortSignal: params.config?.abortSignal,
        })
        .then((httpResponse) => {
          return httpResponse.json();
        }) as Promise<types.DeleteFileResponse>;

      return response.then(() => {
        const resp = converters.deleteFileResponseFromMldev();
        const typedResp = new types.DeleteFileResponse();
        Object.assign(typedResp, resp);
        return typedResp;
      });
    }
  }
}
