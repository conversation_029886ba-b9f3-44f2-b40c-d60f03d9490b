{"version": 3, "sources": ["../../../../node_modules/.pnpm/@tauri-apps+plugin-http@2.5.1/node_modules/@tauri-apps/plugin-http/dist-js/index.js"], "sourcesContent": ["import { invoke, Channel } from '@tauri-apps/api/core';\n\n// Copyright 2019-2023 Tauri Programme within The Commons Conservancy\n// SPDX-License-Identifier: Apache-2.0\n// SPDX-License-Identifier: MIT\n/**\n * Make HTTP requests with the Rust backend.\n *\n * ## Security\n *\n * This API has a scope configuration that forces you to restrict the URLs that can be accessed using glob patterns.\n *\n * For instance, this scope configuration only allows making HTTP requests to all subdomains for `tauri.app` except for `https://private.tauri.app`:\n * ```json\n * {\n *   \"permissions\": [\n *     {\n *       \"identifier\": \"http:default\",\n *       \"allow\": [{ \"url\": \"https://*.tauri.app\" }],\n *       \"deny\": [{ \"url\": \"https://private.tauri.app\" }]\n *     }\n *   ]\n * }\n * ```\n * Trying to execute any API with a URL not configured on the scope results in a promise rejection due to denied access.\n *\n * @module\n */\nconst ERROR_REQUEST_CANCELLED = 'Request cancelled';\n/**\n * Fetch a resource from the network. It returns a `Promise` that resolves to the\n * `Response` to that `Request`, whether it is successful or not.\n *\n * @example\n * ```typescript\n * const response = await fetch(\"http://my.json.host/data.json\");\n * console.log(response.status);  // e.g. 200\n * console.log(response.statusText); // e.g. \"OK\"\n * const jsonData = await response.json();\n * ```\n *\n * @since 2.0.0\n */\nasync function fetch(input, init) {\n    // abort early here if needed\n    const signal = init?.signal;\n    if (signal?.aborted) {\n        throw new Error(ERROR_REQUEST_CANCELLED);\n    }\n    const maxRedirections = init?.maxRedirections;\n    const connectTimeout = init?.connectTimeout;\n    const proxy = init?.proxy;\n    const danger = init?.danger;\n    // Remove these fields before creating the request\n    if (init) {\n        delete init.maxRedirections;\n        delete init.connectTimeout;\n        delete init.proxy;\n        delete init.danger;\n    }\n    const headers = init?.headers\n        ? init.headers instanceof Headers\n            ? init.headers\n            : new Headers(init.headers)\n        : new Headers();\n    const req = new Request(input, init);\n    const buffer = await req.arrayBuffer();\n    const data = buffer.byteLength !== 0 ? Array.from(new Uint8Array(buffer)) : null;\n    // append new headers created by the browser `Request` implementation,\n    // if not already declared by the caller of this function\n    for (const [key, value] of req.headers) {\n        if (!headers.get(key)) {\n            headers.set(key, value);\n        }\n    }\n    const headersArray = headers instanceof Headers\n        ? Array.from(headers.entries())\n        : Array.isArray(headers)\n            ? headers\n            : Object.entries(headers);\n    // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment\n    const mappedHeaders = headersArray.map(([name, val]) => [\n        name,\n        // we need to ensure we have all header values as strings\n        // eslint-disable-next-line\n        typeof val === 'string' ? val : val.toString()\n    ]);\n    // abort early here if needed\n    if (signal?.aborted) {\n        throw new Error(ERROR_REQUEST_CANCELLED);\n    }\n    const rid = await invoke('plugin:http|fetch', {\n        clientConfig: {\n            method: req.method,\n            url: req.url,\n            headers: mappedHeaders,\n            data,\n            maxRedirections,\n            connectTimeout,\n            proxy,\n            danger\n        }\n    });\n    const abort = () => invoke('plugin:http|fetch_cancel', { rid });\n    // abort early here if needed\n    if (signal?.aborted) {\n        // we don't care about the result of this proimse\n        // eslint-disable-next-line @typescript-eslint/no-floating-promises\n        abort();\n        throw new Error(ERROR_REQUEST_CANCELLED);\n    }\n    signal?.addEventListener('abort', () => void abort());\n    const { status, statusText, url, headers: responseHeaders, rid: responseRid } = await invoke('plugin:http|fetch_send', {\n        rid\n    });\n    // no body for 101, 103, 204, 205 and 304\n    // see https://fetch.spec.whatwg.org/#null-body-status\n    const body = [101, 103, 204, 205, 304].includes(status)\n        ? null\n        : new ReadableStream({\n            start: (controller) => {\n                const streamChannel = new Channel();\n                streamChannel.onmessage = (res) => {\n                    // close early if aborted\n                    if (signal?.aborted) {\n                        controller.error(ERROR_REQUEST_CANCELLED);\n                        return;\n                    }\n                    const resUint8 = new Uint8Array(res);\n                    const lastByte = resUint8[resUint8.byteLength - 1];\n                    const actualRes = resUint8.slice(0, resUint8.byteLength - 1);\n                    // close when the signal to close (last byte is 1) is sent from the IPC.\n                    if (lastByte == 1) {\n                        controller.close();\n                        return;\n                    }\n                    controller.enqueue(actualRes);\n                };\n                // run a non-blocking body stream fetch\n                invoke('plugin:http|fetch_read_body', {\n                    rid: responseRid,\n                    streamChannel\n                }).catch((e) => {\n                    controller.error(e);\n                });\n            }\n        });\n    const res = new Response(body, {\n        status,\n        statusText\n    });\n    // Set `Response` properties that are ignored by the\n    // constructor, like url and some headers\n    //\n    // Since url and headers are read only properties\n    // this is the only way to set them.\n    Object.defineProperty(res, 'url', { value: url });\n    Object.defineProperty(res, 'headers', {\n        value: new Headers(responseHeaders)\n    });\n    return res;\n}\n\nexport { fetch };\n"], "mappings": ";;;;;;;AA4BA,IAAM,0BAA0B;AAehC,eAAe,MAAM,OAAO,MAAM;AAE9B,QAAM,SAAS,6BAAM;AACrB,MAAI,iCAAQ,SAAS;AACjB,UAAM,IAAI,MAAM,uBAAuB;AAAA,EAC3C;AACA,QAAM,kBAAkB,6BAAM;AAC9B,QAAM,iBAAiB,6BAAM;AAC7B,QAAM,QAAQ,6BAAM;AACpB,QAAM,SAAS,6BAAM;AAErB,MAAI,MAAM;AACN,WAAO,KAAK;AACZ,WAAO,KAAK;AACZ,WAAO,KAAK;AACZ,WAAO,KAAK;AAAA,EAChB;AACA,QAAM,WAAU,6BAAM,WAChB,KAAK,mBAAmB,UACpB,KAAK,UACL,IAAI,QAAQ,KAAK,OAAO,IAC5B,IAAI,QAAQ;AAClB,QAAM,MAAM,IAAI,QAAQ,OAAO,IAAI;AACnC,QAAM,SAAS,MAAM,IAAI,YAAY;AACrC,QAAM,OAAO,OAAO,eAAe,IAAI,MAAM,KAAK,IAAI,WAAW,MAAM,CAAC,IAAI;AAG5E,aAAW,CAAC,KAAK,KAAK,KAAK,IAAI,SAAS;AACpC,QAAI,CAAC,QAAQ,IAAI,GAAG,GAAG;AACnB,cAAQ,IAAI,KAAK,KAAK;AAAA,IAC1B;AAAA,EACJ;AACA,QAAM,eAAe,mBAAmB,UAClC,MAAM,KAAK,QAAQ,QAAQ,CAAC,IAC5B,MAAM,QAAQ,OAAO,IACjB,UACA,OAAO,QAAQ,OAAO;AAEhC,QAAM,gBAAgB,aAAa,IAAI,CAAC,CAAC,MAAM,GAAG,MAAM;AAAA,IACpD;AAAA;AAAA;AAAA,IAGA,OAAO,QAAQ,WAAW,MAAM,IAAI,SAAS;AAAA,EACjD,CAAC;AAED,MAAI,iCAAQ,SAAS;AACjB,UAAM,IAAI,MAAM,uBAAuB;AAAA,EAC3C;AACA,QAAM,MAAM,MAAM,OAAO,qBAAqB;AAAA,IAC1C,cAAc;AAAA,MACV,QAAQ,IAAI;AAAA,MACZ,KAAK,IAAI;AAAA,MACT,SAAS;AAAA,MACT;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ,CAAC;AACD,QAAM,QAAQ,MAAM,OAAO,4BAA4B,EAAE,IAAI,CAAC;AAE9D,MAAI,iCAAQ,SAAS;AAGjB,UAAM;AACN,UAAM,IAAI,MAAM,uBAAuB;AAAA,EAC3C;AACA,mCAAQ,iBAAiB,SAAS,MAAM,KAAK,MAAM;AACnD,QAAM,EAAE,QAAQ,YAAY,KAAK,SAAS,iBAAiB,KAAK,YAAY,IAAI,MAAM,OAAO,0BAA0B;AAAA,IACnH;AAAA,EACJ,CAAC;AAGD,QAAM,OAAO,CAAC,KAAK,KAAK,KAAK,KAAK,GAAG,EAAE,SAAS,MAAM,IAChD,OACA,IAAI,eAAe;AAAA,IACjB,OAAO,CAAC,eAAe;AACnB,YAAM,gBAAgB,IAAI,QAAQ;AAClC,oBAAc,YAAY,CAACA,SAAQ;AAE/B,YAAI,iCAAQ,SAAS;AACjB,qBAAW,MAAM,uBAAuB;AACxC;AAAA,QACJ;AACA,cAAM,WAAW,IAAI,WAAWA,IAAG;AACnC,cAAM,WAAW,SAAS,SAAS,aAAa,CAAC;AACjD,cAAM,YAAY,SAAS,MAAM,GAAG,SAAS,aAAa,CAAC;AAE3D,YAAI,YAAY,GAAG;AACf,qBAAW,MAAM;AACjB;AAAA,QACJ;AACA,mBAAW,QAAQ,SAAS;AAAA,MAChC;AAEA,aAAO,+BAA+B;AAAA,QAClC,KAAK;AAAA,QACL;AAAA,MACJ,CAAC,EAAE,MAAM,CAAC,MAAM;AACZ,mBAAW,MAAM,CAAC;AAAA,MACtB,CAAC;AAAA,IACL;AAAA,EACJ,CAAC;AACL,QAAM,MAAM,IAAI,SAAS,MAAM;AAAA,IAC3B;AAAA,IACA;AAAA,EACJ,CAAC;AAMD,SAAO,eAAe,KAAK,OAAO,EAAE,OAAO,IAAI,CAAC;AAChD,SAAO,eAAe,KAAK,WAAW;AAAA,IAClC,OAAO,IAAI,QAAQ,eAAe;AAAA,EACtC,CAAC;AACD,SAAO;AACX;", "names": ["res"]}