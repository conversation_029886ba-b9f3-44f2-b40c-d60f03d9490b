name: Release Doctor
on:
  push:
    branches:
      - main
  workflow_dispatch:

jobs:
  release_doctor:
    name: release doctor
    runs-on: ubuntu-latest
    environment: production-release
    if: github.repository == 'anthropics/anthropic-sdk-typescript' && (github.event_name == 'push' || github.event_name == 'workflow_dispatch' || startsWith(github.head_ref, 'release-please') || github.head_ref == 'next')

    steps:
      - uses: actions/checkout@v4

      - name: Check release environment
        run: |
          bash ./bin/check-release-environment
        env:
          STAINLESS_API_KEY: ${{ secrets.STAINLESS_API_KEY }}
          NPM_TOKEN: ${{ secrets.ANTHROPIC_NPM_TOKEN || secrets.NPM_TOKEN }}
