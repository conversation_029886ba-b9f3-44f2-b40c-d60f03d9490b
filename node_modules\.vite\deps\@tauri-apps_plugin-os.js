import {
  invoke
} from "./chunk-Y3KOJBAE.js";
import "./chunk-DC5AMYBS.js";

// ../node_modules/.pnpm/@tauri-apps+plugin-os@2.3.0/node_modules/@tauri-apps/plugin-os/dist-js/index.js
function eol() {
  return window.__TAURI_OS_PLUGIN_INTERNALS__.eol;
}
function platform() {
  return window.__TAURI_OS_PLUGIN_INTERNALS__.platform;
}
function version() {
  return window.__TAURI_OS_PLUGIN_INTERNALS__.version;
}
function family() {
  return window.__TAURI_OS_PLUGIN_INTERNALS__.family;
}
function type() {
  return window.__TAURI_OS_PLUGIN_INTERNALS__.os_type;
}
function arch() {
  return window.__TAURI_OS_PLUGIN_INTERNALS__.arch;
}
function exeExtension() {
  return window.__TAURI_OS_PLUGIN_INTERNALS__.exe_extension;
}
async function locale() {
  return await invoke("plugin:os|locale");
}
async function hostname() {
  return await invoke("plugin:os|hostname");
}
export {
  arch,
  eol,
  exeExtension,
  family,
  hostname,
  locale,
  platform,
  type,
  version
};
//# sourceMappingURL=@tauri-apps_plugin-os.js.map
