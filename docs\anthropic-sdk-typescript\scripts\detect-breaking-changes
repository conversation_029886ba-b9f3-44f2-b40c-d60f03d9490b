#!/usr/bin/env bash

set -e

cd "$(dirname "$0")/.."

echo "==> Detecting breaking changes"

TEST_PATHS=(
	tests/api-resources/completions.test.ts
	tests/api-resources/messages/messages.test.ts
	tests/api-resources/messages/batches.test.ts
	tests/api-resources/models.test.ts
	tests/api-resources/beta/beta.test.ts
	tests/api-resources/beta/models.test.ts
	tests/api-resources/beta/messages/messages.test.ts
	tests/api-resources/beta/messages/batches.test.ts
	tests/api-resources/beta/files.test.ts
	tests/index.test.ts
)

for PATHSPEC in "${TEST_PATHS[@]}"; do
    # Try to check out previous versions of the test files
    # with the current SDK.
    git checkout "$1" -- "${PATHSPEC}" 2>/dev/null || true
done

# Instead of running the tests, use the linter to check if an
# older test is no longer compatible with the latest SDK.
./scripts/lint
