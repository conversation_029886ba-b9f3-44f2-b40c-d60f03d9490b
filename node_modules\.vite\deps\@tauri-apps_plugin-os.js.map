{"version": 3, "sources": ["../../../../node_modules/.pnpm/@tauri-apps+plugin-os@2.3.0/node_modules/@tauri-apps/plugin-os/dist-js/index.js"], "sourcesContent": ["import { invoke } from '@tauri-apps/api/core';\n\n// Copyright 2019-2023 Tauri Programme within The Commons Conservancy\n// SPDX-License-Identifier: Apache-2.0\n// SPDX-License-Identifier: MIT\n/**\n * Provides operating system-related utility methods and properties.\n *\n * @module\n */\n/**\n * Returns the operating system-specific end-of-line marker.\n * - `\\n` on POSIX\n * - `\\r\\n` on Windows\n *\n * @since 2.0.0\n * */\nfunction eol() {\n    return window.__TAURI_OS_PLUGIN_INTERNALS__.eol;\n}\n/**\n * Returns a string describing the specific operating system in use.\n * The value is set at compile time. Possible values are `'linux'`, `'macos'`, `'ios'`, `'freebsd'`, `'dragonfly'`, `'netbsd'`, `'openbsd'`, `'solaris'`, `'android'`, `'windows'`\n *\n * @example\n * ```typescript\n * import { platform } from '@tauri-apps/plugin-os';\n * const platformName = platform();\n * ```\n *\n * @since 2.0.0\n *\n */\nfunction platform() {\n    return window.__TAURI_OS_PLUGIN_INTERNALS__.platform;\n}\n/**\n * Returns the current operating system version.\n * @example\n * ```typescript\n * import { version } from '@tauri-apps/plugin-os';\n * const osVersion = version();\n * ```\n *\n * @since 2.0.0\n */\nfunction version() {\n    return window.__TAURI_OS_PLUGIN_INTERNALS__.version;\n}\n/**\n * Returns the current operating system family. Possible values are `'unix'`, `'windows'`.\n * @example\n * ```typescript\n * import { family } from '@tauri-apps/plugin-os';\n * const family = family();\n * ```\n *\n * @since 2.0.0\n */\nfunction family() {\n    return window.__TAURI_OS_PLUGIN_INTERNALS__.family;\n}\n/**\n * Returns the current operating system type. Returns `'linux'` on Linux, `'macos'` on macOS, `'windows'` on Windows, `'ios'` on iOS and `'android'` on Android.\n * @example\n * ```typescript\n * import { type } from '@tauri-apps/plugin-os';\n * const osType = type();\n * ```\n *\n * @since 2.0.0\n */\nfunction type() {\n    return window.__TAURI_OS_PLUGIN_INTERNALS__.os_type;\n}\n/**\n * Returns the current operating system architecture.\n * Possible values are `'x86'`, `'x86_64'`, `'arm'`, `'aarch64'`, `'mips'`, `'mips64'`, `'powerpc'`, `'powerpc64'`, `'riscv64'`, `'s390x'`, `'sparc64'`.\n * @example\n * ```typescript\n * import { arch } from '@tauri-apps/plugin-os';\n * const archName = arch();\n * ```\n *\n * @since 2.0.0\n */\nfunction arch() {\n    return window.__TAURI_OS_PLUGIN_INTERNALS__.arch;\n}\n/**\n * Returns the file extension, if any, used for executable binaries on this platform. Possible values are `'exe'` and `''` (empty string).\n * @example\n * ```typescript\n * import { exeExtension } from '@tauri-apps/plugin-os';\n * const exeExt = exeExtension();\n * ```\n *\n * @since 2.0.0\n */\nfunction exeExtension() {\n    return window.__TAURI_OS_PLUGIN_INTERNALS__.exe_extension;\n}\n/**\n * Returns a String with a `BCP-47` language tag inside. If the locale couldn’t be obtained, `null` is returned instead.\n * @example\n * ```typescript\n * import { locale } from '@tauri-apps/plugin-os';\n * const locale = await locale();\n * if (locale) {\n *    // use the locale string here\n * }\n * ```\n *\n * @since 2.0.0\n */\nasync function locale() {\n    return await invoke('plugin:os|locale');\n}\n/**\n * Returns the host name of the operating system.\n * @example\n * ```typescript\n * import { hostname } from '@tauri-apps/plugin-os';\n * const hostname = await hostname();\n * ```\n */\nasync function hostname() {\n    return await invoke('plugin:os|hostname');\n}\n\nexport { arch, eol, exeExtension, family, hostname, locale, platform, type, version };\n"], "mappings": ";;;;;;AAiBA,SAAS,MAAM;AACX,SAAO,OAAO,8BAA8B;AAChD;AAcA,SAAS,WAAW;AAChB,SAAO,OAAO,8BAA8B;AAChD;AAWA,SAAS,UAAU;AACf,SAAO,OAAO,8BAA8B;AAChD;AAWA,SAAS,SAAS;AACd,SAAO,OAAO,8BAA8B;AAChD;AAWA,SAAS,OAAO;AACZ,SAAO,OAAO,8BAA8B;AAChD;AAYA,SAAS,OAAO;AACZ,SAAO,OAAO,8BAA8B;AAChD;AAWA,SAAS,eAAe;AACpB,SAAO,OAAO,8BAA8B;AAChD;AAcA,eAAe,SAAS;AACpB,SAAO,MAAM,OAAO,kBAAkB;AAC1C;AASA,eAAe,WAAW;AACtB,SAAO,MAAM,OAAO,oBAAoB;AAC5C;", "names": []}