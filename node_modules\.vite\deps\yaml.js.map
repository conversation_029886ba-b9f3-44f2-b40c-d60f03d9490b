{"version": 3, "sources": ["../../../../node_modules/.pnpm/yaml@2.8.0/node_modules/yaml/browser/dist/index.js", "../../../../node_modules/.pnpm/yaml@2.8.0/node_modules/yaml/browser/dist/nodes/identity.js", "../../../../node_modules/.pnpm/yaml@2.8.0/node_modules/yaml/browser/dist/visit.js", "../../../../node_modules/.pnpm/yaml@2.8.0/node_modules/yaml/browser/dist/doc/directives.js", "../../../../node_modules/.pnpm/yaml@2.8.0/node_modules/yaml/browser/dist/doc/anchors.js", "../../../../node_modules/.pnpm/yaml@2.8.0/node_modules/yaml/browser/dist/doc/applyReviver.js", "../../../../node_modules/.pnpm/yaml@2.8.0/node_modules/yaml/browser/dist/nodes/toJS.js", "../../../../node_modules/.pnpm/yaml@2.8.0/node_modules/yaml/browser/dist/nodes/Node.js", "../../../../node_modules/.pnpm/yaml@2.8.0/node_modules/yaml/browser/dist/nodes/Alias.js", "../../../../node_modules/.pnpm/yaml@2.8.0/node_modules/yaml/browser/dist/nodes/Scalar.js", "../../../../node_modules/.pnpm/yaml@2.8.0/node_modules/yaml/browser/dist/doc/createNode.js", "../../../../node_modules/.pnpm/yaml@2.8.0/node_modules/yaml/browser/dist/nodes/Collection.js", "../../../../node_modules/.pnpm/yaml@2.8.0/node_modules/yaml/browser/dist/stringify/stringifyComment.js", "../../../../node_modules/.pnpm/yaml@2.8.0/node_modules/yaml/browser/dist/stringify/foldFlowLines.js", "../../../../node_modules/.pnpm/yaml@2.8.0/node_modules/yaml/browser/dist/stringify/stringifyString.js", "../../../../node_modules/.pnpm/yaml@2.8.0/node_modules/yaml/browser/dist/stringify/stringify.js", "../../../../node_modules/.pnpm/yaml@2.8.0/node_modules/yaml/browser/dist/stringify/stringifyPair.js", "../../../../node_modules/.pnpm/yaml@2.8.0/node_modules/yaml/browser/dist/log.js", "../../../../node_modules/.pnpm/yaml@2.8.0/node_modules/yaml/browser/dist/schema/yaml-1.1/merge.js", "../../../../node_modules/.pnpm/yaml@2.8.0/node_modules/yaml/browser/dist/nodes/addPairToJSMap.js", "../../../../node_modules/.pnpm/yaml@2.8.0/node_modules/yaml/browser/dist/nodes/Pair.js", "../../../../node_modules/.pnpm/yaml@2.8.0/node_modules/yaml/browser/dist/stringify/stringifyCollection.js", "../../../../node_modules/.pnpm/yaml@2.8.0/node_modules/yaml/browser/dist/nodes/YAMLMap.js", "../../../../node_modules/.pnpm/yaml@2.8.0/node_modules/yaml/browser/dist/schema/common/map.js", "../../../../node_modules/.pnpm/yaml@2.8.0/node_modules/yaml/browser/dist/nodes/YAMLSeq.js", "../../../../node_modules/.pnpm/yaml@2.8.0/node_modules/yaml/browser/dist/schema/common/seq.js", "../../../../node_modules/.pnpm/yaml@2.8.0/node_modules/yaml/browser/dist/schema/common/string.js", "../../../../node_modules/.pnpm/yaml@2.8.0/node_modules/yaml/browser/dist/schema/common/null.js", "../../../../node_modules/.pnpm/yaml@2.8.0/node_modules/yaml/browser/dist/schema/core/bool.js", "../../../../node_modules/.pnpm/yaml@2.8.0/node_modules/yaml/browser/dist/stringify/stringifyNumber.js", "../../../../node_modules/.pnpm/yaml@2.8.0/node_modules/yaml/browser/dist/schema/core/float.js", "../../../../node_modules/.pnpm/yaml@2.8.0/node_modules/yaml/browser/dist/schema/core/int.js", "../../../../node_modules/.pnpm/yaml@2.8.0/node_modules/yaml/browser/dist/schema/core/schema.js", "../../../../node_modules/.pnpm/yaml@2.8.0/node_modules/yaml/browser/dist/schema/json/schema.js", "../../../../node_modules/.pnpm/yaml@2.8.0/node_modules/yaml/browser/dist/schema/yaml-1.1/binary.js", "../../../../node_modules/.pnpm/yaml@2.8.0/node_modules/yaml/browser/dist/schema/yaml-1.1/pairs.js", "../../../../node_modules/.pnpm/yaml@2.8.0/node_modules/yaml/browser/dist/schema/yaml-1.1/omap.js", "../../../../node_modules/.pnpm/yaml@2.8.0/node_modules/yaml/browser/dist/schema/yaml-1.1/bool.js", "../../../../node_modules/.pnpm/yaml@2.8.0/node_modules/yaml/browser/dist/schema/yaml-1.1/float.js", "../../../../node_modules/.pnpm/yaml@2.8.0/node_modules/yaml/browser/dist/schema/yaml-1.1/int.js", "../../../../node_modules/.pnpm/yaml@2.8.0/node_modules/yaml/browser/dist/schema/yaml-1.1/set.js", "../../../../node_modules/.pnpm/yaml@2.8.0/node_modules/yaml/browser/dist/schema/yaml-1.1/timestamp.js", "../../../../node_modules/.pnpm/yaml@2.8.0/node_modules/yaml/browser/dist/schema/yaml-1.1/schema.js", "../../../../node_modules/.pnpm/yaml@2.8.0/node_modules/yaml/browser/dist/schema/tags.js", "../../../../node_modules/.pnpm/yaml@2.8.0/node_modules/yaml/browser/dist/schema/Schema.js", "../../../../node_modules/.pnpm/yaml@2.8.0/node_modules/yaml/browser/dist/stringify/stringifyDocument.js", "../../../../node_modules/.pnpm/yaml@2.8.0/node_modules/yaml/browser/dist/doc/Document.js", "../../../../node_modules/.pnpm/yaml@2.8.0/node_modules/yaml/browser/dist/errors.js", "../../../../node_modules/.pnpm/yaml@2.8.0/node_modules/yaml/browser/dist/compose/resolve-props.js", "../../../../node_modules/.pnpm/yaml@2.8.0/node_modules/yaml/browser/dist/compose/util-contains-newline.js", "../../../../node_modules/.pnpm/yaml@2.8.0/node_modules/yaml/browser/dist/compose/util-flow-indent-check.js", "../../../../node_modules/.pnpm/yaml@2.8.0/node_modules/yaml/browser/dist/compose/util-map-includes.js", "../../../../node_modules/.pnpm/yaml@2.8.0/node_modules/yaml/browser/dist/compose/resolve-block-map.js", "../../../../node_modules/.pnpm/yaml@2.8.0/node_modules/yaml/browser/dist/compose/resolve-block-seq.js", "../../../../node_modules/.pnpm/yaml@2.8.0/node_modules/yaml/browser/dist/compose/resolve-end.js", "../../../../node_modules/.pnpm/yaml@2.8.0/node_modules/yaml/browser/dist/compose/resolve-flow-collection.js", "../../../../node_modules/.pnpm/yaml@2.8.0/node_modules/yaml/browser/dist/compose/compose-collection.js", "../../../../node_modules/.pnpm/yaml@2.8.0/node_modules/yaml/browser/dist/compose/resolve-block-scalar.js", "../../../../node_modules/.pnpm/yaml@2.8.0/node_modules/yaml/browser/dist/compose/resolve-flow-scalar.js", "../../../../node_modules/.pnpm/yaml@2.8.0/node_modules/yaml/browser/dist/compose/compose-scalar.js", "../../../../node_modules/.pnpm/yaml@2.8.0/node_modules/yaml/browser/dist/compose/util-empty-scalar-position.js", "../../../../node_modules/.pnpm/yaml@2.8.0/node_modules/yaml/browser/dist/compose/compose-node.js", "../../../../node_modules/.pnpm/yaml@2.8.0/node_modules/yaml/browser/dist/compose/compose-doc.js", "../../../../node_modules/.pnpm/yaml@2.8.0/node_modules/yaml/browser/dist/compose/composer.js", "../../../../node_modules/.pnpm/yaml@2.8.0/node_modules/yaml/browser/dist/parse/cst.js", "../../../../node_modules/.pnpm/yaml@2.8.0/node_modules/yaml/browser/dist/parse/cst-scalar.js", "../../../../node_modules/.pnpm/yaml@2.8.0/node_modules/yaml/browser/dist/parse/cst-stringify.js", "../../../../node_modules/.pnpm/yaml@2.8.0/node_modules/yaml/browser/dist/parse/cst-visit.js", "../../../../node_modules/.pnpm/yaml@2.8.0/node_modules/yaml/browser/dist/parse/lexer.js", "../../../../node_modules/.pnpm/yaml@2.8.0/node_modules/yaml/browser/dist/parse/line-counter.js", "../../../../node_modules/.pnpm/yaml@2.8.0/node_modules/yaml/browser/dist/parse/parser.js", "../../../../node_modules/.pnpm/yaml@2.8.0/node_modules/yaml/browser/dist/public-api.js", "../../../../node_modules/.pnpm/yaml@2.8.0/node_modules/yaml/browser/index.js"], "sourcesContent": ["export { Composer } from './compose/composer.js';\nexport { Document } from './doc/Document.js';\nexport { Schema } from './schema/Schema.js';\nexport { YAMLError, YAMLParseError, YAMLWarning } from './errors.js';\nexport { Alias } from './nodes/Alias.js';\nexport { isAlias, isCollection, isDocument, isMap, isNode, isPair, isScalar, isSeq } from './nodes/identity.js';\nexport { Pair } from './nodes/Pair.js';\nexport { Scalar } from './nodes/Scalar.js';\nexport { YAMLMap } from './nodes/YAMLMap.js';\nexport { YAMLSeq } from './nodes/YAMLSeq.js';\nimport * as cst from './parse/cst.js';\nexport { cst as CST };\nexport { Lexer } from './parse/lexer.js';\nexport { LineCounter } from './parse/line-counter.js';\nexport { Parser } from './parse/parser.js';\nexport { parse, parseAllDocuments, parseDocument, stringify } from './public-api.js';\nexport { visit, visitAsync } from './visit.js';\n", "const ALIAS = Symbol.for('yaml.alias');\nconst DOC = Symbol.for('yaml.document');\nconst MAP = Symbol.for('yaml.map');\nconst PAIR = Symbol.for('yaml.pair');\nconst SCALAR = Symbol.for('yaml.scalar');\nconst SEQ = Symbol.for('yaml.seq');\nconst NODE_TYPE = Symbol.for('yaml.node.type');\nconst isAlias = (node) => !!node && typeof node === 'object' && node[NODE_TYPE] === ALIAS;\nconst isDocument = (node) => !!node && typeof node === 'object' && node[NODE_TYPE] === DOC;\nconst isMap = (node) => !!node && typeof node === 'object' && node[NODE_TYPE] === MAP;\nconst isPair = (node) => !!node && typeof node === 'object' && node[NODE_TYPE] === PAIR;\nconst isScalar = (node) => !!node && typeof node === 'object' && node[NODE_TYPE] === SCALAR;\nconst isSeq = (node) => !!node && typeof node === 'object' && node[NODE_TYPE] === SEQ;\nfunction isCollection(node) {\n    if (node && typeof node === 'object')\n        switch (node[NODE_TYPE]) {\n            case MAP:\n            case SEQ:\n                return true;\n        }\n    return false;\n}\nfunction isNode(node) {\n    if (node && typeof node === 'object')\n        switch (node[NODE_TYPE]) {\n            case ALIAS:\n            case MAP:\n            case SCALAR:\n            case SEQ:\n                return true;\n        }\n    return false;\n}\nconst hasAnchor = (node) => (isScalar(node) || isCollection(node)) && !!node.anchor;\n\nexport { ALIAS, DOC, MAP, NODE_TYPE, PAIR, SCALAR, SEQ, hasAnchor, isAlias, isCollection, isDocument, isMap, isNode, isPair, isScalar, isSeq };\n", "import { isDocument, isNode, isPair, isCollection, isMap, isSeq, isScalar, isAlias } from './nodes/identity.js';\n\nconst BREAK = Symbol('break visit');\nconst SKIP = Symbol('skip children');\nconst REMOVE = Symbol('remove node');\n/**\n * Apply a visitor to an AST node or document.\n *\n * Walks through the tree (depth-first) starting from `node`, calling a\n * `visitor` function with three arguments:\n *   - `key`: For sequence values and map `Pair`, the node's index in the\n *     collection. Within a `Pair`, `'key'` or `'value'`, correspondingly.\n *     `null` for the root node.\n *   - `node`: The current node.\n *   - `path`: The ancestry of the current node.\n *\n * The return value of the visitor may be used to control the traversal:\n *   - `undefined` (default): Do nothing and continue\n *   - `visit.SKIP`: Do not visit the children of this node, continue with next\n *     sibling\n *   - `visit.BREAK`: Terminate traversal completely\n *   - `visit.REMOVE`: Remove the current node, then continue with the next one\n *   - `Node`: Replace the current node, then continue by visiting it\n *   - `number`: While iterating the items of a sequence or map, set the index\n *     of the next step. This is useful especially if the index of the current\n *     node has changed.\n *\n * If `visitor` is a single function, it will be called with all values\n * encountered in the tree, including e.g. `null` values. Alternatively,\n * separate visitor functions may be defined for each `Map`, `Pair`, `Seq`,\n * `Alias` and `Scalar` node. To define the same visitor function for more than\n * one node type, use the `Collection` (map and seq), `Value` (map, seq & scalar)\n * and `Node` (alias, map, seq & scalar) targets. Of all these, only the most\n * specific defined one will be used for each node.\n */\nfunction visit(node, visitor) {\n    const visitor_ = initVisitor(visitor);\n    if (isDocument(node)) {\n        const cd = visit_(null, node.contents, visitor_, Object.freeze([node]));\n        if (cd === REMOVE)\n            node.contents = null;\n    }\n    else\n        visit_(null, node, visitor_, Object.freeze([]));\n}\n// Without the `as symbol` casts, TS declares these in the `visit`\n// namespace using `var`, but then complains about that because\n// `unique symbol` must be `const`.\n/** Terminate visit traversal completely */\nvisit.BREAK = BREAK;\n/** Do not visit the children of the current node */\nvisit.SKIP = SKIP;\n/** Remove the current node */\nvisit.REMOVE = REMOVE;\nfunction visit_(key, node, visitor, path) {\n    const ctrl = callVisitor(key, node, visitor, path);\n    if (isNode(ctrl) || isPair(ctrl)) {\n        replaceNode(key, path, ctrl);\n        return visit_(key, ctrl, visitor, path);\n    }\n    if (typeof ctrl !== 'symbol') {\n        if (isCollection(node)) {\n            path = Object.freeze(path.concat(node));\n            for (let i = 0; i < node.items.length; ++i) {\n                const ci = visit_(i, node.items[i], visitor, path);\n                if (typeof ci === 'number')\n                    i = ci - 1;\n                else if (ci === BREAK)\n                    return BREAK;\n                else if (ci === REMOVE) {\n                    node.items.splice(i, 1);\n                    i -= 1;\n                }\n            }\n        }\n        else if (isPair(node)) {\n            path = Object.freeze(path.concat(node));\n            const ck = visit_('key', node.key, visitor, path);\n            if (ck === BREAK)\n                return BREAK;\n            else if (ck === REMOVE)\n                node.key = null;\n            const cv = visit_('value', node.value, visitor, path);\n            if (cv === BREAK)\n                return BREAK;\n            else if (cv === REMOVE)\n                node.value = null;\n        }\n    }\n    return ctrl;\n}\n/**\n * Apply an async visitor to an AST node or document.\n *\n * Walks through the tree (depth-first) starting from `node`, calling a\n * `visitor` function with three arguments:\n *   - `key`: For sequence values and map `Pair`, the node's index in the\n *     collection. Within a `Pair`, `'key'` or `'value'`, correspondingly.\n *     `null` for the root node.\n *   - `node`: The current node.\n *   - `path`: The ancestry of the current node.\n *\n * The return value of the visitor may be used to control the traversal:\n *   - `Promise`: Must resolve to one of the following values\n *   - `undefined` (default): Do nothing and continue\n *   - `visit.SKIP`: Do not visit the children of this node, continue with next\n *     sibling\n *   - `visit.BREAK`: Terminate traversal completely\n *   - `visit.REMOVE`: Remove the current node, then continue with the next one\n *   - `Node`: Replace the current node, then continue by visiting it\n *   - `number`: While iterating the items of a sequence or map, set the index\n *     of the next step. This is useful especially if the index of the current\n *     node has changed.\n *\n * If `visitor` is a single function, it will be called with all values\n * encountered in the tree, including e.g. `null` values. Alternatively,\n * separate visitor functions may be defined for each `Map`, `Pair`, `Seq`,\n * `Alias` and `Scalar` node. To define the same visitor function for more than\n * one node type, use the `Collection` (map and seq), `Value` (map, seq & scalar)\n * and `Node` (alias, map, seq & scalar) targets. Of all these, only the most\n * specific defined one will be used for each node.\n */\nasync function visitAsync(node, visitor) {\n    const visitor_ = initVisitor(visitor);\n    if (isDocument(node)) {\n        const cd = await visitAsync_(null, node.contents, visitor_, Object.freeze([node]));\n        if (cd === REMOVE)\n            node.contents = null;\n    }\n    else\n        await visitAsync_(null, node, visitor_, Object.freeze([]));\n}\n// Without the `as symbol` casts, TS declares these in the `visit`\n// namespace using `var`, but then complains about that because\n// `unique symbol` must be `const`.\n/** Terminate visit traversal completely */\nvisitAsync.BREAK = BREAK;\n/** Do not visit the children of the current node */\nvisitAsync.SKIP = SKIP;\n/** Remove the current node */\nvisitAsync.REMOVE = REMOVE;\nasync function visitAsync_(key, node, visitor, path) {\n    const ctrl = await callVisitor(key, node, visitor, path);\n    if (isNode(ctrl) || isPair(ctrl)) {\n        replaceNode(key, path, ctrl);\n        return visitAsync_(key, ctrl, visitor, path);\n    }\n    if (typeof ctrl !== 'symbol') {\n        if (isCollection(node)) {\n            path = Object.freeze(path.concat(node));\n            for (let i = 0; i < node.items.length; ++i) {\n                const ci = await visitAsync_(i, node.items[i], visitor, path);\n                if (typeof ci === 'number')\n                    i = ci - 1;\n                else if (ci === BREAK)\n                    return BREAK;\n                else if (ci === REMOVE) {\n                    node.items.splice(i, 1);\n                    i -= 1;\n                }\n            }\n        }\n        else if (isPair(node)) {\n            path = Object.freeze(path.concat(node));\n            const ck = await visitAsync_('key', node.key, visitor, path);\n            if (ck === BREAK)\n                return BREAK;\n            else if (ck === REMOVE)\n                node.key = null;\n            const cv = await visitAsync_('value', node.value, visitor, path);\n            if (cv === BREAK)\n                return BREAK;\n            else if (cv === REMOVE)\n                node.value = null;\n        }\n    }\n    return ctrl;\n}\nfunction initVisitor(visitor) {\n    if (typeof visitor === 'object' &&\n        (visitor.Collection || visitor.Node || visitor.Value)) {\n        return Object.assign({\n            Alias: visitor.Node,\n            Map: visitor.Node,\n            Scalar: visitor.Node,\n            Seq: visitor.Node\n        }, visitor.Value && {\n            Map: visitor.Value,\n            Scalar: visitor.Value,\n            Seq: visitor.Value\n        }, visitor.Collection && {\n            Map: visitor.Collection,\n            Seq: visitor.Collection\n        }, visitor);\n    }\n    return visitor;\n}\nfunction callVisitor(key, node, visitor, path) {\n    if (typeof visitor === 'function')\n        return visitor(key, node, path);\n    if (isMap(node))\n        return visitor.Map?.(key, node, path);\n    if (isSeq(node))\n        return visitor.Seq?.(key, node, path);\n    if (isPair(node))\n        return visitor.Pair?.(key, node, path);\n    if (isScalar(node))\n        return visitor.Scalar?.(key, node, path);\n    if (isAlias(node))\n        return visitor.Alias?.(key, node, path);\n    return undefined;\n}\nfunction replaceNode(key, path, node) {\n    const parent = path[path.length - 1];\n    if (isCollection(parent)) {\n        parent.items[key] = node;\n    }\n    else if (isPair(parent)) {\n        if (key === 'key')\n            parent.key = node;\n        else\n            parent.value = node;\n    }\n    else if (isDocument(parent)) {\n        parent.contents = node;\n    }\n    else {\n        const pt = isAlias(parent) ? 'alias' : 'scalar';\n        throw new Error(`Cannot replace node with ${pt} parent`);\n    }\n}\n\nexport { visit, visitAsync };\n", "import { isNode } from '../nodes/identity.js';\nimport { visit } from '../visit.js';\n\nconst escapeChars = {\n    '!': '%21',\n    ',': '%2C',\n    '[': '%5B',\n    ']': '%5D',\n    '{': '%7B',\n    '}': '%7D'\n};\nconst escapeTagName = (tn) => tn.replace(/[!,[\\]{}]/g, ch => escapeChars[ch]);\nclass Directives {\n    constructor(yaml, tags) {\n        /**\n         * The directives-end/doc-start marker `---`. If `null`, a marker may still be\n         * included in the document's stringified representation.\n         */\n        this.docStart = null;\n        /** The doc-end marker `...`.  */\n        this.docEnd = false;\n        this.yaml = Object.assign({}, Directives.defaultYaml, yaml);\n        this.tags = Object.assign({}, Directives.defaultTags, tags);\n    }\n    clone() {\n        const copy = new Directives(this.yaml, this.tags);\n        copy.docStart = this.docStart;\n        return copy;\n    }\n    /**\n     * During parsing, get a Directives instance for the current document and\n     * update the stream state according to the current version's spec.\n     */\n    atDocument() {\n        const res = new Directives(this.yaml, this.tags);\n        switch (this.yaml.version) {\n            case '1.1':\n                this.atNextDocument = true;\n                break;\n            case '1.2':\n                this.atNextDocument = false;\n                this.yaml = {\n                    explicit: Directives.defaultYaml.explicit,\n                    version: '1.2'\n                };\n                this.tags = Object.assign({}, Directives.defaultTags);\n                break;\n        }\n        return res;\n    }\n    /**\n     * @param onError - May be called even if the action was successful\n     * @returns `true` on success\n     */\n    add(line, onError) {\n        if (this.atNextDocument) {\n            this.yaml = { explicit: Directives.defaultYaml.explicit, version: '1.1' };\n            this.tags = Object.assign({}, Directives.defaultTags);\n            this.atNextDocument = false;\n        }\n        const parts = line.trim().split(/[ \\t]+/);\n        const name = parts.shift();\n        switch (name) {\n            case '%TAG': {\n                if (parts.length !== 2) {\n                    onError(0, '%TAG directive should contain exactly two parts');\n                    if (parts.length < 2)\n                        return false;\n                }\n                const [handle, prefix] = parts;\n                this.tags[handle] = prefix;\n                return true;\n            }\n            case '%YAML': {\n                this.yaml.explicit = true;\n                if (parts.length !== 1) {\n                    onError(0, '%YAML directive should contain exactly one part');\n                    return false;\n                }\n                const [version] = parts;\n                if (version === '1.1' || version === '1.2') {\n                    this.yaml.version = version;\n                    return true;\n                }\n                else {\n                    const isValid = /^\\d+\\.\\d+$/.test(version);\n                    onError(6, `Unsupported YAML version ${version}`, isValid);\n                    return false;\n                }\n            }\n            default:\n                onError(0, `Unknown directive ${name}`, true);\n                return false;\n        }\n    }\n    /**\n     * Resolves a tag, matching handles to those defined in %TAG directives.\n     *\n     * @returns Resolved tag, which may also be the non-specific tag `'!'` or a\n     *   `'!local'` tag, or `null` if unresolvable.\n     */\n    tagName(source, onError) {\n        if (source === '!')\n            return '!'; // non-specific tag\n        if (source[0] !== '!') {\n            onError(`Not a valid tag: ${source}`);\n            return null;\n        }\n        if (source[1] === '<') {\n            const verbatim = source.slice(2, -1);\n            if (verbatim === '!' || verbatim === '!!') {\n                onError(`Verbatim tags aren't resolved, so ${source} is invalid.`);\n                return null;\n            }\n            if (source[source.length - 1] !== '>')\n                onError('Verbatim tags must end with a >');\n            return verbatim;\n        }\n        const [, handle, suffix] = source.match(/^(.*!)([^!]*)$/s);\n        if (!suffix)\n            onError(`The ${source} tag has no suffix`);\n        const prefix = this.tags[handle];\n        if (prefix) {\n            try {\n                return prefix + decodeURIComponent(suffix);\n            }\n            catch (error) {\n                onError(String(error));\n                return null;\n            }\n        }\n        if (handle === '!')\n            return source; // local tag\n        onError(`Could not resolve tag: ${source}`);\n        return null;\n    }\n    /**\n     * Given a fully resolved tag, returns its printable string form,\n     * taking into account current tag prefixes and defaults.\n     */\n    tagString(tag) {\n        for (const [handle, prefix] of Object.entries(this.tags)) {\n            if (tag.startsWith(prefix))\n                return handle + escapeTagName(tag.substring(prefix.length));\n        }\n        return tag[0] === '!' ? tag : `!<${tag}>`;\n    }\n    toString(doc) {\n        const lines = this.yaml.explicit\n            ? [`%YAML ${this.yaml.version || '1.2'}`]\n            : [];\n        const tagEntries = Object.entries(this.tags);\n        let tagNames;\n        if (doc && tagEntries.length > 0 && isNode(doc.contents)) {\n            const tags = {};\n            visit(doc.contents, (_key, node) => {\n                if (isNode(node) && node.tag)\n                    tags[node.tag] = true;\n            });\n            tagNames = Object.keys(tags);\n        }\n        else\n            tagNames = [];\n        for (const [handle, prefix] of tagEntries) {\n            if (handle === '!!' && prefix === 'tag:yaml.org,2002:')\n                continue;\n            if (!doc || tagNames.some(tn => tn.startsWith(prefix)))\n                lines.push(`%TAG ${handle} ${prefix}`);\n        }\n        return lines.join('\\n');\n    }\n}\nDirectives.defaultYaml = { explicit: false, version: '1.2' };\nDirectives.defaultTags = { '!!': 'tag:yaml.org,2002:' };\n\nexport { Directives };\n", "import { isScalar, isCollection } from '../nodes/identity.js';\nimport { visit } from '../visit.js';\n\n/**\n * Verify that the input string is a valid anchor.\n *\n * Will throw on errors.\n */\nfunction anchorIsValid(anchor) {\n    if (/[\\x00-\\x19\\s,[\\]{}]/.test(anchor)) {\n        const sa = JSON.stringify(anchor);\n        const msg = `Anchor must not contain whitespace or control characters: ${sa}`;\n        throw new Error(msg);\n    }\n    return true;\n}\nfunction anchorNames(root) {\n    const anchors = new Set();\n    visit(root, {\n        Value(_key, node) {\n            if (node.anchor)\n                anchors.add(node.anchor);\n        }\n    });\n    return anchors;\n}\n/** Find a new anchor name with the given `prefix` and a one-indexed suffix. */\nfunction findNewAnchor(prefix, exclude) {\n    for (let i = 1; true; ++i) {\n        const name = `${prefix}${i}`;\n        if (!exclude.has(name))\n            return name;\n    }\n}\nfunction createNodeAnchors(doc, prefix) {\n    const aliasObjects = [];\n    const sourceObjects = new Map();\n    let prevAnchors = null;\n    return {\n        onAnchor: (source) => {\n            aliasObjects.push(source);\n            prevAnchors ?? (prevAnchors = anchorNames(doc));\n            const anchor = findNewAnchor(prefix, prevAnchors);\n            prevAnchors.add(anchor);\n            return anchor;\n        },\n        /**\n         * With circular references, the source node is only resolved after all\n         * of its child nodes are. This is why anchors are set only after all of\n         * the nodes have been created.\n         */\n        setAnchors: () => {\n            for (const source of aliasObjects) {\n                const ref = sourceObjects.get(source);\n                if (typeof ref === 'object' &&\n                    ref.anchor &&\n                    (isScalar(ref.node) || isCollection(ref.node))) {\n                    ref.node.anchor = ref.anchor;\n                }\n                else {\n                    const error = new Error('Failed to resolve repeated object (this should not happen)');\n                    error.source = source;\n                    throw error;\n                }\n            }\n        },\n        sourceObjects\n    };\n}\n\nexport { anchorIsValid, anchorNames, createNodeAnchors, findNewAnchor };\n", "/**\n * Applies the JSON.parse reviver algorithm as defined in the ECMA-262 spec,\n * in section 24.5.1.1 \"Runtime Semantics: InternalizeJSONProperty\" of the\n * 2021 edition: https://tc39.es/ecma262/#sec-json.parse\n *\n * Includes extensions for handling Map and Set objects.\n */\nfunction applyReviver(reviver, obj, key, val) {\n    if (val && typeof val === 'object') {\n        if (Array.isArray(val)) {\n            for (let i = 0, len = val.length; i < len; ++i) {\n                const v0 = val[i];\n                const v1 = applyReviver(reviver, val, String(i), v0);\n                // eslint-disable-next-line @typescript-eslint/no-array-delete\n                if (v1 === undefined)\n                    delete val[i];\n                else if (v1 !== v0)\n                    val[i] = v1;\n            }\n        }\n        else if (val instanceof Map) {\n            for (const k of Array.from(val.keys())) {\n                const v0 = val.get(k);\n                const v1 = applyReviver(reviver, val, k, v0);\n                if (v1 === undefined)\n                    val.delete(k);\n                else if (v1 !== v0)\n                    val.set(k, v1);\n            }\n        }\n        else if (val instanceof Set) {\n            for (const v0 of Array.from(val)) {\n                const v1 = applyReviver(reviver, val, v0, v0);\n                if (v1 === undefined)\n                    val.delete(v0);\n                else if (v1 !== v0) {\n                    val.delete(v0);\n                    val.add(v1);\n                }\n            }\n        }\n        else {\n            for (const [k, v0] of Object.entries(val)) {\n                const v1 = applyReviver(reviver, val, k, v0);\n                if (v1 === undefined)\n                    delete val[k];\n                else if (v1 !== v0)\n                    val[k] = v1;\n            }\n        }\n    }\n    return reviver.call(obj, key, val);\n}\n\nexport { applyReviver };\n", "import { hasAnchor } from './identity.js';\n\n/**\n * Recursively convert any node or its contents to native JavaScript\n *\n * @param value - The input value\n * @param arg - If `value` defines a `toJSON()` method, use this\n *   as its first argument\n * @param ctx - Conversion context, originally set in Document#toJS(). If\n *   `{ keep: true }` is not set, output should be suitable for JSON\n *   stringification.\n */\nfunction toJS(value, arg, ctx) {\n    // eslint-disable-next-line @typescript-eslint/no-unsafe-return\n    if (Array.isArray(value))\n        return value.map((v, i) => toJS(v, String(i), ctx));\n    if (value && typeof value.toJSON === 'function') {\n        // eslint-disable-next-line @typescript-eslint/no-unsafe-call\n        if (!ctx || !hasAnchor(value))\n            return value.toJSON(arg, ctx);\n        const data = { aliasCount: 0, count: 1, res: undefined };\n        ctx.anchors.set(value, data);\n        ctx.onCreate = res => {\n            data.res = res;\n            delete ctx.onCreate;\n        };\n        const res = value.toJSON(arg, ctx);\n        if (ctx.onCreate)\n            ctx.onCreate(res);\n        return res;\n    }\n    if (typeof value === 'bigint' && !ctx?.keep)\n        return Number(value);\n    return value;\n}\n\nexport { toJS };\n", "import { applyReviver } from '../doc/applyReviver.js';\nimport { NODE_TYPE, isDocument } from './identity.js';\nimport { toJS } from './toJS.js';\n\nclass NodeBase {\n    constructor(type) {\n        Object.defineProperty(this, NODE_TYPE, { value: type });\n    }\n    /** Create a copy of this node.  */\n    clone() {\n        const copy = Object.create(Object.getPrototypeOf(this), Object.getOwnPropertyDescriptors(this));\n        if (this.range)\n            copy.range = this.range.slice();\n        return copy;\n    }\n    /** A plain JavaScript representation of this node. */\n    toJS(doc, { mapAsMap, maxAliasCount, onAnchor, reviver } = {}) {\n        if (!isDocument(doc))\n            throw new TypeError('A document argument is required');\n        const ctx = {\n            anchors: new Map(),\n            doc,\n            keep: true,\n            mapAsMap: mapAsMap === true,\n            mapKeyWarned: false,\n            maxAliasCount: typeof maxAliasCount === 'number' ? maxAliasCount : 100\n        };\n        const res = toJS(this, '', ctx);\n        if (typeof onAnchor === 'function')\n            for (const { count, res } of ctx.anchors.values())\n                onAnchor(res, count);\n        return typeof reviver === 'function'\n            ? applyReviver(reviver, { '': res }, '', res)\n            : res;\n    }\n}\n\nexport { NodeBase };\n", "import { anchorIsValid } from '../doc/anchors.js';\nimport { visit } from '../visit.js';\nimport { ALIAS, isAlias, isCollection, isPair, hasAnchor } from './identity.js';\nimport { NodeBase } from './Node.js';\nimport { toJS } from './toJS.js';\n\nclass Alias extends NodeBase {\n    constructor(source) {\n        super(ALIAS);\n        this.source = source;\n        Object.defineProperty(this, 'tag', {\n            set() {\n                throw new Error('Alias nodes cannot have tags');\n            }\n        });\n    }\n    /**\n     * Resolve the value of this alias within `doc`, finding the last\n     * instance of the `source` anchor before this node.\n     */\n    resolve(doc, ctx) {\n        let nodes;\n        if (ctx?.aliasResolveCache) {\n            nodes = ctx.aliasResolveCache;\n        }\n        else {\n            nodes = [];\n            visit(doc, {\n                Node: (_key, node) => {\n                    if (isAlias(node) || hasAnchor(node))\n                        nodes.push(node);\n                }\n            });\n            if (ctx)\n                ctx.aliasResolveCache = nodes;\n        }\n        let found = undefined;\n        for (const node of nodes) {\n            if (node === this)\n                break;\n            if (node.anchor === this.source)\n                found = node;\n        }\n        return found;\n    }\n    toJSON(_arg, ctx) {\n        if (!ctx)\n            return { source: this.source };\n        const { anchors, doc, maxAliasCount } = ctx;\n        const source = this.resolve(doc, ctx);\n        if (!source) {\n            const msg = `Unresolved alias (the anchor must be set before the alias): ${this.source}`;\n            throw new ReferenceError(msg);\n        }\n        let data = anchors.get(source);\n        if (!data) {\n            // Resolve anchors for Node.prototype.toJS()\n            toJS(source, null, ctx);\n            data = anchors.get(source);\n        }\n        /* istanbul ignore if */\n        if (!data || data.res === undefined) {\n            const msg = 'This should not happen: Alias anchor was not resolved?';\n            throw new ReferenceError(msg);\n        }\n        if (maxAliasCount >= 0) {\n            data.count += 1;\n            if (data.aliasCount === 0)\n                data.aliasCount = getAliasCount(doc, source, anchors);\n            if (data.count * data.aliasCount > maxAliasCount) {\n                const msg = 'Excessive alias count indicates a resource exhaustion attack';\n                throw new ReferenceError(msg);\n            }\n        }\n        return data.res;\n    }\n    toString(ctx, _onComment, _onChompKeep) {\n        const src = `*${this.source}`;\n        if (ctx) {\n            anchorIsValid(this.source);\n            if (ctx.options.verifyAliasOrder && !ctx.anchors.has(this.source)) {\n                const msg = `Unresolved alias (the anchor must be set before the alias): ${this.source}`;\n                throw new Error(msg);\n            }\n            if (ctx.implicitKey)\n                return `${src} `;\n        }\n        return src;\n    }\n}\nfunction getAliasCount(doc, node, anchors) {\n    if (isAlias(node)) {\n        const source = node.resolve(doc);\n        const anchor = anchors && source && anchors.get(source);\n        return anchor ? anchor.count * anchor.aliasCount : 0;\n    }\n    else if (isCollection(node)) {\n        let count = 0;\n        for (const item of node.items) {\n            const c = getAliasCount(doc, item, anchors);\n            if (c > count)\n                count = c;\n        }\n        return count;\n    }\n    else if (isPair(node)) {\n        const kc = getAliasCount(doc, node.key, anchors);\n        const vc = getAliasCount(doc, node.value, anchors);\n        return Math.max(kc, vc);\n    }\n    return 1;\n}\n\nexport { Alias };\n", "import { SCALAR } from './identity.js';\nimport { NodeBase } from './Node.js';\nimport { toJS } from './toJS.js';\n\nconst isScalarValue = (value) => !value || (typeof value !== 'function' && typeof value !== 'object');\nclass Scalar extends NodeBase {\n    constructor(value) {\n        super(SCALAR);\n        this.value = value;\n    }\n    toJSON(arg, ctx) {\n        return ctx?.keep ? this.value : toJS(this.value, arg, ctx);\n    }\n    toString() {\n        return String(this.value);\n    }\n}\nScalar.BLOCK_FOLDED = 'BLOCK_FOLDED';\nScalar.BLOCK_LITERAL = 'BLOCK_LITERAL';\nScalar.PLAIN = 'PLAIN';\nScalar.QUOTE_DOUBLE = 'QUOTE_DOUBLE';\nScalar.QUOTE_SINGLE = 'QUOTE_SINGLE';\n\nexport { Scalar, isScalarValue };\n", "import { <PERSON>as } from '../nodes/Alias.js';\nimport { isNode, isPair, MAP, SEQ, isDocument } from '../nodes/identity.js';\nimport { <PERSON>alar } from '../nodes/Scalar.js';\n\nconst defaultTagPrefix = 'tag:yaml.org,2002:';\nfunction findTagObject(value, tagName, tags) {\n    if (tagName) {\n        const match = tags.filter(t => t.tag === tagName);\n        const tagObj = match.find(t => !t.format) ?? match[0];\n        if (!tagObj)\n            throw new Error(`Tag ${tagName} not found`);\n        return tagObj;\n    }\n    return tags.find(t => t.identify?.(value) && !t.format);\n}\nfunction createNode(value, tagName, ctx) {\n    if (isDocument(value))\n        value = value.contents;\n    if (isNode(value))\n        return value;\n    if (isPair(value)) {\n        const map = ctx.schema[MAP].createNode?.(ctx.schema, null, ctx);\n        map.items.push(value);\n        return map;\n    }\n    if (value instanceof String ||\n        value instanceof Number ||\n        value instanceof Boolean ||\n        (typeof BigInt !== 'undefined' && value instanceof BigInt) // not supported everywhere\n    ) {\n        // https://tc39.es/ecma262/#sec-serializejsonproperty\n        value = value.valueOf();\n    }\n    const { aliasDuplicateObjects, onAnchor, onTagObj, schema, sourceObjects } = ctx;\n    // Detect duplicate references to the same object & use Alias nodes for all\n    // after first. The `ref` wrapper allows for circular references to resolve.\n    let ref = undefined;\n    if (aliasDuplicateObjects && value && typeof value === 'object') {\n        ref = sourceObjects.get(value);\n        if (ref) {\n            ref.anchor ?? (ref.anchor = onAnchor(value));\n            return new Alias(ref.anchor);\n        }\n        else {\n            ref = { anchor: null, node: null };\n            sourceObjects.set(value, ref);\n        }\n    }\n    if (tagName?.startsWith('!!'))\n        tagName = defaultTagPrefix + tagName.slice(2);\n    let tagObj = findTagObject(value, tagName, schema.tags);\n    if (!tagObj) {\n        if (value && typeof value.toJSON === 'function') {\n            // eslint-disable-next-line @typescript-eslint/no-unsafe-call\n            value = value.toJSON();\n        }\n        if (!value || typeof value !== 'object') {\n            const node = new Scalar(value);\n            if (ref)\n                ref.node = node;\n            return node;\n        }\n        tagObj =\n            value instanceof Map\n                ? schema[MAP]\n                : Symbol.iterator in Object(value)\n                    ? schema[SEQ]\n                    : schema[MAP];\n    }\n    if (onTagObj) {\n        onTagObj(tagObj);\n        delete ctx.onTagObj;\n    }\n    const node = tagObj?.createNode\n        ? tagObj.createNode(ctx.schema, value, ctx)\n        : typeof tagObj?.nodeClass?.from === 'function'\n            ? tagObj.nodeClass.from(ctx.schema, value, ctx)\n            : new Scalar(value);\n    if (tagName)\n        node.tag = tagName;\n    else if (!tagObj.default)\n        node.tag = tagObj.tag;\n    if (ref)\n        ref.node = node;\n    return node;\n}\n\nexport { createNode };\n", "import { createNode } from '../doc/createNode.js';\nimport { isNode, isPair, isCollection, isScalar } from './identity.js';\nimport { NodeBase } from './Node.js';\n\nfunction collectionFromPath(schema, path, value) {\n    let v = value;\n    for (let i = path.length - 1; i >= 0; --i) {\n        const k = path[i];\n        if (typeof k === 'number' && Number.isInteger(k) && k >= 0) {\n            const a = [];\n            a[k] = v;\n            v = a;\n        }\n        else {\n            v = new Map([[k, v]]);\n        }\n    }\n    return createNode(v, undefined, {\n        aliasDuplicateObjects: false,\n        keepUndefined: false,\n        onAnchor: () => {\n            throw new Error('This should not happen, please report a bug.');\n        },\n        schema,\n        sourceObjects: new Map()\n    });\n}\n// Type guard is intentionally a little wrong so as to be more useful,\n// as it does not cover untypable empty non-string iterables (e.g. []).\nconst isEmptyPath = (path) => path == null ||\n    (typeof path === 'object' && !!path[Symbol.iterator]().next().done);\nclass Collection extends NodeBase {\n    constructor(type, schema) {\n        super(type);\n        Object.defineProperty(this, 'schema', {\n            value: schema,\n            configurable: true,\n            enumerable: false,\n            writable: true\n        });\n    }\n    /**\n     * Create a copy of this collection.\n     *\n     * @param schema - If defined, overwrites the original's schema\n     */\n    clone(schema) {\n        const copy = Object.create(Object.getPrototypeOf(this), Object.getOwnPropertyDescriptors(this));\n        if (schema)\n            copy.schema = schema;\n        copy.items = copy.items.map(it => isNode(it) || isPair(it) ? it.clone(schema) : it);\n        if (this.range)\n            copy.range = this.range.slice();\n        return copy;\n    }\n    /**\n     * Adds a value to the collection. For `!!map` and `!!omap` the value must\n     * be a Pair instance or a `{ key, value }` object, which may not have a key\n     * that already exists in the map.\n     */\n    addIn(path, value) {\n        if (isEmptyPath(path))\n            this.add(value);\n        else {\n            const [key, ...rest] = path;\n            const node = this.get(key, true);\n            if (isCollection(node))\n                node.addIn(rest, value);\n            else if (node === undefined && this.schema)\n                this.set(key, collectionFromPath(this.schema, rest, value));\n            else\n                throw new Error(`Expected YAML collection at ${key}. Remaining path: ${rest}`);\n        }\n    }\n    /**\n     * Removes a value from the collection.\n     * @returns `true` if the item was found and removed.\n     */\n    deleteIn(path) {\n        const [key, ...rest] = path;\n        if (rest.length === 0)\n            return this.delete(key);\n        const node = this.get(key, true);\n        if (isCollection(node))\n            return node.deleteIn(rest);\n        else\n            throw new Error(`Expected YAML collection at ${key}. Remaining path: ${rest}`);\n    }\n    /**\n     * Returns item at `key`, or `undefined` if not found. By default unwraps\n     * scalar values from their surrounding node; to disable set `keepScalar` to\n     * `true` (collections are always returned intact).\n     */\n    getIn(path, keepScalar) {\n        const [key, ...rest] = path;\n        const node = this.get(key, true);\n        if (rest.length === 0)\n            return !keepScalar && isScalar(node) ? node.value : node;\n        else\n            return isCollection(node) ? node.getIn(rest, keepScalar) : undefined;\n    }\n    hasAllNullValues(allowScalar) {\n        return this.items.every(node => {\n            if (!isPair(node))\n                return false;\n            const n = node.value;\n            return (n == null ||\n                (allowScalar &&\n                    isScalar(n) &&\n                    n.value == null &&\n                    !n.commentBefore &&\n                    !n.comment &&\n                    !n.tag));\n        });\n    }\n    /**\n     * Checks if the collection includes a value with the key `key`.\n     */\n    hasIn(path) {\n        const [key, ...rest] = path;\n        if (rest.length === 0)\n            return this.has(key);\n        const node = this.get(key, true);\n        return isCollection(node) ? node.hasIn(rest) : false;\n    }\n    /**\n     * Sets a value in this collection. For `!!set`, `value` needs to be a\n     * boolean to add/remove the item from the set.\n     */\n    setIn(path, value) {\n        const [key, ...rest] = path;\n        if (rest.length === 0) {\n            this.set(key, value);\n        }\n        else {\n            const node = this.get(key, true);\n            if (isCollection(node))\n                node.setIn(rest, value);\n            else if (node === undefined && this.schema)\n                this.set(key, collectionFromPath(this.schema, rest, value));\n            else\n                throw new Error(`Expected YAML collection at ${key}. Remaining path: ${rest}`);\n        }\n    }\n}\n\nexport { Collection, collectionFromPath, isEmptyPath };\n", "/**\n * Stringifies a comment.\n *\n * Empty comment lines are left empty,\n * lines consisting of a single space are replaced by `#`,\n * and all other lines are prefixed with a `#`.\n */\nconst stringifyComment = (str) => str.replace(/^(?!$)(?: $)?/gm, '#');\nfunction indentComment(comment, indent) {\n    if (/^\\n+$/.test(comment))\n        return comment.substring(1);\n    return indent ? comment.replace(/^(?! *$)/gm, indent) : comment;\n}\nconst lineComment = (str, indent, comment) => str.endsWith('\\n')\n    ? indentComment(comment, indent)\n    : comment.includes('\\n')\n        ? '\\n' + indentComment(comment, indent)\n        : (str.endsWith(' ') ? '' : ' ') + comment;\n\nexport { indentComment, lineComment, stringifyComment };\n", "const FOLD_FLOW = 'flow';\nconst FOLD_BLOCK = 'block';\nconst FOLD_QUOTED = 'quoted';\n/**\n * Tries to keep input at up to `lineWidth` characters, splitting only on spaces\n * not followed by newlines or spaces unless `mode` is `'quoted'`. Lines are\n * terminated with `\\n` and started with `indent`.\n */\nfunction foldFlowLines(text, indent, mode = 'flow', { indentAtStart, lineWidth = 80, minContentWidth = 20, onFold, onOverflow } = {}) {\n    if (!lineWidth || lineWidth < 0)\n        return text;\n    if (lineWidth < minContentWidth)\n        minContentWidth = 0;\n    const endStep = Math.max(1 + minContentWidth, 1 + lineWidth - indent.length);\n    if (text.length <= endStep)\n        return text;\n    const folds = [];\n    const escapedFolds = {};\n    let end = lineWidth - indent.length;\n    if (typeof indentAtStart === 'number') {\n        if (indentAtStart > lineWidth - Math.max(2, minContentWidth))\n            folds.push(0);\n        else\n            end = lineWidth - indentAtStart;\n    }\n    let split = undefined;\n    let prev = undefined;\n    let overflow = false;\n    let i = -1;\n    let escStart = -1;\n    let escEnd = -1;\n    if (mode === FOLD_BLOCK) {\n        i = consumeMoreIndentedLines(text, i, indent.length);\n        if (i !== -1)\n            end = i + endStep;\n    }\n    for (let ch; (ch = text[(i += 1)]);) {\n        if (mode === FOLD_QUOTED && ch === '\\\\') {\n            escStart = i;\n            switch (text[i + 1]) {\n                case 'x':\n                    i += 3;\n                    break;\n                case 'u':\n                    i += 5;\n                    break;\n                case 'U':\n                    i += 9;\n                    break;\n                default:\n                    i += 1;\n            }\n            escEnd = i;\n        }\n        if (ch === '\\n') {\n            if (mode === FOLD_BLOCK)\n                i = consumeMoreIndentedLines(text, i, indent.length);\n            end = i + indent.length + endStep;\n            split = undefined;\n        }\n        else {\n            if (ch === ' ' &&\n                prev &&\n                prev !== ' ' &&\n                prev !== '\\n' &&\n                prev !== '\\t') {\n                // space surrounded by non-space can be replaced with newline + indent\n                const next = text[i + 1];\n                if (next && next !== ' ' && next !== '\\n' && next !== '\\t')\n                    split = i;\n            }\n            if (i >= end) {\n                if (split) {\n                    folds.push(split);\n                    end = split + endStep;\n                    split = undefined;\n                }\n                else if (mode === FOLD_QUOTED) {\n                    // white-space collected at end may stretch past lineWidth\n                    while (prev === ' ' || prev === '\\t') {\n                        prev = ch;\n                        ch = text[(i += 1)];\n                        overflow = true;\n                    }\n                    // Account for newline escape, but don't break preceding escape\n                    const j = i > escEnd + 1 ? i - 2 : escStart - 1;\n                    // Bail out if lineWidth & minContentWidth are shorter than an escape string\n                    if (escapedFolds[j])\n                        return text;\n                    folds.push(j);\n                    escapedFolds[j] = true;\n                    end = j + endStep;\n                    split = undefined;\n                }\n                else {\n                    overflow = true;\n                }\n            }\n        }\n        prev = ch;\n    }\n    if (overflow && onOverflow)\n        onOverflow();\n    if (folds.length === 0)\n        return text;\n    if (onFold)\n        onFold();\n    let res = text.slice(0, folds[0]);\n    for (let i = 0; i < folds.length; ++i) {\n        const fold = folds[i];\n        const end = folds[i + 1] || text.length;\n        if (fold === 0)\n            res = `\\n${indent}${text.slice(0, end)}`;\n        else {\n            if (mode === FOLD_QUOTED && escapedFolds[fold])\n                res += `${text[fold]}\\\\`;\n            res += `\\n${indent}${text.slice(fold + 1, end)}`;\n        }\n    }\n    return res;\n}\n/**\n * Presumes `i + 1` is at the start of a line\n * @returns index of last newline in more-indented block\n */\nfunction consumeMoreIndentedLines(text, i, indent) {\n    let end = i;\n    let start = i + 1;\n    let ch = text[start];\n    while (ch === ' ' || ch === '\\t') {\n        if (i < start + indent) {\n            ch = text[++i];\n        }\n        else {\n            do {\n                ch = text[++i];\n            } while (ch && ch !== '\\n');\n            end = i;\n            start = i + 1;\n            ch = text[start];\n        }\n    }\n    return end;\n}\n\nexport { FOLD_BLOCK, FOLD_FLOW, FOLD_QUOTED, foldFlowLines };\n", "import { <PERSON><PERSON><PERSON> } from '../nodes/Scalar.js';\nimport { foldFlowLines, FOLD_FLOW, FOLD_QUOTED, FOLD_BLOCK } from './foldFlowLines.js';\n\nconst getFoldOptions = (ctx, isBlock) => ({\n    indentAtStart: isBlock ? ctx.indent.length : ctx.indentAtStart,\n    lineWidth: ctx.options.lineWidth,\n    minContentWidth: ctx.options.minContentWidth\n});\n// Also checks for lines starting with %, as parsing the output as YAML 1.1 will\n// presume that's starting a new document.\nconst containsDocumentMarker = (str) => /^(%|---|\\.\\.\\.)/m.test(str);\nfunction lineLengthOverLimit(str, lineWidth, indentLength) {\n    if (!lineWidth || lineWidth < 0)\n        return false;\n    const limit = lineWidth - indentLength;\n    const strLen = str.length;\n    if (strLen <= limit)\n        return false;\n    for (let i = 0, start = 0; i < strLen; ++i) {\n        if (str[i] === '\\n') {\n            if (i - start > limit)\n                return true;\n            start = i + 1;\n            if (strLen - start <= limit)\n                return false;\n        }\n    }\n    return true;\n}\nfunction doubleQuotedString(value, ctx) {\n    const json = JSON.stringify(value);\n    if (ctx.options.doubleQuotedAsJSON)\n        return json;\n    const { implicitKey } = ctx;\n    const minMultiLineLength = ctx.options.doubleQuotedMinMultiLineLength;\n    const indent = ctx.indent || (containsDocumentMarker(value) ? '  ' : '');\n    let str = '';\n    let start = 0;\n    for (let i = 0, ch = json[i]; ch; ch = json[++i]) {\n        if (ch === ' ' && json[i + 1] === '\\\\' && json[i + 2] === 'n') {\n            // space before newline needs to be escaped to not be folded\n            str += json.slice(start, i) + '\\\\ ';\n            i += 1;\n            start = i;\n            ch = '\\\\';\n        }\n        if (ch === '\\\\')\n            switch (json[i + 1]) {\n                case 'u':\n                    {\n                        str += json.slice(start, i);\n                        const code = json.substr(i + 2, 4);\n                        switch (code) {\n                            case '0000':\n                                str += '\\\\0';\n                                break;\n                            case '0007':\n                                str += '\\\\a';\n                                break;\n                            case '000b':\n                                str += '\\\\v';\n                                break;\n                            case '001b':\n                                str += '\\\\e';\n                                break;\n                            case '0085':\n                                str += '\\\\N';\n                                break;\n                            case '00a0':\n                                str += '\\\\_';\n                                break;\n                            case '2028':\n                                str += '\\\\L';\n                                break;\n                            case '2029':\n                                str += '\\\\P';\n                                break;\n                            default:\n                                if (code.substr(0, 2) === '00')\n                                    str += '\\\\x' + code.substr(2);\n                                else\n                                    str += json.substr(i, 6);\n                        }\n                        i += 5;\n                        start = i + 1;\n                    }\n                    break;\n                case 'n':\n                    if (implicitKey ||\n                        json[i + 2] === '\"' ||\n                        json.length < minMultiLineLength) {\n                        i += 1;\n                    }\n                    else {\n                        // folding will eat first newline\n                        str += json.slice(start, i) + '\\n\\n';\n                        while (json[i + 2] === '\\\\' &&\n                            json[i + 3] === 'n' &&\n                            json[i + 4] !== '\"') {\n                            str += '\\n';\n                            i += 2;\n                        }\n                        str += indent;\n                        // space after newline needs to be escaped to not be folded\n                        if (json[i + 2] === ' ')\n                            str += '\\\\';\n                        i += 1;\n                        start = i + 1;\n                    }\n                    break;\n                default:\n                    i += 1;\n            }\n    }\n    str = start ? str + json.slice(start) : json;\n    return implicitKey\n        ? str\n        : foldFlowLines(str, indent, FOLD_QUOTED, getFoldOptions(ctx, false));\n}\nfunction singleQuotedString(value, ctx) {\n    if (ctx.options.singleQuote === false ||\n        (ctx.implicitKey && value.includes('\\n')) ||\n        /[ \\t]\\n|\\n[ \\t]/.test(value) // single quoted string can't have leading or trailing whitespace around newline\n    )\n        return doubleQuotedString(value, ctx);\n    const indent = ctx.indent || (containsDocumentMarker(value) ? '  ' : '');\n    const res = \"'\" + value.replace(/'/g, \"''\").replace(/\\n+/g, `$&\\n${indent}`) + \"'\";\n    return ctx.implicitKey\n        ? res\n        : foldFlowLines(res, indent, FOLD_FLOW, getFoldOptions(ctx, false));\n}\nfunction quotedString(value, ctx) {\n    const { singleQuote } = ctx.options;\n    let qs;\n    if (singleQuote === false)\n        qs = doubleQuotedString;\n    else {\n        const hasDouble = value.includes('\"');\n        const hasSingle = value.includes(\"'\");\n        if (hasDouble && !hasSingle)\n            qs = singleQuotedString;\n        else if (hasSingle && !hasDouble)\n            qs = doubleQuotedString;\n        else\n            qs = singleQuote ? singleQuotedString : doubleQuotedString;\n    }\n    return qs(value, ctx);\n}\n// The negative lookbehind avoids a polynomial search,\n// but isn't supported yet on Safari: https://caniuse.com/js-regexp-lookbehind\nlet blockEndNewlines;\ntry {\n    blockEndNewlines = new RegExp('(^|(?<!\\n))\\n+(?!\\n|$)', 'g');\n}\ncatch {\n    blockEndNewlines = /\\n+(?!\\n|$)/g;\n}\nfunction blockString({ comment, type, value }, ctx, onComment, onChompKeep) {\n    const { blockQuote, commentString, lineWidth } = ctx.options;\n    // 1. Block can't end in whitespace unless the last line is non-empty.\n    // 2. Strings consisting of only whitespace are best rendered explicitly.\n    if (!blockQuote || /\\n[\\t ]+$/.test(value) || /^\\s*$/.test(value)) {\n        return quotedString(value, ctx);\n    }\n    const indent = ctx.indent ||\n        (ctx.forceBlockIndent || containsDocumentMarker(value) ? '  ' : '');\n    const literal = blockQuote === 'literal'\n        ? true\n        : blockQuote === 'folded' || type === Scalar.BLOCK_FOLDED\n            ? false\n            : type === Scalar.BLOCK_LITERAL\n                ? true\n                : !lineLengthOverLimit(value, lineWidth, indent.length);\n    if (!value)\n        return literal ? '|\\n' : '>\\n';\n    // determine chomping from whitespace at value end\n    let chomp;\n    let endStart;\n    for (endStart = value.length; endStart > 0; --endStart) {\n        const ch = value[endStart - 1];\n        if (ch !== '\\n' && ch !== '\\t' && ch !== ' ')\n            break;\n    }\n    let end = value.substring(endStart);\n    const endNlPos = end.indexOf('\\n');\n    if (endNlPos === -1) {\n        chomp = '-'; // strip\n    }\n    else if (value === end || endNlPos !== end.length - 1) {\n        chomp = '+'; // keep\n        if (onChompKeep)\n            onChompKeep();\n    }\n    else {\n        chomp = ''; // clip\n    }\n    if (end) {\n        value = value.slice(0, -end.length);\n        if (end[end.length - 1] === '\\n')\n            end = end.slice(0, -1);\n        end = end.replace(blockEndNewlines, `$&${indent}`);\n    }\n    // determine indent indicator from whitespace at value start\n    let startWithSpace = false;\n    let startEnd;\n    let startNlPos = -1;\n    for (startEnd = 0; startEnd < value.length; ++startEnd) {\n        const ch = value[startEnd];\n        if (ch === ' ')\n            startWithSpace = true;\n        else if (ch === '\\n')\n            startNlPos = startEnd;\n        else\n            break;\n    }\n    let start = value.substring(0, startNlPos < startEnd ? startNlPos + 1 : startEnd);\n    if (start) {\n        value = value.substring(start.length);\n        start = start.replace(/\\n+/g, `$&${indent}`);\n    }\n    const indentSize = indent ? '2' : '1'; // root is at -1\n    // Leading | or > is added later\n    let header = (startWithSpace ? indentSize : '') + chomp;\n    if (comment) {\n        header += ' ' + commentString(comment.replace(/ ?[\\r\\n]+/g, ' '));\n        if (onComment)\n            onComment();\n    }\n    if (!literal) {\n        const foldedValue = value\n            .replace(/\\n+/g, '\\n$&')\n            .replace(/(?:^|\\n)([\\t ].*)(?:([\\n\\t ]*)\\n(?![\\n\\t ]))?/g, '$1$2') // more-indented lines aren't folded\n            //                ^ more-ind. ^ empty     ^ capture next empty lines only at end of indent\n            .replace(/\\n+/g, `$&${indent}`);\n        let literalFallback = false;\n        const foldOptions = getFoldOptions(ctx, true);\n        if (blockQuote !== 'folded' && type !== Scalar.BLOCK_FOLDED) {\n            foldOptions.onOverflow = () => {\n                literalFallback = true;\n            };\n        }\n        const body = foldFlowLines(`${start}${foldedValue}${end}`, indent, FOLD_BLOCK, foldOptions);\n        if (!literalFallback)\n            return `>${header}\\n${indent}${body}`;\n    }\n    value = value.replace(/\\n+/g, `$&${indent}`);\n    return `|${header}\\n${indent}${start}${value}${end}`;\n}\nfunction plainString(item, ctx, onComment, onChompKeep) {\n    const { type, value } = item;\n    const { actualString, implicitKey, indent, indentStep, inFlow } = ctx;\n    if ((implicitKey && value.includes('\\n')) ||\n        (inFlow && /[[\\]{},]/.test(value))) {\n        return quotedString(value, ctx);\n    }\n    if (/^[\\n\\t ,[\\]{}#&*!|>'\"%@`]|^[?-]$|^[?-][ \\t]|[\\n:][ \\t]|[ \\t]\\n|[\\n\\t ]#|[\\n\\t :]$/.test(value)) {\n        // not allowed:\n        // - '-' or '?'\n        // - start with an indicator character (except [?:-]) or /[?-] /\n        // - '\\n ', ': ' or ' \\n' anywhere\n        // - '#' not preceded by a non-space char\n        // - end with ' ' or ':'\n        return implicitKey || inFlow || !value.includes('\\n')\n            ? quotedString(value, ctx)\n            : blockString(item, ctx, onComment, onChompKeep);\n    }\n    if (!implicitKey &&\n        !inFlow &&\n        type !== Scalar.PLAIN &&\n        value.includes('\\n')) {\n        // Where allowed & type not set explicitly, prefer block style for multiline strings\n        return blockString(item, ctx, onComment, onChompKeep);\n    }\n    if (containsDocumentMarker(value)) {\n        if (indent === '') {\n            ctx.forceBlockIndent = true;\n            return blockString(item, ctx, onComment, onChompKeep);\n        }\n        else if (implicitKey && indent === indentStep) {\n            return quotedString(value, ctx);\n        }\n    }\n    const str = value.replace(/\\n+/g, `$&\\n${indent}`);\n    // Verify that output will be parsed as a string, as e.g. plain numbers and\n    // booleans get parsed with those types in v1.2 (e.g. '42', 'true' & '0.9e-3'),\n    // and others in v1.1.\n    if (actualString) {\n        const test = (tag) => tag.default && tag.tag !== 'tag:yaml.org,2002:str' && tag.test?.test(str);\n        const { compat, tags } = ctx.doc.schema;\n        if (tags.some(test) || compat?.some(test))\n            return quotedString(value, ctx);\n    }\n    return implicitKey\n        ? str\n        : foldFlowLines(str, indent, FOLD_FLOW, getFoldOptions(ctx, false));\n}\nfunction stringifyString(item, ctx, onComment, onChompKeep) {\n    const { implicitKey, inFlow } = ctx;\n    const ss = typeof item.value === 'string'\n        ? item\n        : Object.assign({}, item, { value: String(item.value) });\n    let { type } = item;\n    if (type !== Scalar.QUOTE_DOUBLE) {\n        // force double quotes on control characters & unpaired surrogates\n        if (/[\\x00-\\x08\\x0b-\\x1f\\x7f-\\x9f\\u{D800}-\\u{DFFF}]/u.test(ss.value))\n            type = Scalar.QUOTE_DOUBLE;\n    }\n    const _stringify = (_type) => {\n        switch (_type) {\n            case Scalar.BLOCK_FOLDED:\n            case Scalar.BLOCK_LITERAL:\n                return implicitKey || inFlow\n                    ? quotedString(ss.value, ctx) // blocks are not valid inside flow containers\n                    : blockString(ss, ctx, onComment, onChompKeep);\n            case Scalar.QUOTE_DOUBLE:\n                return doubleQuotedString(ss.value, ctx);\n            case Scalar.QUOTE_SINGLE:\n                return singleQuotedString(ss.value, ctx);\n            case Scalar.PLAIN:\n                return plainString(ss, ctx, onComment, onChompKeep);\n            default:\n                return null;\n        }\n    };\n    let res = _stringify(type);\n    if (res === null) {\n        const { defaultKeyType, defaultStringType } = ctx.options;\n        const t = (implicitKey && defaultKeyType) || defaultStringType;\n        res = _stringify(t);\n        if (res === null)\n            throw new Error(`Unsupported default string type ${t}`);\n    }\n    return res;\n}\n\nexport { stringifyString };\n", "import { anchorIsValid } from '../doc/anchors.js';\nimport { isPair, isAlias, isNode, isScalar, isCollection } from '../nodes/identity.js';\nimport { stringifyComment } from './stringifyComment.js';\nimport { stringifyString } from './stringifyString.js';\n\nfunction createStringifyContext(doc, options) {\n    const opt = Object.assign({\n        blockQuote: true,\n        commentString: stringifyComment,\n        defaultKeyType: null,\n        defaultStringType: 'PLAIN',\n        directives: null,\n        doubleQuotedAsJSON: false,\n        doubleQuotedMinMultiLineLength: 40,\n        falseStr: 'false',\n        flowCollectionPadding: true,\n        indentSeq: true,\n        lineWidth: 80,\n        minContentWidth: 20,\n        nullStr: 'null',\n        simpleKeys: false,\n        singleQuote: null,\n        trueStr: 'true',\n        verifyAliasOrder: true\n    }, doc.schema.toStringOptions, options);\n    let inFlow;\n    switch (opt.collectionStyle) {\n        case 'block':\n            inFlow = false;\n            break;\n        case 'flow':\n            inFlow = true;\n            break;\n        default:\n            inFlow = null;\n    }\n    return {\n        anchors: new Set(),\n        doc,\n        flowCollectionPadding: opt.flowCollectionPadding ? ' ' : '',\n        indent: '',\n        indentStep: typeof opt.indent === 'number' ? ' '.repeat(opt.indent) : '  ',\n        inFlow,\n        options: opt\n    };\n}\nfunction getTagObject(tags, item) {\n    if (item.tag) {\n        const match = tags.filter(t => t.tag === item.tag);\n        if (match.length > 0)\n            return match.find(t => t.format === item.format) ?? match[0];\n    }\n    let tagObj = undefined;\n    let obj;\n    if (isScalar(item)) {\n        obj = item.value;\n        let match = tags.filter(t => t.identify?.(obj));\n        if (match.length > 1) {\n            const testMatch = match.filter(t => t.test);\n            if (testMatch.length > 0)\n                match = testMatch;\n        }\n        tagObj =\n            match.find(t => t.format === item.format) ?? match.find(t => !t.format);\n    }\n    else {\n        obj = item;\n        tagObj = tags.find(t => t.nodeClass && obj instanceof t.nodeClass);\n    }\n    if (!tagObj) {\n        const name = obj?.constructor?.name ?? (obj === null ? 'null' : typeof obj);\n        throw new Error(`Tag not resolved for ${name} value`);\n    }\n    return tagObj;\n}\n// needs to be called before value stringifier to allow for circular anchor refs\nfunction stringifyProps(node, tagObj, { anchors, doc }) {\n    if (!doc.directives)\n        return '';\n    const props = [];\n    const anchor = (isScalar(node) || isCollection(node)) && node.anchor;\n    if (anchor && anchorIsValid(anchor)) {\n        anchors.add(anchor);\n        props.push(`&${anchor}`);\n    }\n    const tag = node.tag ?? (tagObj.default ? null : tagObj.tag);\n    if (tag)\n        props.push(doc.directives.tagString(tag));\n    return props.join(' ');\n}\nfunction stringify(item, ctx, onComment, onChompKeep) {\n    if (isPair(item))\n        return item.toString(ctx, onComment, onChompKeep);\n    if (isAlias(item)) {\n        if (ctx.doc.directives)\n            return item.toString(ctx);\n        if (ctx.resolvedAliases?.has(item)) {\n            throw new TypeError(`Cannot stringify circular structure without alias nodes`);\n        }\n        else {\n            if (ctx.resolvedAliases)\n                ctx.resolvedAliases.add(item);\n            else\n                ctx.resolvedAliases = new Set([item]);\n            item = item.resolve(ctx.doc);\n        }\n    }\n    let tagObj = undefined;\n    const node = isNode(item)\n        ? item\n        : ctx.doc.createNode(item, { onTagObj: o => (tagObj = o) });\n    tagObj ?? (tagObj = getTagObject(ctx.doc.schema.tags, node));\n    const props = stringifyProps(node, tagObj, ctx);\n    if (props.length > 0)\n        ctx.indentAtStart = (ctx.indentAtStart ?? 0) + props.length + 1;\n    const str = typeof tagObj.stringify === 'function'\n        ? tagObj.stringify(node, ctx, onComment, onChompKeep)\n        : isScalar(node)\n            ? stringifyString(node, ctx, onComment, onChompKeep)\n            : node.toString(ctx, onComment, onChompKeep);\n    if (!props)\n        return str;\n    return isScalar(node) || str[0] === '{' || str[0] === '['\n        ? `${props} ${str}`\n        : `${props}\\n${ctx.indent}${str}`;\n}\n\nexport { createStringifyContext, stringify };\n", "import { isCollection, isNode, isScalar, isSeq } from '../nodes/identity.js';\nimport { Scalar } from '../nodes/Scalar.js';\nimport { stringify } from './stringify.js';\nimport { lineComment, indentComment } from './stringifyComment.js';\n\nfunction stringifyPair({ key, value }, ctx, onComment, onChompKeep) {\n    const { allNullValues, doc, indent, indentStep, options: { commentString, indentSeq, simpleKeys } } = ctx;\n    let keyComment = (isNode(key) && key.comment) || null;\n    if (simpleKeys) {\n        if (keyComment) {\n            throw new Error('With simple keys, key nodes cannot have comments');\n        }\n        if (isCollection(key) || (!isNode(key) && typeof key === 'object')) {\n            const msg = 'With simple keys, collection cannot be used as a key value';\n            throw new Error(msg);\n        }\n    }\n    let explicitKey = !simpleKeys &&\n        (!key ||\n            (keyComment && value == null && !ctx.inFlow) ||\n            isCollection(key) ||\n            (isScalar(key)\n                ? key.type === Scalar.BLOCK_FOLDED || key.type === Scalar.BLOCK_LITERAL\n                : typeof key === 'object'));\n    ctx = Object.assign({}, ctx, {\n        allNullValues: false,\n        implicitKey: !explicitKey && (simpleKeys || !allNullValues),\n        indent: indent + indentStep\n    });\n    let keyCommentDone = false;\n    let chompKeep = false;\n    let str = stringify(key, ctx, () => (keyCommentDone = true), () => (chompKeep = true));\n    if (!explicitKey && !ctx.inFlow && str.length > 1024) {\n        if (simpleKeys)\n            throw new Error('With simple keys, single line scalar must not span more than 1024 characters');\n        explicitKey = true;\n    }\n    if (ctx.inFlow) {\n        if (allNullValues || value == null) {\n            if (keyCommentDone && onComment)\n                onComment();\n            return str === '' ? '?' : explicitKey ? `? ${str}` : str;\n        }\n    }\n    else if ((allNullValues && !simpleKeys) || (value == null && explicitKey)) {\n        str = `? ${str}`;\n        if (keyComment && !keyCommentDone) {\n            str += lineComment(str, ctx.indent, commentString(keyComment));\n        }\n        else if (chompKeep && onChompKeep)\n            onChompKeep();\n        return str;\n    }\n    if (keyCommentDone)\n        keyComment = null;\n    if (explicitKey) {\n        if (keyComment)\n            str += lineComment(str, ctx.indent, commentString(keyComment));\n        str = `? ${str}\\n${indent}:`;\n    }\n    else {\n        str = `${str}:`;\n        if (keyComment)\n            str += lineComment(str, ctx.indent, commentString(keyComment));\n    }\n    let vsb, vcb, valueComment;\n    if (isNode(value)) {\n        vsb = !!value.spaceBefore;\n        vcb = value.commentBefore;\n        valueComment = value.comment;\n    }\n    else {\n        vsb = false;\n        vcb = null;\n        valueComment = null;\n        if (value && typeof value === 'object')\n            value = doc.createNode(value);\n    }\n    ctx.implicitKey = false;\n    if (!explicitKey && !keyComment && isScalar(value))\n        ctx.indentAtStart = str.length + 1;\n    chompKeep = false;\n    if (!indentSeq &&\n        indentStep.length >= 2 &&\n        !ctx.inFlow &&\n        !explicitKey &&\n        isSeq(value) &&\n        !value.flow &&\n        !value.tag &&\n        !value.anchor) {\n        // If indentSeq === false, consider '- ' as part of indentation where possible\n        ctx.indent = ctx.indent.substring(2);\n    }\n    let valueCommentDone = false;\n    const valueStr = stringify(value, ctx, () => (valueCommentDone = true), () => (chompKeep = true));\n    let ws = ' ';\n    if (keyComment || vsb || vcb) {\n        ws = vsb ? '\\n' : '';\n        if (vcb) {\n            const cs = commentString(vcb);\n            ws += `\\n${indentComment(cs, ctx.indent)}`;\n        }\n        if (valueStr === '' && !ctx.inFlow) {\n            if (ws === '\\n')\n                ws = '\\n\\n';\n        }\n        else {\n            ws += `\\n${ctx.indent}`;\n        }\n    }\n    else if (!explicitKey && isCollection(value)) {\n        const vs0 = valueStr[0];\n        const nl0 = valueStr.indexOf('\\n');\n        const hasNewline = nl0 !== -1;\n        const flow = ctx.inFlow ?? value.flow ?? value.items.length === 0;\n        if (hasNewline || !flow) {\n            let hasPropsLine = false;\n            if (hasNewline && (vs0 === '&' || vs0 === '!')) {\n                let sp0 = valueStr.indexOf(' ');\n                if (vs0 === '&' &&\n                    sp0 !== -1 &&\n                    sp0 < nl0 &&\n                    valueStr[sp0 + 1] === '!') {\n                    sp0 = valueStr.indexOf(' ', sp0 + 1);\n                }\n                if (sp0 === -1 || nl0 < sp0)\n                    hasPropsLine = true;\n            }\n            if (!hasPropsLine)\n                ws = `\\n${ctx.indent}`;\n        }\n    }\n    else if (valueStr === '' || valueStr[0] === '\\n') {\n        ws = '';\n    }\n    str += ws + valueStr;\n    if (ctx.inFlow) {\n        if (valueCommentDone && onComment)\n            onComment();\n    }\n    else if (valueComment && !valueCommentDone) {\n        str += lineComment(str, ctx.indent, commentString(valueComment));\n    }\n    else if (chompKeep && onChompKeep) {\n        onChompKeep();\n    }\n    return str;\n}\n\nexport { stringifyPair };\n", "function debug(logLevel, ...messages) {\n    if (logLevel === 'debug')\n        console.log(...messages);\n}\nfunction warn(logLevel, warning) {\n    if (logLevel === 'debug' || logLevel === 'warn') {\n        console.warn(warning);\n    }\n}\n\nexport { debug, warn };\n", "import { isScalar, isAlias, isSeq, isMap } from '../../nodes/identity.js';\nimport { <PERSON>alar } from '../../nodes/Scalar.js';\n\n// If the value associated with a merge key is a single mapping node, each of\n// its key/value pairs is inserted into the current mapping, unless the key\n// already exists in it. If the value associated with the merge key is a\n// sequence, then this sequence is expected to contain mapping nodes and each\n// of these nodes is merged in turn according to its order in the sequence.\n// Keys in mapping nodes earlier in the sequence override keys specified in\n// later mapping nodes. -- http://yaml.org/type/merge.html\nconst MERGE_KEY = '<<';\nconst merge = {\n    identify: value => value === MERGE_KEY ||\n        (typeof value === 'symbol' && value.description === MERGE_KEY),\n    default: 'key',\n    tag: 'tag:yaml.org,2002:merge',\n    test: /^<<$/,\n    resolve: () => Object.assign(new Scalar(Symbol(MERGE_KEY)), {\n        addToJSMap: addMergeToJSMap\n    }),\n    stringify: () => MERGE_KEY\n};\nconst isMergeKey = (ctx, key) => (merge.identify(key) ||\n    (isScalar(key) &&\n        (!key.type || key.type === Scalar.PLAIN) &&\n        merge.identify(key.value))) &&\n    ctx?.doc.schema.tags.some(tag => tag.tag === merge.tag && tag.default);\nfunction addMergeToJSMap(ctx, map, value) {\n    value = ctx && isAlias(value) ? value.resolve(ctx.doc) : value;\n    if (isSeq(value))\n        for (const it of value.items)\n            mergeValue(ctx, map, it);\n    else if (Array.isArray(value))\n        for (const it of value)\n            mergeValue(ctx, map, it);\n    else\n        mergeValue(ctx, map, value);\n}\nfunction mergeValue(ctx, map, value) {\n    const source = ctx && isAlias(value) ? value.resolve(ctx.doc) : value;\n    if (!isMap(source))\n        throw new Error('Merge sources must be maps or map aliases');\n    const srcMap = source.toJSON(null, ctx, Map);\n    for (const [key, value] of srcMap) {\n        if (map instanceof Map) {\n            if (!map.has(key))\n                map.set(key, value);\n        }\n        else if (map instanceof Set) {\n            map.add(key);\n        }\n        else if (!Object.prototype.hasOwnProperty.call(map, key)) {\n            Object.defineProperty(map, key, {\n                value,\n                writable: true,\n                enumerable: true,\n                configurable: true\n            });\n        }\n    }\n    return map;\n}\n\nexport { addMergeToJSMap, isMergeKey, merge };\n", "import { warn } from '../log.js';\nimport { isMergeKey, addMergeToJSMap } from '../schema/yaml-1.1/merge.js';\nimport { createStringifyContext } from '../stringify/stringify.js';\nimport { isNode } from './identity.js';\nimport { toJS } from './toJS.js';\n\nfunction addPairToJSMap(ctx, map, { key, value }) {\n    if (isNode(key) && key.addToJSMap)\n        key.addToJSMap(ctx, map, value);\n    // TODO: Should drop this special case for bare << handling\n    else if (isMergeKey(ctx, key))\n        addMergeToJSMap(ctx, map, value);\n    else {\n        const jsKey = toJS(key, '', ctx);\n        if (map instanceof Map) {\n            map.set(jsKey, toJS(value, jsKey, ctx));\n        }\n        else if (map instanceof Set) {\n            map.add(jsKey);\n        }\n        else {\n            const stringKey = stringifyKey(key, jsKey, ctx);\n            const jsValue = toJS(value, stringKey, ctx);\n            if (stringKey in map)\n                Object.defineProperty(map, stringKey, {\n                    value: jsValue,\n                    writable: true,\n                    enumerable: true,\n                    configurable: true\n                });\n            else\n                map[stringKey] = jsValue;\n        }\n    }\n    return map;\n}\nfunction stringifyKey(key, jsKey, ctx) {\n    if (jsKey === null)\n        return '';\n    // eslint-disable-next-line @typescript-eslint/no-base-to-string\n    if (typeof jsKey !== 'object')\n        return String(jsKey);\n    if (isNode(key) && ctx?.doc) {\n        const strCtx = createStringifyContext(ctx.doc, {});\n        strCtx.anchors = new Set();\n        for (const node of ctx.anchors.keys())\n            strCtx.anchors.add(node.anchor);\n        strCtx.inFlow = true;\n        strCtx.inStringifyKey = true;\n        const strKey = key.toString(strCtx);\n        if (!ctx.mapKeyWarned) {\n            let jsonStr = JSON.stringify(strKey);\n            if (jsonStr.length > 40)\n                jsonStr = jsonStr.substring(0, 36) + '...\"';\n            warn(ctx.doc.options.logLevel, `Keys with collection values will be stringified due to JS Object restrictions: ${jsonStr}. Set mapAsMap: true to use object keys.`);\n            ctx.mapKeyWarned = true;\n        }\n        return strKey;\n    }\n    return JSON.stringify(jsKey);\n}\n\nexport { addPairToJSMap };\n", "import { createNode } from '../doc/createNode.js';\nimport { stringifyPair } from '../stringify/stringifyPair.js';\nimport { addPairToJSMap } from './addPairToJSMap.js';\nimport { NODE_TYPE, PAIR, isNode } from './identity.js';\n\nfunction createPair(key, value, ctx) {\n    const k = createNode(key, undefined, ctx);\n    const v = createNode(value, undefined, ctx);\n    return new Pair(k, v);\n}\nclass Pair {\n    constructor(key, value = null) {\n        Object.defineProperty(this, NODE_TYPE, { value: PAIR });\n        this.key = key;\n        this.value = value;\n    }\n    clone(schema) {\n        let { key, value } = this;\n        if (isNode(key))\n            key = key.clone(schema);\n        if (isNode(value))\n            value = value.clone(schema);\n        return new Pair(key, value);\n    }\n    toJSON(_, ctx) {\n        const pair = ctx?.mapAsMap ? new Map() : {};\n        return addPairToJSMap(ctx, pair, this);\n    }\n    toString(ctx, onComment, onChompKeep) {\n        return ctx?.doc\n            ? stringifyPair(this, ctx, onComment, onChompKeep)\n            : JSON.stringify(this);\n    }\n}\n\nexport { Pair, createPair };\n", "import { isNode, isPair } from '../nodes/identity.js';\nimport { stringify } from './stringify.js';\nimport { lineComment, indentComment } from './stringifyComment.js';\n\nfunction stringifyCollection(collection, ctx, options) {\n    const flow = ctx.inFlow ?? collection.flow;\n    const stringify = flow ? stringifyFlowCollection : stringifyBlockCollection;\n    return stringify(collection, ctx, options);\n}\nfunction stringifyBlockCollection({ comment, items }, ctx, { blockItemPrefix, flowChars, itemIndent, onChompKeep, onComment }) {\n    const { indent, options: { commentString } } = ctx;\n    const itemCtx = Object.assign({}, ctx, { indent: itemIndent, type: null });\n    let chompKeep = false; // flag for the preceding node's status\n    const lines = [];\n    for (let i = 0; i < items.length; ++i) {\n        const item = items[i];\n        let comment = null;\n        if (isNode(item)) {\n            if (!chompKeep && item.spaceBefore)\n                lines.push('');\n            addCommentBefore(ctx, lines, item.commentBefore, chompKeep);\n            if (item.comment)\n                comment = item.comment;\n        }\n        else if (isPair(item)) {\n            const ik = isNode(item.key) ? item.key : null;\n            if (ik) {\n                if (!chompKeep && ik.spaceBefore)\n                    lines.push('');\n                addCommentBefore(ctx, lines, ik.commentBefore, chompKeep);\n            }\n        }\n        chompKeep = false;\n        let str = stringify(item, itemCtx, () => (comment = null), () => (chompKeep = true));\n        if (comment)\n            str += lineComment(str, itemIndent, commentString(comment));\n        if (chompKeep && comment)\n            chompKeep = false;\n        lines.push(blockItemPrefix + str);\n    }\n    let str;\n    if (lines.length === 0) {\n        str = flowChars.start + flowChars.end;\n    }\n    else {\n        str = lines[0];\n        for (let i = 1; i < lines.length; ++i) {\n            const line = lines[i];\n            str += line ? `\\n${indent}${line}` : '\\n';\n        }\n    }\n    if (comment) {\n        str += '\\n' + indentComment(commentString(comment), indent);\n        if (onComment)\n            onComment();\n    }\n    else if (chompKeep && onChompKeep)\n        onChompKeep();\n    return str;\n}\nfunction stringifyFlowCollection({ items }, ctx, { flowChars, itemIndent }) {\n    const { indent, indentStep, flowCollectionPadding: fcPadding, options: { commentString } } = ctx;\n    itemIndent += indentStep;\n    const itemCtx = Object.assign({}, ctx, {\n        indent: itemIndent,\n        inFlow: true,\n        type: null\n    });\n    let reqNewline = false;\n    let linesAtValue = 0;\n    const lines = [];\n    for (let i = 0; i < items.length; ++i) {\n        const item = items[i];\n        let comment = null;\n        if (isNode(item)) {\n            if (item.spaceBefore)\n                lines.push('');\n            addCommentBefore(ctx, lines, item.commentBefore, false);\n            if (item.comment)\n                comment = item.comment;\n        }\n        else if (isPair(item)) {\n            const ik = isNode(item.key) ? item.key : null;\n            if (ik) {\n                if (ik.spaceBefore)\n                    lines.push('');\n                addCommentBefore(ctx, lines, ik.commentBefore, false);\n                if (ik.comment)\n                    reqNewline = true;\n            }\n            const iv = isNode(item.value) ? item.value : null;\n            if (iv) {\n                if (iv.comment)\n                    comment = iv.comment;\n                if (iv.commentBefore)\n                    reqNewline = true;\n            }\n            else if (item.value == null && ik?.comment) {\n                comment = ik.comment;\n            }\n        }\n        if (comment)\n            reqNewline = true;\n        let str = stringify(item, itemCtx, () => (comment = null));\n        if (i < items.length - 1)\n            str += ',';\n        if (comment)\n            str += lineComment(str, itemIndent, commentString(comment));\n        if (!reqNewline && (lines.length > linesAtValue || str.includes('\\n')))\n            reqNewline = true;\n        lines.push(str);\n        linesAtValue = lines.length;\n    }\n    const { start, end } = flowChars;\n    if (lines.length === 0) {\n        return start + end;\n    }\n    else {\n        if (!reqNewline) {\n            const len = lines.reduce((sum, line) => sum + line.length + 2, 2);\n            reqNewline = ctx.options.lineWidth > 0 && len > ctx.options.lineWidth;\n        }\n        if (reqNewline) {\n            let str = start;\n            for (const line of lines)\n                str += line ? `\\n${indentStep}${indent}${line}` : '\\n';\n            return `${str}\\n${indent}${end}`;\n        }\n        else {\n            return `${start}${fcPadding}${lines.join(' ')}${fcPadding}${end}`;\n        }\n    }\n}\nfunction addCommentBefore({ indent, options: { commentString } }, lines, comment, chompKeep) {\n    if (comment && chompKeep)\n        comment = comment.replace(/^\\n+/, '');\n    if (comment) {\n        const ic = indentComment(commentString(comment), indent);\n        lines.push(ic.trimStart()); // Avoid double indent on first line\n    }\n}\n\nexport { stringifyCollection };\n", "import { stringifyCollection } from '../stringify/stringifyCollection.js';\nimport { addPairToJSMap } from './addPairToJSMap.js';\nimport { Collection } from './Collection.js';\nimport { MAP, isPair, isScalar } from './identity.js';\nimport { Pair, createPair } from './Pair.js';\nimport { isScalarValue } from './Scalar.js';\n\nfunction findPair(items, key) {\n    const k = isScalar(key) ? key.value : key;\n    for (const it of items) {\n        if (isPair(it)) {\n            if (it.key === key || it.key === k)\n                return it;\n            if (isScalar(it.key) && it.key.value === k)\n                return it;\n        }\n    }\n    return undefined;\n}\nclass YAMLMap extends Collection {\n    static get tagName() {\n        return 'tag:yaml.org,2002:map';\n    }\n    constructor(schema) {\n        super(MAP, schema);\n        this.items = [];\n    }\n    /**\n     * A generic collection parsing method that can be extended\n     * to other node classes that inherit from YAMLMap\n     */\n    static from(schema, obj, ctx) {\n        const { keepUndefined, replacer } = ctx;\n        const map = new this(schema);\n        const add = (key, value) => {\n            if (typeof replacer === 'function')\n                value = replacer.call(obj, key, value);\n            else if (Array.isArray(replacer) && !replacer.includes(key))\n                return;\n            if (value !== undefined || keepUndefined)\n                map.items.push(createPair(key, value, ctx));\n        };\n        if (obj instanceof Map) {\n            for (const [key, value] of obj)\n                add(key, value);\n        }\n        else if (obj && typeof obj === 'object') {\n            for (const key of Object.keys(obj))\n                add(key, obj[key]);\n        }\n        if (typeof schema.sortMapEntries === 'function') {\n            map.items.sort(schema.sortMapEntries);\n        }\n        return map;\n    }\n    /**\n     * Adds a value to the collection.\n     *\n     * @param overwrite - If not set `true`, using a key that is already in the\n     *   collection will throw. Otherwise, overwrites the previous value.\n     */\n    add(pair, overwrite) {\n        let _pair;\n        if (isPair(pair))\n            _pair = pair;\n        else if (!pair || typeof pair !== 'object' || !('key' in pair)) {\n            // In TypeScript, this never happens.\n            _pair = new Pair(pair, pair?.value);\n        }\n        else\n            _pair = new Pair(pair.key, pair.value);\n        const prev = findPair(this.items, _pair.key);\n        const sortEntries = this.schema?.sortMapEntries;\n        if (prev) {\n            if (!overwrite)\n                throw new Error(`Key ${_pair.key} already set`);\n            // For scalars, keep the old node & its comments and anchors\n            if (isScalar(prev.value) && isScalarValue(_pair.value))\n                prev.value.value = _pair.value;\n            else\n                prev.value = _pair.value;\n        }\n        else if (sortEntries) {\n            const i = this.items.findIndex(item => sortEntries(_pair, item) < 0);\n            if (i === -1)\n                this.items.push(_pair);\n            else\n                this.items.splice(i, 0, _pair);\n        }\n        else {\n            this.items.push(_pair);\n        }\n    }\n    delete(key) {\n        const it = findPair(this.items, key);\n        if (!it)\n            return false;\n        const del = this.items.splice(this.items.indexOf(it), 1);\n        return del.length > 0;\n    }\n    get(key, keepScalar) {\n        const it = findPair(this.items, key);\n        const node = it?.value;\n        return (!keepScalar && isScalar(node) ? node.value : node) ?? undefined;\n    }\n    has(key) {\n        return !!findPair(this.items, key);\n    }\n    set(key, value) {\n        this.add(new Pair(key, value), true);\n    }\n    /**\n     * @param ctx - Conversion context, originally set in Document#toJS()\n     * @param {Class} Type - If set, forces the returned collection type\n     * @returns Instance of Type, Map, or Object\n     */\n    toJSON(_, ctx, Type) {\n        const map = Type ? new Type() : ctx?.mapAsMap ? new Map() : {};\n        if (ctx?.onCreate)\n            ctx.onCreate(map);\n        for (const item of this.items)\n            addPairToJSMap(ctx, map, item);\n        return map;\n    }\n    toString(ctx, onComment, onChompKeep) {\n        if (!ctx)\n            return JSON.stringify(this);\n        for (const item of this.items) {\n            if (!isPair(item))\n                throw new Error(`Map items must all be pairs; found ${JSON.stringify(item)} instead`);\n        }\n        if (!ctx.allNullValues && this.hasAllNullValues(false))\n            ctx = Object.assign({}, ctx, { allNullValues: true });\n        return stringifyCollection(this, ctx, {\n            blockItemPrefix: '',\n            flowChars: { start: '{', end: '}' },\n            itemIndent: ctx.indent || '',\n            onChompKeep,\n            onComment\n        });\n    }\n}\n\nexport { YAMLMap, findPair };\n", "import { isMap } from '../../nodes/identity.js';\nimport { YAMLMap } from '../../nodes/YAMLMap.js';\n\nconst map = {\n    collection: 'map',\n    default: true,\n    nodeClass: YAMLMap,\n    tag: 'tag:yaml.org,2002:map',\n    resolve(map, onError) {\n        if (!isMap(map))\n            onError('Expected a mapping for this tag');\n        return map;\n    },\n    createNode: (schema, obj, ctx) => YAMLMap.from(schema, obj, ctx)\n};\n\nexport { map };\n", "import { createNode } from '../doc/createNode.js';\nimport { stringifyCollection } from '../stringify/stringifyCollection.js';\nimport { Collection } from './Collection.js';\nimport { SEQ, isScalar } from './identity.js';\nimport { isScalarValue } from './Scalar.js';\nimport { toJS } from './toJS.js';\n\nclass YAMLSeq extends Collection {\n    static get tagName() {\n        return 'tag:yaml.org,2002:seq';\n    }\n    constructor(schema) {\n        super(SEQ, schema);\n        this.items = [];\n    }\n    add(value) {\n        this.items.push(value);\n    }\n    /**\n     * Removes a value from the collection.\n     *\n     * `key` must contain a representation of an integer for this to succeed.\n     * It may be wrapped in a `Scalar`.\n     *\n     * @returns `true` if the item was found and removed.\n     */\n    delete(key) {\n        const idx = asItemIndex(key);\n        if (typeof idx !== 'number')\n            return false;\n        const del = this.items.splice(idx, 1);\n        return del.length > 0;\n    }\n    get(key, keepScalar) {\n        const idx = asItemIndex(key);\n        if (typeof idx !== 'number')\n            return undefined;\n        const it = this.items[idx];\n        return !keepScalar && isScalar(it) ? it.value : it;\n    }\n    /**\n     * Checks if the collection includes a value with the key `key`.\n     *\n     * `key` must contain a representation of an integer for this to succeed.\n     * It may be wrapped in a `Scalar`.\n     */\n    has(key) {\n        const idx = asItemIndex(key);\n        return typeof idx === 'number' && idx < this.items.length;\n    }\n    /**\n     * Sets a value in this collection. For `!!set`, `value` needs to be a\n     * boolean to add/remove the item from the set.\n     *\n     * If `key` does not contain a representation of an integer, this will throw.\n     * It may be wrapped in a `Scalar`.\n     */\n    set(key, value) {\n        const idx = asItemIndex(key);\n        if (typeof idx !== 'number')\n            throw new Error(`Expected a valid index, not ${key}.`);\n        const prev = this.items[idx];\n        if (isScalar(prev) && isScalarValue(value))\n            prev.value = value;\n        else\n            this.items[idx] = value;\n    }\n    toJSON(_, ctx) {\n        const seq = [];\n        if (ctx?.onCreate)\n            ctx.onCreate(seq);\n        let i = 0;\n        for (const item of this.items)\n            seq.push(toJS(item, String(i++), ctx));\n        return seq;\n    }\n    toString(ctx, onComment, onChompKeep) {\n        if (!ctx)\n            return JSON.stringify(this);\n        return stringifyCollection(this, ctx, {\n            blockItemPrefix: '- ',\n            flowChars: { start: '[', end: ']' },\n            itemIndent: (ctx.indent || '') + '  ',\n            onChompKeep,\n            onComment\n        });\n    }\n    static from(schema, obj, ctx) {\n        const { replacer } = ctx;\n        const seq = new this(schema);\n        if (obj && Symbol.iterator in Object(obj)) {\n            let i = 0;\n            for (let it of obj) {\n                if (typeof replacer === 'function') {\n                    const key = obj instanceof Set ? it : String(i++);\n                    it = replacer.call(obj, key, it);\n                }\n                seq.items.push(createNode(it, undefined, ctx));\n            }\n        }\n        return seq;\n    }\n}\nfunction asItemIndex(key) {\n    let idx = isScalar(key) ? key.value : key;\n    if (idx && typeof idx === 'string')\n        idx = Number(idx);\n    return typeof idx === 'number' && Number.isInteger(idx) && idx >= 0\n        ? idx\n        : null;\n}\n\nexport { YAMLSeq };\n", "import { isSeq } from '../../nodes/identity.js';\nimport { YAMLSeq } from '../../nodes/YAMLSeq.js';\n\nconst seq = {\n    collection: 'seq',\n    default: true,\n    nodeClass: YAMLSeq,\n    tag: 'tag:yaml.org,2002:seq',\n    resolve(seq, onError) {\n        if (!isSeq(seq))\n            onError('Expected a sequence for this tag');\n        return seq;\n    },\n    createNode: (schema, obj, ctx) => YAMLSeq.from(schema, obj, ctx)\n};\n\nexport { seq };\n", "import { stringifyString } from '../../stringify/stringifyString.js';\n\nconst string = {\n    identify: value => typeof value === 'string',\n    default: true,\n    tag: 'tag:yaml.org,2002:str',\n    resolve: str => str,\n    stringify(item, ctx, onComment, onChompKeep) {\n        ctx = Object.assign({ actualString: true }, ctx);\n        return stringifyString(item, ctx, onComment, onChompKeep);\n    }\n};\n\nexport { string };\n", "import { Scalar } from '../../nodes/Scalar.js';\n\nconst nullTag = {\n    identify: value => value == null,\n    createNode: () => new Scalar(null),\n    default: true,\n    tag: 'tag:yaml.org,2002:null',\n    test: /^(?:~|[Nn]ull|NULL)?$/,\n    resolve: () => new Scalar(null),\n    stringify: ({ source }, ctx) => typeof source === 'string' && nullTag.test.test(source)\n        ? source\n        : ctx.options.nullStr\n};\n\nexport { nullTag };\n", "import { Scalar } from '../../nodes/Scalar.js';\n\nconst boolTag = {\n    identify: value => typeof value === 'boolean',\n    default: true,\n    tag: 'tag:yaml.org,2002:bool',\n    test: /^(?:[Tt]rue|TRUE|[Ff]alse|FALSE)$/,\n    resolve: str => new Scalar(str[0] === 't' || str[0] === 'T'),\n    stringify({ source, value }, ctx) {\n        if (source && boolTag.test.test(source)) {\n            const sv = source[0] === 't' || source[0] === 'T';\n            if (value === sv)\n                return source;\n        }\n        return value ? ctx.options.trueStr : ctx.options.falseStr;\n    }\n};\n\nexport { boolTag };\n", "function stringifyNumber({ format, minFractionDigits, tag, value }) {\n    if (typeof value === 'bigint')\n        return String(value);\n    const num = typeof value === 'number' ? value : Number(value);\n    if (!isFinite(num))\n        return isNaN(num) ? '.nan' : num < 0 ? '-.inf' : '.inf';\n    let n = JSON.stringify(value);\n    if (!format &&\n        minFractionDigits &&\n        (!tag || tag === 'tag:yaml.org,2002:float') &&\n        /^\\d/.test(n)) {\n        let i = n.indexOf('.');\n        if (i < 0) {\n            i = n.length;\n            n += '.';\n        }\n        let d = minFractionDigits - (n.length - i - 1);\n        while (d-- > 0)\n            n += '0';\n    }\n    return n;\n}\n\nexport { stringifyNumber };\n", "import { <PERSON>alar } from '../../nodes/Scalar.js';\nimport { stringifyNumber } from '../../stringify/stringifyNumber.js';\n\nconst floatNaN = {\n    identify: value => typeof value === 'number',\n    default: true,\n    tag: 'tag:yaml.org,2002:float',\n    test: /^(?:[-+]?\\.(?:inf|Inf|INF)|\\.nan|\\.NaN|\\.NAN)$/,\n    resolve: str => str.slice(-3).toLowerCase() === 'nan'\n        ? NaN\n        : str[0] === '-'\n            ? Number.NEGATIVE_INFINITY\n            : Number.POSITIVE_INFINITY,\n    stringify: stringifyNumber\n};\nconst floatExp = {\n    identify: value => typeof value === 'number',\n    default: true,\n    tag: 'tag:yaml.org,2002:float',\n    format: 'EXP',\n    test: /^[-+]?(?:\\.[0-9]+|[0-9]+(?:\\.[0-9]*)?)[eE][-+]?[0-9]+$/,\n    resolve: str => parseFloat(str),\n    stringify(node) {\n        const num = Number(node.value);\n        return isFinite(num) ? num.toExponential() : stringifyNumber(node);\n    }\n};\nconst float = {\n    identify: value => typeof value === 'number',\n    default: true,\n    tag: 'tag:yaml.org,2002:float',\n    test: /^[-+]?(?:\\.[0-9]+|[0-9]+\\.[0-9]*)$/,\n    resolve(str) {\n        const node = new Scalar(parseFloat(str));\n        const dot = str.indexOf('.');\n        if (dot !== -1 && str[str.length - 1] === '0')\n            node.minFractionDigits = str.length - dot - 1;\n        return node;\n    },\n    stringify: stringifyNumber\n};\n\nexport { float, floatExp, floatNaN };\n", "import { stringifyNumber } from '../../stringify/stringifyNumber.js';\n\nconst intIdentify = (value) => typeof value === 'bigint' || Number.isInteger(value);\nconst intResolve = (str, offset, radix, { intAsBigInt }) => (intAsBigInt ? BigInt(str) : parseInt(str.substring(offset), radix));\nfunction intStringify(node, radix, prefix) {\n    const { value } = node;\n    if (intIdentify(value) && value >= 0)\n        return prefix + value.toString(radix);\n    return stringifyNumber(node);\n}\nconst intOct = {\n    identify: value => intIdentify(value) && value >= 0,\n    default: true,\n    tag: 'tag:yaml.org,2002:int',\n    format: 'OCT',\n    test: /^0o[0-7]+$/,\n    resolve: (str, _onError, opt) => intResolve(str, 2, 8, opt),\n    stringify: node => intStringify(node, 8, '0o')\n};\nconst int = {\n    identify: intIdentify,\n    default: true,\n    tag: 'tag:yaml.org,2002:int',\n    test: /^[-+]?[0-9]+$/,\n    resolve: (str, _onError, opt) => intResolve(str, 0, 10, opt),\n    stringify: stringifyNumber\n};\nconst intHex = {\n    identify: value => intIdentify(value) && value >= 0,\n    default: true,\n    tag: 'tag:yaml.org,2002:int',\n    format: 'HEX',\n    test: /^0x[0-9a-fA-F]+$/,\n    resolve: (str, _onError, opt) => intResolve(str, 2, 16, opt),\n    stringify: node => intStringify(node, 16, '0x')\n};\n\nexport { int, intHex, intOct };\n", "import { map } from '../common/map.js';\nimport { nullTag } from '../common/null.js';\nimport { seq } from '../common/seq.js';\nimport { string } from '../common/string.js';\nimport { boolTag } from './bool.js';\nimport { floatNaN, floatExp, float } from './float.js';\nimport { intOct, int, intHex } from './int.js';\n\nconst schema = [\n    map,\n    seq,\n    string,\n    nullTag,\n    boolTag,\n    intOct,\n    int,\n    intHex,\n    floatNaN,\n    floatExp,\n    float\n];\n\nexport { schema };\n", "import { Scalar } from '../../nodes/Scalar.js';\nimport { map } from '../common/map.js';\nimport { seq } from '../common/seq.js';\n\nfunction intIdentify(value) {\n    return typeof value === 'bigint' || Number.isInteger(value);\n}\nconst stringifyJSON = ({ value }) => JSON.stringify(value);\nconst jsonScalars = [\n    {\n        identify: value => typeof value === 'string',\n        default: true,\n        tag: 'tag:yaml.org,2002:str',\n        resolve: str => str,\n        stringify: stringifyJSON\n    },\n    {\n        identify: value => value == null,\n        createNode: () => new Scalar(null),\n        default: true,\n        tag: 'tag:yaml.org,2002:null',\n        test: /^null$/,\n        resolve: () => null,\n        stringify: stringifyJSON\n    },\n    {\n        identify: value => typeof value === 'boolean',\n        default: true,\n        tag: 'tag:yaml.org,2002:bool',\n        test: /^true$|^false$/,\n        resolve: str => str === 'true',\n        stringify: stringifyJSON\n    },\n    {\n        identify: intIdentify,\n        default: true,\n        tag: 'tag:yaml.org,2002:int',\n        test: /^-?(?:0|[1-9][0-9]*)$/,\n        resolve: (str, _onError, { intAsBigInt }) => intAsBigInt ? BigInt(str) : parseInt(str, 10),\n        stringify: ({ value }) => intIdentify(value) ? value.toString() : JSON.stringify(value)\n    },\n    {\n        identify: value => typeof value === 'number',\n        default: true,\n        tag: 'tag:yaml.org,2002:float',\n        test: /^-?(?:0|[1-9][0-9]*)(?:\\.[0-9]*)?(?:[eE][-+]?[0-9]+)?$/,\n        resolve: str => parseFloat(str),\n        stringify: stringifyJSON\n    }\n];\nconst jsonError = {\n    default: true,\n    tag: '',\n    test: /^/,\n    resolve(str, onError) {\n        onError(`Unresolved plain scalar ${JSON.stringify(str)}`);\n        return str;\n    }\n};\nconst schema = [map, seq].concat(jsonScalars, jsonError);\n\nexport { schema };\n", "import { Scalar } from '../../nodes/Scalar.js';\nimport { stringifyString } from '../../stringify/stringifyString.js';\n\nconst binary = {\n    identify: value => value instanceof Uint8Array, // Buffer inherits from Uint8Array\n    default: false,\n    tag: 'tag:yaml.org,2002:binary',\n    /**\n     * Returns a Buffer in node and an Uint8Array in browsers\n     *\n     * To use the resulting buffer as an image, you'll want to do something like:\n     *\n     *   const blob = new Blob([buffer], { type: 'image/jpeg' })\n     *   document.querySelector('#photo').src = URL.createObjectURL(blob)\n     */\n    resolve(src, onError) {\n        if (typeof atob === 'function') {\n            // On IE 11, atob() can't handle newlines\n            const str = atob(src.replace(/[\\n\\r]/g, ''));\n            const buffer = new Uint8Array(str.length);\n            for (let i = 0; i < str.length; ++i)\n                buffer[i] = str.charCodeAt(i);\n            return buffer;\n        }\n        else {\n            onError('This environment does not support reading binary tags; either Buffer or atob is required');\n            return src;\n        }\n    },\n    stringify({ comment, type, value }, ctx, onComment, onChompKeep) {\n        if (!value)\n            return '';\n        const buf = value; // checked earlier by binary.identify()\n        let str;\n        if (typeof btoa === 'function') {\n            let s = '';\n            for (let i = 0; i < buf.length; ++i)\n                s += String.fromCharCode(buf[i]);\n            str = btoa(s);\n        }\n        else {\n            throw new Error('This environment does not support writing binary tags; either Buffer or btoa is required');\n        }\n        type ?? (type = Scalar.BLOCK_LITERAL);\n        if (type !== Scalar.QUOTE_DOUBLE) {\n            const lineWidth = Math.max(ctx.options.lineWidth - ctx.indent.length, ctx.options.minContentWidth);\n            const n = Math.ceil(str.length / lineWidth);\n            const lines = new Array(n);\n            for (let i = 0, o = 0; i < n; ++i, o += lineWidth) {\n                lines[i] = str.substr(o, lineWidth);\n            }\n            str = lines.join(type === Scalar.BLOCK_LITERAL ? '\\n' : ' ');\n        }\n        return stringifyString({ comment, type, value: str }, ctx, onComment, onChompKeep);\n    }\n};\n\nexport { binary };\n", "import { isSeq, isPair, isMap } from '../../nodes/identity.js';\nimport { createPair, Pair } from '../../nodes/Pair.js';\nimport { Scalar } from '../../nodes/Scalar.js';\nimport { YAMLSeq } from '../../nodes/YAMLSeq.js';\n\nfunction resolvePairs(seq, onError) {\n    if (isSeq(seq)) {\n        for (let i = 0; i < seq.items.length; ++i) {\n            let item = seq.items[i];\n            if (isPair(item))\n                continue;\n            else if (isMap(item)) {\n                if (item.items.length > 1)\n                    onError('Each pair must have its own sequence indicator');\n                const pair = item.items[0] || new Pair(new Scalar(null));\n                if (item.commentBefore)\n                    pair.key.commentBefore = pair.key.commentBefore\n                        ? `${item.commentBefore}\\n${pair.key.commentBefore}`\n                        : item.commentBefore;\n                if (item.comment) {\n                    const cn = pair.value ?? pair.key;\n                    cn.comment = cn.comment\n                        ? `${item.comment}\\n${cn.comment}`\n                        : item.comment;\n                }\n                item = pair;\n            }\n            seq.items[i] = isPair(item) ? item : new Pair(item);\n        }\n    }\n    else\n        onError('Expected a sequence for this tag');\n    return seq;\n}\nfunction createPairs(schema, iterable, ctx) {\n    const { replacer } = ctx;\n    const pairs = new YAMLSeq(schema);\n    pairs.tag = 'tag:yaml.org,2002:pairs';\n    let i = 0;\n    if (iterable && Symbol.iterator in Object(iterable))\n        for (let it of iterable) {\n            if (typeof replacer === 'function')\n                it = replacer.call(iterable, String(i++), it);\n            let key, value;\n            if (Array.isArray(it)) {\n                if (it.length === 2) {\n                    key = it[0];\n                    value = it[1];\n                }\n                else\n                    throw new TypeError(`Expected [key, value] tuple: ${it}`);\n            }\n            else if (it && it instanceof Object) {\n                const keys = Object.keys(it);\n                if (keys.length === 1) {\n                    key = keys[0];\n                    value = it[key];\n                }\n                else {\n                    throw new TypeError(`Expected tuple with one key, not ${keys.length} keys`);\n                }\n            }\n            else {\n                key = it;\n            }\n            pairs.items.push(createPair(key, value, ctx));\n        }\n    return pairs;\n}\nconst pairs = {\n    collection: 'seq',\n    default: false,\n    tag: 'tag:yaml.org,2002:pairs',\n    resolve: resolvePairs,\n    createNode: createPairs\n};\n\nexport { createPairs, pairs, resolvePairs };\n", "import { isScalar, isPair } from '../../nodes/identity.js';\nimport { toJS } from '../../nodes/toJS.js';\nimport { YAMLMap } from '../../nodes/YAMLMap.js';\nimport { YAMLSeq } from '../../nodes/YAMLSeq.js';\nimport { resolvePairs, createPairs } from './pairs.js';\n\nclass YAMLOMap extends YAMLSeq {\n    constructor() {\n        super();\n        this.add = YAMLMap.prototype.add.bind(this);\n        this.delete = YAMLMap.prototype.delete.bind(this);\n        this.get = YAMLMap.prototype.get.bind(this);\n        this.has = YAMLMap.prototype.has.bind(this);\n        this.set = YAMLMap.prototype.set.bind(this);\n        this.tag = YAMLOMap.tag;\n    }\n    /**\n     * If `ctx` is given, the return type is actually `Map<unknown, unknown>`,\n     * but TypeScript won't allow widening the signature of a child method.\n     */\n    toJSON(_, ctx) {\n        if (!ctx)\n            return super.toJSON(_);\n        const map = new Map();\n        if (ctx?.onCreate)\n            ctx.onCreate(map);\n        for (const pair of this.items) {\n            let key, value;\n            if (isPair(pair)) {\n                key = toJS(pair.key, '', ctx);\n                value = toJS(pair.value, key, ctx);\n            }\n            else {\n                key = toJS(pair, '', ctx);\n            }\n            if (map.has(key))\n                throw new Error('Ordered maps must not include duplicate keys');\n            map.set(key, value);\n        }\n        return map;\n    }\n    static from(schema, iterable, ctx) {\n        const pairs = createPairs(schema, iterable, ctx);\n        const omap = new this();\n        omap.items = pairs.items;\n        return omap;\n    }\n}\nYAMLOMap.tag = 'tag:yaml.org,2002:omap';\nconst omap = {\n    collection: 'seq',\n    identify: value => value instanceof Map,\n    nodeClass: YAMLOMap,\n    default: false,\n    tag: 'tag:yaml.org,2002:omap',\n    resolve(seq, onError) {\n        const pairs = resolvePairs(seq, onError);\n        const seenKeys = [];\n        for (const { key } of pairs.items) {\n            if (isScalar(key)) {\n                if (seenKeys.includes(key.value)) {\n                    onError(`Ordered maps must not include duplicate keys: ${key.value}`);\n                }\n                else {\n                    seenKeys.push(key.value);\n                }\n            }\n        }\n        return Object.assign(new YAMLOMap(), pairs);\n    },\n    createNode: (schema, iterable, ctx) => YAMLOMap.from(schema, iterable, ctx)\n};\n\nexport { YAMLOMap, omap };\n", "import { Scalar } from '../../nodes/Scalar.js';\n\nfunction boolStringify({ value, source }, ctx) {\n    const boolObj = value ? trueTag : falseTag;\n    if (source && boolObj.test.test(source))\n        return source;\n    return value ? ctx.options.trueStr : ctx.options.falseStr;\n}\nconst trueTag = {\n    identify: value => value === true,\n    default: true,\n    tag: 'tag:yaml.org,2002:bool',\n    test: /^(?:Y|y|[Yy]es|YES|[Tt]rue|TRUE|[Oo]n|ON)$/,\n    resolve: () => new Scalar(true),\n    stringify: boolStringify\n};\nconst falseTag = {\n    identify: value => value === false,\n    default: true,\n    tag: 'tag:yaml.org,2002:bool',\n    test: /^(?:N|n|[Nn]o|NO|[Ff]alse|FALSE|[Oo]ff|OFF)$/,\n    resolve: () => new Scalar(false),\n    stringify: boolStringify\n};\n\nexport { falseTag, trueTag };\n", "import { <PERSON>alar } from '../../nodes/Scalar.js';\nimport { stringifyNumber } from '../../stringify/stringifyNumber.js';\n\nconst floatNaN = {\n    identify: value => typeof value === 'number',\n    default: true,\n    tag: 'tag:yaml.org,2002:float',\n    test: /^(?:[-+]?\\.(?:inf|Inf|INF)|\\.nan|\\.NaN|\\.NAN)$/,\n    resolve: (str) => str.slice(-3).toLowerCase() === 'nan'\n        ? NaN\n        : str[0] === '-'\n            ? Number.NEGATIVE_INFINITY\n            : Number.POSITIVE_INFINITY,\n    stringify: stringifyNumber\n};\nconst floatExp = {\n    identify: value => typeof value === 'number',\n    default: true,\n    tag: 'tag:yaml.org,2002:float',\n    format: 'EXP',\n    test: /^[-+]?(?:[0-9][0-9_]*)?(?:\\.[0-9_]*)?[eE][-+]?[0-9]+$/,\n    resolve: (str) => parseFloat(str.replace(/_/g, '')),\n    stringify(node) {\n        const num = Number(node.value);\n        return isFinite(num) ? num.toExponential() : stringifyNumber(node);\n    }\n};\nconst float = {\n    identify: value => typeof value === 'number',\n    default: true,\n    tag: 'tag:yaml.org,2002:float',\n    test: /^[-+]?(?:[0-9][0-9_]*)?\\.[0-9_]*$/,\n    resolve(str) {\n        const node = new Scalar(parseFloat(str.replace(/_/g, '')));\n        const dot = str.indexOf('.');\n        if (dot !== -1) {\n            const f = str.substring(dot + 1).replace(/_/g, '');\n            if (f[f.length - 1] === '0')\n                node.minFractionDigits = f.length;\n        }\n        return node;\n    },\n    stringify: stringifyNumber\n};\n\nexport { float, floatExp, floatNaN };\n", "import { stringifyNumber } from '../../stringify/stringifyNumber.js';\n\nconst intIdentify = (value) => typeof value === 'bigint' || Number.isInteger(value);\nfunction intResolve(str, offset, radix, { intAsBigInt }) {\n    const sign = str[0];\n    if (sign === '-' || sign === '+')\n        offset += 1;\n    str = str.substring(offset).replace(/_/g, '');\n    if (intAsBigInt) {\n        switch (radix) {\n            case 2:\n                str = `0b${str}`;\n                break;\n            case 8:\n                str = `0o${str}`;\n                break;\n            case 16:\n                str = `0x${str}`;\n                break;\n        }\n        const n = BigInt(str);\n        return sign === '-' ? BigInt(-1) * n : n;\n    }\n    const n = parseInt(str, radix);\n    return sign === '-' ? -1 * n : n;\n}\nfunction intStringify(node, radix, prefix) {\n    const { value } = node;\n    if (intIdentify(value)) {\n        const str = value.toString(radix);\n        return value < 0 ? '-' + prefix + str.substr(1) : prefix + str;\n    }\n    return stringifyNumber(node);\n}\nconst intBin = {\n    identify: intIdentify,\n    default: true,\n    tag: 'tag:yaml.org,2002:int',\n    format: 'BIN',\n    test: /^[-+]?0b[0-1_]+$/,\n    resolve: (str, _onError, opt) => intResolve(str, 2, 2, opt),\n    stringify: node => intStringify(node, 2, '0b')\n};\nconst intOct = {\n    identify: intIdentify,\n    default: true,\n    tag: 'tag:yaml.org,2002:int',\n    format: 'OCT',\n    test: /^[-+]?0[0-7_]+$/,\n    resolve: (str, _onError, opt) => intResolve(str, 1, 8, opt),\n    stringify: node => intStringify(node, 8, '0')\n};\nconst int = {\n    identify: intIdentify,\n    default: true,\n    tag: 'tag:yaml.org,2002:int',\n    test: /^[-+]?[0-9][0-9_]*$/,\n    resolve: (str, _onError, opt) => intResolve(str, 0, 10, opt),\n    stringify: stringifyNumber\n};\nconst intHex = {\n    identify: intIdentify,\n    default: true,\n    tag: 'tag:yaml.org,2002:int',\n    format: 'HEX',\n    test: /^[-+]?0x[0-9a-fA-F_]+$/,\n    resolve: (str, _onError, opt) => intResolve(str, 2, 16, opt),\n    stringify: node => intStringify(node, 16, '0x')\n};\n\nexport { int, intBin, intHex, intOct };\n", "import { isMap, isPair, isScalar } from '../../nodes/identity.js';\nimport { Pair, createPair } from '../../nodes/Pair.js';\nimport { YAMLMap, findPair } from '../../nodes/YAMLMap.js';\n\nclass YAMLSet extends YAMLMap {\n    constructor(schema) {\n        super(schema);\n        this.tag = YAMLSet.tag;\n    }\n    add(key) {\n        let pair;\n        if (isPair(key))\n            pair = key;\n        else if (key &&\n            typeof key === 'object' &&\n            'key' in key &&\n            'value' in key &&\n            key.value === null)\n            pair = new Pair(key.key, null);\n        else\n            pair = new Pair(key, null);\n        const prev = findPair(this.items, pair.key);\n        if (!prev)\n            this.items.push(pair);\n    }\n    /**\n     * If `keepPair` is `true`, returns the Pair matching `key`.\n     * Otherwise, returns the value of that Pair's key.\n     */\n    get(key, keepPair) {\n        const pair = findPair(this.items, key);\n        return !keepPair && isPair(pair)\n            ? isScalar(pair.key)\n                ? pair.key.value\n                : pair.key\n            : pair;\n    }\n    set(key, value) {\n        if (typeof value !== 'boolean')\n            throw new Error(`Expected boolean value for set(key, value) in a YAML set, not ${typeof value}`);\n        const prev = findPair(this.items, key);\n        if (prev && !value) {\n            this.items.splice(this.items.indexOf(prev), 1);\n        }\n        else if (!prev && value) {\n            this.items.push(new Pair(key));\n        }\n    }\n    toJSON(_, ctx) {\n        return super.toJSON(_, ctx, Set);\n    }\n    toString(ctx, onComment, onChompKeep) {\n        if (!ctx)\n            return JSON.stringify(this);\n        if (this.hasAllNullValues(true))\n            return super.toString(Object.assign({}, ctx, { allNullValues: true }), onComment, onChompKeep);\n        else\n            throw new Error('Set items must all have null values');\n    }\n    static from(schema, iterable, ctx) {\n        const { replacer } = ctx;\n        const set = new this(schema);\n        if (iterable && Symbol.iterator in Object(iterable))\n            for (let value of iterable) {\n                if (typeof replacer === 'function')\n                    value = replacer.call(iterable, value, value);\n                set.items.push(createPair(value, null, ctx));\n            }\n        return set;\n    }\n}\nYAMLSet.tag = 'tag:yaml.org,2002:set';\nconst set = {\n    collection: 'map',\n    identify: value => value instanceof Set,\n    nodeClass: YAMLSet,\n    default: false,\n    tag: 'tag:yaml.org,2002:set',\n    createNode: (schema, iterable, ctx) => YAMLSet.from(schema, iterable, ctx),\n    resolve(map, onError) {\n        if (isMap(map)) {\n            if (map.hasAllNullValues(true))\n                return Object.assign(new YAMLSet(), map);\n            else\n                onError('Set items must all have null values');\n        }\n        else\n            onError('Expected a mapping for this tag');\n        return map;\n    }\n};\n\nexport { YAMLSet, set };\n", "import { stringifyNumber } from '../../stringify/stringifyNumber.js';\n\n/** Internal types handle bigint as number, because TS can't figure it out. */\nfunction parseSexagesimal(str, asBigInt) {\n    const sign = str[0];\n    const parts = sign === '-' || sign === '+' ? str.substring(1) : str;\n    const num = (n) => asBigInt ? BigInt(n) : Number(n);\n    const res = parts\n        .replace(/_/g, '')\n        .split(':')\n        .reduce((res, p) => res * num(60) + num(p), num(0));\n    return (sign === '-' ? num(-1) * res : res);\n}\n/**\n * hhhh:mm:ss.sss\n *\n * Internal types handle bigint as number, because TS can't figure it out.\n */\nfunction stringifySexagesimal(node) {\n    let { value } = node;\n    let num = (n) => n;\n    if (typeof value === 'bigint')\n        num = n => BigInt(n);\n    else if (isNaN(value) || !isFinite(value))\n        return stringifyNumber(node);\n    let sign = '';\n    if (value < 0) {\n        sign = '-';\n        value *= num(-1);\n    }\n    const _60 = num(60);\n    const parts = [value % _60]; // seconds, including ms\n    if (value < 60) {\n        parts.unshift(0); // at least one : is required\n    }\n    else {\n        value = (value - parts[0]) / _60;\n        parts.unshift(value % _60); // minutes\n        if (value >= 60) {\n            value = (value - parts[0]) / _60;\n            parts.unshift(value); // hours\n        }\n    }\n    return (sign +\n        parts\n            .map(n => String(n).padStart(2, '0'))\n            .join(':')\n            .replace(/000000\\d*$/, '') // % 60 may introduce error\n    );\n}\nconst intTime = {\n    identify: value => typeof value === 'bigint' || Number.isInteger(value),\n    default: true,\n    tag: 'tag:yaml.org,2002:int',\n    format: 'TIME',\n    test: /^[-+]?[0-9][0-9_]*(?::[0-5]?[0-9])+$/,\n    resolve: (str, _onError, { intAsBigInt }) => parseSexagesimal(str, intAsBigInt),\n    stringify: stringifySexagesimal\n};\nconst floatTime = {\n    identify: value => typeof value === 'number',\n    default: true,\n    tag: 'tag:yaml.org,2002:float',\n    format: 'TIME',\n    test: /^[-+]?[0-9][0-9_]*(?::[0-5]?[0-9])+\\.[0-9_]*$/,\n    resolve: str => parseSexagesimal(str, false),\n    stringify: stringifySexagesimal\n};\nconst timestamp = {\n    identify: value => value instanceof Date,\n    default: true,\n    tag: 'tag:yaml.org,2002:timestamp',\n    // If the time zone is omitted, the timestamp is assumed to be specified in UTC. The time part\n    // may be omitted altogether, resulting in a date format. In such a case, the time part is\n    // assumed to be 00:00:00Z (start of day, UTC).\n    test: RegExp('^([0-9]{4})-([0-9]{1,2})-([0-9]{1,2})' + // YYYY-Mm-Dd\n        '(?:' + // time is optional\n        '(?:t|T|[ \\\\t]+)' + // t | T | whitespace\n        '([0-9]{1,2}):([0-9]{1,2}):([0-9]{1,2}(\\\\.[0-9]+)?)' + // Hh:Mm:Ss(.ss)?\n        '(?:[ \\\\t]*(Z|[-+][012]?[0-9](?::[0-9]{2})?))?' + // Z | +5 | -03:30\n        ')?$'),\n    resolve(str) {\n        const match = str.match(timestamp.test);\n        if (!match)\n            throw new Error('!!timestamp expects a date, starting with yyyy-mm-dd');\n        const [, year, month, day, hour, minute, second] = match.map(Number);\n        const millisec = match[7] ? Number((match[7] + '00').substr(1, 3)) : 0;\n        let date = Date.UTC(year, month - 1, day, hour || 0, minute || 0, second || 0, millisec);\n        const tz = match[8];\n        if (tz && tz !== 'Z') {\n            let d = parseSexagesimal(tz, false);\n            if (Math.abs(d) < 30)\n                d *= 60;\n            date -= 60000 * d;\n        }\n        return new Date(date);\n    },\n    stringify: ({ value }) => value?.toISOString().replace(/(T00:00:00)?\\.000Z$/, '') ?? ''\n};\n\nexport { floatTime, intTime, timestamp };\n", "import { map } from '../common/map.js';\nimport { nullTag } from '../common/null.js';\nimport { seq } from '../common/seq.js';\nimport { string } from '../common/string.js';\nimport { binary } from './binary.js';\nimport { trueTag, falseTag } from './bool.js';\nimport { floatNaN, floatExp, float } from './float.js';\nimport { intBin, intOct, int, intHex } from './int.js';\nimport { merge } from './merge.js';\nimport { omap } from './omap.js';\nimport { pairs } from './pairs.js';\nimport { set } from './set.js';\nimport { intTime, floatTime, timestamp } from './timestamp.js';\n\nconst schema = [\n    map,\n    seq,\n    string,\n    nullTag,\n    trueTag,\n    falseTag,\n    intBin,\n    intOct,\n    int,\n    intHex,\n    floatNaN,\n    floatExp,\n    float,\n    binary,\n    merge,\n    omap,\n    pairs,\n    set,\n    intTime,\n    floatTime,\n    timestamp\n];\n\nexport { schema };\n", "import { map } from './common/map.js';\nimport { nullTag } from './common/null.js';\nimport { seq } from './common/seq.js';\nimport { string } from './common/string.js';\nimport { boolTag } from './core/bool.js';\nimport { floatNaN, floatExp, float } from './core/float.js';\nimport { intOct, intHex, int } from './core/int.js';\nimport { schema } from './core/schema.js';\nimport { schema as schema$1 } from './json/schema.js';\nimport { binary } from './yaml-1.1/binary.js';\nimport { merge } from './yaml-1.1/merge.js';\nimport { omap } from './yaml-1.1/omap.js';\nimport { pairs } from './yaml-1.1/pairs.js';\nimport { schema as schema$2 } from './yaml-1.1/schema.js';\nimport { set } from './yaml-1.1/set.js';\nimport { timestamp, intTime, floatTime } from './yaml-1.1/timestamp.js';\n\nconst schemas = new Map([\n    ['core', schema],\n    ['failsafe', [map, seq, string]],\n    ['json', schema$1],\n    ['yaml11', schema$2],\n    ['yaml-1.1', schema$2]\n]);\nconst tagsByName = {\n    binary,\n    bool: boolTag,\n    float,\n    floatExp,\n    floatNaN,\n    floatTime,\n    int,\n    intHex,\n    intOct,\n    intTime,\n    map,\n    merge,\n    null: nullTag,\n    omap,\n    pairs,\n    seq,\n    set,\n    timestamp\n};\nconst coreKnownTags = {\n    'tag:yaml.org,2002:binary': binary,\n    'tag:yaml.org,2002:merge': merge,\n    'tag:yaml.org,2002:omap': omap,\n    'tag:yaml.org,2002:pairs': pairs,\n    'tag:yaml.org,2002:set': set,\n    'tag:yaml.org,2002:timestamp': timestamp\n};\nfunction getTags(customTags, schemaName, addMergeTag) {\n    const schemaTags = schemas.get(schemaName);\n    if (schemaTags && !customTags) {\n        return addMergeTag && !schemaTags.includes(merge)\n            ? schemaTags.concat(merge)\n            : schemaTags.slice();\n    }\n    let tags = schemaTags;\n    if (!tags) {\n        if (Array.isArray(customTags))\n            tags = [];\n        else {\n            const keys = Array.from(schemas.keys())\n                .filter(key => key !== 'yaml11')\n                .map(key => JSON.stringify(key))\n                .join(', ');\n            throw new Error(`Unknown schema \"${schemaName}\"; use one of ${keys} or define customTags array`);\n        }\n    }\n    if (Array.isArray(customTags)) {\n        for (const tag of customTags)\n            tags = tags.concat(tag);\n    }\n    else if (typeof customTags === 'function') {\n        tags = customTags(tags.slice());\n    }\n    if (addMergeTag)\n        tags = tags.concat(merge);\n    return tags.reduce((tags, tag) => {\n        const tagObj = typeof tag === 'string' ? tagsByName[tag] : tag;\n        if (!tagObj) {\n            const tagName = JSON.stringify(tag);\n            const keys = Object.keys(tagsByName)\n                .map(key => JSON.stringify(key))\n                .join(', ');\n            throw new Error(`Unknown custom tag ${tagName}; use one of ${keys}`);\n        }\n        if (!tags.includes(tagObj))\n            tags.push(tagObj);\n        return tags;\n    }, []);\n}\n\nexport { coreKnownTags, getTags };\n", "import { MAP, SCALAR, SEQ } from '../nodes/identity.js';\nimport { map } from './common/map.js';\nimport { seq } from './common/seq.js';\nimport { string } from './common/string.js';\nimport { getTags, coreKnownTags } from './tags.js';\n\nconst sortMapEntriesByKey = (a, b) => a.key < b.key ? -1 : a.key > b.key ? 1 : 0;\nclass Schema {\n    constructor({ compat, customTags, merge, resolveKnownTags, schema, sortMapEntries, toStringDefaults }) {\n        this.compat = Array.isArray(compat)\n            ? getTags(compat, 'compat')\n            : compat\n                ? getTags(null, compat)\n                : null;\n        this.name = (typeof schema === 'string' && schema) || 'core';\n        this.knownTags = resolveKnownTags ? coreKnownTags : {};\n        this.tags = getTags(customTags, this.name, merge);\n        this.toStringOptions = toStringDefaults ?? null;\n        Object.defineProperty(this, MAP, { value: map });\n        Object.defineProperty(this, SCALAR, { value: string });\n        Object.defineProperty(this, SEQ, { value: seq });\n        // Used by createMap()\n        this.sortMapEntries =\n            typeof sortMapEntries === 'function'\n                ? sortMapEntries\n                : sortMapEntries === true\n                    ? sortMapEntriesByKey\n                    : null;\n    }\n    clone() {\n        const copy = Object.create(Schema.prototype, Object.getOwnPropertyDescriptors(this));\n        copy.tags = this.tags.slice();\n        return copy;\n    }\n}\n\nexport { Schema };\n", "import { isNode } from '../nodes/identity.js';\nimport { createStringifyContext, stringify } from './stringify.js';\nimport { indentComment, lineComment } from './stringifyComment.js';\n\nfunction stringifyDocument(doc, options) {\n    const lines = [];\n    let hasDirectives = options.directives === true;\n    if (options.directives !== false && doc.directives) {\n        const dir = doc.directives.toString(doc);\n        if (dir) {\n            lines.push(dir);\n            hasDirectives = true;\n        }\n        else if (doc.directives.docStart)\n            hasDirectives = true;\n    }\n    if (hasDirectives)\n        lines.push('---');\n    const ctx = createStringifyContext(doc, options);\n    const { commentString } = ctx.options;\n    if (doc.commentBefore) {\n        if (lines.length !== 1)\n            lines.unshift('');\n        const cs = commentString(doc.commentBefore);\n        lines.unshift(indentComment(cs, ''));\n    }\n    let chompKeep = false;\n    let contentComment = null;\n    if (doc.contents) {\n        if (isNode(doc.contents)) {\n            if (doc.contents.spaceBefore && hasDirectives)\n                lines.push('');\n            if (doc.contents.commentBefore) {\n                const cs = commentString(doc.contents.commentBefore);\n                lines.push(indentComment(cs, ''));\n            }\n            // top-level block scalars need to be indented if followed by a comment\n            ctx.forceBlockIndent = !!doc.comment;\n            contentComment = doc.contents.comment;\n        }\n        const onChompKeep = contentComment ? undefined : () => (chompKeep = true);\n        let body = stringify(doc.contents, ctx, () => (contentComment = null), onChompKeep);\n        if (contentComment)\n            body += lineComment(body, '', commentString(contentComment));\n        if ((body[0] === '|' || body[0] === '>') &&\n            lines[lines.length - 1] === '---') {\n            // Top-level block scalars with a preceding doc marker ought to use the\n            // same line for their header.\n            lines[lines.length - 1] = `--- ${body}`;\n        }\n        else\n            lines.push(body);\n    }\n    else {\n        lines.push(stringify(doc.contents, ctx));\n    }\n    if (doc.directives?.docEnd) {\n        if (doc.comment) {\n            const cs = commentString(doc.comment);\n            if (cs.includes('\\n')) {\n                lines.push('...');\n                lines.push(indentComment(cs, ''));\n            }\n            else {\n                lines.push(`... ${cs}`);\n            }\n        }\n        else {\n            lines.push('...');\n        }\n    }\n    else {\n        let dc = doc.comment;\n        if (dc && chompKeep)\n            dc = dc.replace(/^\\n+/, '');\n        if (dc) {\n            if ((!chompKeep || contentComment) && lines[lines.length - 1] !== '')\n                lines.push('');\n            lines.push(indentComment(commentString(dc), ''));\n        }\n    }\n    return lines.join('\\n') + '\\n';\n}\n\nexport { stringifyDocument };\n", "import { Alias } from '../nodes/Alias.js';\nimport { isEmptyPath, collectionFromPath } from '../nodes/Collection.js';\nimport { NODE_TYPE, DOC, isNode, isCollection, isScalar } from '../nodes/identity.js';\nimport { Pair } from '../nodes/Pair.js';\nimport { toJS } from '../nodes/toJS.js';\nimport { Schema } from '../schema/Schema.js';\nimport { stringifyDocument } from '../stringify/stringifyDocument.js';\nimport { anchorNames, findNewAnchor, createNodeAnchors } from './anchors.js';\nimport { applyReviver } from './applyReviver.js';\nimport { createNode } from './createNode.js';\nimport { Directives } from './directives.js';\n\nclass Document {\n    constructor(value, replacer, options) {\n        /** A comment before this Document */\n        this.commentBefore = null;\n        /** A comment immediately after this Document */\n        this.comment = null;\n        /** Errors encountered during parsing. */\n        this.errors = [];\n        /** Warnings encountered during parsing. */\n        this.warnings = [];\n        Object.defineProperty(this, NODE_TYPE, { value: DOC });\n        let _replacer = null;\n        if (typeof replacer === 'function' || Array.isArray(replacer)) {\n            _replacer = replacer;\n        }\n        else if (options === undefined && replacer) {\n            options = replacer;\n            replacer = undefined;\n        }\n        const opt = Object.assign({\n            intAsBigInt: false,\n            keepSourceTokens: false,\n            logLevel: 'warn',\n            prettyErrors: true,\n            strict: true,\n            stringKeys: false,\n            uniqueKeys: true,\n            version: '1.2'\n        }, options);\n        this.options = opt;\n        let { version } = opt;\n        if (options?._directives) {\n            this.directives = options._directives.atDocument();\n            if (this.directives.yaml.explicit)\n                version = this.directives.yaml.version;\n        }\n        else\n            this.directives = new Directives({ version });\n        this.setSchema(version, options);\n        // @ts-expect-error We can't really know that this matches Contents.\n        this.contents =\n            value === undefined ? null : this.createNode(value, _replacer, options);\n    }\n    /**\n     * Create a deep copy of this Document and its contents.\n     *\n     * Custom Node values that inherit from `Object` still refer to their original instances.\n     */\n    clone() {\n        const copy = Object.create(Document.prototype, {\n            [NODE_TYPE]: { value: DOC }\n        });\n        copy.commentBefore = this.commentBefore;\n        copy.comment = this.comment;\n        copy.errors = this.errors.slice();\n        copy.warnings = this.warnings.slice();\n        copy.options = Object.assign({}, this.options);\n        if (this.directives)\n            copy.directives = this.directives.clone();\n        copy.schema = this.schema.clone();\n        // @ts-expect-error We can't really know that this matches Contents.\n        copy.contents = isNode(this.contents)\n            ? this.contents.clone(copy.schema)\n            : this.contents;\n        if (this.range)\n            copy.range = this.range.slice();\n        return copy;\n    }\n    /** Adds a value to the document. */\n    add(value) {\n        if (assertCollection(this.contents))\n            this.contents.add(value);\n    }\n    /** Adds a value to the document. */\n    addIn(path, value) {\n        if (assertCollection(this.contents))\n            this.contents.addIn(path, value);\n    }\n    /**\n     * Create a new `Alias` node, ensuring that the target `node` has the required anchor.\n     *\n     * If `node` already has an anchor, `name` is ignored.\n     * Otherwise, the `node.anchor` value will be set to `name`,\n     * or if an anchor with that name is already present in the document,\n     * `name` will be used as a prefix for a new unique anchor.\n     * If `name` is undefined, the generated anchor will use 'a' as a prefix.\n     */\n    createAlias(node, name) {\n        if (!node.anchor) {\n            const prev = anchorNames(this);\n            node.anchor =\n                // eslint-disable-next-line @typescript-eslint/prefer-nullish-coalescing\n                !name || prev.has(name) ? findNewAnchor(name || 'a', prev) : name;\n        }\n        return new Alias(node.anchor);\n    }\n    createNode(value, replacer, options) {\n        let _replacer = undefined;\n        if (typeof replacer === 'function') {\n            value = replacer.call({ '': value }, '', value);\n            _replacer = replacer;\n        }\n        else if (Array.isArray(replacer)) {\n            const keyToStr = (v) => typeof v === 'number' || v instanceof String || v instanceof Number;\n            const asStr = replacer.filter(keyToStr).map(String);\n            if (asStr.length > 0)\n                replacer = replacer.concat(asStr);\n            _replacer = replacer;\n        }\n        else if (options === undefined && replacer) {\n            options = replacer;\n            replacer = undefined;\n        }\n        const { aliasDuplicateObjects, anchorPrefix, flow, keepUndefined, onTagObj, tag } = options ?? {};\n        const { onAnchor, setAnchors, sourceObjects } = createNodeAnchors(this, \n        // eslint-disable-next-line @typescript-eslint/prefer-nullish-coalescing\n        anchorPrefix || 'a');\n        const ctx = {\n            aliasDuplicateObjects: aliasDuplicateObjects ?? true,\n            keepUndefined: keepUndefined ?? false,\n            onAnchor,\n            onTagObj,\n            replacer: _replacer,\n            schema: this.schema,\n            sourceObjects\n        };\n        const node = createNode(value, tag, ctx);\n        if (flow && isCollection(node))\n            node.flow = true;\n        setAnchors();\n        return node;\n    }\n    /**\n     * Convert a key and a value into a `Pair` using the current schema,\n     * recursively wrapping all values as `Scalar` or `Collection` nodes.\n     */\n    createPair(key, value, options = {}) {\n        const k = this.createNode(key, null, options);\n        const v = this.createNode(value, null, options);\n        return new Pair(k, v);\n    }\n    /**\n     * Removes a value from the document.\n     * @returns `true` if the item was found and removed.\n     */\n    delete(key) {\n        return assertCollection(this.contents) ? this.contents.delete(key) : false;\n    }\n    /**\n     * Removes a value from the document.\n     * @returns `true` if the item was found and removed.\n     */\n    deleteIn(path) {\n        if (isEmptyPath(path)) {\n            if (this.contents == null)\n                return false;\n            // @ts-expect-error Presumed impossible if Strict extends false\n            this.contents = null;\n            return true;\n        }\n        return assertCollection(this.contents)\n            ? this.contents.deleteIn(path)\n            : false;\n    }\n    /**\n     * Returns item at `key`, or `undefined` if not found. By default unwraps\n     * scalar values from their surrounding node; to disable set `keepScalar` to\n     * `true` (collections are always returned intact).\n     */\n    get(key, keepScalar) {\n        return isCollection(this.contents)\n            ? this.contents.get(key, keepScalar)\n            : undefined;\n    }\n    /**\n     * Returns item at `path`, or `undefined` if not found. By default unwraps\n     * scalar values from their surrounding node; to disable set `keepScalar` to\n     * `true` (collections are always returned intact).\n     */\n    getIn(path, keepScalar) {\n        if (isEmptyPath(path))\n            return !keepScalar && isScalar(this.contents)\n                ? this.contents.value\n                : this.contents;\n        return isCollection(this.contents)\n            ? this.contents.getIn(path, keepScalar)\n            : undefined;\n    }\n    /**\n     * Checks if the document includes a value with the key `key`.\n     */\n    has(key) {\n        return isCollection(this.contents) ? this.contents.has(key) : false;\n    }\n    /**\n     * Checks if the document includes a value at `path`.\n     */\n    hasIn(path) {\n        if (isEmptyPath(path))\n            return this.contents !== undefined;\n        return isCollection(this.contents) ? this.contents.hasIn(path) : false;\n    }\n    /**\n     * Sets a value in this document. For `!!set`, `value` needs to be a\n     * boolean to add/remove the item from the set.\n     */\n    set(key, value) {\n        if (this.contents == null) {\n            // @ts-expect-error We can't really know that this matches Contents.\n            this.contents = collectionFromPath(this.schema, [key], value);\n        }\n        else if (assertCollection(this.contents)) {\n            this.contents.set(key, value);\n        }\n    }\n    /**\n     * Sets a value in this document. For `!!set`, `value` needs to be a\n     * boolean to add/remove the item from the set.\n     */\n    setIn(path, value) {\n        if (isEmptyPath(path)) {\n            // @ts-expect-error We can't really know that this matches Contents.\n            this.contents = value;\n        }\n        else if (this.contents == null) {\n            // @ts-expect-error We can't really know that this matches Contents.\n            this.contents = collectionFromPath(this.schema, Array.from(path), value);\n        }\n        else if (assertCollection(this.contents)) {\n            this.contents.setIn(path, value);\n        }\n    }\n    /**\n     * Change the YAML version and schema used by the document.\n     * A `null` version disables support for directives, explicit tags, anchors, and aliases.\n     * It also requires the `schema` option to be given as a `Schema` instance value.\n     *\n     * Overrides all previously set schema options.\n     */\n    setSchema(version, options = {}) {\n        if (typeof version === 'number')\n            version = String(version);\n        let opt;\n        switch (version) {\n            case '1.1':\n                if (this.directives)\n                    this.directives.yaml.version = '1.1';\n                else\n                    this.directives = new Directives({ version: '1.1' });\n                opt = { resolveKnownTags: false, schema: 'yaml-1.1' };\n                break;\n            case '1.2':\n            case 'next':\n                if (this.directives)\n                    this.directives.yaml.version = version;\n                else\n                    this.directives = new Directives({ version });\n                opt = { resolveKnownTags: true, schema: 'core' };\n                break;\n            case null:\n                if (this.directives)\n                    delete this.directives;\n                opt = null;\n                break;\n            default: {\n                const sv = JSON.stringify(version);\n                throw new Error(`Expected '1.1', '1.2' or null as first argument, but found: ${sv}`);\n            }\n        }\n        // Not using `instanceof Schema` to allow for duck typing\n        if (options.schema instanceof Object)\n            this.schema = options.schema;\n        else if (opt)\n            this.schema = new Schema(Object.assign(opt, options));\n        else\n            throw new Error(`With a null YAML version, the { schema: Schema } option is required`);\n    }\n    // json & jsonArg are only used from toJSON()\n    toJS({ json, jsonArg, mapAsMap, maxAliasCount, onAnchor, reviver } = {}) {\n        const ctx = {\n            anchors: new Map(),\n            doc: this,\n            keep: !json,\n            mapAsMap: mapAsMap === true,\n            mapKeyWarned: false,\n            maxAliasCount: typeof maxAliasCount === 'number' ? maxAliasCount : 100\n        };\n        const res = toJS(this.contents, jsonArg ?? '', ctx);\n        if (typeof onAnchor === 'function')\n            for (const { count, res } of ctx.anchors.values())\n                onAnchor(res, count);\n        return typeof reviver === 'function'\n            ? applyReviver(reviver, { '': res }, '', res)\n            : res;\n    }\n    /**\n     * A JSON representation of the document `contents`.\n     *\n     * @param jsonArg Used by `JSON.stringify` to indicate the array index or\n     *   property name.\n     */\n    toJSON(jsonArg, onAnchor) {\n        return this.toJS({ json: true, jsonArg, mapAsMap: false, onAnchor });\n    }\n    /** A YAML representation of the document. */\n    toString(options = {}) {\n        if (this.errors.length > 0)\n            throw new Error('Document with errors cannot be stringified');\n        if ('indent' in options &&\n            (!Number.isInteger(options.indent) || Number(options.indent) <= 0)) {\n            const s = JSON.stringify(options.indent);\n            throw new Error(`\"indent\" option must be a positive integer, not ${s}`);\n        }\n        return stringifyDocument(this, options);\n    }\n}\nfunction assertCollection(contents) {\n    if (isCollection(contents))\n        return true;\n    throw new Error('Expected a YAML collection as document contents');\n}\n\nexport { Document };\n", "class YAMLError extends <PERSON><PERSON>r {\n    constructor(name, pos, code, message) {\n        super();\n        this.name = name;\n        this.code = code;\n        this.message = message;\n        this.pos = pos;\n    }\n}\nclass YAMLParseError extends YAMLError {\n    constructor(pos, code, message) {\n        super('YAMLParseError', pos, code, message);\n    }\n}\nclass YAMLWarning extends YAMLError {\n    constructor(pos, code, message) {\n        super('YAMLWarning', pos, code, message);\n    }\n}\nconst prettifyError = (src, lc) => (error) => {\n    if (error.pos[0] === -1)\n        return;\n    error.linePos = error.pos.map(pos => lc.linePos(pos));\n    const { line, col } = error.linePos[0];\n    error.message += ` at line ${line}, column ${col}`;\n    let ci = col - 1;\n    let lineStr = src\n        .substring(lc.lineStarts[line - 1], lc.lineStarts[line])\n        .replace(/[\\n\\r]+$/, '');\n    // Trim to max 80 chars, keeping col position near the middle\n    if (ci >= 60 && lineStr.length > 80) {\n        const trimStart = Math.min(ci - 39, lineStr.length - 79);\n        lineStr = '…' + lineStr.substring(trimStart);\n        ci -= trimStart - 1;\n    }\n    if (lineStr.length > 80)\n        lineStr = lineStr.substring(0, 79) + '…';\n    // Include previous line in context if pointing at line start\n    if (line > 1 && /^ *$/.test(lineStr.substring(0, ci))) {\n        // Regexp won't match if start is trimmed\n        let prev = src.substring(lc.lineStarts[line - 2], lc.lineStarts[line - 1]);\n        if (prev.length > 80)\n            prev = prev.substring(0, 79) + '…\\n';\n        lineStr = prev + lineStr;\n    }\n    if (/[^ ]/.test(lineStr)) {\n        let count = 1;\n        const end = error.linePos[1];\n        if (end && end.line === line && end.col > col) {\n            count = Math.max(1, Math.min(end.col - col, 80 - ci));\n        }\n        const pointer = ' '.repeat(ci) + '^'.repeat(count);\n        error.message += `:\\n\\n${lineStr}\\n${pointer}\\n`;\n    }\n};\n\nexport { YAMLError, YAMLParseError, YAMLWarning, prettifyError };\n", "function resolveProps(tokens, { flow, indicator, next, offset, onError, parentIndent, startOnNewline }) {\n    let spaceBefore = false;\n    let atNewline = startOnNewline;\n    let hasSpace = startOnNewline;\n    let comment = '';\n    let commentSep = '';\n    let hasNewline = false;\n    let reqSpace = false;\n    let tab = null;\n    let anchor = null;\n    let tag = null;\n    let newlineAfterProp = null;\n    let comma = null;\n    let found = null;\n    let start = null;\n    for (const token of tokens) {\n        if (reqSpace) {\n            if (token.type !== 'space' &&\n                token.type !== 'newline' &&\n                token.type !== 'comma')\n                onError(token.offset, 'MISSING_CHAR', 'Tags and anchors must be separated from the next token by white space');\n            reqSpace = false;\n        }\n        if (tab) {\n            if (atNewline && token.type !== 'comment' && token.type !== 'newline') {\n                onError(tab, 'TAB_AS_INDENT', 'Tabs are not allowed as indentation');\n            }\n            tab = null;\n        }\n        switch (token.type) {\n            case 'space':\n                // At the doc level, tabs at line start may be parsed\n                // as leading white space rather than indentation.\n                // In a flow collection, only the parser handles indent.\n                if (!flow &&\n                    (indicator !== 'doc-start' || next?.type !== 'flow-collection') &&\n                    token.source.includes('\\t')) {\n                    tab = token;\n                }\n                hasSpace = true;\n                break;\n            case 'comment': {\n                if (!hasSpace)\n                    onError(token, 'MISSING_CHAR', 'Comments must be separated from other tokens by white space characters');\n                const cb = token.source.substring(1) || ' ';\n                if (!comment)\n                    comment = cb;\n                else\n                    comment += commentSep + cb;\n                commentSep = '';\n                atNewline = false;\n                break;\n            }\n            case 'newline':\n                if (atNewline) {\n                    if (comment)\n                        comment += token.source;\n                    else if (!found || indicator !== 'seq-item-ind')\n                        spaceBefore = true;\n                }\n                else\n                    commentSep += token.source;\n                atNewline = true;\n                hasNewline = true;\n                if (anchor || tag)\n                    newlineAfterProp = token;\n                hasSpace = true;\n                break;\n            case 'anchor':\n                if (anchor)\n                    onError(token, 'MULTIPLE_ANCHORS', 'A node can have at most one anchor');\n                if (token.source.endsWith(':'))\n                    onError(token.offset + token.source.length - 1, 'BAD_ALIAS', 'Anchor ending in : is ambiguous', true);\n                anchor = token;\n                start ?? (start = token.offset);\n                atNewline = false;\n                hasSpace = false;\n                reqSpace = true;\n                break;\n            case 'tag': {\n                if (tag)\n                    onError(token, 'MULTIPLE_TAGS', 'A node can have at most one tag');\n                tag = token;\n                start ?? (start = token.offset);\n                atNewline = false;\n                hasSpace = false;\n                reqSpace = true;\n                break;\n            }\n            case indicator:\n                // Could here handle preceding comments differently\n                if (anchor || tag)\n                    onError(token, 'BAD_PROP_ORDER', `Anchors and tags must be after the ${token.source} indicator`);\n                if (found)\n                    onError(token, 'UNEXPECTED_TOKEN', `Unexpected ${token.source} in ${flow ?? 'collection'}`);\n                found = token;\n                atNewline =\n                    indicator === 'seq-item-ind' || indicator === 'explicit-key-ind';\n                hasSpace = false;\n                break;\n            case 'comma':\n                if (flow) {\n                    if (comma)\n                        onError(token, 'UNEXPECTED_TOKEN', `Unexpected , in ${flow}`);\n                    comma = token;\n                    atNewline = false;\n                    hasSpace = false;\n                    break;\n                }\n            // else fallthrough\n            default:\n                onError(token, 'UNEXPECTED_TOKEN', `Unexpected ${token.type} token`);\n                atNewline = false;\n                hasSpace = false;\n        }\n    }\n    const last = tokens[tokens.length - 1];\n    const end = last ? last.offset + last.source.length : offset;\n    if (reqSpace &&\n        next &&\n        next.type !== 'space' &&\n        next.type !== 'newline' &&\n        next.type !== 'comma' &&\n        (next.type !== 'scalar' || next.source !== '')) {\n        onError(next.offset, 'MISSING_CHAR', 'Tags and anchors must be separated from the next token by white space');\n    }\n    if (tab &&\n        ((atNewline && tab.indent <= parentIndent) ||\n            next?.type === 'block-map' ||\n            next?.type === 'block-seq'))\n        onError(tab, 'TAB_AS_INDENT', 'Tabs are not allowed as indentation');\n    return {\n        comma,\n        found,\n        spaceBefore,\n        comment,\n        hasNewline,\n        anchor,\n        tag,\n        newlineAfterProp,\n        end,\n        start: start ?? end\n    };\n}\n\nexport { resolveProps };\n", "function containsNewline(key) {\n    if (!key)\n        return null;\n    switch (key.type) {\n        case 'alias':\n        case 'scalar':\n        case 'double-quoted-scalar':\n        case 'single-quoted-scalar':\n            if (key.source.includes('\\n'))\n                return true;\n            if (key.end)\n                for (const st of key.end)\n                    if (st.type === 'newline')\n                        return true;\n            return false;\n        case 'flow-collection':\n            for (const it of key.items) {\n                for (const st of it.start)\n                    if (st.type === 'newline')\n                        return true;\n                if (it.sep)\n                    for (const st of it.sep)\n                        if (st.type === 'newline')\n                            return true;\n                if (containsNewline(it.key) || containsNewline(it.value))\n                    return true;\n            }\n            return false;\n        default:\n            return true;\n    }\n}\n\nexport { containsNewline };\n", "import { containsNewline } from './util-contains-newline.js';\n\nfunction flowIndentCheck(indent, fc, onError) {\n    if (fc?.type === 'flow-collection') {\n        const end = fc.end[0];\n        if (end.indent === indent &&\n            (end.source === ']' || end.source === '}') &&\n            containsNewline(fc)) {\n            const msg = 'Flow end indicator should be more indented than parent';\n            onError(end, 'BAD_INDENT', msg, true);\n        }\n    }\n}\n\nexport { flowIndentCheck };\n", "import { isScalar } from '../nodes/identity.js';\n\nfunction mapIncludes(ctx, items, search) {\n    const { uniqueKeys } = ctx.options;\n    if (uniqueKeys === false)\n        return false;\n    const isEqual = typeof uniqueKeys === 'function'\n        ? uniqueKeys\n        : (a, b) => a === b || (isScalar(a) && isScalar(b) && a.value === b.value);\n    return items.some(pair => isEqual(pair.key, search));\n}\n\nexport { mapIncludes };\n", "import { Pair } from '../nodes/Pair.js';\nimport { YAMLMap } from '../nodes/YAMLMap.js';\nimport { resolveProps } from './resolve-props.js';\nimport { containsNewline } from './util-contains-newline.js';\nimport { flowIndentCheck } from './util-flow-indent-check.js';\nimport { mapIncludes } from './util-map-includes.js';\n\nconst startColMsg = 'All mapping items must start at the same column';\nfunction resolveBlockMap({ composeNode, composeEmptyNode }, ctx, bm, onError, tag) {\n    const NodeClass = tag?.nodeClass ?? YAMLMap;\n    const map = new NodeClass(ctx.schema);\n    if (ctx.atRoot)\n        ctx.atRoot = false;\n    let offset = bm.offset;\n    let commentEnd = null;\n    for (const collItem of bm.items) {\n        const { start, key, sep, value } = collItem;\n        // key properties\n        const keyProps = resolveProps(start, {\n            indicator: 'explicit-key-ind',\n            next: key ?? sep?.[0],\n            offset,\n            onError,\n            parentIndent: bm.indent,\n            startOnNewline: true\n        });\n        const implicitKey = !keyProps.found;\n        if (implicitKey) {\n            if (key) {\n                if (key.type === 'block-seq')\n                    onError(offset, 'BLOCK_AS_IMPLICIT_KEY', 'A block sequence may not be used as an implicit map key');\n                else if ('indent' in key && key.indent !== bm.indent)\n                    onError(offset, 'BAD_INDENT', startColMsg);\n            }\n            if (!keyProps.anchor && !keyProps.tag && !sep) {\n                commentEnd = keyProps.end;\n                if (keyProps.comment) {\n                    if (map.comment)\n                        map.comment += '\\n' + keyProps.comment;\n                    else\n                        map.comment = keyProps.comment;\n                }\n                continue;\n            }\n            if (keyProps.newlineAfterProp || containsNewline(key)) {\n                onError(key ?? start[start.length - 1], 'MULTILINE_IMPLICIT_KEY', 'Implicit keys need to be on a single line');\n            }\n        }\n        else if (keyProps.found?.indent !== bm.indent) {\n            onError(offset, 'BAD_INDENT', startColMsg);\n        }\n        // key value\n        ctx.atKey = true;\n        const keyStart = keyProps.end;\n        const keyNode = key\n            ? composeNode(ctx, key, keyProps, onError)\n            : composeEmptyNode(ctx, keyStart, start, null, keyProps, onError);\n        if (ctx.schema.compat)\n            flowIndentCheck(bm.indent, key, onError);\n        ctx.atKey = false;\n        if (mapIncludes(ctx, map.items, keyNode))\n            onError(keyStart, 'DUPLICATE_KEY', 'Map keys must be unique');\n        // value properties\n        const valueProps = resolveProps(sep ?? [], {\n            indicator: 'map-value-ind',\n            next: value,\n            offset: keyNode.range[2],\n            onError,\n            parentIndent: bm.indent,\n            startOnNewline: !key || key.type === 'block-scalar'\n        });\n        offset = valueProps.end;\n        if (valueProps.found) {\n            if (implicitKey) {\n                if (value?.type === 'block-map' && !valueProps.hasNewline)\n                    onError(offset, 'BLOCK_AS_IMPLICIT_KEY', 'Nested mappings are not allowed in compact mappings');\n                if (ctx.options.strict &&\n                    keyProps.start < valueProps.found.offset - 1024)\n                    onError(keyNode.range, 'KEY_OVER_1024_CHARS', 'The : indicator must be at most 1024 chars after the start of an implicit block mapping key');\n            }\n            // value value\n            const valueNode = value\n                ? composeNode(ctx, value, valueProps, onError)\n                : composeEmptyNode(ctx, offset, sep, null, valueProps, onError);\n            if (ctx.schema.compat)\n                flowIndentCheck(bm.indent, value, onError);\n            offset = valueNode.range[2];\n            const pair = new Pair(keyNode, valueNode);\n            if (ctx.options.keepSourceTokens)\n                pair.srcToken = collItem;\n            map.items.push(pair);\n        }\n        else {\n            // key with no value\n            if (implicitKey)\n                onError(keyNode.range, 'MISSING_CHAR', 'Implicit map keys need to be followed by map values');\n            if (valueProps.comment) {\n                if (keyNode.comment)\n                    keyNode.comment += '\\n' + valueProps.comment;\n                else\n                    keyNode.comment = valueProps.comment;\n            }\n            const pair = new Pair(keyNode);\n            if (ctx.options.keepSourceTokens)\n                pair.srcToken = collItem;\n            map.items.push(pair);\n        }\n    }\n    if (commentEnd && commentEnd < offset)\n        onError(commentEnd, 'IMPOSSIBLE', 'Map comment with trailing content');\n    map.range = [bm.offset, offset, commentEnd ?? offset];\n    return map;\n}\n\nexport { resolveBlockMap };\n", "import { YAMLSeq } from '../nodes/YAMLSeq.js';\nimport { resolveProps } from './resolve-props.js';\nimport { flowIndentCheck } from './util-flow-indent-check.js';\n\nfunction resolveBlockSeq({ composeNode, composeEmptyNode }, ctx, bs, onError, tag) {\n    const NodeClass = tag?.nodeClass ?? YAMLSeq;\n    const seq = new NodeClass(ctx.schema);\n    if (ctx.atRoot)\n        ctx.atRoot = false;\n    if (ctx.atKey)\n        ctx.atKey = false;\n    let offset = bs.offset;\n    let commentEnd = null;\n    for (const { start, value } of bs.items) {\n        const props = resolveProps(start, {\n            indicator: 'seq-item-ind',\n            next: value,\n            offset,\n            onError,\n            parentIndent: bs.indent,\n            startOnNewline: true\n        });\n        if (!props.found) {\n            if (props.anchor || props.tag || value) {\n                if (value && value.type === 'block-seq')\n                    onError(props.end, 'BAD_INDENT', 'All sequence items must start at the same column');\n                else\n                    onError(offset, 'MISSING_CHAR', 'Sequence item without - indicator');\n            }\n            else {\n                commentEnd = props.end;\n                if (props.comment)\n                    seq.comment = props.comment;\n                continue;\n            }\n        }\n        const node = value\n            ? composeNode(ctx, value, props, onError)\n            : composeEmptyNode(ctx, props.end, start, null, props, onError);\n        if (ctx.schema.compat)\n            flowIndentCheck(bs.indent, value, onError);\n        offset = node.range[2];\n        seq.items.push(node);\n    }\n    seq.range = [bs.offset, offset, commentEnd ?? offset];\n    return seq;\n}\n\nexport { resolveBlockSeq };\n", "function resolveEnd(end, offset, reqSpace, onError) {\n    let comment = '';\n    if (end) {\n        let hasSpace = false;\n        let sep = '';\n        for (const token of end) {\n            const { source, type } = token;\n            switch (type) {\n                case 'space':\n                    hasSpace = true;\n                    break;\n                case 'comment': {\n                    if (reqSpace && !hasSpace)\n                        onError(token, 'MISSING_CHAR', 'Comments must be separated from other tokens by white space characters');\n                    const cb = source.substring(1) || ' ';\n                    if (!comment)\n                        comment = cb;\n                    else\n                        comment += sep + cb;\n                    sep = '';\n                    break;\n                }\n                case 'newline':\n                    if (comment)\n                        sep += source;\n                    hasSpace = true;\n                    break;\n                default:\n                    onError(token, 'UNEXPECTED_TOKEN', `Unexpected ${type} at node end`);\n            }\n            offset += source.length;\n        }\n    }\n    return { comment, offset };\n}\n\nexport { resolveEnd };\n", "import { isPair } from '../nodes/identity.js';\nimport { Pair } from '../nodes/Pair.js';\nimport { YAMLMap } from '../nodes/YAMLMap.js';\nimport { YAMLSeq } from '../nodes/YAMLSeq.js';\nimport { resolveEnd } from './resolve-end.js';\nimport { resolveProps } from './resolve-props.js';\nimport { containsNewline } from './util-contains-newline.js';\nimport { mapIncludes } from './util-map-includes.js';\n\nconst blockMsg = 'Block collections are not allowed within flow collections';\nconst isBlock = (token) => token && (token.type === 'block-map' || token.type === 'block-seq');\nfunction resolveFlowCollection({ composeNode, composeEmptyNode }, ctx, fc, onError, tag) {\n    const isMap = fc.start.source === '{';\n    const fcName = isMap ? 'flow map' : 'flow sequence';\n    const NodeClass = (tag?.nodeClass ?? (isMap ? YAMLMap : YAMLSeq));\n    const coll = new NodeClass(ctx.schema);\n    coll.flow = true;\n    const atRoot = ctx.atRoot;\n    if (atRoot)\n        ctx.atRoot = false;\n    if (ctx.atKey)\n        ctx.atKey = false;\n    let offset = fc.offset + fc.start.source.length;\n    for (let i = 0; i < fc.items.length; ++i) {\n        const collItem = fc.items[i];\n        const { start, key, sep, value } = collItem;\n        const props = resolveProps(start, {\n            flow: fcName,\n            indicator: 'explicit-key-ind',\n            next: key ?? sep?.[0],\n            offset,\n            onError,\n            parentIndent: fc.indent,\n            startOnNewline: false\n        });\n        if (!props.found) {\n            if (!props.anchor && !props.tag && !sep && !value) {\n                if (i === 0 && props.comma)\n                    onError(props.comma, 'UNEXPECTED_TOKEN', `Unexpected , in ${fcName}`);\n                else if (i < fc.items.length - 1)\n                    onError(props.start, 'UNEXPECTED_TOKEN', `Unexpected empty item in ${fcName}`);\n                if (props.comment) {\n                    if (coll.comment)\n                        coll.comment += '\\n' + props.comment;\n                    else\n                        coll.comment = props.comment;\n                }\n                offset = props.end;\n                continue;\n            }\n            if (!isMap && ctx.options.strict && containsNewline(key))\n                onError(key, // checked by containsNewline()\n                'MULTILINE_IMPLICIT_KEY', 'Implicit keys of flow sequence pairs need to be on a single line');\n        }\n        if (i === 0) {\n            if (props.comma)\n                onError(props.comma, 'UNEXPECTED_TOKEN', `Unexpected , in ${fcName}`);\n        }\n        else {\n            if (!props.comma)\n                onError(props.start, 'MISSING_CHAR', `Missing , between ${fcName} items`);\n            if (props.comment) {\n                let prevItemComment = '';\n                loop: for (const st of start) {\n                    switch (st.type) {\n                        case 'comma':\n                        case 'space':\n                            break;\n                        case 'comment':\n                            prevItemComment = st.source.substring(1);\n                            break loop;\n                        default:\n                            break loop;\n                    }\n                }\n                if (prevItemComment) {\n                    let prev = coll.items[coll.items.length - 1];\n                    if (isPair(prev))\n                        prev = prev.value ?? prev.key;\n                    if (prev.comment)\n                        prev.comment += '\\n' + prevItemComment;\n                    else\n                        prev.comment = prevItemComment;\n                    props.comment = props.comment.substring(prevItemComment.length + 1);\n                }\n            }\n        }\n        if (!isMap && !sep && !props.found) {\n            // item is a value in a seq\n            // → key & sep are empty, start does not include ? or :\n            const valueNode = value\n                ? composeNode(ctx, value, props, onError)\n                : composeEmptyNode(ctx, props.end, sep, null, props, onError);\n            coll.items.push(valueNode);\n            offset = valueNode.range[2];\n            if (isBlock(value))\n                onError(valueNode.range, 'BLOCK_IN_FLOW', blockMsg);\n        }\n        else {\n            // item is a key+value pair\n            // key value\n            ctx.atKey = true;\n            const keyStart = props.end;\n            const keyNode = key\n                ? composeNode(ctx, key, props, onError)\n                : composeEmptyNode(ctx, keyStart, start, null, props, onError);\n            if (isBlock(key))\n                onError(keyNode.range, 'BLOCK_IN_FLOW', blockMsg);\n            ctx.atKey = false;\n            // value properties\n            const valueProps = resolveProps(sep ?? [], {\n                flow: fcName,\n                indicator: 'map-value-ind',\n                next: value,\n                offset: keyNode.range[2],\n                onError,\n                parentIndent: fc.indent,\n                startOnNewline: false\n            });\n            if (valueProps.found) {\n                if (!isMap && !props.found && ctx.options.strict) {\n                    if (sep)\n                        for (const st of sep) {\n                            if (st === valueProps.found)\n                                break;\n                            if (st.type === 'newline') {\n                                onError(st, 'MULTILINE_IMPLICIT_KEY', 'Implicit keys of flow sequence pairs need to be on a single line');\n                                break;\n                            }\n                        }\n                    if (props.start < valueProps.found.offset - 1024)\n                        onError(valueProps.found, 'KEY_OVER_1024_CHARS', 'The : indicator must be at most 1024 chars after the start of an implicit flow sequence key');\n                }\n            }\n            else if (value) {\n                if ('source' in value && value.source && value.source[0] === ':')\n                    onError(value, 'MISSING_CHAR', `Missing space after : in ${fcName}`);\n                else\n                    onError(valueProps.start, 'MISSING_CHAR', `Missing , or : between ${fcName} items`);\n            }\n            // value value\n            const valueNode = value\n                ? composeNode(ctx, value, valueProps, onError)\n                : valueProps.found\n                    ? composeEmptyNode(ctx, valueProps.end, sep, null, valueProps, onError)\n                    : null;\n            if (valueNode) {\n                if (isBlock(value))\n                    onError(valueNode.range, 'BLOCK_IN_FLOW', blockMsg);\n            }\n            else if (valueProps.comment) {\n                if (keyNode.comment)\n                    keyNode.comment += '\\n' + valueProps.comment;\n                else\n                    keyNode.comment = valueProps.comment;\n            }\n            const pair = new Pair(keyNode, valueNode);\n            if (ctx.options.keepSourceTokens)\n                pair.srcToken = collItem;\n            if (isMap) {\n                const map = coll;\n                if (mapIncludes(ctx, map.items, keyNode))\n                    onError(keyStart, 'DUPLICATE_KEY', 'Map keys must be unique');\n                map.items.push(pair);\n            }\n            else {\n                const map = new YAMLMap(ctx.schema);\n                map.flow = true;\n                map.items.push(pair);\n                const endRange = (valueNode ?? keyNode).range;\n                map.range = [keyNode.range[0], endRange[1], endRange[2]];\n                coll.items.push(map);\n            }\n            offset = valueNode ? valueNode.range[2] : valueProps.end;\n        }\n    }\n    const expectedEnd = isMap ? '}' : ']';\n    const [ce, ...ee] = fc.end;\n    let cePos = offset;\n    if (ce && ce.source === expectedEnd)\n        cePos = ce.offset + ce.source.length;\n    else {\n        const name = fcName[0].toUpperCase() + fcName.substring(1);\n        const msg = atRoot\n            ? `${name} must end with a ${expectedEnd}`\n            : `${name} in block collection must be sufficiently indented and end with a ${expectedEnd}`;\n        onError(offset, atRoot ? 'MISSING_CHAR' : 'BAD_INDENT', msg);\n        if (ce && ce.source.length !== 1)\n            ee.unshift(ce);\n    }\n    if (ee.length > 0) {\n        const end = resolveEnd(ee, cePos, ctx.options.strict, onError);\n        if (end.comment) {\n            if (coll.comment)\n                coll.comment += '\\n' + end.comment;\n            else\n                coll.comment = end.comment;\n        }\n        coll.range = [fc.offset, cePos, end.offset];\n    }\n    else {\n        coll.range = [fc.offset, cePos, cePos];\n    }\n    return coll;\n}\n\nexport { resolveFlowCollection };\n", "import { isNode } from '../nodes/identity.js';\nimport { <PERSON><PERSON>r } from '../nodes/Scalar.js';\nimport { YAMLMap } from '../nodes/YAMLMap.js';\nimport { YAMLSeq } from '../nodes/YAMLSeq.js';\nimport { resolveBlockMap } from './resolve-block-map.js';\nimport { resolveBlockSeq } from './resolve-block-seq.js';\nimport { resolveFlowCollection } from './resolve-flow-collection.js';\n\nfunction resolveCollection(CN, ctx, token, onError, tagName, tag) {\n    const coll = token.type === 'block-map'\n        ? resolveBlockMap(CN, ctx, token, onError, tag)\n        : token.type === 'block-seq'\n            ? resolveBlockSeq(CN, ctx, token, onError, tag)\n            : resolveFlowCollection(CN, ctx, token, onError, tag);\n    const Coll = coll.constructor;\n    // If we got a tagName matching the class, or the tag name is '!',\n    // then use the tagName from the node class used to create it.\n    if (tagName === '!' || tagName === Coll.tagName) {\n        coll.tag = Coll.tagName;\n        return coll;\n    }\n    if (tagName)\n        coll.tag = tagName;\n    return coll;\n}\nfunction composeCollection(CN, ctx, token, props, onError) {\n    const tagToken = props.tag;\n    const tagName = !tagToken\n        ? null\n        : ctx.directives.tagName(tagToken.source, msg => onError(tagToken, 'TAG_RESOLVE_FAILED', msg));\n    if (token.type === 'block-seq') {\n        const { anchor, newlineAfterProp: nl } = props;\n        const lastProp = anchor && tagToken\n            ? anchor.offset > tagToken.offset\n                ? anchor\n                : tagToken\n            : (anchor ?? tagToken);\n        if (lastProp && (!nl || nl.offset < lastProp.offset)) {\n            const message = 'Missing newline after block sequence props';\n            onError(lastProp, 'MISSING_CHAR', message);\n        }\n    }\n    const expType = token.type === 'block-map'\n        ? 'map'\n        : token.type === 'block-seq'\n            ? 'seq'\n            : token.start.source === '{'\n                ? 'map'\n                : 'seq';\n    // shortcut: check if it's a generic YAMLMap or YAMLSeq\n    // before jumping into the custom tag logic.\n    if (!tagToken ||\n        !tagName ||\n        tagName === '!' ||\n        (tagName === YAMLMap.tagName && expType === 'map') ||\n        (tagName === YAMLSeq.tagName && expType === 'seq')) {\n        return resolveCollection(CN, ctx, token, onError, tagName);\n    }\n    let tag = ctx.schema.tags.find(t => t.tag === tagName && t.collection === expType);\n    if (!tag) {\n        const kt = ctx.schema.knownTags[tagName];\n        if (kt && kt.collection === expType) {\n            ctx.schema.tags.push(Object.assign({}, kt, { default: false }));\n            tag = kt;\n        }\n        else {\n            if (kt) {\n                onError(tagToken, 'BAD_COLLECTION_TYPE', `${kt.tag} used for ${expType} collection, but expects ${kt.collection ?? 'scalar'}`, true);\n            }\n            else {\n                onError(tagToken, 'TAG_RESOLVE_FAILED', `Unresolved tag: ${tagName}`, true);\n            }\n            return resolveCollection(CN, ctx, token, onError, tagName);\n        }\n    }\n    const coll = resolveCollection(CN, ctx, token, onError, tagName, tag);\n    const res = tag.resolve?.(coll, msg => onError(tagToken, 'TAG_RESOLVE_FAILED', msg), ctx.options) ?? coll;\n    const node = isNode(res)\n        ? res\n        : new Scalar(res);\n    node.range = coll.range;\n    node.tag = tagName;\n    if (tag?.format)\n        node.format = tag.format;\n    return node;\n}\n\nexport { composeCollection };\n", "import { <PERSON>alar } from '../nodes/Scalar.js';\n\nfunction resolveBlockScalar(ctx, scalar, onError) {\n    const start = scalar.offset;\n    const header = parseBlockScalarHeader(scalar, ctx.options.strict, onError);\n    if (!header)\n        return { value: '', type: null, comment: '', range: [start, start, start] };\n    const type = header.mode === '>' ? Scalar.BLOCK_FOLDED : Scalar.BLOCK_LITERAL;\n    const lines = scalar.source ? splitLines(scalar.source) : [];\n    // determine the end of content & start of chomping\n    let chompStart = lines.length;\n    for (let i = lines.length - 1; i >= 0; --i) {\n        const content = lines[i][1];\n        if (content === '' || content === '\\r')\n            chompStart = i;\n        else\n            break;\n    }\n    // shortcut for empty contents\n    if (chompStart === 0) {\n        const value = header.chomp === '+' && lines.length > 0\n            ? '\\n'.repeat(Math.max(1, lines.length - 1))\n            : '';\n        let end = start + header.length;\n        if (scalar.source)\n            end += scalar.source.length;\n        return { value, type, comment: header.comment, range: [start, end, end] };\n    }\n    // find the indentation level to trim from start\n    let trimIndent = scalar.indent + header.indent;\n    let offset = scalar.offset + header.length;\n    let contentStart = 0;\n    for (let i = 0; i < chompStart; ++i) {\n        const [indent, content] = lines[i];\n        if (content === '' || content === '\\r') {\n            if (header.indent === 0 && indent.length > trimIndent)\n                trimIndent = indent.length;\n        }\n        else {\n            if (indent.length < trimIndent) {\n                const message = 'Block scalars with more-indented leading empty lines must use an explicit indentation indicator';\n                onError(offset + indent.length, 'MISSING_CHAR', message);\n            }\n            if (header.indent === 0)\n                trimIndent = indent.length;\n            contentStart = i;\n            if (trimIndent === 0 && !ctx.atRoot) {\n                const message = 'Block scalar values in collections must be indented';\n                onError(offset, 'BAD_INDENT', message);\n            }\n            break;\n        }\n        offset += indent.length + content.length + 1;\n    }\n    // include trailing more-indented empty lines in content\n    for (let i = lines.length - 1; i >= chompStart; --i) {\n        if (lines[i][0].length > trimIndent)\n            chompStart = i + 1;\n    }\n    let value = '';\n    let sep = '';\n    let prevMoreIndented = false;\n    // leading whitespace is kept intact\n    for (let i = 0; i < contentStart; ++i)\n        value += lines[i][0].slice(trimIndent) + '\\n';\n    for (let i = contentStart; i < chompStart; ++i) {\n        let [indent, content] = lines[i];\n        offset += indent.length + content.length + 1;\n        const crlf = content[content.length - 1] === '\\r';\n        if (crlf)\n            content = content.slice(0, -1);\n        /* istanbul ignore if already caught in lexer */\n        if (content && indent.length < trimIndent) {\n            const src = header.indent\n                ? 'explicit indentation indicator'\n                : 'first line';\n            const message = `Block scalar lines must not be less indented than their ${src}`;\n            onError(offset - content.length - (crlf ? 2 : 1), 'BAD_INDENT', message);\n            indent = '';\n        }\n        if (type === Scalar.BLOCK_LITERAL) {\n            value += sep + indent.slice(trimIndent) + content;\n            sep = '\\n';\n        }\n        else if (indent.length > trimIndent || content[0] === '\\t') {\n            // more-indented content within a folded block\n            if (sep === ' ')\n                sep = '\\n';\n            else if (!prevMoreIndented && sep === '\\n')\n                sep = '\\n\\n';\n            value += sep + indent.slice(trimIndent) + content;\n            sep = '\\n';\n            prevMoreIndented = true;\n        }\n        else if (content === '') {\n            // empty line\n            if (sep === '\\n')\n                value += '\\n';\n            else\n                sep = '\\n';\n        }\n        else {\n            value += sep + content;\n            sep = ' ';\n            prevMoreIndented = false;\n        }\n    }\n    switch (header.chomp) {\n        case '-':\n            break;\n        case '+':\n            for (let i = chompStart; i < lines.length; ++i)\n                value += '\\n' + lines[i][0].slice(trimIndent);\n            if (value[value.length - 1] !== '\\n')\n                value += '\\n';\n            break;\n        default:\n            value += '\\n';\n    }\n    const end = start + header.length + scalar.source.length;\n    return { value, type, comment: header.comment, range: [start, end, end] };\n}\nfunction parseBlockScalarHeader({ offset, props }, strict, onError) {\n    /* istanbul ignore if should not happen */\n    if (props[0].type !== 'block-scalar-header') {\n        onError(props[0], 'IMPOSSIBLE', 'Block scalar header not found');\n        return null;\n    }\n    const { source } = props[0];\n    const mode = source[0];\n    let indent = 0;\n    let chomp = '';\n    let error = -1;\n    for (let i = 1; i < source.length; ++i) {\n        const ch = source[i];\n        if (!chomp && (ch === '-' || ch === '+'))\n            chomp = ch;\n        else {\n            const n = Number(ch);\n            if (!indent && n)\n                indent = n;\n            else if (error === -1)\n                error = offset + i;\n        }\n    }\n    if (error !== -1)\n        onError(error, 'UNEXPECTED_TOKEN', `Block scalar header includes extra characters: ${source}`);\n    let hasSpace = false;\n    let comment = '';\n    let length = source.length;\n    for (let i = 1; i < props.length; ++i) {\n        const token = props[i];\n        switch (token.type) {\n            case 'space':\n                hasSpace = true;\n            // fallthrough\n            case 'newline':\n                length += token.source.length;\n                break;\n            case 'comment':\n                if (strict && !hasSpace) {\n                    const message = 'Comments must be separated from other tokens by white space characters';\n                    onError(token, 'MISSING_CHAR', message);\n                }\n                length += token.source.length;\n                comment = token.source.substring(1);\n                break;\n            case 'error':\n                onError(token, 'UNEXPECTED_TOKEN', token.message);\n                length += token.source.length;\n                break;\n            /* istanbul ignore next should not happen */\n            default: {\n                const message = `Unexpected token in block scalar header: ${token.type}`;\n                onError(token, 'UNEXPECTED_TOKEN', message);\n                const ts = token.source;\n                if (ts && typeof ts === 'string')\n                    length += ts.length;\n            }\n        }\n    }\n    return { mode, indent, chomp, comment, length };\n}\n/** @returns Array of lines split up as `[indent, content]` */\nfunction splitLines(source) {\n    const split = source.split(/\\n( *)/);\n    const first = split[0];\n    const m = first.match(/^( *)/);\n    const line0 = m?.[1]\n        ? [m[1], first.slice(m[1].length)]\n        : ['', first];\n    const lines = [line0];\n    for (let i = 1; i < split.length; i += 2)\n        lines.push([split[i], split[i + 1]]);\n    return lines;\n}\n\nexport { resolveBlockScalar };\n", "import { <PERSON>alar } from '../nodes/Scalar.js';\nimport { resolveEnd } from './resolve-end.js';\n\nfunction resolveFlowScalar(scalar, strict, onError) {\n    const { offset, type, source, end } = scalar;\n    let _type;\n    let value;\n    const _onError = (rel, code, msg) => onError(offset + rel, code, msg);\n    switch (type) {\n        case 'scalar':\n            _type = Scalar.PLAIN;\n            value = plainValue(source, _onError);\n            break;\n        case 'single-quoted-scalar':\n            _type = Scalar.QUOTE_SINGLE;\n            value = singleQuotedValue(source, _onError);\n            break;\n        case 'double-quoted-scalar':\n            _type = Scalar.QUOTE_DOUBLE;\n            value = doubleQuotedValue(source, _onError);\n            break;\n        /* istanbul ignore next should not happen */\n        default:\n            onError(scalar, 'UNEXPECTED_TOKEN', `Expected a flow scalar value, but found: ${type}`);\n            return {\n                value: '',\n                type: null,\n                comment: '',\n                range: [offset, offset + source.length, offset + source.length]\n            };\n    }\n    const valueEnd = offset + source.length;\n    const re = resolveEnd(end, valueEnd, strict, onError);\n    return {\n        value,\n        type: _type,\n        comment: re.comment,\n        range: [offset, valueEnd, re.offset]\n    };\n}\nfunction plainValue(source, onError) {\n    let badChar = '';\n    switch (source[0]) {\n        /* istanbul ignore next should not happen */\n        case '\\t':\n            badChar = 'a tab character';\n            break;\n        case ',':\n            badChar = 'flow indicator character ,';\n            break;\n        case '%':\n            badChar = 'directive indicator character %';\n            break;\n        case '|':\n        case '>': {\n            badChar = `block scalar indicator ${source[0]}`;\n            break;\n        }\n        case '@':\n        case '`': {\n            badChar = `reserved character ${source[0]}`;\n            break;\n        }\n    }\n    if (badChar)\n        onError(0, 'BAD_SCALAR_START', `Plain value cannot start with ${badChar}`);\n    return foldLines(source);\n}\nfunction singleQuotedValue(source, onError) {\n    if (source[source.length - 1] !== \"'\" || source.length === 1)\n        onError(source.length, 'MISSING_CHAR', \"Missing closing 'quote\");\n    return foldLines(source.slice(1, -1)).replace(/''/g, \"'\");\n}\nfunction foldLines(source) {\n    /**\n     * The negative lookbehind here and in the `re` RegExp is to\n     * prevent causing a polynomial search time in certain cases.\n     *\n     * The try-catch is for Safari, which doesn't support this yet:\n     * https://caniuse.com/js-regexp-lookbehind\n     */\n    let first, line;\n    try {\n        first = new RegExp('(.*?)(?<![ \\t])[ \\t]*\\r?\\n', 'sy');\n        line = new RegExp('[ \\t]*(.*?)(?:(?<![ \\t])[ \\t]*)?\\r?\\n', 'sy');\n    }\n    catch {\n        first = /(.*?)[ \\t]*\\r?\\n/sy;\n        line = /[ \\t]*(.*?)[ \\t]*\\r?\\n/sy;\n    }\n    let match = first.exec(source);\n    if (!match)\n        return source;\n    let res = match[1];\n    let sep = ' ';\n    let pos = first.lastIndex;\n    line.lastIndex = pos;\n    while ((match = line.exec(source))) {\n        if (match[1] === '') {\n            if (sep === '\\n')\n                res += sep;\n            else\n                sep = '\\n';\n        }\n        else {\n            res += sep + match[1];\n            sep = ' ';\n        }\n        pos = line.lastIndex;\n    }\n    const last = /[ \\t]*(.*)/sy;\n    last.lastIndex = pos;\n    match = last.exec(source);\n    return res + sep + (match?.[1] ?? '');\n}\nfunction doubleQuotedValue(source, onError) {\n    let res = '';\n    for (let i = 1; i < source.length - 1; ++i) {\n        const ch = source[i];\n        if (ch === '\\r' && source[i + 1] === '\\n')\n            continue;\n        if (ch === '\\n') {\n            const { fold, offset } = foldNewline(source, i);\n            res += fold;\n            i = offset;\n        }\n        else if (ch === '\\\\') {\n            let next = source[++i];\n            const cc = escapeCodes[next];\n            if (cc)\n                res += cc;\n            else if (next === '\\n') {\n                // skip escaped newlines, but still trim the following line\n                next = source[i + 1];\n                while (next === ' ' || next === '\\t')\n                    next = source[++i + 1];\n            }\n            else if (next === '\\r' && source[i + 1] === '\\n') {\n                // skip escaped CRLF newlines, but still trim the following line\n                next = source[++i + 1];\n                while (next === ' ' || next === '\\t')\n                    next = source[++i + 1];\n            }\n            else if (next === 'x' || next === 'u' || next === 'U') {\n                const length = { x: 2, u: 4, U: 8 }[next];\n                res += parseCharCode(source, i + 1, length, onError);\n                i += length;\n            }\n            else {\n                const raw = source.substr(i - 1, 2);\n                onError(i - 1, 'BAD_DQ_ESCAPE', `Invalid escape sequence ${raw}`);\n                res += raw;\n            }\n        }\n        else if (ch === ' ' || ch === '\\t') {\n            // trim trailing whitespace\n            const wsStart = i;\n            let next = source[i + 1];\n            while (next === ' ' || next === '\\t')\n                next = source[++i + 1];\n            if (next !== '\\n' && !(next === '\\r' && source[i + 2] === '\\n'))\n                res += i > wsStart ? source.slice(wsStart, i + 1) : ch;\n        }\n        else {\n            res += ch;\n        }\n    }\n    if (source[source.length - 1] !== '\"' || source.length === 1)\n        onError(source.length, 'MISSING_CHAR', 'Missing closing \"quote');\n    return res;\n}\n/**\n * Fold a single newline into a space, multiple newlines to N - 1 newlines.\n * Presumes `source[offset] === '\\n'`\n */\nfunction foldNewline(source, offset) {\n    let fold = '';\n    let ch = source[offset + 1];\n    while (ch === ' ' || ch === '\\t' || ch === '\\n' || ch === '\\r') {\n        if (ch === '\\r' && source[offset + 2] !== '\\n')\n            break;\n        if (ch === '\\n')\n            fold += '\\n';\n        offset += 1;\n        ch = source[offset + 1];\n    }\n    if (!fold)\n        fold = ' ';\n    return { fold, offset };\n}\nconst escapeCodes = {\n    '0': '\\0', // null character\n    a: '\\x07', // bell character\n    b: '\\b', // backspace\n    e: '\\x1b', // escape character\n    f: '\\f', // form feed\n    n: '\\n', // line feed\n    r: '\\r', // carriage return\n    t: '\\t', // horizontal tab\n    v: '\\v', // vertical tab\n    N: '\\u0085', // Unicode next line\n    _: '\\u00a0', // Unicode non-breaking space\n    L: '\\u2028', // Unicode line separator\n    P: '\\u2029', // Unicode paragraph separator\n    ' ': ' ',\n    '\"': '\"',\n    '/': '/',\n    '\\\\': '\\\\',\n    '\\t': '\\t'\n};\nfunction parseCharCode(source, offset, length, onError) {\n    const cc = source.substr(offset, length);\n    const ok = cc.length === length && /^[0-9a-fA-F]+$/.test(cc);\n    const code = ok ? parseInt(cc, 16) : NaN;\n    if (isNaN(code)) {\n        const raw = source.substr(offset - 2, length + 2);\n        onError(offset - 2, 'BAD_DQ_ESCAPE', `Invalid escape sequence ${raw}`);\n        return raw;\n    }\n    return String.fromCodePoint(code);\n}\n\nexport { resolveFlowScalar };\n", "import { isScalar, SCALAR } from '../nodes/identity.js';\nimport { <PERSON>alar } from '../nodes/Scalar.js';\nimport { resolveBlockScalar } from './resolve-block-scalar.js';\nimport { resolveFlowScalar } from './resolve-flow-scalar.js';\n\nfunction composeScalar(ctx, token, tagToken, onError) {\n    const { value, type, comment, range } = token.type === 'block-scalar'\n        ? resolveBlockScalar(ctx, token, onError)\n        : resolveFlowScalar(token, ctx.options.strict, onError);\n    const tagName = tagToken\n        ? ctx.directives.tagName(tagToken.source, msg => onError(tagToken, 'TAG_RESOLVE_FAILED', msg))\n        : null;\n    let tag;\n    if (ctx.options.stringKeys && ctx.atKey) {\n        tag = ctx.schema[SCALAR];\n    }\n    else if (tagName)\n        tag = findScalarTagByName(ctx.schema, value, tagName, tagToken, onError);\n    else if (token.type === 'scalar')\n        tag = findScalarTagByTest(ctx, value, token, onError);\n    else\n        tag = ctx.schema[SCALAR];\n    let scalar;\n    try {\n        const res = tag.resolve(value, msg => onError(tagToken ?? token, 'TAG_RESOLVE_FAILED', msg), ctx.options);\n        scalar = isScalar(res) ? res : new Scalar(res);\n    }\n    catch (error) {\n        const msg = error instanceof Error ? error.message : String(error);\n        onError(tagToken ?? token, 'TAG_RESOLVE_FAILED', msg);\n        scalar = new Scalar(value);\n    }\n    scalar.range = range;\n    scalar.source = value;\n    if (type)\n        scalar.type = type;\n    if (tagName)\n        scalar.tag = tagName;\n    if (tag.format)\n        scalar.format = tag.format;\n    if (comment)\n        scalar.comment = comment;\n    return scalar;\n}\nfunction findScalarTagByName(schema, value, tagName, tagToken, onError) {\n    if (tagName === '!')\n        return schema[SCALAR]; // non-specific tag\n    const matchWithTest = [];\n    for (const tag of schema.tags) {\n        if (!tag.collection && tag.tag === tagName) {\n            if (tag.default && tag.test)\n                matchWithTest.push(tag);\n            else\n                return tag;\n        }\n    }\n    for (const tag of matchWithTest)\n        if (tag.test?.test(value))\n            return tag;\n    const kt = schema.knownTags[tagName];\n    if (kt && !kt.collection) {\n        // Ensure that the known tag is available for stringifying,\n        // but does not get used by default.\n        schema.tags.push(Object.assign({}, kt, { default: false, test: undefined }));\n        return kt;\n    }\n    onError(tagToken, 'TAG_RESOLVE_FAILED', `Unresolved tag: ${tagName}`, tagName !== 'tag:yaml.org,2002:str');\n    return schema[SCALAR];\n}\nfunction findScalarTagByTest({ atKey, directives, schema }, value, token, onError) {\n    const tag = schema.tags.find(tag => (tag.default === true || (atKey && tag.default === 'key')) &&\n        tag.test?.test(value)) || schema[SCALAR];\n    if (schema.compat) {\n        const compat = schema.compat.find(tag => tag.default && tag.test?.test(value)) ??\n            schema[SCALAR];\n        if (tag.tag !== compat.tag) {\n            const ts = directives.tagString(tag.tag);\n            const cs = directives.tagString(compat.tag);\n            const msg = `Value may be parsed as either ${ts} or ${cs}`;\n            onError(token, 'TAG_RESOLVE_FAILED', msg, true);\n        }\n    }\n    return tag;\n}\n\nexport { composeScalar };\n", "function emptyScalarPosition(offset, before, pos) {\n    if (before) {\n        pos ?? (pos = before.length);\n        for (let i = pos - 1; i >= 0; --i) {\n            let st = before[i];\n            switch (st.type) {\n                case 'space':\n                case 'comment':\n                case 'newline':\n                    offset -= st.source.length;\n                    continue;\n            }\n            // Technically, an empty scalar is immediately after the last non-empty\n            // node, but it's more useful to place it after any whitespace.\n            st = before[++i];\n            while (st?.type === 'space') {\n                offset += st.source.length;\n                st = before[++i];\n            }\n            break;\n        }\n    }\n    return offset;\n}\n\nexport { emptyScalarPosition };\n", "import { <PERSON>as } from '../nodes/Alias.js';\nimport { isScalar } from '../nodes/identity.js';\nimport { composeCollection } from './compose-collection.js';\nimport { composeScalar } from './compose-scalar.js';\nimport { resolveEnd } from './resolve-end.js';\nimport { emptyScalarPosition } from './util-empty-scalar-position.js';\n\nconst CN = { composeNode, composeEmptyNode };\nfunction composeNode(ctx, token, props, onError) {\n    const atKey = ctx.atKey;\n    const { spaceBefore, comment, anchor, tag } = props;\n    let node;\n    let isSrcToken = true;\n    switch (token.type) {\n        case 'alias':\n            node = composeAlias(ctx, token, onError);\n            if (anchor || tag)\n                onError(token, 'ALIAS_PROPS', 'An alias node must not specify any properties');\n            break;\n        case 'scalar':\n        case 'single-quoted-scalar':\n        case 'double-quoted-scalar':\n        case 'block-scalar':\n            node = composeScalar(ctx, token, tag, onError);\n            if (anchor)\n                node.anchor = anchor.source.substring(1);\n            break;\n        case 'block-map':\n        case 'block-seq':\n        case 'flow-collection':\n            node = composeCollection(CN, ctx, token, props, onError);\n            if (anchor)\n                node.anchor = anchor.source.substring(1);\n            break;\n        default: {\n            const message = token.type === 'error'\n                ? token.message\n                : `Unsupported token (type: ${token.type})`;\n            onError(token, 'UNEXPECTED_TOKEN', message);\n            node = composeEmptyNode(ctx, token.offset, undefined, null, props, onError);\n            isSrcToken = false;\n        }\n    }\n    if (anchor && node.anchor === '')\n        onError(anchor, 'BAD_ALIAS', 'Anchor cannot be an empty string');\n    if (atKey &&\n        ctx.options.stringKeys &&\n        (!isScalar(node) ||\n            typeof node.value !== 'string' ||\n            (node.tag && node.tag !== 'tag:yaml.org,2002:str'))) {\n        const msg = 'With stringKeys, all keys must be strings';\n        onError(tag ?? token, 'NON_STRING_KEY', msg);\n    }\n    if (spaceBefore)\n        node.spaceBefore = true;\n    if (comment) {\n        if (token.type === 'scalar' && token.source === '')\n            node.comment = comment;\n        else\n            node.commentBefore = comment;\n    }\n    // @ts-expect-error Type checking misses meaning of isSrcToken\n    if (ctx.options.keepSourceTokens && isSrcToken)\n        node.srcToken = token;\n    return node;\n}\nfunction composeEmptyNode(ctx, offset, before, pos, { spaceBefore, comment, anchor, tag, end }, onError) {\n    const token = {\n        type: 'scalar',\n        offset: emptyScalarPosition(offset, before, pos),\n        indent: -1,\n        source: ''\n    };\n    const node = composeScalar(ctx, token, tag, onError);\n    if (anchor) {\n        node.anchor = anchor.source.substring(1);\n        if (node.anchor === '')\n            onError(anchor, 'BAD_ALIAS', 'Anchor cannot be an empty string');\n    }\n    if (spaceBefore)\n        node.spaceBefore = true;\n    if (comment) {\n        node.comment = comment;\n        node.range[2] = end;\n    }\n    return node;\n}\nfunction composeAlias({ options }, { offset, source, end }, onError) {\n    const alias = new Alias(source.substring(1));\n    if (alias.source === '')\n        onError(offset, 'BAD_ALIAS', 'Alias cannot be an empty string');\n    if (alias.source.endsWith(':'))\n        onError(offset + source.length - 1, 'BAD_ALIAS', 'Alias ending in : is ambiguous', true);\n    const valueEnd = offset + source.length;\n    const re = resolveEnd(end, valueEnd, options.strict, onError);\n    alias.range = [offset, valueEnd, re.offset];\n    if (re.comment)\n        alias.comment = re.comment;\n    return alias;\n}\n\nexport { composeEmptyNode, composeNode };\n", "import { Document } from '../doc/Document.js';\nimport { composeNode, composeEmptyNode } from './compose-node.js';\nimport { resolveEnd } from './resolve-end.js';\nimport { resolveProps } from './resolve-props.js';\n\nfunction composeDoc(options, directives, { offset, start, value, end }, onError) {\n    const opts = Object.assign({ _directives: directives }, options);\n    const doc = new Document(undefined, opts);\n    const ctx = {\n        atKey: false,\n        atRoot: true,\n        directives: doc.directives,\n        options: doc.options,\n        schema: doc.schema\n    };\n    const props = resolveProps(start, {\n        indicator: 'doc-start',\n        next: value ?? end?.[0],\n        offset,\n        onError,\n        parentIndent: 0,\n        startOnNewline: true\n    });\n    if (props.found) {\n        doc.directives.docStart = true;\n        if (value &&\n            (value.type === 'block-map' || value.type === 'block-seq') &&\n            !props.hasNewline)\n            onError(props.end, 'MISSING_CHAR', 'Block collection cannot start on same line with directives-end marker');\n    }\n    // @ts-expect-error If Contents is set, let's trust the user\n    doc.contents = value\n        ? composeNode(ctx, value, props, onError)\n        : composeEmptyNode(ctx, props.end, start, null, props, onError);\n    const contentEnd = doc.contents.range[2];\n    const re = resolveEnd(end, contentEnd, false, onError);\n    if (re.comment)\n        doc.comment = re.comment;\n    doc.range = [offset, contentEnd, re.offset];\n    return doc;\n}\n\nexport { composeDoc };\n", "import { Directives } from '../doc/directives.js';\nimport { Document } from '../doc/Document.js';\nimport { YAMLWarning, YAMLParseError } from '../errors.js';\nimport { isCollection, isPair } from '../nodes/identity.js';\nimport { composeDoc } from './compose-doc.js';\nimport { resolveEnd } from './resolve-end.js';\n\nfunction getErrorPos(src) {\n    if (typeof src === 'number')\n        return [src, src + 1];\n    if (Array.isArray(src))\n        return src.length === 2 ? src : [src[0], src[1]];\n    const { offset, source } = src;\n    return [offset, offset + (typeof source === 'string' ? source.length : 1)];\n}\nfunction parsePrelude(prelude) {\n    let comment = '';\n    let atComment = false;\n    let afterEmptyLine = false;\n    for (let i = 0; i < prelude.length; ++i) {\n        const source = prelude[i];\n        switch (source[0]) {\n            case '#':\n                comment +=\n                    (comment === '' ? '' : afterEmptyLine ? '\\n\\n' : '\\n') +\n                        (source.substring(1) || ' ');\n                atComment = true;\n                afterEmptyLine = false;\n                break;\n            case '%':\n                if (prelude[i + 1]?.[0] !== '#')\n                    i += 1;\n                atComment = false;\n                break;\n            default:\n                // This may be wrong after doc-end, but in that case it doesn't matter\n                if (!atComment)\n                    afterEmptyLine = true;\n                atComment = false;\n        }\n    }\n    return { comment, afterEmptyLine };\n}\n/**\n * Compose a stream of CST nodes into a stream of YAML Documents.\n *\n * ```ts\n * import { Composer, Parser } from 'yaml'\n *\n * const src: string = ...\n * const tokens = new Parser().parse(src)\n * const docs = new Composer().compose(tokens)\n * ```\n */\nclass Composer {\n    constructor(options = {}) {\n        this.doc = null;\n        this.atDirectives = false;\n        this.prelude = [];\n        this.errors = [];\n        this.warnings = [];\n        this.onError = (source, code, message, warning) => {\n            const pos = getErrorPos(source);\n            if (warning)\n                this.warnings.push(new YAMLWarning(pos, code, message));\n            else\n                this.errors.push(new YAMLParseError(pos, code, message));\n        };\n        // eslint-disable-next-line @typescript-eslint/prefer-nullish-coalescing\n        this.directives = new Directives({ version: options.version || '1.2' });\n        this.options = options;\n    }\n    decorate(doc, afterDoc) {\n        const { comment, afterEmptyLine } = parsePrelude(this.prelude);\n        //console.log({ dc: doc.comment, prelude, comment })\n        if (comment) {\n            const dc = doc.contents;\n            if (afterDoc) {\n                doc.comment = doc.comment ? `${doc.comment}\\n${comment}` : comment;\n            }\n            else if (afterEmptyLine || doc.directives.docStart || !dc) {\n                doc.commentBefore = comment;\n            }\n            else if (isCollection(dc) && !dc.flow && dc.items.length > 0) {\n                let it = dc.items[0];\n                if (isPair(it))\n                    it = it.key;\n                const cb = it.commentBefore;\n                it.commentBefore = cb ? `${comment}\\n${cb}` : comment;\n            }\n            else {\n                const cb = dc.commentBefore;\n                dc.commentBefore = cb ? `${comment}\\n${cb}` : comment;\n            }\n        }\n        if (afterDoc) {\n            Array.prototype.push.apply(doc.errors, this.errors);\n            Array.prototype.push.apply(doc.warnings, this.warnings);\n        }\n        else {\n            doc.errors = this.errors;\n            doc.warnings = this.warnings;\n        }\n        this.prelude = [];\n        this.errors = [];\n        this.warnings = [];\n    }\n    /**\n     * Current stream status information.\n     *\n     * Mostly useful at the end of input for an empty stream.\n     */\n    streamInfo() {\n        return {\n            comment: parsePrelude(this.prelude).comment,\n            directives: this.directives,\n            errors: this.errors,\n            warnings: this.warnings\n        };\n    }\n    /**\n     * Compose tokens into documents.\n     *\n     * @param forceDoc - If the stream contains no document, still emit a final document including any comments and directives that would be applied to a subsequent document.\n     * @param endOffset - Should be set if `forceDoc` is also set, to set the document range end and to indicate errors correctly.\n     */\n    *compose(tokens, forceDoc = false, endOffset = -1) {\n        for (const token of tokens)\n            yield* this.next(token);\n        yield* this.end(forceDoc, endOffset);\n    }\n    /** Advance the composer by one CST token. */\n    *next(token) {\n        switch (token.type) {\n            case 'directive':\n                this.directives.add(token.source, (offset, message, warning) => {\n                    const pos = getErrorPos(token);\n                    pos[0] += offset;\n                    this.onError(pos, 'BAD_DIRECTIVE', message, warning);\n                });\n                this.prelude.push(token.source);\n                this.atDirectives = true;\n                break;\n            case 'document': {\n                const doc = composeDoc(this.options, this.directives, token, this.onError);\n                if (this.atDirectives && !doc.directives.docStart)\n                    this.onError(token, 'MISSING_CHAR', 'Missing directives-end/doc-start indicator line');\n                this.decorate(doc, false);\n                if (this.doc)\n                    yield this.doc;\n                this.doc = doc;\n                this.atDirectives = false;\n                break;\n            }\n            case 'byte-order-mark':\n            case 'space':\n                break;\n            case 'comment':\n            case 'newline':\n                this.prelude.push(token.source);\n                break;\n            case 'error': {\n                const msg = token.source\n                    ? `${token.message}: ${JSON.stringify(token.source)}`\n                    : token.message;\n                const error = new YAMLParseError(getErrorPos(token), 'UNEXPECTED_TOKEN', msg);\n                if (this.atDirectives || !this.doc)\n                    this.errors.push(error);\n                else\n                    this.doc.errors.push(error);\n                break;\n            }\n            case 'doc-end': {\n                if (!this.doc) {\n                    const msg = 'Unexpected doc-end without preceding document';\n                    this.errors.push(new YAMLParseError(getErrorPos(token), 'UNEXPECTED_TOKEN', msg));\n                    break;\n                }\n                this.doc.directives.docEnd = true;\n                const end = resolveEnd(token.end, token.offset + token.source.length, this.doc.options.strict, this.onError);\n                this.decorate(this.doc, true);\n                if (end.comment) {\n                    const dc = this.doc.comment;\n                    this.doc.comment = dc ? `${dc}\\n${end.comment}` : end.comment;\n                }\n                this.doc.range[2] = end.offset;\n                break;\n            }\n            default:\n                this.errors.push(new YAMLParseError(getErrorPos(token), 'UNEXPECTED_TOKEN', `Unsupported token ${token.type}`));\n        }\n    }\n    /**\n     * Call at end of input to yield any remaining document.\n     *\n     * @param forceDoc - If the stream contains no document, still emit a final document including any comments and directives that would be applied to a subsequent document.\n     * @param endOffset - Should be set if `forceDoc` is also set, to set the document range end and to indicate errors correctly.\n     */\n    *end(forceDoc = false, endOffset = -1) {\n        if (this.doc) {\n            this.decorate(this.doc, true);\n            yield this.doc;\n            this.doc = null;\n        }\n        else if (forceDoc) {\n            const opts = Object.assign({ _directives: this.directives }, this.options);\n            const doc = new Document(undefined, opts);\n            if (this.atDirectives)\n                this.onError(endOffset, 'MISSING_CHAR', 'Missing directives-end indicator line');\n            doc.range = [0, endOffset, endOffset];\n            this.decorate(doc, false);\n            yield doc;\n        }\n    }\n}\n\nexport { Composer };\n", "export { createScalarToken, resolveAsScalar, setScalarValue } from './cst-scalar.js';\nexport { stringify } from './cst-stringify.js';\nexport { visit } from './cst-visit.js';\n\n/** The byte order mark */\nconst BOM = '\\u{FEFF}';\n/** Start of doc-mode */\nconst DOCUMENT = '\\x02'; // C0: Start of Text\n/** Unexpected end of flow-mode */\nconst FLOW_END = '\\x18'; // C0: Cancel\n/** Next token is a scalar value */\nconst SCALAR = '\\x1f'; // C0: Unit Separator\n/** @returns `true` if `token` is a flow or block collection */\nconst isCollection = (token) => !!token && 'items' in token;\n/** @returns `true` if `token` is a flow or block scalar; not an alias */\nconst isScalar = (token) => !!token &&\n    (token.type === 'scalar' ||\n        token.type === 'single-quoted-scalar' ||\n        token.type === 'double-quoted-scalar' ||\n        token.type === 'block-scalar');\n/* istanbul ignore next */\n/** Get a printable representation of a lexer token */\nfunction prettyToken(token) {\n    switch (token) {\n        case BOM:\n            return '<BOM>';\n        case DOCUMENT:\n            return '<DOC>';\n        case FLOW_END:\n            return '<FLOW_END>';\n        case SCALAR:\n            return '<SCALAR>';\n        default:\n            return JSON.stringify(token);\n    }\n}\n/** Identify the type of a lexer token. May return `null` for unknown tokens. */\nfunction tokenType(source) {\n    switch (source) {\n        case BOM:\n            return 'byte-order-mark';\n        case DOCUMENT:\n            return 'doc-mode';\n        case FLOW_END:\n            return 'flow-error-end';\n        case SCALAR:\n            return 'scalar';\n        case '---':\n            return 'doc-start';\n        case '...':\n            return 'doc-end';\n        case '':\n        case '\\n':\n        case '\\r\\n':\n            return 'newline';\n        case '-':\n            return 'seq-item-ind';\n        case '?':\n            return 'explicit-key-ind';\n        case ':':\n            return 'map-value-ind';\n        case '{':\n            return 'flow-map-start';\n        case '}':\n            return 'flow-map-end';\n        case '[':\n            return 'flow-seq-start';\n        case ']':\n            return 'flow-seq-end';\n        case ',':\n            return 'comma';\n    }\n    switch (source[0]) {\n        case ' ':\n        case '\\t':\n            return 'space';\n        case '#':\n            return 'comment';\n        case '%':\n            return 'directive-line';\n        case '*':\n            return 'alias';\n        case '&':\n            return 'anchor';\n        case '!':\n            return 'tag';\n        case \"'\":\n            return 'single-quoted-scalar';\n        case '\"':\n            return 'double-quoted-scalar';\n        case '|':\n        case '>':\n            return 'block-scalar-header';\n    }\n    return null;\n}\n\nexport { BOM, DOCUMENT, FLOW_END, SCALAR, isCollection, isScalar, prettyToken, tokenType };\n", "import { resolveBlockScalar } from '../compose/resolve-block-scalar.js';\nimport { resolveFlowScalar } from '../compose/resolve-flow-scalar.js';\nimport { YAMLParseError } from '../errors.js';\nimport { stringifyString } from '../stringify/stringifyString.js';\n\nfunction resolveAsScalar(token, strict = true, onError) {\n    if (token) {\n        const _onError = (pos, code, message) => {\n            const offset = typeof pos === 'number' ? pos : Array.isArray(pos) ? pos[0] : pos.offset;\n            if (onError)\n                onError(offset, code, message);\n            else\n                throw new YAMLParseError([offset, offset + 1], code, message);\n        };\n        switch (token.type) {\n            case 'scalar':\n            case 'single-quoted-scalar':\n            case 'double-quoted-scalar':\n                return resolveFlowScalar(token, strict, _onError);\n            case 'block-scalar':\n                return resolveBlockScalar({ options: { strict } }, token, _onError);\n        }\n    }\n    return null;\n}\n/**\n * Create a new scalar token with `value`\n *\n * Values that represent an actual string but may be parsed as a different type should use a `type` other than `'PLAIN'`,\n * as this function does not support any schema operations and won't check for such conflicts.\n *\n * @param value The string representation of the value, which will have its content properly indented.\n * @param context.end Comments and whitespace after the end of the value, or after the block scalar header. If undefined, a newline will be added.\n * @param context.implicitKey Being within an implicit key may affect the resolved type of the token's value.\n * @param context.indent The indent level of the token.\n * @param context.inFlow Is this scalar within a flow collection? This may affect the resolved type of the token's value.\n * @param context.offset The offset position of the token.\n * @param context.type The preferred type of the scalar token. If undefined, the previous type of the `token` will be used, defaulting to `'PLAIN'`.\n */\nfunction createScalarToken(value, context) {\n    const { implicitKey = false, indent, inFlow = false, offset = -1, type = 'PLAIN' } = context;\n    const source = stringifyString({ type, value }, {\n        implicitKey,\n        indent: indent > 0 ? ' '.repeat(indent) : '',\n        inFlow,\n        options: { blockQuote: true, lineWidth: -1 }\n    });\n    const end = context.end ?? [\n        { type: 'newline', offset: -1, indent, source: '\\n' }\n    ];\n    switch (source[0]) {\n        case '|':\n        case '>': {\n            const he = source.indexOf('\\n');\n            const head = source.substring(0, he);\n            const body = source.substring(he + 1) + '\\n';\n            const props = [\n                { type: 'block-scalar-header', offset, indent, source: head }\n            ];\n            if (!addEndtoBlockProps(props, end))\n                props.push({ type: 'newline', offset: -1, indent, source: '\\n' });\n            return { type: 'block-scalar', offset, indent, props, source: body };\n        }\n        case '\"':\n            return { type: 'double-quoted-scalar', offset, indent, source, end };\n        case \"'\":\n            return { type: 'single-quoted-scalar', offset, indent, source, end };\n        default:\n            return { type: 'scalar', offset, indent, source, end };\n    }\n}\n/**\n * Set the value of `token` to the given string `value`, overwriting any previous contents and type that it may have.\n *\n * Best efforts are made to retain any comments previously associated with the `token`,\n * though all contents within a collection's `items` will be overwritten.\n *\n * Values that represent an actual string but may be parsed as a different type should use a `type` other than `'PLAIN'`,\n * as this function does not support any schema operations and won't check for such conflicts.\n *\n * @param token Any token. If it does not include an `indent` value, the value will be stringified as if it were an implicit key.\n * @param value The string representation of the value, which will have its content properly indented.\n * @param context.afterKey In most cases, values after a key should have an additional level of indentation.\n * @param context.implicitKey Being within an implicit key may affect the resolved type of the token's value.\n * @param context.inFlow Being within a flow collection may affect the resolved type of the token's value.\n * @param context.type The preferred type of the scalar token. If undefined, the previous type of the `token` will be used, defaulting to `'PLAIN'`.\n */\nfunction setScalarValue(token, value, context = {}) {\n    let { afterKey = false, implicitKey = false, inFlow = false, type } = context;\n    let indent = 'indent' in token ? token.indent : null;\n    if (afterKey && typeof indent === 'number')\n        indent += 2;\n    if (!type)\n        switch (token.type) {\n            case 'single-quoted-scalar':\n                type = 'QUOTE_SINGLE';\n                break;\n            case 'double-quoted-scalar':\n                type = 'QUOTE_DOUBLE';\n                break;\n            case 'block-scalar': {\n                const header = token.props[0];\n                if (header.type !== 'block-scalar-header')\n                    throw new Error('Invalid block scalar header');\n                type = header.source[0] === '>' ? 'BLOCK_FOLDED' : 'BLOCK_LITERAL';\n                break;\n            }\n            default:\n                type = 'PLAIN';\n        }\n    const source = stringifyString({ type, value }, {\n        implicitKey: implicitKey || indent === null,\n        indent: indent !== null && indent > 0 ? ' '.repeat(indent) : '',\n        inFlow,\n        options: { blockQuote: true, lineWidth: -1 }\n    });\n    switch (source[0]) {\n        case '|':\n        case '>':\n            setBlockScalarValue(token, source);\n            break;\n        case '\"':\n            setFlowScalarValue(token, source, 'double-quoted-scalar');\n            break;\n        case \"'\":\n            setFlowScalarValue(token, source, 'single-quoted-scalar');\n            break;\n        default:\n            setFlowScalarValue(token, source, 'scalar');\n    }\n}\nfunction setBlockScalarValue(token, source) {\n    const he = source.indexOf('\\n');\n    const head = source.substring(0, he);\n    const body = source.substring(he + 1) + '\\n';\n    if (token.type === 'block-scalar') {\n        const header = token.props[0];\n        if (header.type !== 'block-scalar-header')\n            throw new Error('Invalid block scalar header');\n        header.source = head;\n        token.source = body;\n    }\n    else {\n        const { offset } = token;\n        const indent = 'indent' in token ? token.indent : -1;\n        const props = [\n            { type: 'block-scalar-header', offset, indent, source: head }\n        ];\n        if (!addEndtoBlockProps(props, 'end' in token ? token.end : undefined))\n            props.push({ type: 'newline', offset: -1, indent, source: '\\n' });\n        for (const key of Object.keys(token))\n            if (key !== 'type' && key !== 'offset')\n                delete token[key];\n        Object.assign(token, { type: 'block-scalar', indent, props, source: body });\n    }\n}\n/** @returns `true` if last token is a newline */\nfunction addEndtoBlockProps(props, end) {\n    if (end)\n        for (const st of end)\n            switch (st.type) {\n                case 'space':\n                case 'comment':\n                    props.push(st);\n                    break;\n                case 'newline':\n                    props.push(st);\n                    return true;\n            }\n    return false;\n}\nfunction setFlowScalarValue(token, source, type) {\n    switch (token.type) {\n        case 'scalar':\n        case 'double-quoted-scalar':\n        case 'single-quoted-scalar':\n            token.type = type;\n            token.source = source;\n            break;\n        case 'block-scalar': {\n            const end = token.props.slice(1);\n            let oa = source.length;\n            if (token.props[0].type === 'block-scalar-header')\n                oa -= token.props[0].source.length;\n            for (const tok of end)\n                tok.offset += oa;\n            delete token.props;\n            Object.assign(token, { type, source, end });\n            break;\n        }\n        case 'block-map':\n        case 'block-seq': {\n            const offset = token.offset + source.length;\n            const nl = { type: 'newline', offset, indent: token.indent, source: '\\n' };\n            delete token.items;\n            Object.assign(token, { type, source, end: [nl] });\n            break;\n        }\n        default: {\n            const indent = 'indent' in token ? token.indent : -1;\n            const end = 'end' in token && Array.isArray(token.end)\n                ? token.end.filter(st => st.type === 'space' ||\n                    st.type === 'comment' ||\n                    st.type === 'newline')\n                : [];\n            for (const key of Object.keys(token))\n                if (key !== 'type' && key !== 'offset')\n                    delete token[key];\n            Object.assign(token, { type, indent, source, end });\n        }\n    }\n}\n\nexport { createScalarToken, resolveAsScalar, setScalarValue };\n", "/**\n * Stringify a CST document, token, or collection item\n *\n * Fair warning: This applies no validation whatsoever, and\n * simply concatenates the sources in their logical order.\n */\nconst stringify = (cst) => 'type' in cst ? stringifyToken(cst) : stringifyItem(cst);\nfunction stringifyToken(token) {\n    switch (token.type) {\n        case 'block-scalar': {\n            let res = '';\n            for (const tok of token.props)\n                res += stringifyToken(tok);\n            return res + token.source;\n        }\n        case 'block-map':\n        case 'block-seq': {\n            let res = '';\n            for (const item of token.items)\n                res += stringifyItem(item);\n            return res;\n        }\n        case 'flow-collection': {\n            let res = token.start.source;\n            for (const item of token.items)\n                res += stringifyItem(item);\n            for (const st of token.end)\n                res += st.source;\n            return res;\n        }\n        case 'document': {\n            let res = stringifyItem(token);\n            if (token.end)\n                for (const st of token.end)\n                    res += st.source;\n            return res;\n        }\n        default: {\n            let res = token.source;\n            if ('end' in token && token.end)\n                for (const st of token.end)\n                    res += st.source;\n            return res;\n        }\n    }\n}\nfunction stringifyItem({ start, key, sep, value }) {\n    let res = '';\n    for (const st of start)\n        res += st.source;\n    if (key)\n        res += stringifyToken(key);\n    if (sep)\n        for (const st of sep)\n            res += st.source;\n    if (value)\n        res += stringifyToken(value);\n    return res;\n}\n\nexport { stringify };\n", "const BREAK = Symbol('break visit');\nconst SKIP = Symbol('skip children');\nconst REMOVE = Symbol('remove item');\n/**\n * Apply a visitor to a CST document or item.\n *\n * Walks through the tree (depth-first) starting from the root, calling a\n * `visitor` function with two arguments when entering each item:\n *   - `item`: The current item, which included the following members:\n *     - `start: SourceToken[]` – Source tokens before the key or value,\n *       possibly including its anchor or tag.\n *     - `key?: Token | null` – Set for pair values. May then be `null`, if\n *       the key before the `:` separator is empty.\n *     - `sep?: SourceToken[]` – Source tokens between the key and the value,\n *       which should include the `:` map value indicator if `value` is set.\n *     - `value?: Token` – The value of a sequence item, or of a map pair.\n *   - `path`: The steps from the root to the current node, as an array of\n *     `['key' | 'value', number]` tuples.\n *\n * The return value of the visitor may be used to control the traversal:\n *   - `undefined` (default): Do nothing and continue\n *   - `visit.SKIP`: Do not visit the children of this token, continue with\n *      next sibling\n *   - `visit.BREAK`: Terminate traversal completely\n *   - `visit.REMOVE`: Remove the current item, then continue with the next one\n *   - `number`: Set the index of the next step. This is useful especially if\n *     the index of the current token has changed.\n *   - `function`: Define the next visitor for this item. After the original\n *     visitor is called on item entry, next visitors are called after handling\n *     a non-empty `key` and when exiting the item.\n */\nfunction visit(cst, visitor) {\n    if ('type' in cst && cst.type === 'document')\n        cst = { start: cst.start, value: cst.value };\n    _visit(Object.freeze([]), cst, visitor);\n}\n// Without the `as symbol` casts, TS declares these in the `visit`\n// namespace using `var`, but then complains about that because\n// `unique symbol` must be `const`.\n/** Terminate visit traversal completely */\nvisit.BREAK = BREAK;\n/** Do not visit the children of the current item */\nvisit.SKIP = SKIP;\n/** Remove the current item */\nvisit.REMOVE = REMOVE;\n/** Find the item at `path` from `cst` as the root */\nvisit.itemAtPath = (cst, path) => {\n    let item = cst;\n    for (const [field, index] of path) {\n        const tok = item?.[field];\n        if (tok && 'items' in tok) {\n            item = tok.items[index];\n        }\n        else\n            return undefined;\n    }\n    return item;\n};\n/**\n * Get the immediate parent collection of the item at `path` from `cst` as the root.\n *\n * Throws an error if the collection is not found, which should never happen if the item itself exists.\n */\nvisit.parentCollection = (cst, path) => {\n    const parent = visit.itemAtPath(cst, path.slice(0, -1));\n    const field = path[path.length - 1][0];\n    const coll = parent?.[field];\n    if (coll && 'items' in coll)\n        return coll;\n    throw new Error('Parent collection not found');\n};\nfunction _visit(path, item, visitor) {\n    let ctrl = visitor(item, path);\n    if (typeof ctrl === 'symbol')\n        return ctrl;\n    for (const field of ['key', 'value']) {\n        const token = item[field];\n        if (token && 'items' in token) {\n            for (let i = 0; i < token.items.length; ++i) {\n                const ci = _visit(Object.freeze(path.concat([[field, i]])), token.items[i], visitor);\n                if (typeof ci === 'number')\n                    i = ci - 1;\n                else if (ci === BREAK)\n                    return BREAK;\n                else if (ci === REMOVE) {\n                    token.items.splice(i, 1);\n                    i -= 1;\n                }\n            }\n            if (typeof ctrl === 'function' && field === 'key')\n                ctrl = ctrl(item, path);\n        }\n    }\n    return typeof ctrl === 'function' ? ctrl(item, path) : ctrl;\n}\n\nexport { visit };\n", "import { B<PERSON>, DOCUMENT, FLOW_END, SCALAR } from './cst.js';\n\n/*\nSTART -> stream\n\nstream\n  directive -> line-end -> stream\n  indent + line-end -> stream\n  [else] -> line-start\n\nline-end\n  comment -> line-end\n  newline -> .\n  input-end -> END\n\nline-start\n  doc-start -> doc\n  doc-end -> stream\n  [else] -> indent -> block-start\n\nblock-start\n  seq-item-start -> block-start\n  explicit-key-start -> block-start\n  map-value-start -> block-start\n  [else] -> doc\n\ndoc\n  line-end -> line-start\n  spaces -> doc\n  anchor -> doc\n  tag -> doc\n  flow-start -> flow -> doc\n  flow-end -> error -> doc\n  seq-item-start -> error -> doc\n  explicit-key-start -> error -> doc\n  map-value-start -> doc\n  alias -> doc\n  quote-start -> quoted-scalar -> doc\n  block-scalar-header -> line-end -> block-scalar(min) -> line-start\n  [else] -> plain-scalar(false, min) -> doc\n\nflow\n  line-end -> flow\n  spaces -> flow\n  anchor -> flow\n  tag -> flow\n  flow-start -> flow -> flow\n  flow-end -> .\n  seq-item-start -> error -> flow\n  explicit-key-start -> flow\n  map-value-start -> flow\n  alias -> flow\n  quote-start -> quoted-scalar -> flow\n  comma -> flow\n  [else] -> plain-scalar(true, 0) -> flow\n\nquoted-scalar\n  quote-end -> .\n  [else] -> quoted-scalar\n\nblock-scalar(min)\n  newline + peek(indent < min) -> .\n  [else] -> block-scalar(min)\n\nplain-scalar(is-flow, min)\n  scalar-end(is-flow) -> .\n  peek(newline + (indent < min)) -> .\n  [else] -> plain-scalar(min)\n*/\nfunction isEmpty(ch) {\n    switch (ch) {\n        case undefined:\n        case ' ':\n        case '\\n':\n        case '\\r':\n        case '\\t':\n            return true;\n        default:\n            return false;\n    }\n}\nconst hexDigits = new Set('0123456789ABCDEFabcdef');\nconst tagChars = new Set(\"0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz-#;/?:@&=+$_.!~*'()\");\nconst flowIndicatorChars = new Set(',[]{}');\nconst invalidAnchorChars = new Set(' ,[]{}\\n\\r\\t');\nconst isNotAnchorChar = (ch) => !ch || invalidAnchorChars.has(ch);\n/**\n * Splits an input string into lexical tokens, i.e. smaller strings that are\n * easily identifiable by `tokens.tokenType()`.\n *\n * Lexing starts always in a \"stream\" context. Incomplete input may be buffered\n * until a complete token can be emitted.\n *\n * In addition to slices of the original input, the following control characters\n * may also be emitted:\n *\n * - `\\x02` (Start of Text): A document starts with the next token\n * - `\\x18` (Cancel): Unexpected end of flow-mode (indicates an error)\n * - `\\x1f` (Unit Separator): Next token is a scalar value\n * - `\\u{FEFF}` (Byte order mark): Emitted separately outside documents\n */\nclass Lexer {\n    constructor() {\n        /**\n         * Flag indicating whether the end of the current buffer marks the end of\n         * all input\n         */\n        this.atEnd = false;\n        /**\n         * Explicit indent set in block scalar header, as an offset from the current\n         * minimum indent, so e.g. set to 1 from a header `|2+`. Set to -1 if not\n         * explicitly set.\n         */\n        this.blockScalarIndent = -1;\n        /**\n         * Block scalars that include a + (keep) chomping indicator in their header\n         * include trailing empty lines, which are otherwise excluded from the\n         * scalar's contents.\n         */\n        this.blockScalarKeep = false;\n        /** Current input */\n        this.buffer = '';\n        /**\n         * Flag noting whether the map value indicator : can immediately follow this\n         * node within a flow context.\n         */\n        this.flowKey = false;\n        /** Count of surrounding flow collection levels. */\n        this.flowLevel = 0;\n        /**\n         * Minimum level of indentation required for next lines to be parsed as a\n         * part of the current scalar value.\n         */\n        this.indentNext = 0;\n        /** Indentation level of the current line. */\n        this.indentValue = 0;\n        /** Position of the next \\n character. */\n        this.lineEndPos = null;\n        /** Stores the state of the lexer if reaching the end of incpomplete input */\n        this.next = null;\n        /** A pointer to `buffer`; the current position of the lexer. */\n        this.pos = 0;\n    }\n    /**\n     * Generate YAML tokens from the `source` string. If `incomplete`,\n     * a part of the last line may be left as a buffer for the next call.\n     *\n     * @returns A generator of lexical tokens\n     */\n    *lex(source, incomplete = false) {\n        if (source) {\n            if (typeof source !== 'string')\n                throw TypeError('source is not a string');\n            this.buffer = this.buffer ? this.buffer + source : source;\n            this.lineEndPos = null;\n        }\n        this.atEnd = !incomplete;\n        let next = this.next ?? 'stream';\n        while (next && (incomplete || this.hasChars(1)))\n            next = yield* this.parseNext(next);\n    }\n    atLineEnd() {\n        let i = this.pos;\n        let ch = this.buffer[i];\n        while (ch === ' ' || ch === '\\t')\n            ch = this.buffer[++i];\n        if (!ch || ch === '#' || ch === '\\n')\n            return true;\n        if (ch === '\\r')\n            return this.buffer[i + 1] === '\\n';\n        return false;\n    }\n    charAt(n) {\n        return this.buffer[this.pos + n];\n    }\n    continueScalar(offset) {\n        let ch = this.buffer[offset];\n        if (this.indentNext > 0) {\n            let indent = 0;\n            while (ch === ' ')\n                ch = this.buffer[++indent + offset];\n            if (ch === '\\r') {\n                const next = this.buffer[indent + offset + 1];\n                if (next === '\\n' || (!next && !this.atEnd))\n                    return offset + indent + 1;\n            }\n            return ch === '\\n' || indent >= this.indentNext || (!ch && !this.atEnd)\n                ? offset + indent\n                : -1;\n        }\n        if (ch === '-' || ch === '.') {\n            const dt = this.buffer.substr(offset, 3);\n            if ((dt === '---' || dt === '...') && isEmpty(this.buffer[offset + 3]))\n                return -1;\n        }\n        return offset;\n    }\n    getLine() {\n        let end = this.lineEndPos;\n        if (typeof end !== 'number' || (end !== -1 && end < this.pos)) {\n            end = this.buffer.indexOf('\\n', this.pos);\n            this.lineEndPos = end;\n        }\n        if (end === -1)\n            return this.atEnd ? this.buffer.substring(this.pos) : null;\n        if (this.buffer[end - 1] === '\\r')\n            end -= 1;\n        return this.buffer.substring(this.pos, end);\n    }\n    hasChars(n) {\n        return this.pos + n <= this.buffer.length;\n    }\n    setNext(state) {\n        this.buffer = this.buffer.substring(this.pos);\n        this.pos = 0;\n        this.lineEndPos = null;\n        this.next = state;\n        return null;\n    }\n    peek(n) {\n        return this.buffer.substr(this.pos, n);\n    }\n    *parseNext(next) {\n        switch (next) {\n            case 'stream':\n                return yield* this.parseStream();\n            case 'line-start':\n                return yield* this.parseLineStart();\n            case 'block-start':\n                return yield* this.parseBlockStart();\n            case 'doc':\n                return yield* this.parseDocument();\n            case 'flow':\n                return yield* this.parseFlowCollection();\n            case 'quoted-scalar':\n                return yield* this.parseQuotedScalar();\n            case 'block-scalar':\n                return yield* this.parseBlockScalar();\n            case 'plain-scalar':\n                return yield* this.parsePlainScalar();\n        }\n    }\n    *parseStream() {\n        let line = this.getLine();\n        if (line === null)\n            return this.setNext('stream');\n        if (line[0] === BOM) {\n            yield* this.pushCount(1);\n            line = line.substring(1);\n        }\n        if (line[0] === '%') {\n            let dirEnd = line.length;\n            let cs = line.indexOf('#');\n            while (cs !== -1) {\n                const ch = line[cs - 1];\n                if (ch === ' ' || ch === '\\t') {\n                    dirEnd = cs - 1;\n                    break;\n                }\n                else {\n                    cs = line.indexOf('#', cs + 1);\n                }\n            }\n            while (true) {\n                const ch = line[dirEnd - 1];\n                if (ch === ' ' || ch === '\\t')\n                    dirEnd -= 1;\n                else\n                    break;\n            }\n            const n = (yield* this.pushCount(dirEnd)) + (yield* this.pushSpaces(true));\n            yield* this.pushCount(line.length - n); // possible comment\n            this.pushNewline();\n            return 'stream';\n        }\n        if (this.atLineEnd()) {\n            const sp = yield* this.pushSpaces(true);\n            yield* this.pushCount(line.length - sp);\n            yield* this.pushNewline();\n            return 'stream';\n        }\n        yield DOCUMENT;\n        return yield* this.parseLineStart();\n    }\n    *parseLineStart() {\n        const ch = this.charAt(0);\n        if (!ch && !this.atEnd)\n            return this.setNext('line-start');\n        if (ch === '-' || ch === '.') {\n            if (!this.atEnd && !this.hasChars(4))\n                return this.setNext('line-start');\n            const s = this.peek(3);\n            if ((s === '---' || s === '...') && isEmpty(this.charAt(3))) {\n                yield* this.pushCount(3);\n                this.indentValue = 0;\n                this.indentNext = 0;\n                return s === '---' ? 'doc' : 'stream';\n            }\n        }\n        this.indentValue = yield* this.pushSpaces(false);\n        if (this.indentNext > this.indentValue && !isEmpty(this.charAt(1)))\n            this.indentNext = this.indentValue;\n        return yield* this.parseBlockStart();\n    }\n    *parseBlockStart() {\n        const [ch0, ch1] = this.peek(2);\n        if (!ch1 && !this.atEnd)\n            return this.setNext('block-start');\n        if ((ch0 === '-' || ch0 === '?' || ch0 === ':') && isEmpty(ch1)) {\n            const n = (yield* this.pushCount(1)) + (yield* this.pushSpaces(true));\n            this.indentNext = this.indentValue + 1;\n            this.indentValue += n;\n            return yield* this.parseBlockStart();\n        }\n        return 'doc';\n    }\n    *parseDocument() {\n        yield* this.pushSpaces(true);\n        const line = this.getLine();\n        if (line === null)\n            return this.setNext('doc');\n        let n = yield* this.pushIndicators();\n        switch (line[n]) {\n            case '#':\n                yield* this.pushCount(line.length - n);\n            // fallthrough\n            case undefined:\n                yield* this.pushNewline();\n                return yield* this.parseLineStart();\n            case '{':\n            case '[':\n                yield* this.pushCount(1);\n                this.flowKey = false;\n                this.flowLevel = 1;\n                return 'flow';\n            case '}':\n            case ']':\n                // this is an error\n                yield* this.pushCount(1);\n                return 'doc';\n            case '*':\n                yield* this.pushUntil(isNotAnchorChar);\n                return 'doc';\n            case '\"':\n            case \"'\":\n                return yield* this.parseQuotedScalar();\n            case '|':\n            case '>':\n                n += yield* this.parseBlockScalarHeader();\n                n += yield* this.pushSpaces(true);\n                yield* this.pushCount(line.length - n);\n                yield* this.pushNewline();\n                return yield* this.parseBlockScalar();\n            default:\n                return yield* this.parsePlainScalar();\n        }\n    }\n    *parseFlowCollection() {\n        let nl, sp;\n        let indent = -1;\n        do {\n            nl = yield* this.pushNewline();\n            if (nl > 0) {\n                sp = yield* this.pushSpaces(false);\n                this.indentValue = indent = sp;\n            }\n            else {\n                sp = 0;\n            }\n            sp += yield* this.pushSpaces(true);\n        } while (nl + sp > 0);\n        const line = this.getLine();\n        if (line === null)\n            return this.setNext('flow');\n        if ((indent !== -1 && indent < this.indentNext && line[0] !== '#') ||\n            (indent === 0 &&\n                (line.startsWith('---') || line.startsWith('...')) &&\n                isEmpty(line[3]))) {\n            // Allowing for the terminal ] or } at the same (rather than greater)\n            // indent level as the initial [ or { is technically invalid, but\n            // failing here would be surprising to users.\n            const atFlowEndMarker = indent === this.indentNext - 1 &&\n                this.flowLevel === 1 &&\n                (line[0] === ']' || line[0] === '}');\n            if (!atFlowEndMarker) {\n                // this is an error\n                this.flowLevel = 0;\n                yield FLOW_END;\n                return yield* this.parseLineStart();\n            }\n        }\n        let n = 0;\n        while (line[n] === ',') {\n            n += yield* this.pushCount(1);\n            n += yield* this.pushSpaces(true);\n            this.flowKey = false;\n        }\n        n += yield* this.pushIndicators();\n        switch (line[n]) {\n            case undefined:\n                return 'flow';\n            case '#':\n                yield* this.pushCount(line.length - n);\n                return 'flow';\n            case '{':\n            case '[':\n                yield* this.pushCount(1);\n                this.flowKey = false;\n                this.flowLevel += 1;\n                return 'flow';\n            case '}':\n            case ']':\n                yield* this.pushCount(1);\n                this.flowKey = true;\n                this.flowLevel -= 1;\n                return this.flowLevel ? 'flow' : 'doc';\n            case '*':\n                yield* this.pushUntil(isNotAnchorChar);\n                return 'flow';\n            case '\"':\n            case \"'\":\n                this.flowKey = true;\n                return yield* this.parseQuotedScalar();\n            case ':': {\n                const next = this.charAt(1);\n                if (this.flowKey || isEmpty(next) || next === ',') {\n                    this.flowKey = false;\n                    yield* this.pushCount(1);\n                    yield* this.pushSpaces(true);\n                    return 'flow';\n                }\n            }\n            // fallthrough\n            default:\n                this.flowKey = false;\n                return yield* this.parsePlainScalar();\n        }\n    }\n    *parseQuotedScalar() {\n        const quote = this.charAt(0);\n        let end = this.buffer.indexOf(quote, this.pos + 1);\n        if (quote === \"'\") {\n            while (end !== -1 && this.buffer[end + 1] === \"'\")\n                end = this.buffer.indexOf(\"'\", end + 2);\n        }\n        else {\n            // double-quote\n            while (end !== -1) {\n                let n = 0;\n                while (this.buffer[end - 1 - n] === '\\\\')\n                    n += 1;\n                if (n % 2 === 0)\n                    break;\n                end = this.buffer.indexOf('\"', end + 1);\n            }\n        }\n        // Only looking for newlines within the quotes\n        const qb = this.buffer.substring(0, end);\n        let nl = qb.indexOf('\\n', this.pos);\n        if (nl !== -1) {\n            while (nl !== -1) {\n                const cs = this.continueScalar(nl + 1);\n                if (cs === -1)\n                    break;\n                nl = qb.indexOf('\\n', cs);\n            }\n            if (nl !== -1) {\n                // this is an error caused by an unexpected unindent\n                end = nl - (qb[nl - 1] === '\\r' ? 2 : 1);\n            }\n        }\n        if (end === -1) {\n            if (!this.atEnd)\n                return this.setNext('quoted-scalar');\n            end = this.buffer.length;\n        }\n        yield* this.pushToIndex(end + 1, false);\n        return this.flowLevel ? 'flow' : 'doc';\n    }\n    *parseBlockScalarHeader() {\n        this.blockScalarIndent = -1;\n        this.blockScalarKeep = false;\n        let i = this.pos;\n        while (true) {\n            const ch = this.buffer[++i];\n            if (ch === '+')\n                this.blockScalarKeep = true;\n            else if (ch > '0' && ch <= '9')\n                this.blockScalarIndent = Number(ch) - 1;\n            else if (ch !== '-')\n                break;\n        }\n        return yield* this.pushUntil(ch => isEmpty(ch) || ch === '#');\n    }\n    *parseBlockScalar() {\n        let nl = this.pos - 1; // may be -1 if this.pos === 0\n        let indent = 0;\n        let ch;\n        loop: for (let i = this.pos; (ch = this.buffer[i]); ++i) {\n            switch (ch) {\n                case ' ':\n                    indent += 1;\n                    break;\n                case '\\n':\n                    nl = i;\n                    indent = 0;\n                    break;\n                case '\\r': {\n                    const next = this.buffer[i + 1];\n                    if (!next && !this.atEnd)\n                        return this.setNext('block-scalar');\n                    if (next === '\\n')\n                        break;\n                } // fallthrough\n                default:\n                    break loop;\n            }\n        }\n        if (!ch && !this.atEnd)\n            return this.setNext('block-scalar');\n        if (indent >= this.indentNext) {\n            if (this.blockScalarIndent === -1)\n                this.indentNext = indent;\n            else {\n                this.indentNext =\n                    this.blockScalarIndent + (this.indentNext === 0 ? 1 : this.indentNext);\n            }\n            do {\n                const cs = this.continueScalar(nl + 1);\n                if (cs === -1)\n                    break;\n                nl = this.buffer.indexOf('\\n', cs);\n            } while (nl !== -1);\n            if (nl === -1) {\n                if (!this.atEnd)\n                    return this.setNext('block-scalar');\n                nl = this.buffer.length;\n            }\n        }\n        // Trailing insufficiently indented tabs are invalid.\n        // To catch that during parsing, we include them in the block scalar value.\n        let i = nl + 1;\n        ch = this.buffer[i];\n        while (ch === ' ')\n            ch = this.buffer[++i];\n        if (ch === '\\t') {\n            while (ch === '\\t' || ch === ' ' || ch === '\\r' || ch === '\\n')\n                ch = this.buffer[++i];\n            nl = i - 1;\n        }\n        else if (!this.blockScalarKeep) {\n            do {\n                let i = nl - 1;\n                let ch = this.buffer[i];\n                if (ch === '\\r')\n                    ch = this.buffer[--i];\n                const lastChar = i; // Drop the line if last char not more indented\n                while (ch === ' ')\n                    ch = this.buffer[--i];\n                if (ch === '\\n' && i >= this.pos && i + 1 + indent > lastChar)\n                    nl = i;\n                else\n                    break;\n            } while (true);\n        }\n        yield SCALAR;\n        yield* this.pushToIndex(nl + 1, true);\n        return yield* this.parseLineStart();\n    }\n    *parsePlainScalar() {\n        const inFlow = this.flowLevel > 0;\n        let end = this.pos - 1;\n        let i = this.pos - 1;\n        let ch;\n        while ((ch = this.buffer[++i])) {\n            if (ch === ':') {\n                const next = this.buffer[i + 1];\n                if (isEmpty(next) || (inFlow && flowIndicatorChars.has(next)))\n                    break;\n                end = i;\n            }\n            else if (isEmpty(ch)) {\n                let next = this.buffer[i + 1];\n                if (ch === '\\r') {\n                    if (next === '\\n') {\n                        i += 1;\n                        ch = '\\n';\n                        next = this.buffer[i + 1];\n                    }\n                    else\n                        end = i;\n                }\n                if (next === '#' || (inFlow && flowIndicatorChars.has(next)))\n                    break;\n                if (ch === '\\n') {\n                    const cs = this.continueScalar(i + 1);\n                    if (cs === -1)\n                        break;\n                    i = Math.max(i, cs - 2); // to advance, but still account for ' #'\n                }\n            }\n            else {\n                if (inFlow && flowIndicatorChars.has(ch))\n                    break;\n                end = i;\n            }\n        }\n        if (!ch && !this.atEnd)\n            return this.setNext('plain-scalar');\n        yield SCALAR;\n        yield* this.pushToIndex(end + 1, true);\n        return inFlow ? 'flow' : 'doc';\n    }\n    *pushCount(n) {\n        if (n > 0) {\n            yield this.buffer.substr(this.pos, n);\n            this.pos += n;\n            return n;\n        }\n        return 0;\n    }\n    *pushToIndex(i, allowEmpty) {\n        const s = this.buffer.slice(this.pos, i);\n        if (s) {\n            yield s;\n            this.pos += s.length;\n            return s.length;\n        }\n        else if (allowEmpty)\n            yield '';\n        return 0;\n    }\n    *pushIndicators() {\n        switch (this.charAt(0)) {\n            case '!':\n                return ((yield* this.pushTag()) +\n                    (yield* this.pushSpaces(true)) +\n                    (yield* this.pushIndicators()));\n            case '&':\n                return ((yield* this.pushUntil(isNotAnchorChar)) +\n                    (yield* this.pushSpaces(true)) +\n                    (yield* this.pushIndicators()));\n            case '-': // this is an error\n            case '?': // this is an error outside flow collections\n            case ':': {\n                const inFlow = this.flowLevel > 0;\n                const ch1 = this.charAt(1);\n                if (isEmpty(ch1) || (inFlow && flowIndicatorChars.has(ch1))) {\n                    if (!inFlow)\n                        this.indentNext = this.indentValue + 1;\n                    else if (this.flowKey)\n                        this.flowKey = false;\n                    return ((yield* this.pushCount(1)) +\n                        (yield* this.pushSpaces(true)) +\n                        (yield* this.pushIndicators()));\n                }\n            }\n        }\n        return 0;\n    }\n    *pushTag() {\n        if (this.charAt(1) === '<') {\n            let i = this.pos + 2;\n            let ch = this.buffer[i];\n            while (!isEmpty(ch) && ch !== '>')\n                ch = this.buffer[++i];\n            return yield* this.pushToIndex(ch === '>' ? i + 1 : i, false);\n        }\n        else {\n            let i = this.pos + 1;\n            let ch = this.buffer[i];\n            while (ch) {\n                if (tagChars.has(ch))\n                    ch = this.buffer[++i];\n                else if (ch === '%' &&\n                    hexDigits.has(this.buffer[i + 1]) &&\n                    hexDigits.has(this.buffer[i + 2])) {\n                    ch = this.buffer[(i += 3)];\n                }\n                else\n                    break;\n            }\n            return yield* this.pushToIndex(i, false);\n        }\n    }\n    *pushNewline() {\n        const ch = this.buffer[this.pos];\n        if (ch === '\\n')\n            return yield* this.pushCount(1);\n        else if (ch === '\\r' && this.charAt(1) === '\\n')\n            return yield* this.pushCount(2);\n        else\n            return 0;\n    }\n    *pushSpaces(allowTabs) {\n        let i = this.pos - 1;\n        let ch;\n        do {\n            ch = this.buffer[++i];\n        } while (ch === ' ' || (allowTabs && ch === '\\t'));\n        const n = i - this.pos;\n        if (n > 0) {\n            yield this.buffer.substr(this.pos, n);\n            this.pos = i;\n        }\n        return n;\n    }\n    *pushUntil(test) {\n        let i = this.pos;\n        let ch = this.buffer[i];\n        while (!test(ch))\n            ch = this.buffer[++i];\n        return yield* this.pushToIndex(i, false);\n    }\n}\n\nexport { Lexer };\n", "/**\n * Tracks newlines during parsing in order to provide an efficient API for\n * determining the one-indexed `{ line, col }` position for any offset\n * within the input.\n */\nclass LineCounter {\n    constructor() {\n        this.lineStarts = [];\n        /**\n         * Should be called in ascending order. Otherwise, call\n         * `lineCounter.lineStarts.sort()` before calling `linePos()`.\n         */\n        this.addNewLine = (offset) => this.lineStarts.push(offset);\n        /**\n         * Performs a binary search and returns the 1-indexed { line, col }\n         * position of `offset`. If `line === 0`, `addNewLine` has never been\n         * called or `offset` is before the first known newline.\n         */\n        this.linePos = (offset) => {\n            let low = 0;\n            let high = this.lineStarts.length;\n            while (low < high) {\n                const mid = (low + high) >> 1; // Math.floor((low + high) / 2)\n                if (this.lineStarts[mid] < offset)\n                    low = mid + 1;\n                else\n                    high = mid;\n            }\n            if (this.lineStarts[low] === offset)\n                return { line: low + 1, col: 1 };\n            if (low === 0)\n                return { line: 0, col: offset };\n            const start = this.lineStarts[low - 1];\n            return { line: low, col: offset - start + 1 };\n        };\n    }\n}\n\nexport { LineCounter };\n", "import { tokenType } from './cst.js';\nimport { <PERSON><PERSON> } from './lexer.js';\n\nfunction includesToken(list, type) {\n    for (let i = 0; i < list.length; ++i)\n        if (list[i].type === type)\n            return true;\n    return false;\n}\nfunction findNonEmptyIndex(list) {\n    for (let i = 0; i < list.length; ++i) {\n        switch (list[i].type) {\n            case 'space':\n            case 'comment':\n            case 'newline':\n                break;\n            default:\n                return i;\n        }\n    }\n    return -1;\n}\nfunction isFlowToken(token) {\n    switch (token?.type) {\n        case 'alias':\n        case 'scalar':\n        case 'single-quoted-scalar':\n        case 'double-quoted-scalar':\n        case 'flow-collection':\n            return true;\n        default:\n            return false;\n    }\n}\nfunction getPrevProps(parent) {\n    switch (parent.type) {\n        case 'document':\n            return parent.start;\n        case 'block-map': {\n            const it = parent.items[parent.items.length - 1];\n            return it.sep ?? it.start;\n        }\n        case 'block-seq':\n            return parent.items[parent.items.length - 1].start;\n        /* istanbul ignore next should not happen */\n        default:\n            return [];\n    }\n}\n/** Note: May modify input array */\nfunction getFirstKeyStartProps(prev) {\n    if (prev.length === 0)\n        return [];\n    let i = prev.length;\n    loop: while (--i >= 0) {\n        switch (prev[i].type) {\n            case 'doc-start':\n            case 'explicit-key-ind':\n            case 'map-value-ind':\n            case 'seq-item-ind':\n            case 'newline':\n                break loop;\n        }\n    }\n    while (prev[++i]?.type === 'space') {\n        /* loop */\n    }\n    return prev.splice(i, prev.length);\n}\nfunction fixFlowSeqItems(fc) {\n    if (fc.start.type === 'flow-seq-start') {\n        for (const it of fc.items) {\n            if (it.sep &&\n                !it.value &&\n                !includesToken(it.start, 'explicit-key-ind') &&\n                !includesToken(it.sep, 'map-value-ind')) {\n                if (it.key)\n                    it.value = it.key;\n                delete it.key;\n                if (isFlowToken(it.value)) {\n                    if (it.value.end)\n                        Array.prototype.push.apply(it.value.end, it.sep);\n                    else\n                        it.value.end = it.sep;\n                }\n                else\n                    Array.prototype.push.apply(it.start, it.sep);\n                delete it.sep;\n            }\n        }\n    }\n}\n/**\n * A YAML concrete syntax tree (CST) parser\n *\n * ```ts\n * const src: string = ...\n * for (const token of new Parser().parse(src)) {\n *   // token: Token\n * }\n * ```\n *\n * To use the parser with a user-provided lexer:\n *\n * ```ts\n * function* parse(source: string, lexer: Lexer) {\n *   const parser = new Parser()\n *   for (const lexeme of lexer.lex(source))\n *     yield* parser.next(lexeme)\n *   yield* parser.end()\n * }\n *\n * const src: string = ...\n * const lexer = new Lexer()\n * for (const token of parse(src, lexer)) {\n *   // token: Token\n * }\n * ```\n */\nclass Parser {\n    /**\n     * @param onNewLine - If defined, called separately with the start position of\n     *   each new line (in `parse()`, including the start of input).\n     */\n    constructor(onNewLine) {\n        /** If true, space and sequence indicators count as indentation */\n        this.atNewLine = true;\n        /** If true, next token is a scalar value */\n        this.atScalar = false;\n        /** Current indentation level */\n        this.indent = 0;\n        /** Current offset since the start of parsing */\n        this.offset = 0;\n        /** On the same line with a block map key */\n        this.onKeyLine = false;\n        /** Top indicates the node that's currently being built */\n        this.stack = [];\n        /** The source of the current token, set in parse() */\n        this.source = '';\n        /** The type of the current token, set in parse() */\n        this.type = '';\n        // Must be defined after `next()`\n        this.lexer = new Lexer();\n        this.onNewLine = onNewLine;\n    }\n    /**\n     * Parse `source` as a YAML stream.\n     * If `incomplete`, a part of the last line may be left as a buffer for the next call.\n     *\n     * Errors are not thrown, but yielded as `{ type: 'error', message }` tokens.\n     *\n     * @returns A generator of tokens representing each directive, document, and other structure.\n     */\n    *parse(source, incomplete = false) {\n        if (this.onNewLine && this.offset === 0)\n            this.onNewLine(0);\n        for (const lexeme of this.lexer.lex(source, incomplete))\n            yield* this.next(lexeme);\n        if (!incomplete)\n            yield* this.end();\n    }\n    /**\n     * Advance the parser by the `source` of one lexical token.\n     */\n    *next(source) {\n        this.source = source;\n        if (this.atScalar) {\n            this.atScalar = false;\n            yield* this.step();\n            this.offset += source.length;\n            return;\n        }\n        const type = tokenType(source);\n        if (!type) {\n            const message = `Not a YAML token: ${source}`;\n            yield* this.pop({ type: 'error', offset: this.offset, message, source });\n            this.offset += source.length;\n        }\n        else if (type === 'scalar') {\n            this.atNewLine = false;\n            this.atScalar = true;\n            this.type = 'scalar';\n        }\n        else {\n            this.type = type;\n            yield* this.step();\n            switch (type) {\n                case 'newline':\n                    this.atNewLine = true;\n                    this.indent = 0;\n                    if (this.onNewLine)\n                        this.onNewLine(this.offset + source.length);\n                    break;\n                case 'space':\n                    if (this.atNewLine && source[0] === ' ')\n                        this.indent += source.length;\n                    break;\n                case 'explicit-key-ind':\n                case 'map-value-ind':\n                case 'seq-item-ind':\n                    if (this.atNewLine)\n                        this.indent += source.length;\n                    break;\n                case 'doc-mode':\n                case 'flow-error-end':\n                    return;\n                default:\n                    this.atNewLine = false;\n            }\n            this.offset += source.length;\n        }\n    }\n    /** Call at end of input to push out any remaining constructions */\n    *end() {\n        while (this.stack.length > 0)\n            yield* this.pop();\n    }\n    get sourceToken() {\n        const st = {\n            type: this.type,\n            offset: this.offset,\n            indent: this.indent,\n            source: this.source\n        };\n        return st;\n    }\n    *step() {\n        const top = this.peek(1);\n        if (this.type === 'doc-end' && (!top || top.type !== 'doc-end')) {\n            while (this.stack.length > 0)\n                yield* this.pop();\n            this.stack.push({\n                type: 'doc-end',\n                offset: this.offset,\n                source: this.source\n            });\n            return;\n        }\n        if (!top)\n            return yield* this.stream();\n        switch (top.type) {\n            case 'document':\n                return yield* this.document(top);\n            case 'alias':\n            case 'scalar':\n            case 'single-quoted-scalar':\n            case 'double-quoted-scalar':\n                return yield* this.scalar(top);\n            case 'block-scalar':\n                return yield* this.blockScalar(top);\n            case 'block-map':\n                return yield* this.blockMap(top);\n            case 'block-seq':\n                return yield* this.blockSequence(top);\n            case 'flow-collection':\n                return yield* this.flowCollection(top);\n            case 'doc-end':\n                return yield* this.documentEnd(top);\n        }\n        /* istanbul ignore next should not happen */\n        yield* this.pop();\n    }\n    peek(n) {\n        return this.stack[this.stack.length - n];\n    }\n    *pop(error) {\n        const token = error ?? this.stack.pop();\n        /* istanbul ignore if should not happen */\n        if (!token) {\n            const message = 'Tried to pop an empty stack';\n            yield { type: 'error', offset: this.offset, source: '', message };\n        }\n        else if (this.stack.length === 0) {\n            yield token;\n        }\n        else {\n            const top = this.peek(1);\n            if (token.type === 'block-scalar') {\n                // Block scalars use their parent rather than header indent\n                token.indent = 'indent' in top ? top.indent : 0;\n            }\n            else if (token.type === 'flow-collection' && top.type === 'document') {\n                // Ignore all indent for top-level flow collections\n                token.indent = 0;\n            }\n            if (token.type === 'flow-collection')\n                fixFlowSeqItems(token);\n            switch (top.type) {\n                case 'document':\n                    top.value = token;\n                    break;\n                case 'block-scalar':\n                    top.props.push(token); // error\n                    break;\n                case 'block-map': {\n                    const it = top.items[top.items.length - 1];\n                    if (it.value) {\n                        top.items.push({ start: [], key: token, sep: [] });\n                        this.onKeyLine = true;\n                        return;\n                    }\n                    else if (it.sep) {\n                        it.value = token;\n                    }\n                    else {\n                        Object.assign(it, { key: token, sep: [] });\n                        this.onKeyLine = !it.explicitKey;\n                        return;\n                    }\n                    break;\n                }\n                case 'block-seq': {\n                    const it = top.items[top.items.length - 1];\n                    if (it.value)\n                        top.items.push({ start: [], value: token });\n                    else\n                        it.value = token;\n                    break;\n                }\n                case 'flow-collection': {\n                    const it = top.items[top.items.length - 1];\n                    if (!it || it.value)\n                        top.items.push({ start: [], key: token, sep: [] });\n                    else if (it.sep)\n                        it.value = token;\n                    else\n                        Object.assign(it, { key: token, sep: [] });\n                    return;\n                }\n                /* istanbul ignore next should not happen */\n                default:\n                    yield* this.pop();\n                    yield* this.pop(token);\n            }\n            if ((top.type === 'document' ||\n                top.type === 'block-map' ||\n                top.type === 'block-seq') &&\n                (token.type === 'block-map' || token.type === 'block-seq')) {\n                const last = token.items[token.items.length - 1];\n                if (last &&\n                    !last.sep &&\n                    !last.value &&\n                    last.start.length > 0 &&\n                    findNonEmptyIndex(last.start) === -1 &&\n                    (token.indent === 0 ||\n                        last.start.every(st => st.type !== 'comment' || st.indent < token.indent))) {\n                    if (top.type === 'document')\n                        top.end = last.start;\n                    else\n                        top.items.push({ start: last.start });\n                    token.items.splice(-1, 1);\n                }\n            }\n        }\n    }\n    *stream() {\n        switch (this.type) {\n            case 'directive-line':\n                yield { type: 'directive', offset: this.offset, source: this.source };\n                return;\n            case 'byte-order-mark':\n            case 'space':\n            case 'comment':\n            case 'newline':\n                yield this.sourceToken;\n                return;\n            case 'doc-mode':\n            case 'doc-start': {\n                const doc = {\n                    type: 'document',\n                    offset: this.offset,\n                    start: []\n                };\n                if (this.type === 'doc-start')\n                    doc.start.push(this.sourceToken);\n                this.stack.push(doc);\n                return;\n            }\n        }\n        yield {\n            type: 'error',\n            offset: this.offset,\n            message: `Unexpected ${this.type} token in YAML stream`,\n            source: this.source\n        };\n    }\n    *document(doc) {\n        if (doc.value)\n            return yield* this.lineEnd(doc);\n        switch (this.type) {\n            case 'doc-start': {\n                if (findNonEmptyIndex(doc.start) !== -1) {\n                    yield* this.pop();\n                    yield* this.step();\n                }\n                else\n                    doc.start.push(this.sourceToken);\n                return;\n            }\n            case 'anchor':\n            case 'tag':\n            case 'space':\n            case 'comment':\n            case 'newline':\n                doc.start.push(this.sourceToken);\n                return;\n        }\n        const bv = this.startBlockValue(doc);\n        if (bv)\n            this.stack.push(bv);\n        else {\n            yield {\n                type: 'error',\n                offset: this.offset,\n                message: `Unexpected ${this.type} token in YAML document`,\n                source: this.source\n            };\n        }\n    }\n    *scalar(scalar) {\n        if (this.type === 'map-value-ind') {\n            const prev = getPrevProps(this.peek(2));\n            const start = getFirstKeyStartProps(prev);\n            let sep;\n            if (scalar.end) {\n                sep = scalar.end;\n                sep.push(this.sourceToken);\n                delete scalar.end;\n            }\n            else\n                sep = [this.sourceToken];\n            const map = {\n                type: 'block-map',\n                offset: scalar.offset,\n                indent: scalar.indent,\n                items: [{ start, key: scalar, sep }]\n            };\n            this.onKeyLine = true;\n            this.stack[this.stack.length - 1] = map;\n        }\n        else\n            yield* this.lineEnd(scalar);\n    }\n    *blockScalar(scalar) {\n        switch (this.type) {\n            case 'space':\n            case 'comment':\n            case 'newline':\n                scalar.props.push(this.sourceToken);\n                return;\n            case 'scalar':\n                scalar.source = this.source;\n                // block-scalar source includes trailing newline\n                this.atNewLine = true;\n                this.indent = 0;\n                if (this.onNewLine) {\n                    let nl = this.source.indexOf('\\n') + 1;\n                    while (nl !== 0) {\n                        this.onNewLine(this.offset + nl);\n                        nl = this.source.indexOf('\\n', nl) + 1;\n                    }\n                }\n                yield* this.pop();\n                break;\n            /* istanbul ignore next should not happen */\n            default:\n                yield* this.pop();\n                yield* this.step();\n        }\n    }\n    *blockMap(map) {\n        const it = map.items[map.items.length - 1];\n        // it.sep is true-ish if pair already has key or : separator\n        switch (this.type) {\n            case 'newline':\n                this.onKeyLine = false;\n                if (it.value) {\n                    const end = 'end' in it.value ? it.value.end : undefined;\n                    const last = Array.isArray(end) ? end[end.length - 1] : undefined;\n                    if (last?.type === 'comment')\n                        end?.push(this.sourceToken);\n                    else\n                        map.items.push({ start: [this.sourceToken] });\n                }\n                else if (it.sep) {\n                    it.sep.push(this.sourceToken);\n                }\n                else {\n                    it.start.push(this.sourceToken);\n                }\n                return;\n            case 'space':\n            case 'comment':\n                if (it.value) {\n                    map.items.push({ start: [this.sourceToken] });\n                }\n                else if (it.sep) {\n                    it.sep.push(this.sourceToken);\n                }\n                else {\n                    if (this.atIndentedComment(it.start, map.indent)) {\n                        const prev = map.items[map.items.length - 2];\n                        const end = prev?.value?.end;\n                        if (Array.isArray(end)) {\n                            Array.prototype.push.apply(end, it.start);\n                            end.push(this.sourceToken);\n                            map.items.pop();\n                            return;\n                        }\n                    }\n                    it.start.push(this.sourceToken);\n                }\n                return;\n        }\n        if (this.indent >= map.indent) {\n            const atMapIndent = !this.onKeyLine && this.indent === map.indent;\n            const atNextItem = atMapIndent &&\n                (it.sep || it.explicitKey) &&\n                this.type !== 'seq-item-ind';\n            // For empty nodes, assign newline-separated not indented empty tokens to following node\n            let start = [];\n            if (atNextItem && it.sep && !it.value) {\n                const nl = [];\n                for (let i = 0; i < it.sep.length; ++i) {\n                    const st = it.sep[i];\n                    switch (st.type) {\n                        case 'newline':\n                            nl.push(i);\n                            break;\n                        case 'space':\n                            break;\n                        case 'comment':\n                            if (st.indent > map.indent)\n                                nl.length = 0;\n                            break;\n                        default:\n                            nl.length = 0;\n                    }\n                }\n                if (nl.length >= 2)\n                    start = it.sep.splice(nl[1]);\n            }\n            switch (this.type) {\n                case 'anchor':\n                case 'tag':\n                    if (atNextItem || it.value) {\n                        start.push(this.sourceToken);\n                        map.items.push({ start });\n                        this.onKeyLine = true;\n                    }\n                    else if (it.sep) {\n                        it.sep.push(this.sourceToken);\n                    }\n                    else {\n                        it.start.push(this.sourceToken);\n                    }\n                    return;\n                case 'explicit-key-ind':\n                    if (!it.sep && !it.explicitKey) {\n                        it.start.push(this.sourceToken);\n                        it.explicitKey = true;\n                    }\n                    else if (atNextItem || it.value) {\n                        start.push(this.sourceToken);\n                        map.items.push({ start, explicitKey: true });\n                    }\n                    else {\n                        this.stack.push({\n                            type: 'block-map',\n                            offset: this.offset,\n                            indent: this.indent,\n                            items: [{ start: [this.sourceToken], explicitKey: true }]\n                        });\n                    }\n                    this.onKeyLine = true;\n                    return;\n                case 'map-value-ind':\n                    if (it.explicitKey) {\n                        if (!it.sep) {\n                            if (includesToken(it.start, 'newline')) {\n                                Object.assign(it, { key: null, sep: [this.sourceToken] });\n                            }\n                            else {\n                                const start = getFirstKeyStartProps(it.start);\n                                this.stack.push({\n                                    type: 'block-map',\n                                    offset: this.offset,\n                                    indent: this.indent,\n                                    items: [{ start, key: null, sep: [this.sourceToken] }]\n                                });\n                            }\n                        }\n                        else if (it.value) {\n                            map.items.push({ start: [], key: null, sep: [this.sourceToken] });\n                        }\n                        else if (includesToken(it.sep, 'map-value-ind')) {\n                            this.stack.push({\n                                type: 'block-map',\n                                offset: this.offset,\n                                indent: this.indent,\n                                items: [{ start, key: null, sep: [this.sourceToken] }]\n                            });\n                        }\n                        else if (isFlowToken(it.key) &&\n                            !includesToken(it.sep, 'newline')) {\n                            const start = getFirstKeyStartProps(it.start);\n                            const key = it.key;\n                            const sep = it.sep;\n                            sep.push(this.sourceToken);\n                            // @ts-expect-error type guard is wrong here\n                            delete it.key;\n                            // @ts-expect-error type guard is wrong here\n                            delete it.sep;\n                            this.stack.push({\n                                type: 'block-map',\n                                offset: this.offset,\n                                indent: this.indent,\n                                items: [{ start, key, sep }]\n                            });\n                        }\n                        else if (start.length > 0) {\n                            // Not actually at next item\n                            it.sep = it.sep.concat(start, this.sourceToken);\n                        }\n                        else {\n                            it.sep.push(this.sourceToken);\n                        }\n                    }\n                    else {\n                        if (!it.sep) {\n                            Object.assign(it, { key: null, sep: [this.sourceToken] });\n                        }\n                        else if (it.value || atNextItem) {\n                            map.items.push({ start, key: null, sep: [this.sourceToken] });\n                        }\n                        else if (includesToken(it.sep, 'map-value-ind')) {\n                            this.stack.push({\n                                type: 'block-map',\n                                offset: this.offset,\n                                indent: this.indent,\n                                items: [{ start: [], key: null, sep: [this.sourceToken] }]\n                            });\n                        }\n                        else {\n                            it.sep.push(this.sourceToken);\n                        }\n                    }\n                    this.onKeyLine = true;\n                    return;\n                case 'alias':\n                case 'scalar':\n                case 'single-quoted-scalar':\n                case 'double-quoted-scalar': {\n                    const fs = this.flowScalar(this.type);\n                    if (atNextItem || it.value) {\n                        map.items.push({ start, key: fs, sep: [] });\n                        this.onKeyLine = true;\n                    }\n                    else if (it.sep) {\n                        this.stack.push(fs);\n                    }\n                    else {\n                        Object.assign(it, { key: fs, sep: [] });\n                        this.onKeyLine = true;\n                    }\n                    return;\n                }\n                default: {\n                    const bv = this.startBlockValue(map);\n                    if (bv) {\n                        if (bv.type === 'block-seq') {\n                            if (!it.explicitKey &&\n                                it.sep &&\n                                !includesToken(it.sep, 'newline')) {\n                                yield* this.pop({\n                                    type: 'error',\n                                    offset: this.offset,\n                                    message: 'Unexpected block-seq-ind on same line with key',\n                                    source: this.source\n                                });\n                                return;\n                            }\n                        }\n                        else if (atMapIndent) {\n                            map.items.push({ start });\n                        }\n                        this.stack.push(bv);\n                        return;\n                    }\n                }\n            }\n        }\n        yield* this.pop();\n        yield* this.step();\n    }\n    *blockSequence(seq) {\n        const it = seq.items[seq.items.length - 1];\n        switch (this.type) {\n            case 'newline':\n                if (it.value) {\n                    const end = 'end' in it.value ? it.value.end : undefined;\n                    const last = Array.isArray(end) ? end[end.length - 1] : undefined;\n                    if (last?.type === 'comment')\n                        end?.push(this.sourceToken);\n                    else\n                        seq.items.push({ start: [this.sourceToken] });\n                }\n                else\n                    it.start.push(this.sourceToken);\n                return;\n            case 'space':\n            case 'comment':\n                if (it.value)\n                    seq.items.push({ start: [this.sourceToken] });\n                else {\n                    if (this.atIndentedComment(it.start, seq.indent)) {\n                        const prev = seq.items[seq.items.length - 2];\n                        const end = prev?.value?.end;\n                        if (Array.isArray(end)) {\n                            Array.prototype.push.apply(end, it.start);\n                            end.push(this.sourceToken);\n                            seq.items.pop();\n                            return;\n                        }\n                    }\n                    it.start.push(this.sourceToken);\n                }\n                return;\n            case 'anchor':\n            case 'tag':\n                if (it.value || this.indent <= seq.indent)\n                    break;\n                it.start.push(this.sourceToken);\n                return;\n            case 'seq-item-ind':\n                if (this.indent !== seq.indent)\n                    break;\n                if (it.value || includesToken(it.start, 'seq-item-ind'))\n                    seq.items.push({ start: [this.sourceToken] });\n                else\n                    it.start.push(this.sourceToken);\n                return;\n        }\n        if (this.indent > seq.indent) {\n            const bv = this.startBlockValue(seq);\n            if (bv) {\n                this.stack.push(bv);\n                return;\n            }\n        }\n        yield* this.pop();\n        yield* this.step();\n    }\n    *flowCollection(fc) {\n        const it = fc.items[fc.items.length - 1];\n        if (this.type === 'flow-error-end') {\n            let top;\n            do {\n                yield* this.pop();\n                top = this.peek(1);\n            } while (top && top.type === 'flow-collection');\n        }\n        else if (fc.end.length === 0) {\n            switch (this.type) {\n                case 'comma':\n                case 'explicit-key-ind':\n                    if (!it || it.sep)\n                        fc.items.push({ start: [this.sourceToken] });\n                    else\n                        it.start.push(this.sourceToken);\n                    return;\n                case 'map-value-ind':\n                    if (!it || it.value)\n                        fc.items.push({ start: [], key: null, sep: [this.sourceToken] });\n                    else if (it.sep)\n                        it.sep.push(this.sourceToken);\n                    else\n                        Object.assign(it, { key: null, sep: [this.sourceToken] });\n                    return;\n                case 'space':\n                case 'comment':\n                case 'newline':\n                case 'anchor':\n                case 'tag':\n                    if (!it || it.value)\n                        fc.items.push({ start: [this.sourceToken] });\n                    else if (it.sep)\n                        it.sep.push(this.sourceToken);\n                    else\n                        it.start.push(this.sourceToken);\n                    return;\n                case 'alias':\n                case 'scalar':\n                case 'single-quoted-scalar':\n                case 'double-quoted-scalar': {\n                    const fs = this.flowScalar(this.type);\n                    if (!it || it.value)\n                        fc.items.push({ start: [], key: fs, sep: [] });\n                    else if (it.sep)\n                        this.stack.push(fs);\n                    else\n                        Object.assign(it, { key: fs, sep: [] });\n                    return;\n                }\n                case 'flow-map-end':\n                case 'flow-seq-end':\n                    fc.end.push(this.sourceToken);\n                    return;\n            }\n            const bv = this.startBlockValue(fc);\n            /* istanbul ignore else should not happen */\n            if (bv)\n                this.stack.push(bv);\n            else {\n                yield* this.pop();\n                yield* this.step();\n            }\n        }\n        else {\n            const parent = this.peek(2);\n            if (parent.type === 'block-map' &&\n                ((this.type === 'map-value-ind' && parent.indent === fc.indent) ||\n                    (this.type === 'newline' &&\n                        !parent.items[parent.items.length - 1].sep))) {\n                yield* this.pop();\n                yield* this.step();\n            }\n            else if (this.type === 'map-value-ind' &&\n                parent.type !== 'flow-collection') {\n                const prev = getPrevProps(parent);\n                const start = getFirstKeyStartProps(prev);\n                fixFlowSeqItems(fc);\n                const sep = fc.end.splice(1, fc.end.length);\n                sep.push(this.sourceToken);\n                const map = {\n                    type: 'block-map',\n                    offset: fc.offset,\n                    indent: fc.indent,\n                    items: [{ start, key: fc, sep }]\n                };\n                this.onKeyLine = true;\n                this.stack[this.stack.length - 1] = map;\n            }\n            else {\n                yield* this.lineEnd(fc);\n            }\n        }\n    }\n    flowScalar(type) {\n        if (this.onNewLine) {\n            let nl = this.source.indexOf('\\n') + 1;\n            while (nl !== 0) {\n                this.onNewLine(this.offset + nl);\n                nl = this.source.indexOf('\\n', nl) + 1;\n            }\n        }\n        return {\n            type,\n            offset: this.offset,\n            indent: this.indent,\n            source: this.source\n        };\n    }\n    startBlockValue(parent) {\n        switch (this.type) {\n            case 'alias':\n            case 'scalar':\n            case 'single-quoted-scalar':\n            case 'double-quoted-scalar':\n                return this.flowScalar(this.type);\n            case 'block-scalar-header':\n                return {\n                    type: 'block-scalar',\n                    offset: this.offset,\n                    indent: this.indent,\n                    props: [this.sourceToken],\n                    source: ''\n                };\n            case 'flow-map-start':\n            case 'flow-seq-start':\n                return {\n                    type: 'flow-collection',\n                    offset: this.offset,\n                    indent: this.indent,\n                    start: this.sourceToken,\n                    items: [],\n                    end: []\n                };\n            case 'seq-item-ind':\n                return {\n                    type: 'block-seq',\n                    offset: this.offset,\n                    indent: this.indent,\n                    items: [{ start: [this.sourceToken] }]\n                };\n            case 'explicit-key-ind': {\n                this.onKeyLine = true;\n                const prev = getPrevProps(parent);\n                const start = getFirstKeyStartProps(prev);\n                start.push(this.sourceToken);\n                return {\n                    type: 'block-map',\n                    offset: this.offset,\n                    indent: this.indent,\n                    items: [{ start, explicitKey: true }]\n                };\n            }\n            case 'map-value-ind': {\n                this.onKeyLine = true;\n                const prev = getPrevProps(parent);\n                const start = getFirstKeyStartProps(prev);\n                return {\n                    type: 'block-map',\n                    offset: this.offset,\n                    indent: this.indent,\n                    items: [{ start, key: null, sep: [this.sourceToken] }]\n                };\n            }\n        }\n        return null;\n    }\n    atIndentedComment(start, indent) {\n        if (this.type !== 'comment')\n            return false;\n        if (this.indent <= indent)\n            return false;\n        return start.every(st => st.type === 'newline' || st.type === 'space');\n    }\n    *documentEnd(docEnd) {\n        if (this.type !== 'doc-mode') {\n            if (docEnd.end)\n                docEnd.end.push(this.sourceToken);\n            else\n                docEnd.end = [this.sourceToken];\n            if (this.type === 'newline')\n                yield* this.pop();\n        }\n    }\n    *lineEnd(token) {\n        switch (this.type) {\n            case 'comma':\n            case 'doc-start':\n            case 'doc-end':\n            case 'flow-seq-end':\n            case 'flow-map-end':\n            case 'map-value-ind':\n                yield* this.pop();\n                yield* this.step();\n                break;\n            case 'newline':\n                this.onKeyLine = false;\n            // fallthrough\n            case 'space':\n            case 'comment':\n            default:\n                // all other values are errors\n                if (token.end)\n                    token.end.push(this.sourceToken);\n                else\n                    token.end = [this.sourceToken];\n                if (this.type === 'newline')\n                    yield* this.pop();\n        }\n    }\n}\n\nexport { Parser };\n", "import { Composer } from './compose/composer.js';\nimport { Document } from './doc/Document.js';\nimport { prettifyError, YAMLParseError } from './errors.js';\nimport { warn } from './log.js';\nimport { isDocument } from './nodes/identity.js';\nimport { LineCounter } from './parse/line-counter.js';\nimport { Parser } from './parse/parser.js';\n\nfunction parseOptions(options) {\n    const prettyErrors = options.prettyErrors !== false;\n    const lineCounter = options.lineCounter || (prettyErrors && new LineCounter()) || null;\n    return { lineCounter, prettyErrors };\n}\n/**\n * Parse the input as a stream of YAML documents.\n *\n * Documents should be separated from each other by `...` or `---` marker lines.\n *\n * @returns If an empty `docs` array is returned, it will be of type\n *   EmptyStream and contain additional stream information. In\n *   TypeScript, you should use `'empty' in docs` as a type guard for it.\n */\nfunction parseAllDocuments(source, options = {}) {\n    const { lineCounter, prettyErrors } = parseOptions(options);\n    const parser = new Parser(lineCounter?.addNewLine);\n    const composer = new Composer(options);\n    const docs = Array.from(composer.compose(parser.parse(source)));\n    if (prettyErrors && lineCounter)\n        for (const doc of docs) {\n            doc.errors.forEach(prettifyError(source, lineCounter));\n            doc.warnings.forEach(prettifyError(source, lineCounter));\n        }\n    if (docs.length > 0)\n        return docs;\n    return Object.assign([], { empty: true }, composer.streamInfo());\n}\n/** Parse an input string into a single YAML.Document */\nfunction parseDocument(source, options = {}) {\n    const { lineCounter, prettyErrors } = parseOptions(options);\n    const parser = new Parser(lineCounter?.addNewLine);\n    const composer = new Composer(options);\n    // `doc` is always set by compose.end(true) at the very latest\n    let doc = null;\n    for (const _doc of composer.compose(parser.parse(source), true, source.length)) {\n        if (!doc)\n            doc = _doc;\n        else if (doc.options.logLevel !== 'silent') {\n            doc.errors.push(new YAMLParseError(_doc.range.slice(0, 2), 'MULTIPLE_DOCS', 'Source contains multiple documents; please use YAML.parseAllDocuments()'));\n            break;\n        }\n    }\n    if (prettyErrors && lineCounter) {\n        doc.errors.forEach(prettifyError(source, lineCounter));\n        doc.warnings.forEach(prettifyError(source, lineCounter));\n    }\n    return doc;\n}\nfunction parse(src, reviver, options) {\n    let _reviver = undefined;\n    if (typeof reviver === 'function') {\n        _reviver = reviver;\n    }\n    else if (options === undefined && reviver && typeof reviver === 'object') {\n        options = reviver;\n    }\n    const doc = parseDocument(src, options);\n    if (!doc)\n        return null;\n    doc.warnings.forEach(warning => warn(doc.options.logLevel, warning));\n    if (doc.errors.length > 0) {\n        if (doc.options.logLevel !== 'silent')\n            throw doc.errors[0];\n        else\n            doc.errors = [];\n    }\n    return doc.toJS(Object.assign({ reviver: _reviver }, options));\n}\nfunction stringify(value, replacer, options) {\n    let _replacer = null;\n    if (typeof replacer === 'function' || Array.isArray(replacer)) {\n        _replacer = replacer;\n    }\n    else if (options === undefined && replacer) {\n        options = replacer;\n    }\n    if (typeof options === 'string')\n        options = options.length;\n    if (typeof options === 'number') {\n        const indent = Math.round(options);\n        options = indent < 1 ? undefined : indent > 8 ? { indent: 8 } : { indent };\n    }\n    if (value === undefined) {\n        const { keepUndefined } = options ?? replacer ?? {};\n        if (!keepUndefined)\n            return undefined;\n    }\n    if (isDocument(value) && !_replacer)\n        return value.toString(options);\n    return new Document(value, _replacer, options).toString(options);\n}\n\nexport { parse, parseAllDocuments, parseDocument, stringify };\n", "// `export * as default from ...` fails on Webpack v4\n// https://github.com/eemeli/yaml/issues/228\nimport * as YAML from './dist/index.js'\nexport default YAML\nexport * from './dist/index.js'\n"], "mappings": ";;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,mBAAAA;AAAA,EAAA;AAAA;AAAA;;;ACAA,IAAM,QAAQ,OAAO,IAAI,YAAY;AACrC,IAAM,MAAM,OAAO,IAAI,eAAe;AACtC,IAAM,MAAM,OAAO,IAAI,UAAU;AACjC,IAAM,OAAO,OAAO,IAAI,WAAW;AACnC,IAAM,SAAS,OAAO,IAAI,aAAa;AACvC,IAAM,MAAM,OAAO,IAAI,UAAU;AACjC,IAAM,YAAY,OAAO,IAAI,gBAAgB;AAC7C,IAAM,UAAU,CAAC,SAAS,CAAC,CAAC,QAAQ,OAAO,SAAS,YAAY,KAAK,SAAS,MAAM;AACpF,IAAM,aAAa,CAAC,SAAS,CAAC,CAAC,QAAQ,OAAO,SAAS,YAAY,KAAK,SAAS,MAAM;AACvF,IAAM,QAAQ,CAAC,SAAS,CAAC,CAAC,QAAQ,OAAO,SAAS,YAAY,KAAK,SAAS,MAAM;AAClF,IAAM,SAAS,CAAC,SAAS,CAAC,CAAC,QAAQ,OAAO,SAAS,YAAY,KAAK,SAAS,MAAM;AACnF,IAAM,WAAW,CAAC,SAAS,CAAC,CAAC,QAAQ,OAAO,SAAS,YAAY,KAAK,SAAS,MAAM;AACrF,IAAM,QAAQ,CAAC,SAAS,CAAC,CAAC,QAAQ,OAAO,SAAS,YAAY,KAAK,SAAS,MAAM;AAClF,SAAS,aAAa,MAAM;AACxB,MAAI,QAAQ,OAAO,SAAS;AACxB,YAAQ,KAAK,SAAS,GAAG;AAAA,MACrB,KAAK;AAAA,MACL,KAAK;AACD,eAAO;AAAA,IACf;AACJ,SAAO;AACX;AACA,SAAS,OAAO,MAAM;AAClB,MAAI,QAAQ,OAAO,SAAS;AACxB,YAAQ,KAAK,SAAS,GAAG;AAAA,MACrB,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AACD,eAAO;AAAA,IACf;AACJ,SAAO;AACX;AACA,IAAM,YAAY,CAAC,UAAU,SAAS,IAAI,KAAK,aAAa,IAAI,MAAM,CAAC,CAAC,KAAK;;;AC/B7E,IAAM,QAAQ,OAAO,aAAa;AAClC,IAAM,OAAO,OAAO,eAAe;AACnC,IAAM,SAAS,OAAO,aAAa;AA+BnC,SAAS,MAAM,MAAM,SAAS;AAC1B,QAAM,WAAW,YAAY,OAAO;AACpC,MAAI,WAAW,IAAI,GAAG;AAClB,UAAM,KAAK,OAAO,MAAM,KAAK,UAAU,UAAU,OAAO,OAAO,CAAC,IAAI,CAAC,CAAC;AACtE,QAAI,OAAO;AACP,WAAK,WAAW;AAAA,EACxB;AAEI,WAAO,MAAM,MAAM,UAAU,OAAO,OAAO,CAAC,CAAC,CAAC;AACtD;AAKA,MAAM,QAAQ;AAEd,MAAM,OAAO;AAEb,MAAM,SAAS;AACf,SAAS,OAAO,KAAK,MAAM,SAAS,MAAM;AACtC,QAAM,OAAO,YAAY,KAAK,MAAM,SAAS,IAAI;AACjD,MAAI,OAAO,IAAI,KAAK,OAAO,IAAI,GAAG;AAC9B,gBAAY,KAAK,MAAM,IAAI;AAC3B,WAAO,OAAO,KAAK,MAAM,SAAS,IAAI;AAAA,EAC1C;AACA,MAAI,OAAO,SAAS,UAAU;AAC1B,QAAI,aAAa,IAAI,GAAG;AACpB,aAAO,OAAO,OAAO,KAAK,OAAO,IAAI,CAAC;AACtC,eAAS,IAAI,GAAG,IAAI,KAAK,MAAM,QAAQ,EAAE,GAAG;AACxC,cAAM,KAAK,OAAO,GAAG,KAAK,MAAM,CAAC,GAAG,SAAS,IAAI;AACjD,YAAI,OAAO,OAAO;AACd,cAAI,KAAK;AAAA,iBACJ,OAAO;AACZ,iBAAO;AAAA,iBACF,OAAO,QAAQ;AACpB,eAAK,MAAM,OAAO,GAAG,CAAC;AACtB,eAAK;AAAA,QACT;AAAA,MACJ;AAAA,IACJ,WACS,OAAO,IAAI,GAAG;AACnB,aAAO,OAAO,OAAO,KAAK,OAAO,IAAI,CAAC;AACtC,YAAM,KAAK,OAAO,OAAO,KAAK,KAAK,SAAS,IAAI;AAChD,UAAI,OAAO;AACP,eAAO;AAAA,eACF,OAAO;AACZ,aAAK,MAAM;AACf,YAAM,KAAK,OAAO,SAAS,KAAK,OAAO,SAAS,IAAI;AACpD,UAAI,OAAO;AACP,eAAO;AAAA,eACF,OAAO;AACZ,aAAK,QAAQ;AAAA,IACrB;AAAA,EACJ;AACA,SAAO;AACX;AAgCA,eAAe,WAAW,MAAM,SAAS;AACrC,QAAM,WAAW,YAAY,OAAO;AACpC,MAAI,WAAW,IAAI,GAAG;AAClB,UAAM,KAAK,MAAM,YAAY,MAAM,KAAK,UAAU,UAAU,OAAO,OAAO,CAAC,IAAI,CAAC,CAAC;AACjF,QAAI,OAAO;AACP,WAAK,WAAW;AAAA,EACxB;AAEI,UAAM,YAAY,MAAM,MAAM,UAAU,OAAO,OAAO,CAAC,CAAC,CAAC;AACjE;AAKA,WAAW,QAAQ;AAEnB,WAAW,OAAO;AAElB,WAAW,SAAS;AACpB,eAAe,YAAY,KAAK,MAAM,SAAS,MAAM;AACjD,QAAM,OAAO,MAAM,YAAY,KAAK,MAAM,SAAS,IAAI;AACvD,MAAI,OAAO,IAAI,KAAK,OAAO,IAAI,GAAG;AAC9B,gBAAY,KAAK,MAAM,IAAI;AAC3B,WAAO,YAAY,KAAK,MAAM,SAAS,IAAI;AAAA,EAC/C;AACA,MAAI,OAAO,SAAS,UAAU;AAC1B,QAAI,aAAa,IAAI,GAAG;AACpB,aAAO,OAAO,OAAO,KAAK,OAAO,IAAI,CAAC;AACtC,eAAS,IAAI,GAAG,IAAI,KAAK,MAAM,QAAQ,EAAE,GAAG;AACxC,cAAM,KAAK,MAAM,YAAY,GAAG,KAAK,MAAM,CAAC,GAAG,SAAS,IAAI;AAC5D,YAAI,OAAO,OAAO;AACd,cAAI,KAAK;AAAA,iBACJ,OAAO;AACZ,iBAAO;AAAA,iBACF,OAAO,QAAQ;AACpB,eAAK,MAAM,OAAO,GAAG,CAAC;AACtB,eAAK;AAAA,QACT;AAAA,MACJ;AAAA,IACJ,WACS,OAAO,IAAI,GAAG;AACnB,aAAO,OAAO,OAAO,KAAK,OAAO,IAAI,CAAC;AACtC,YAAM,KAAK,MAAM,YAAY,OAAO,KAAK,KAAK,SAAS,IAAI;AAC3D,UAAI,OAAO;AACP,eAAO;AAAA,eACF,OAAO;AACZ,aAAK,MAAM;AACf,YAAM,KAAK,MAAM,YAAY,SAAS,KAAK,OAAO,SAAS,IAAI;AAC/D,UAAI,OAAO;AACP,eAAO;AAAA,eACF,OAAO;AACZ,aAAK,QAAQ;AAAA,IACrB;AAAA,EACJ;AACA,SAAO;AACX;AACA,SAAS,YAAY,SAAS;AAC1B,MAAI,OAAO,YAAY,aAClB,QAAQ,cAAc,QAAQ,QAAQ,QAAQ,QAAQ;AACvD,WAAO,OAAO,OAAO;AAAA,MACjB,OAAO,QAAQ;AAAA,MACf,KAAK,QAAQ;AAAA,MACb,QAAQ,QAAQ;AAAA,MAChB,KAAK,QAAQ;AAAA,IACjB,GAAG,QAAQ,SAAS;AAAA,MAChB,KAAK,QAAQ;AAAA,MACb,QAAQ,QAAQ;AAAA,MAChB,KAAK,QAAQ;AAAA,IACjB,GAAG,QAAQ,cAAc;AAAA,MACrB,KAAK,QAAQ;AAAA,MACb,KAAK,QAAQ;AAAA,IACjB,GAAG,OAAO;AAAA,EACd;AACA,SAAO;AACX;AACA,SAAS,YAAY,KAAK,MAAM,SAAS,MAAM;AArM/C;AAsMI,MAAI,OAAO,YAAY;AACnB,WAAO,QAAQ,KAAK,MAAM,IAAI;AAClC,MAAI,MAAM,IAAI;AACV,YAAO,aAAQ,QAAR,iCAAc,KAAK,MAAM;AACpC,MAAI,MAAM,IAAI;AACV,YAAO,aAAQ,QAAR,iCAAc,KAAK,MAAM;AACpC,MAAI,OAAO,IAAI;AACX,YAAO,aAAQ,SAAR,iCAAe,KAAK,MAAM;AACrC,MAAI,SAAS,IAAI;AACb,YAAO,aAAQ,WAAR,iCAAiB,KAAK,MAAM;AACvC,MAAI,QAAQ,IAAI;AACZ,YAAO,aAAQ,UAAR,iCAAgB,KAAK,MAAM;AACtC,SAAO;AACX;AACA,SAAS,YAAY,KAAK,MAAM,MAAM;AAClC,QAAM,SAAS,KAAK,KAAK,SAAS,CAAC;AACnC,MAAI,aAAa,MAAM,GAAG;AACtB,WAAO,MAAM,GAAG,IAAI;AAAA,EACxB,WACS,OAAO,MAAM,GAAG;AACrB,QAAI,QAAQ;AACR,aAAO,MAAM;AAAA;AAEb,aAAO,QAAQ;AAAA,EACvB,WACS,WAAW,MAAM,GAAG;AACzB,WAAO,WAAW;AAAA,EACtB,OACK;AACD,UAAM,KAAK,QAAQ,MAAM,IAAI,UAAU;AACvC,UAAM,IAAI,MAAM,4BAA4B,EAAE,SAAS;AAAA,EAC3D;AACJ;;;ACnOA,IAAM,cAAc;AAAA,EAChB,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AACT;AACA,IAAM,gBAAgB,CAAC,OAAO,GAAG,QAAQ,cAAc,QAAM,YAAY,EAAE,CAAC;AAC5E,IAAM,aAAN,MAAM,YAAW;AAAA,EACb,YAAY,MAAM,MAAM;AAKpB,SAAK,WAAW;AAEhB,SAAK,SAAS;AACd,SAAK,OAAO,OAAO,OAAO,CAAC,GAAG,YAAW,aAAa,IAAI;AAC1D,SAAK,OAAO,OAAO,OAAO,CAAC,GAAG,YAAW,aAAa,IAAI;AAAA,EAC9D;AAAA,EACA,QAAQ;AACJ,UAAM,OAAO,IAAI,YAAW,KAAK,MAAM,KAAK,IAAI;AAChD,SAAK,WAAW,KAAK;AACrB,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,aAAa;AACT,UAAM,MAAM,IAAI,YAAW,KAAK,MAAM,KAAK,IAAI;AAC/C,YAAQ,KAAK,KAAK,SAAS;AAAA,MACvB,KAAK;AACD,aAAK,iBAAiB;AACtB;AAAA,MACJ,KAAK;AACD,aAAK,iBAAiB;AACtB,aAAK,OAAO;AAAA,UACR,UAAU,YAAW,YAAY;AAAA,UACjC,SAAS;AAAA,QACb;AACA,aAAK,OAAO,OAAO,OAAO,CAAC,GAAG,YAAW,WAAW;AACpD;AAAA,IACR;AACA,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,MAAM,SAAS;AACf,QAAI,KAAK,gBAAgB;AACrB,WAAK,OAAO,EAAE,UAAU,YAAW,YAAY,UAAU,SAAS,MAAM;AACxE,WAAK,OAAO,OAAO,OAAO,CAAC,GAAG,YAAW,WAAW;AACpD,WAAK,iBAAiB;AAAA,IAC1B;AACA,UAAM,QAAQ,KAAK,KAAK,EAAE,MAAM,QAAQ;AACxC,UAAM,OAAO,MAAM,MAAM;AACzB,YAAQ,MAAM;AAAA,MACV,KAAK,QAAQ;AACT,YAAI,MAAM,WAAW,GAAG;AACpB,kBAAQ,GAAG,iDAAiD;AAC5D,cAAI,MAAM,SAAS;AACf,mBAAO;AAAA,QACf;AACA,cAAM,CAAC,QAAQ,MAAM,IAAI;AACzB,aAAK,KAAK,MAAM,IAAI;AACpB,eAAO;AAAA,MACX;AAAA,MACA,KAAK,SAAS;AACV,aAAK,KAAK,WAAW;AACrB,YAAI,MAAM,WAAW,GAAG;AACpB,kBAAQ,GAAG,iDAAiD;AAC5D,iBAAO;AAAA,QACX;AACA,cAAM,CAAC,OAAO,IAAI;AAClB,YAAI,YAAY,SAAS,YAAY,OAAO;AACxC,eAAK,KAAK,UAAU;AACpB,iBAAO;AAAA,QACX,OACK;AACD,gBAAM,UAAU,aAAa,KAAK,OAAO;AACzC,kBAAQ,GAAG,4BAA4B,OAAO,IAAI,OAAO;AACzD,iBAAO;AAAA,QACX;AAAA,MACJ;AAAA,MACA;AACI,gBAAQ,GAAG,qBAAqB,IAAI,IAAI,IAAI;AAC5C,eAAO;AAAA,IACf;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,QAAQ,QAAQ,SAAS;AACrB,QAAI,WAAW;AACX,aAAO;AACX,QAAI,OAAO,CAAC,MAAM,KAAK;AACnB,cAAQ,oBAAoB,MAAM,EAAE;AACpC,aAAO;AAAA,IACX;AACA,QAAI,OAAO,CAAC,MAAM,KAAK;AACnB,YAAM,WAAW,OAAO,MAAM,GAAG,EAAE;AACnC,UAAI,aAAa,OAAO,aAAa,MAAM;AACvC,gBAAQ,qCAAqC,MAAM,cAAc;AACjE,eAAO;AAAA,MACX;AACA,UAAI,OAAO,OAAO,SAAS,CAAC,MAAM;AAC9B,gBAAQ,iCAAiC;AAC7C,aAAO;AAAA,IACX;AACA,UAAM,CAAC,EAAE,QAAQ,MAAM,IAAI,OAAO,MAAM,iBAAiB;AACzD,QAAI,CAAC;AACD,cAAQ,OAAO,MAAM,oBAAoB;AAC7C,UAAM,SAAS,KAAK,KAAK,MAAM;AAC/B,QAAI,QAAQ;AACR,UAAI;AACA,eAAO,SAAS,mBAAmB,MAAM;AAAA,MAC7C,SACO,OAAO;AACV,gBAAQ,OAAO,KAAK,CAAC;AACrB,eAAO;AAAA,MACX;AAAA,IACJ;AACA,QAAI,WAAW;AACX,aAAO;AACX,YAAQ,0BAA0B,MAAM,EAAE;AAC1C,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,UAAU,KAAK;AACX,eAAW,CAAC,QAAQ,MAAM,KAAK,OAAO,QAAQ,KAAK,IAAI,GAAG;AACtD,UAAI,IAAI,WAAW,MAAM;AACrB,eAAO,SAAS,cAAc,IAAI,UAAU,OAAO,MAAM,CAAC;AAAA,IAClE;AACA,WAAO,IAAI,CAAC,MAAM,MAAM,MAAM,KAAK,GAAG;AAAA,EAC1C;AAAA,EACA,SAAS,KAAK;AACV,UAAM,QAAQ,KAAK,KAAK,WAClB,CAAC,SAAS,KAAK,KAAK,WAAW,KAAK,EAAE,IACtC,CAAC;AACP,UAAM,aAAa,OAAO,QAAQ,KAAK,IAAI;AAC3C,QAAI;AACJ,QAAI,OAAO,WAAW,SAAS,KAAK,OAAO,IAAI,QAAQ,GAAG;AACtD,YAAM,OAAO,CAAC;AACd,YAAM,IAAI,UAAU,CAAC,MAAM,SAAS;AAChC,YAAI,OAAO,IAAI,KAAK,KAAK;AACrB,eAAK,KAAK,GAAG,IAAI;AAAA,MACzB,CAAC;AACD,iBAAW,OAAO,KAAK,IAAI;AAAA,IAC/B;AAEI,iBAAW,CAAC;AAChB,eAAW,CAAC,QAAQ,MAAM,KAAK,YAAY;AACvC,UAAI,WAAW,QAAQ,WAAW;AAC9B;AACJ,UAAI,CAAC,OAAO,SAAS,KAAK,QAAM,GAAG,WAAW,MAAM,CAAC;AACjD,cAAM,KAAK,QAAQ,MAAM,IAAI,MAAM,EAAE;AAAA,IAC7C;AACA,WAAO,MAAM,KAAK,IAAI;AAAA,EAC1B;AACJ;AACA,WAAW,cAAc,EAAE,UAAU,OAAO,SAAS,MAAM;AAC3D,WAAW,cAAc,EAAE,MAAM,qBAAqB;;;ACrKtD,SAAS,cAAc,QAAQ;AAC3B,MAAI,sBAAsB,KAAK,MAAM,GAAG;AACpC,UAAM,KAAK,KAAK,UAAU,MAAM;AAChC,UAAM,MAAM,6DAA6D,EAAE;AAC3E,UAAM,IAAI,MAAM,GAAG;AAAA,EACvB;AACA,SAAO;AACX;AACA,SAAS,YAAY,MAAM;AACvB,QAAM,UAAU,oBAAI,IAAI;AACxB,QAAM,MAAM;AAAA,IACR,MAAM,MAAM,MAAM;AACd,UAAI,KAAK;AACL,gBAAQ,IAAI,KAAK,MAAM;AAAA,IAC/B;AAAA,EACJ,CAAC;AACD,SAAO;AACX;AAEA,SAAS,cAAc,QAAQ,SAAS;AACpC,WAAS,IAAI,GAAG,MAAM,EAAE,GAAG;AACvB,UAAM,OAAO,GAAG,MAAM,GAAG,CAAC;AAC1B,QAAI,CAAC,QAAQ,IAAI,IAAI;AACjB,aAAO;AAAA,EACf;AACJ;AACA,SAAS,kBAAkB,KAAK,QAAQ;AACpC,QAAM,eAAe,CAAC;AACtB,QAAM,gBAAgB,oBAAI,IAAI;AAC9B,MAAI,cAAc;AAClB,SAAO;AAAA,IACH,UAAU,CAAC,WAAW;AAClB,mBAAa,KAAK,MAAM;AACxB,sBAAgB,cAAc,YAAY,GAAG;AAC7C,YAAM,SAAS,cAAc,QAAQ,WAAW;AAChD,kBAAY,IAAI,MAAM;AACtB,aAAO;AAAA,IACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAMA,YAAY,MAAM;AACd,iBAAW,UAAU,cAAc;AAC/B,cAAM,MAAM,cAAc,IAAI,MAAM;AACpC,YAAI,OAAO,QAAQ,YACf,IAAI,WACH,SAAS,IAAI,IAAI,KAAK,aAAa,IAAI,IAAI,IAAI;AAChD,cAAI,KAAK,SAAS,IAAI;AAAA,QAC1B,OACK;AACD,gBAAM,QAAQ,IAAI,MAAM,4DAA4D;AACpF,gBAAM,SAAS;AACf,gBAAM;AAAA,QACV;AAAA,MACJ;AAAA,IACJ;AAAA,IACA;AAAA,EACJ;AACJ;;;AC7DA,SAAS,aAAa,SAAS,KAAK,KAAK,KAAK;AAC1C,MAAI,OAAO,OAAO,QAAQ,UAAU;AAChC,QAAI,MAAM,QAAQ,GAAG,GAAG;AACpB,eAAS,IAAI,GAAG,MAAM,IAAI,QAAQ,IAAI,KAAK,EAAE,GAAG;AAC5C,cAAM,KAAK,IAAI,CAAC;AAChB,cAAM,KAAK,aAAa,SAAS,KAAK,OAAO,CAAC,GAAG,EAAE;AAEnD,YAAI,OAAO;AACP,iBAAO,IAAI,CAAC;AAAA,iBACP,OAAO;AACZ,cAAI,CAAC,IAAI;AAAA,MACjB;AAAA,IACJ,WACS,eAAe,KAAK;AACzB,iBAAW,KAAK,MAAM,KAAK,IAAI,KAAK,CAAC,GAAG;AACpC,cAAM,KAAK,IAAI,IAAI,CAAC;AACpB,cAAM,KAAK,aAAa,SAAS,KAAK,GAAG,EAAE;AAC3C,YAAI,OAAO;AACP,cAAI,OAAO,CAAC;AAAA,iBACP,OAAO;AACZ,cAAI,IAAI,GAAG,EAAE;AAAA,MACrB;AAAA,IACJ,WACS,eAAe,KAAK;AACzB,iBAAW,MAAM,MAAM,KAAK,GAAG,GAAG;AAC9B,cAAM,KAAK,aAAa,SAAS,KAAK,IAAI,EAAE;AAC5C,YAAI,OAAO;AACP,cAAI,OAAO,EAAE;AAAA,iBACR,OAAO,IAAI;AAChB,cAAI,OAAO,EAAE;AACb,cAAI,IAAI,EAAE;AAAA,QACd;AAAA,MACJ;AAAA,IACJ,OACK;AACD,iBAAW,CAAC,GAAG,EAAE,KAAK,OAAO,QAAQ,GAAG,GAAG;AACvC,cAAM,KAAK,aAAa,SAAS,KAAK,GAAG,EAAE;AAC3C,YAAI,OAAO;AACP,iBAAO,IAAI,CAAC;AAAA,iBACP,OAAO;AACZ,cAAI,CAAC,IAAI;AAAA,MACjB;AAAA,IACJ;AAAA,EACJ;AACA,SAAO,QAAQ,KAAK,KAAK,KAAK,GAAG;AACrC;;;ACxCA,SAAS,KAAK,OAAO,KAAK,KAAK;AAE3B,MAAI,MAAM,QAAQ,KAAK;AACnB,WAAO,MAAM,IAAI,CAAC,GAAG,MAAM,KAAK,GAAG,OAAO,CAAC,GAAG,GAAG,CAAC;AACtD,MAAI,SAAS,OAAO,MAAM,WAAW,YAAY;AAE7C,QAAI,CAAC,OAAO,CAAC,UAAU,KAAK;AACxB,aAAO,MAAM,OAAO,KAAK,GAAG;AAChC,UAAM,OAAO,EAAE,YAAY,GAAG,OAAO,GAAG,KAAK,OAAU;AACvD,QAAI,QAAQ,IAAI,OAAO,IAAI;AAC3B,QAAI,WAAW,CAAAC,SAAO;AAClB,WAAK,MAAMA;AACX,aAAO,IAAI;AAAA,IACf;AACA,UAAM,MAAM,MAAM,OAAO,KAAK,GAAG;AACjC,QAAI,IAAI;AACJ,UAAI,SAAS,GAAG;AACpB,WAAO;AAAA,EACX;AACA,MAAI,OAAO,UAAU,YAAY,EAAC,2BAAK;AACnC,WAAO,OAAO,KAAK;AACvB,SAAO;AACX;;;AC9BA,IAAM,WAAN,MAAe;AAAA,EACX,YAAY,MAAM;AACd,WAAO,eAAe,MAAM,WAAW,EAAE,OAAO,KAAK,CAAC;AAAA,EAC1D;AAAA;AAAA,EAEA,QAAQ;AACJ,UAAM,OAAO,OAAO,OAAO,OAAO,eAAe,IAAI,GAAG,OAAO,0BAA0B,IAAI,CAAC;AAC9F,QAAI,KAAK;AACL,WAAK,QAAQ,KAAK,MAAM,MAAM;AAClC,WAAO;AAAA,EACX;AAAA;AAAA,EAEA,KAAK,KAAK,EAAE,UAAU,eAAe,UAAU,QAAQ,IAAI,CAAC,GAAG;AAC3D,QAAI,CAAC,WAAW,GAAG;AACf,YAAM,IAAI,UAAU,iCAAiC;AACzD,UAAM,MAAM;AAAA,MACR,SAAS,oBAAI,IAAI;AAAA,MACjB;AAAA,MACA,MAAM;AAAA,MACN,UAAU,aAAa;AAAA,MACvB,cAAc;AAAA,MACd,eAAe,OAAO,kBAAkB,WAAW,gBAAgB;AAAA,IACvE;AACA,UAAM,MAAM,KAAK,MAAM,IAAI,GAAG;AAC9B,QAAI,OAAO,aAAa;AACpB,iBAAW,EAAE,OAAO,KAAAC,KAAI,KAAK,IAAI,QAAQ,OAAO;AAC5C,iBAASA,MAAK,KAAK;AAC3B,WAAO,OAAO,YAAY,aACpB,aAAa,SAAS,EAAE,IAAI,IAAI,GAAG,IAAI,GAAG,IAC1C;AAAA,EACV;AACJ;;;AC7BA,IAAM,QAAN,cAAoB,SAAS;AAAA,EACzB,YAAY,QAAQ;AAChB,UAAM,KAAK;AACX,SAAK,SAAS;AACd,WAAO,eAAe,MAAM,OAAO;AAAA,MAC/B,MAAM;AACF,cAAM,IAAI,MAAM,8BAA8B;AAAA,MAClD;AAAA,IACJ,CAAC;AAAA,EACL;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,QAAQ,KAAK,KAAK;AACd,QAAI;AACJ,QAAI,2BAAK,mBAAmB;AACxB,cAAQ,IAAI;AAAA,IAChB,OACK;AACD,cAAQ,CAAC;AACT,YAAM,KAAK;AAAA,QACP,MAAM,CAAC,MAAM,SAAS;AAClB,cAAI,QAAQ,IAAI,KAAK,UAAU,IAAI;AAC/B,kBAAM,KAAK,IAAI;AAAA,QACvB;AAAA,MACJ,CAAC;AACD,UAAI;AACA,YAAI,oBAAoB;AAAA,IAChC;AACA,QAAI,QAAQ;AACZ,eAAW,QAAQ,OAAO;AACtB,UAAI,SAAS;AACT;AACJ,UAAI,KAAK,WAAW,KAAK;AACrB,gBAAQ;AAAA,IAChB;AACA,WAAO;AAAA,EACX;AAAA,EACA,OAAO,MAAM,KAAK;AACd,QAAI,CAAC;AACD,aAAO,EAAE,QAAQ,KAAK,OAAO;AACjC,UAAM,EAAE,SAAS,KAAK,cAAc,IAAI;AACxC,UAAM,SAAS,KAAK,QAAQ,KAAK,GAAG;AACpC,QAAI,CAAC,QAAQ;AACT,YAAM,MAAM,+DAA+D,KAAK,MAAM;AACtF,YAAM,IAAI,eAAe,GAAG;AAAA,IAChC;AACA,QAAI,OAAO,QAAQ,IAAI,MAAM;AAC7B,QAAI,CAAC,MAAM;AAEP,WAAK,QAAQ,MAAM,GAAG;AACtB,aAAO,QAAQ,IAAI,MAAM;AAAA,IAC7B;AAEA,QAAI,CAAC,QAAQ,KAAK,QAAQ,QAAW;AACjC,YAAM,MAAM;AACZ,YAAM,IAAI,eAAe,GAAG;AAAA,IAChC;AACA,QAAI,iBAAiB,GAAG;AACpB,WAAK,SAAS;AACd,UAAI,KAAK,eAAe;AACpB,aAAK,aAAa,cAAc,KAAK,QAAQ,OAAO;AACxD,UAAI,KAAK,QAAQ,KAAK,aAAa,eAAe;AAC9C,cAAM,MAAM;AACZ,cAAM,IAAI,eAAe,GAAG;AAAA,MAChC;AAAA,IACJ;AACA,WAAO,KAAK;AAAA,EAChB;AAAA,EACA,SAAS,KAAK,YAAY,cAAc;AACpC,UAAM,MAAM,IAAI,KAAK,MAAM;AAC3B,QAAI,KAAK;AACL,oBAAc,KAAK,MAAM;AACzB,UAAI,IAAI,QAAQ,oBAAoB,CAAC,IAAI,QAAQ,IAAI,KAAK,MAAM,GAAG;AAC/D,cAAM,MAAM,+DAA+D,KAAK,MAAM;AACtF,cAAM,IAAI,MAAM,GAAG;AAAA,MACvB;AACA,UAAI,IAAI;AACJ,eAAO,GAAG,GAAG;AAAA,IACrB;AACA,WAAO;AAAA,EACX;AACJ;AACA,SAAS,cAAc,KAAK,MAAM,SAAS;AACvC,MAAI,QAAQ,IAAI,GAAG;AACf,UAAM,SAAS,KAAK,QAAQ,GAAG;AAC/B,UAAM,SAAS,WAAW,UAAU,QAAQ,IAAI,MAAM;AACtD,WAAO,SAAS,OAAO,QAAQ,OAAO,aAAa;AAAA,EACvD,WACS,aAAa,IAAI,GAAG;AACzB,QAAI,QAAQ;AACZ,eAAW,QAAQ,KAAK,OAAO;AAC3B,YAAM,IAAI,cAAc,KAAK,MAAM,OAAO;AAC1C,UAAI,IAAI;AACJ,gBAAQ;AAAA,IAChB;AACA,WAAO;AAAA,EACX,WACS,OAAO,IAAI,GAAG;AACnB,UAAM,KAAK,cAAc,KAAK,KAAK,KAAK,OAAO;AAC/C,UAAM,KAAK,cAAc,KAAK,KAAK,OAAO,OAAO;AACjD,WAAO,KAAK,IAAI,IAAI,EAAE;AAAA,EAC1B;AACA,SAAO;AACX;;;AC3GA,IAAM,gBAAgB,CAAC,UAAU,CAAC,SAAU,OAAO,UAAU,cAAc,OAAO,UAAU;AAC5F,IAAM,SAAN,cAAqB,SAAS;AAAA,EAC1B,YAAY,OAAO;AACf,UAAM,MAAM;AACZ,SAAK,QAAQ;AAAA,EACjB;AAAA,EACA,OAAO,KAAK,KAAK;AACb,YAAO,2BAAK,QAAO,KAAK,QAAQ,KAAK,KAAK,OAAO,KAAK,GAAG;AAAA,EAC7D;AAAA,EACA,WAAW;AACP,WAAO,OAAO,KAAK,KAAK;AAAA,EAC5B;AACJ;AACA,OAAO,eAAe;AACtB,OAAO,gBAAgB;AACvB,OAAO,QAAQ;AACf,OAAO,eAAe;AACtB,OAAO,eAAe;;;ACjBtB,IAAM,mBAAmB;AACzB,SAAS,cAAc,OAAO,SAAS,MAAM;AACzC,MAAI,SAAS;AACT,UAAM,QAAQ,KAAK,OAAO,OAAK,EAAE,QAAQ,OAAO;AAChD,UAAM,SAAS,MAAM,KAAK,OAAK,CAAC,EAAE,MAAM,KAAK,MAAM,CAAC;AACpD,QAAI,CAAC;AACD,YAAM,IAAI,MAAM,OAAO,OAAO,YAAY;AAC9C,WAAO;AAAA,EACX;AACA,SAAO,KAAK,KAAK,OAAE;AAbvB;AAa0B,oBAAE,aAAF,2BAAa,WAAU,CAAC,EAAE;AAAA,GAAM;AAC1D;AACA,SAAS,WAAW,OAAO,SAAS,KAAK;AAfzC;AAgBI,MAAI,WAAW,KAAK;AAChB,YAAQ,MAAM;AAClB,MAAI,OAAO,KAAK;AACZ,WAAO;AACX,MAAI,OAAO,KAAK,GAAG;AACf,UAAMC,QAAM,eAAI,OAAO,GAAG,GAAE,eAAhB,4BAA6B,IAAI,QAAQ,MAAM;AAC3D,IAAAA,KAAI,MAAM,KAAK,KAAK;AACpB,WAAOA;AAAA,EACX;AACA,MAAI,iBAAiB,UACjB,iBAAiB,UACjB,iBAAiB,WAChB,OAAO,WAAW,eAAe,iBAAiB,QACrD;AAEE,YAAQ,MAAM,QAAQ;AAAA,EAC1B;AACA,QAAM,EAAE,uBAAuB,UAAU,UAAU,QAAAC,SAAQ,cAAc,IAAI;AAG7E,MAAI,MAAM;AACV,MAAI,yBAAyB,SAAS,OAAO,UAAU,UAAU;AAC7D,UAAM,cAAc,IAAI,KAAK;AAC7B,QAAI,KAAK;AACL,UAAI,WAAW,IAAI,SAAS,SAAS,KAAK;AAC1C,aAAO,IAAI,MAAM,IAAI,MAAM;AAAA,IAC/B,OACK;AACD,YAAM,EAAE,QAAQ,MAAM,MAAM,KAAK;AACjC,oBAAc,IAAI,OAAO,GAAG;AAAA,IAChC;AAAA,EACJ;AACA,MAAI,mCAAS,WAAW;AACpB,cAAU,mBAAmB,QAAQ,MAAM,CAAC;AAChD,MAAI,SAAS,cAAc,OAAO,SAASA,QAAO,IAAI;AACtD,MAAI,CAAC,QAAQ;AACT,QAAI,SAAS,OAAO,MAAM,WAAW,YAAY;AAE7C,cAAQ,MAAM,OAAO;AAAA,IACzB;AACA,QAAI,CAAC,SAAS,OAAO,UAAU,UAAU;AACrC,YAAMC,QAAO,IAAI,OAAO,KAAK;AAC7B,UAAI;AACA,YAAI,OAAOA;AACf,aAAOA;AAAA,IACX;AACA,aACI,iBAAiB,MACXD,QAAO,GAAG,IACV,OAAO,YAAY,OAAO,KAAK,IAC3BA,QAAO,GAAG,IACVA,QAAO,GAAG;AAAA,EAC5B;AACA,MAAI,UAAU;AACV,aAAS,MAAM;AACf,WAAO,IAAI;AAAA,EACf;AACA,QAAM,QAAO,iCAAQ,cACf,OAAO,WAAW,IAAI,QAAQ,OAAO,GAAG,IACxC,SAAO,sCAAQ,cAAR,mBAAmB,UAAS,aAC/B,OAAO,UAAU,KAAK,IAAI,QAAQ,OAAO,GAAG,IAC5C,IAAI,OAAO,KAAK;AAC1B,MAAI;AACA,SAAK,MAAM;AAAA,WACN,CAAC,OAAO;AACb,SAAK,MAAM,OAAO;AACtB,MAAI;AACA,QAAI,OAAO;AACf,SAAO;AACX;;;ACjFA,SAAS,mBAAmBE,SAAQ,MAAM,OAAO;AAC7C,MAAI,IAAI;AACR,WAAS,IAAI,KAAK,SAAS,GAAG,KAAK,GAAG,EAAE,GAAG;AACvC,UAAM,IAAI,KAAK,CAAC;AAChB,QAAI,OAAO,MAAM,YAAY,OAAO,UAAU,CAAC,KAAK,KAAK,GAAG;AACxD,YAAM,IAAI,CAAC;AACX,QAAE,CAAC,IAAI;AACP,UAAI;AAAA,IACR,OACK;AACD,UAAI,oBAAI,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;AAAA,IACxB;AAAA,EACJ;AACA,SAAO,WAAW,GAAG,QAAW;AAAA,IAC5B,uBAAuB;AAAA,IACvB,eAAe;AAAA,IACf,UAAU,MAAM;AACZ,YAAM,IAAI,MAAM,8CAA8C;AAAA,IAClE;AAAA,IACA,QAAAA;AAAA,IACA,eAAe,oBAAI,IAAI;AAAA,EAC3B,CAAC;AACL;AAGA,IAAM,cAAc,CAAC,SAAS,QAAQ,QACjC,OAAO,SAAS,YAAY,CAAC,CAAC,KAAK,OAAO,QAAQ,EAAE,EAAE,KAAK,EAAE;AAClE,IAAM,aAAN,cAAyB,SAAS;AAAA,EAC9B,YAAY,MAAMA,SAAQ;AACtB,UAAM,IAAI;AACV,WAAO,eAAe,MAAM,UAAU;AAAA,MAClC,OAAOA;AAAA,MACP,cAAc;AAAA,MACd,YAAY;AAAA,MACZ,UAAU;AAAA,IACd,CAAC;AAAA,EACL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,MAAMA,SAAQ;AACV,UAAM,OAAO,OAAO,OAAO,OAAO,eAAe,IAAI,GAAG,OAAO,0BAA0B,IAAI,CAAC;AAC9F,QAAIA;AACA,WAAK,SAASA;AAClB,SAAK,QAAQ,KAAK,MAAM,IAAI,QAAM,OAAO,EAAE,KAAK,OAAO,EAAE,IAAI,GAAG,MAAMA,OAAM,IAAI,EAAE;AAClF,QAAI,KAAK;AACL,WAAK,QAAQ,KAAK,MAAM,MAAM;AAClC,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,MAAM,MAAM,OAAO;AACf,QAAI,YAAY,IAAI;AAChB,WAAK,IAAI,KAAK;AAAA,SACb;AACD,YAAM,CAAC,KAAK,GAAG,IAAI,IAAI;AACvB,YAAM,OAAO,KAAK,IAAI,KAAK,IAAI;AAC/B,UAAI,aAAa,IAAI;AACjB,aAAK,MAAM,MAAM,KAAK;AAAA,eACjB,SAAS,UAAa,KAAK;AAChC,aAAK,IAAI,KAAK,mBAAmB,KAAK,QAAQ,MAAM,KAAK,CAAC;AAAA;AAE1D,cAAM,IAAI,MAAM,+BAA+B,GAAG,qBAAqB,IAAI,EAAE;AAAA,IACrF;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,SAAS,MAAM;AACX,UAAM,CAAC,KAAK,GAAG,IAAI,IAAI;AACvB,QAAI,KAAK,WAAW;AAChB,aAAO,KAAK,OAAO,GAAG;AAC1B,UAAM,OAAO,KAAK,IAAI,KAAK,IAAI;AAC/B,QAAI,aAAa,IAAI;AACjB,aAAO,KAAK,SAAS,IAAI;AAAA;AAEzB,YAAM,IAAI,MAAM,+BAA+B,GAAG,qBAAqB,IAAI,EAAE;AAAA,EACrF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,MAAM,MAAM,YAAY;AACpB,UAAM,CAAC,KAAK,GAAG,IAAI,IAAI;AACvB,UAAM,OAAO,KAAK,IAAI,KAAK,IAAI;AAC/B,QAAI,KAAK,WAAW;AAChB,aAAO,CAAC,cAAc,SAAS,IAAI,IAAI,KAAK,QAAQ;AAAA;AAEpD,aAAO,aAAa,IAAI,IAAI,KAAK,MAAM,MAAM,UAAU,IAAI;AAAA,EACnE;AAAA,EACA,iBAAiB,aAAa;AAC1B,WAAO,KAAK,MAAM,MAAM,UAAQ;AAC5B,UAAI,CAAC,OAAO,IAAI;AACZ,eAAO;AACX,YAAM,IAAI,KAAK;AACf,aAAQ,KAAK,QACR,eACG,SAAS,CAAC,KACV,EAAE,SAAS,QACX,CAAC,EAAE,iBACH,CAAC,EAAE,WACH,CAAC,EAAE;AAAA,IACf,CAAC;AAAA,EACL;AAAA;AAAA;AAAA;AAAA,EAIA,MAAM,MAAM;AACR,UAAM,CAAC,KAAK,GAAG,IAAI,IAAI;AACvB,QAAI,KAAK,WAAW;AAChB,aAAO,KAAK,IAAI,GAAG;AACvB,UAAM,OAAO,KAAK,IAAI,KAAK,IAAI;AAC/B,WAAO,aAAa,IAAI,IAAI,KAAK,MAAM,IAAI,IAAI;AAAA,EACnD;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,MAAM,OAAO;AACf,UAAM,CAAC,KAAK,GAAG,IAAI,IAAI;AACvB,QAAI,KAAK,WAAW,GAAG;AACnB,WAAK,IAAI,KAAK,KAAK;AAAA,IACvB,OACK;AACD,YAAM,OAAO,KAAK,IAAI,KAAK,IAAI;AAC/B,UAAI,aAAa,IAAI;AACjB,aAAK,MAAM,MAAM,KAAK;AAAA,eACjB,SAAS,UAAa,KAAK;AAChC,aAAK,IAAI,KAAK,mBAAmB,KAAK,QAAQ,MAAM,KAAK,CAAC;AAAA;AAE1D,cAAM,IAAI,MAAM,+BAA+B,GAAG,qBAAqB,IAAI,EAAE;AAAA,IACrF;AAAA,EACJ;AACJ;;;ACzIA,IAAM,mBAAmB,CAAC,QAAQ,IAAI,QAAQ,mBAAmB,GAAG;AACpE,SAAS,cAAc,SAAS,QAAQ;AACpC,MAAI,QAAQ,KAAK,OAAO;AACpB,WAAO,QAAQ,UAAU,CAAC;AAC9B,SAAO,SAAS,QAAQ,QAAQ,cAAc,MAAM,IAAI;AAC5D;AACA,IAAM,cAAc,CAAC,KAAK,QAAQ,YAAY,IAAI,SAAS,IAAI,IACzD,cAAc,SAAS,MAAM,IAC7B,QAAQ,SAAS,IAAI,IACjB,OAAO,cAAc,SAAS,MAAM,KACnC,IAAI,SAAS,GAAG,IAAI,KAAK,OAAO;;;ACjB3C,IAAM,YAAY;AAClB,IAAM,aAAa;AACnB,IAAM,cAAc;AAMpB,SAAS,cAAc,MAAM,QAAQ,OAAO,QAAQ,EAAE,eAAe,YAAY,IAAI,kBAAkB,IAAI,QAAQ,WAAW,IAAI,CAAC,GAAG;AAClI,MAAI,CAAC,aAAa,YAAY;AAC1B,WAAO;AACX,MAAI,YAAY;AACZ,sBAAkB;AACtB,QAAM,UAAU,KAAK,IAAI,IAAI,iBAAiB,IAAI,YAAY,OAAO,MAAM;AAC3E,MAAI,KAAK,UAAU;AACf,WAAO;AACX,QAAM,QAAQ,CAAC;AACf,QAAM,eAAe,CAAC;AACtB,MAAI,MAAM,YAAY,OAAO;AAC7B,MAAI,OAAO,kBAAkB,UAAU;AACnC,QAAI,gBAAgB,YAAY,KAAK,IAAI,GAAG,eAAe;AACvD,YAAM,KAAK,CAAC;AAAA;AAEZ,YAAM,YAAY;AAAA,EAC1B;AACA,MAAI,QAAQ;AACZ,MAAI,OAAO;AACX,MAAI,WAAW;AACf,MAAI,IAAI;AACR,MAAI,WAAW;AACf,MAAI,SAAS;AACb,MAAI,SAAS,YAAY;AACrB,QAAI,yBAAyB,MAAM,GAAG,OAAO,MAAM;AACnD,QAAI,MAAM;AACN,YAAM,IAAI;AAAA,EAClB;AACA,WAAS,IAAK,KAAK,KAAM,KAAK,CAAE,KAAK;AACjC,QAAI,SAAS,eAAe,OAAO,MAAM;AACrC,iBAAW;AACX,cAAQ,KAAK,IAAI,CAAC,GAAG;AAAA,QACjB,KAAK;AACD,eAAK;AACL;AAAA,QACJ,KAAK;AACD,eAAK;AACL;AAAA,QACJ,KAAK;AACD,eAAK;AACL;AAAA,QACJ;AACI,eAAK;AAAA,MACb;AACA,eAAS;AAAA,IACb;AACA,QAAI,OAAO,MAAM;AACb,UAAI,SAAS;AACT,YAAI,yBAAyB,MAAM,GAAG,OAAO,MAAM;AACvD,YAAM,IAAI,OAAO,SAAS;AAC1B,cAAQ;AAAA,IACZ,OACK;AACD,UAAI,OAAO,OACP,QACA,SAAS,OACT,SAAS,QACT,SAAS,KAAM;AAEf,cAAM,OAAO,KAAK,IAAI,CAAC;AACvB,YAAI,QAAQ,SAAS,OAAO,SAAS,QAAQ,SAAS;AAClD,kBAAQ;AAAA,MAChB;AACA,UAAI,KAAK,KAAK;AACV,YAAI,OAAO;AACP,gBAAM,KAAK,KAAK;AAChB,gBAAM,QAAQ;AACd,kBAAQ;AAAA,QACZ,WACS,SAAS,aAAa;AAE3B,iBAAO,SAAS,OAAO,SAAS,KAAM;AAClC,mBAAO;AACP,iBAAK,KAAM,KAAK,CAAE;AAClB,uBAAW;AAAA,UACf;AAEA,gBAAM,IAAI,IAAI,SAAS,IAAI,IAAI,IAAI,WAAW;AAE9C,cAAI,aAAa,CAAC;AACd,mBAAO;AACX,gBAAM,KAAK,CAAC;AACZ,uBAAa,CAAC,IAAI;AAClB,gBAAM,IAAI;AACV,kBAAQ;AAAA,QACZ,OACK;AACD,qBAAW;AAAA,QACf;AAAA,MACJ;AAAA,IACJ;AACA,WAAO;AAAA,EACX;AACA,MAAI,YAAY;AACZ,eAAW;AACf,MAAI,MAAM,WAAW;AACjB,WAAO;AACX,MAAI;AACA,WAAO;AACX,MAAI,MAAM,KAAK,MAAM,GAAG,MAAM,CAAC,CAAC;AAChC,WAASC,KAAI,GAAGA,KAAI,MAAM,QAAQ,EAAEA,IAAG;AACnC,UAAM,OAAO,MAAMA,EAAC;AACpB,UAAMC,OAAM,MAAMD,KAAI,CAAC,KAAK,KAAK;AACjC,QAAI,SAAS;AACT,YAAM;AAAA,EAAK,MAAM,GAAG,KAAK,MAAM,GAAGC,IAAG,CAAC;AAAA,SACrC;AACD,UAAI,SAAS,eAAe,aAAa,IAAI;AACzC,eAAO,GAAG,KAAK,IAAI,CAAC;AACxB,aAAO;AAAA,EAAK,MAAM,GAAG,KAAK,MAAM,OAAO,GAAGA,IAAG,CAAC;AAAA,IAClD;AAAA,EACJ;AACA,SAAO;AACX;AAKA,SAAS,yBAAyB,MAAM,GAAG,QAAQ;AAC/C,MAAI,MAAM;AACV,MAAI,QAAQ,IAAI;AAChB,MAAI,KAAK,KAAK,KAAK;AACnB,SAAO,OAAO,OAAO,OAAO,KAAM;AAC9B,QAAI,IAAI,QAAQ,QAAQ;AACpB,WAAK,KAAK,EAAE,CAAC;AAAA,IACjB,OACK;AACD,SAAG;AACC,aAAK,KAAK,EAAE,CAAC;AAAA,MACjB,SAAS,MAAM,OAAO;AACtB,YAAM;AACN,cAAQ,IAAI;AACZ,WAAK,KAAK,KAAK;AAAA,IACnB;AAAA,EACJ;AACA,SAAO;AACX;;;AC5IA,IAAM,iBAAiB,CAAC,KAAKC,cAAa;AAAA,EACtC,eAAeA,WAAU,IAAI,OAAO,SAAS,IAAI;AAAA,EACjD,WAAW,IAAI,QAAQ;AAAA,EACvB,iBAAiB,IAAI,QAAQ;AACjC;AAGA,IAAM,yBAAyB,CAAC,QAAQ,mBAAmB,KAAK,GAAG;AACnE,SAAS,oBAAoB,KAAK,WAAW,cAAc;AACvD,MAAI,CAAC,aAAa,YAAY;AAC1B,WAAO;AACX,QAAM,QAAQ,YAAY;AAC1B,QAAM,SAAS,IAAI;AACnB,MAAI,UAAU;AACV,WAAO;AACX,WAAS,IAAI,GAAG,QAAQ,GAAG,IAAI,QAAQ,EAAE,GAAG;AACxC,QAAI,IAAI,CAAC,MAAM,MAAM;AACjB,UAAI,IAAI,QAAQ;AACZ,eAAO;AACX,cAAQ,IAAI;AACZ,UAAI,SAAS,SAAS;AAClB,eAAO;AAAA,IACf;AAAA,EACJ;AACA,SAAO;AACX;AACA,SAAS,mBAAmB,OAAO,KAAK;AACpC,QAAM,OAAO,KAAK,UAAU,KAAK;AACjC,MAAI,IAAI,QAAQ;AACZ,WAAO;AACX,QAAM,EAAE,YAAY,IAAI;AACxB,QAAM,qBAAqB,IAAI,QAAQ;AACvC,QAAM,SAAS,IAAI,WAAW,uBAAuB,KAAK,IAAI,OAAO;AACrE,MAAI,MAAM;AACV,MAAI,QAAQ;AACZ,WAAS,IAAI,GAAG,KAAK,KAAK,CAAC,GAAG,IAAI,KAAK,KAAK,EAAE,CAAC,GAAG;AAC9C,QAAI,OAAO,OAAO,KAAK,IAAI,CAAC,MAAM,QAAQ,KAAK,IAAI,CAAC,MAAM,KAAK;AAE3D,aAAO,KAAK,MAAM,OAAO,CAAC,IAAI;AAC9B,WAAK;AACL,cAAQ;AACR,WAAK;AAAA,IACT;AACA,QAAI,OAAO;AACP,cAAQ,KAAK,IAAI,CAAC,GAAG;AAAA,QACjB,KAAK;AACD;AACI,mBAAO,KAAK,MAAM,OAAO,CAAC;AAC1B,kBAAM,OAAO,KAAK,OAAO,IAAI,GAAG,CAAC;AACjC,oBAAQ,MAAM;AAAA,cACV,KAAK;AACD,uBAAO;AACP;AAAA,cACJ,KAAK;AACD,uBAAO;AACP;AAAA,cACJ,KAAK;AACD,uBAAO;AACP;AAAA,cACJ,KAAK;AACD,uBAAO;AACP;AAAA,cACJ,KAAK;AACD,uBAAO;AACP;AAAA,cACJ,KAAK;AACD,uBAAO;AACP;AAAA,cACJ,KAAK;AACD,uBAAO;AACP;AAAA,cACJ,KAAK;AACD,uBAAO;AACP;AAAA,cACJ;AACI,oBAAI,KAAK,OAAO,GAAG,CAAC,MAAM;AACtB,yBAAO,QAAQ,KAAK,OAAO,CAAC;AAAA;AAE5B,yBAAO,KAAK,OAAO,GAAG,CAAC;AAAA,YACnC;AACA,iBAAK;AACL,oBAAQ,IAAI;AAAA,UAChB;AACA;AAAA,QACJ,KAAK;AACD,cAAI,eACA,KAAK,IAAI,CAAC,MAAM,OAChB,KAAK,SAAS,oBAAoB;AAClC,iBAAK;AAAA,UACT,OACK;AAED,mBAAO,KAAK,MAAM,OAAO,CAAC,IAAI;AAC9B,mBAAO,KAAK,IAAI,CAAC,MAAM,QACnB,KAAK,IAAI,CAAC,MAAM,OAChB,KAAK,IAAI,CAAC,MAAM,KAAK;AACrB,qBAAO;AACP,mBAAK;AAAA,YACT;AACA,mBAAO;AAEP,gBAAI,KAAK,IAAI,CAAC,MAAM;AAChB,qBAAO;AACX,iBAAK;AACL,oBAAQ,IAAI;AAAA,UAChB;AACA;AAAA,QACJ;AACI,eAAK;AAAA,MACb;AAAA,EACR;AACA,QAAM,QAAQ,MAAM,KAAK,MAAM,KAAK,IAAI;AACxC,SAAO,cACD,MACA,cAAc,KAAK,QAAQ,aAAa,eAAe,KAAK,KAAK,CAAC;AAC5E;AACA,SAAS,mBAAmB,OAAO,KAAK;AACpC,MAAI,IAAI,QAAQ,gBAAgB,SAC3B,IAAI,eAAe,MAAM,SAAS,IAAI,KACvC,kBAAkB,KAAK,KAAK;AAE5B,WAAO,mBAAmB,OAAO,GAAG;AACxC,QAAM,SAAS,IAAI,WAAW,uBAAuB,KAAK,IAAI,OAAO;AACrE,QAAM,MAAM,MAAM,MAAM,QAAQ,MAAM,IAAI,EAAE,QAAQ,QAAQ;AAAA,EAAO,MAAM,EAAE,IAAI;AAC/E,SAAO,IAAI,cACL,MACA,cAAc,KAAK,QAAQ,WAAW,eAAe,KAAK,KAAK,CAAC;AAC1E;AACA,SAAS,aAAa,OAAO,KAAK;AAC9B,QAAM,EAAE,YAAY,IAAI,IAAI;AAC5B,MAAI;AACJ,MAAI,gBAAgB;AAChB,SAAK;AAAA,OACJ;AACD,UAAM,YAAY,MAAM,SAAS,GAAG;AACpC,UAAM,YAAY,MAAM,SAAS,GAAG;AACpC,QAAI,aAAa,CAAC;AACd,WAAK;AAAA,aACA,aAAa,CAAC;AACnB,WAAK;AAAA;AAEL,WAAK,cAAc,qBAAqB;AAAA,EAChD;AACA,SAAO,GAAG,OAAO,GAAG;AACxB;AAGA,IAAI;AACJ,IAAI;AACA,qBAAmB,IAAI,OAAO,0BAA0B,GAAG;AAC/D,QACM;AACF,qBAAmB;AACvB;AACA,SAAS,YAAY,EAAE,SAAS,MAAM,MAAM,GAAG,KAAK,WAAW,aAAa;AACxE,QAAM,EAAE,YAAY,eAAe,UAAU,IAAI,IAAI;AAGrD,MAAI,CAAC,cAAc,YAAY,KAAK,KAAK,KAAK,QAAQ,KAAK,KAAK,GAAG;AAC/D,WAAO,aAAa,OAAO,GAAG;AAAA,EAClC;AACA,QAAM,SAAS,IAAI,WACd,IAAI,oBAAoB,uBAAuB,KAAK,IAAI,OAAO;AACpE,QAAM,UAAU,eAAe,YACzB,OACA,eAAe,YAAY,SAAS,OAAO,eACvC,QACA,SAAS,OAAO,gBACZ,OACA,CAAC,oBAAoB,OAAO,WAAW,OAAO,MAAM;AAClE,MAAI,CAAC;AACD,WAAO,UAAU,QAAQ;AAE7B,MAAI;AACJ,MAAI;AACJ,OAAK,WAAW,MAAM,QAAQ,WAAW,GAAG,EAAE,UAAU;AACpD,UAAM,KAAK,MAAM,WAAW,CAAC;AAC7B,QAAI,OAAO,QAAQ,OAAO,OAAQ,OAAO;AACrC;AAAA,EACR;AACA,MAAI,MAAM,MAAM,UAAU,QAAQ;AAClC,QAAM,WAAW,IAAI,QAAQ,IAAI;AACjC,MAAI,aAAa,IAAI;AACjB,YAAQ;AAAA,EACZ,WACS,UAAU,OAAO,aAAa,IAAI,SAAS,GAAG;AACnD,YAAQ;AACR,QAAI;AACA,kBAAY;AAAA,EACpB,OACK;AACD,YAAQ;AAAA,EACZ;AACA,MAAI,KAAK;AACL,YAAQ,MAAM,MAAM,GAAG,CAAC,IAAI,MAAM;AAClC,QAAI,IAAI,IAAI,SAAS,CAAC,MAAM;AACxB,YAAM,IAAI,MAAM,GAAG,EAAE;AACzB,UAAM,IAAI,QAAQ,kBAAkB,KAAK,MAAM,EAAE;AAAA,EACrD;AAEA,MAAI,iBAAiB;AACrB,MAAI;AACJ,MAAI,aAAa;AACjB,OAAK,WAAW,GAAG,WAAW,MAAM,QAAQ,EAAE,UAAU;AACpD,UAAM,KAAK,MAAM,QAAQ;AACzB,QAAI,OAAO;AACP,uBAAiB;AAAA,aACZ,OAAO;AACZ,mBAAa;AAAA;AAEb;AAAA,EACR;AACA,MAAI,QAAQ,MAAM,UAAU,GAAG,aAAa,WAAW,aAAa,IAAI,QAAQ;AAChF,MAAI,OAAO;AACP,YAAQ,MAAM,UAAU,MAAM,MAAM;AACpC,YAAQ,MAAM,QAAQ,QAAQ,KAAK,MAAM,EAAE;AAAA,EAC/C;AACA,QAAM,aAAa,SAAS,MAAM;AAElC,MAAI,UAAU,iBAAiB,aAAa,MAAM;AAClD,MAAI,SAAS;AACT,cAAU,MAAM,cAAc,QAAQ,QAAQ,cAAc,GAAG,CAAC;AAChE,QAAI;AACA,gBAAU;AAAA,EAClB;AACA,MAAI,CAAC,SAAS;AACV,UAAM,cAAc,MACf,QAAQ,QAAQ,MAAM,EACtB,QAAQ,kDAAkD,MAAM,EAEhE,QAAQ,QAAQ,KAAK,MAAM,EAAE;AAClC,QAAI,kBAAkB;AACtB,UAAM,cAAc,eAAe,KAAK,IAAI;AAC5C,QAAI,eAAe,YAAY,SAAS,OAAO,cAAc;AACzD,kBAAY,aAAa,MAAM;AAC3B,0BAAkB;AAAA,MACtB;AAAA,IACJ;AACA,UAAM,OAAO,cAAc,GAAG,KAAK,GAAG,WAAW,GAAG,GAAG,IAAI,QAAQ,YAAY,WAAW;AAC1F,QAAI,CAAC;AACD,aAAO,IAAI,MAAM;AAAA,EAAK,MAAM,GAAG,IAAI;AAAA,EAC3C;AACA,UAAQ,MAAM,QAAQ,QAAQ,KAAK,MAAM,EAAE;AAC3C,SAAO,IAAI,MAAM;AAAA,EAAK,MAAM,GAAG,KAAK,GAAG,KAAK,GAAG,GAAG;AACtD;AACA,SAAS,YAAY,MAAM,KAAK,WAAW,aAAa;AACpD,QAAM,EAAE,MAAM,MAAM,IAAI;AACxB,QAAM,EAAE,cAAc,aAAa,QAAQ,YAAY,OAAO,IAAI;AAClE,MAAK,eAAe,MAAM,SAAS,IAAI,KAClC,UAAU,WAAW,KAAK,KAAK,GAAI;AACpC,WAAO,aAAa,OAAO,GAAG;AAAA,EAClC;AACA,MAAI,oFAAoF,KAAK,KAAK,GAAG;AAOjG,WAAO,eAAe,UAAU,CAAC,MAAM,SAAS,IAAI,IAC9C,aAAa,OAAO,GAAG,IACvB,YAAY,MAAM,KAAK,WAAW,WAAW;AAAA,EACvD;AACA,MAAI,CAAC,eACD,CAAC,UACD,SAAS,OAAO,SAChB,MAAM,SAAS,IAAI,GAAG;AAEtB,WAAO,YAAY,MAAM,KAAK,WAAW,WAAW;AAAA,EACxD;AACA,MAAI,uBAAuB,KAAK,GAAG;AAC/B,QAAI,WAAW,IAAI;AACf,UAAI,mBAAmB;AACvB,aAAO,YAAY,MAAM,KAAK,WAAW,WAAW;AAAA,IACxD,WACS,eAAe,WAAW,YAAY;AAC3C,aAAO,aAAa,OAAO,GAAG;AAAA,IAClC;AAAA,EACJ;AACA,QAAM,MAAM,MAAM,QAAQ,QAAQ;AAAA,EAAO,MAAM,EAAE;AAIjD,MAAI,cAAc;AACd,UAAM,OAAO,CAAC,QAAK;AA/R3B;AA+R8B,iBAAI,WAAW,IAAI,QAAQ,6BAA2B,SAAI,SAAJ,mBAAU,KAAK;AAAA;AAC3F,UAAM,EAAE,QAAQ,KAAK,IAAI,IAAI,IAAI;AACjC,QAAI,KAAK,KAAK,IAAI,MAAK,iCAAQ,KAAK;AAChC,aAAO,aAAa,OAAO,GAAG;AAAA,EACtC;AACA,SAAO,cACD,MACA,cAAc,KAAK,QAAQ,WAAW,eAAe,KAAK,KAAK,CAAC;AAC1E;AACA,SAAS,gBAAgB,MAAM,KAAK,WAAW,aAAa;AACxD,QAAM,EAAE,aAAa,OAAO,IAAI;AAChC,QAAM,KAAK,OAAO,KAAK,UAAU,WAC3B,OACA,OAAO,OAAO,CAAC,GAAG,MAAM,EAAE,OAAO,OAAO,KAAK,KAAK,EAAE,CAAC;AAC3D,MAAI,EAAE,KAAK,IAAI;AACf,MAAI,SAAS,OAAO,cAAc;AAE9B,QAAI,kDAAkD,KAAK,GAAG,KAAK;AAC/D,aAAO,OAAO;AAAA,EACtB;AACA,QAAM,aAAa,CAAC,UAAU;AAC1B,YAAQ,OAAO;AAAA,MACX,KAAK,OAAO;AAAA,MACZ,KAAK,OAAO;AACR,eAAO,eAAe,SAChB,aAAa,GAAG,OAAO,GAAG,IAC1B,YAAY,IAAI,KAAK,WAAW,WAAW;AAAA,MACrD,KAAK,OAAO;AACR,eAAO,mBAAmB,GAAG,OAAO,GAAG;AAAA,MAC3C,KAAK,OAAO;AACR,eAAO,mBAAmB,GAAG,OAAO,GAAG;AAAA,MAC3C,KAAK,OAAO;AACR,eAAO,YAAY,IAAI,KAAK,WAAW,WAAW;AAAA,MACtD;AACI,eAAO;AAAA,IACf;AAAA,EACJ;AACA,MAAI,MAAM,WAAW,IAAI;AACzB,MAAI,QAAQ,MAAM;AACd,UAAM,EAAE,gBAAgB,kBAAkB,IAAI,IAAI;AAClD,UAAM,IAAK,eAAe,kBAAmB;AAC7C,UAAM,WAAW,CAAC;AAClB,QAAI,QAAQ;AACR,YAAM,IAAI,MAAM,mCAAmC,CAAC,EAAE;AAAA,EAC9D;AACA,SAAO;AACX;;;ACxUA,SAAS,uBAAuB,KAAK,SAAS;AAC1C,QAAM,MAAM,OAAO,OAAO;AAAA,IACtB,YAAY;AAAA,IACZ,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,mBAAmB;AAAA,IACnB,YAAY;AAAA,IACZ,oBAAoB;AAAA,IACpB,gCAAgC;AAAA,IAChC,UAAU;AAAA,IACV,uBAAuB;AAAA,IACvB,WAAW;AAAA,IACX,WAAW;AAAA,IACX,iBAAiB;AAAA,IACjB,SAAS;AAAA,IACT,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,SAAS;AAAA,IACT,kBAAkB;AAAA,EACtB,GAAG,IAAI,OAAO,iBAAiB,OAAO;AACtC,MAAI;AACJ,UAAQ,IAAI,iBAAiB;AAAA,IACzB,KAAK;AACD,eAAS;AACT;AAAA,IACJ,KAAK;AACD,eAAS;AACT;AAAA,IACJ;AACI,eAAS;AAAA,EACjB;AACA,SAAO;AAAA,IACH,SAAS,oBAAI,IAAI;AAAA,IACjB;AAAA,IACA,uBAAuB,IAAI,wBAAwB,MAAM;AAAA,IACzD,QAAQ;AAAA,IACR,YAAY,OAAO,IAAI,WAAW,WAAW,IAAI,OAAO,IAAI,MAAM,IAAI;AAAA,IACtE;AAAA,IACA,SAAS;AAAA,EACb;AACJ;AACA,SAAS,aAAa,MAAM,MAAM;AA9ClC;AA+CI,MAAI,KAAK,KAAK;AACV,UAAM,QAAQ,KAAK,OAAO,OAAK,EAAE,QAAQ,KAAK,GAAG;AACjD,QAAI,MAAM,SAAS;AACf,aAAO,MAAM,KAAK,OAAK,EAAE,WAAW,KAAK,MAAM,KAAK,MAAM,CAAC;AAAA,EACnE;AACA,MAAI,SAAS;AACb,MAAI;AACJ,MAAI,SAAS,IAAI,GAAG;AAChB,UAAM,KAAK;AACX,QAAI,QAAQ,KAAK,OAAO,OAAE;AAxDlC,UAAAC;AAwDqC,cAAAA,MAAA,EAAE,aAAF,gBAAAA,IAAA,QAAa;AAAA,KAAI;AAC9C,QAAI,MAAM,SAAS,GAAG;AAClB,YAAM,YAAY,MAAM,OAAO,OAAK,EAAE,IAAI;AAC1C,UAAI,UAAU,SAAS;AACnB,gBAAQ;AAAA,IAChB;AACA,aACI,MAAM,KAAK,OAAK,EAAE,WAAW,KAAK,MAAM,KAAK,MAAM,KAAK,OAAK,CAAC,EAAE,MAAM;AAAA,EAC9E,OACK;AACD,UAAM;AACN,aAAS,KAAK,KAAK,OAAK,EAAE,aAAa,eAAe,EAAE,SAAS;AAAA,EACrE;AACA,MAAI,CAAC,QAAQ;AACT,UAAM,SAAO,gCAAK,gBAAL,mBAAkB,UAAS,QAAQ,OAAO,SAAS,OAAO;AACvE,UAAM,IAAI,MAAM,wBAAwB,IAAI,QAAQ;AAAA,EACxD;AACA,SAAO;AACX;AAEA,SAAS,eAAe,MAAM,QAAQ,EAAE,SAAS,IAAI,GAAG;AACpD,MAAI,CAAC,IAAI;AACL,WAAO;AACX,QAAM,QAAQ,CAAC;AACf,QAAM,UAAU,SAAS,IAAI,KAAK,aAAa,IAAI,MAAM,KAAK;AAC9D,MAAI,UAAU,cAAc,MAAM,GAAG;AACjC,YAAQ,IAAI,MAAM;AAClB,UAAM,KAAK,IAAI,MAAM,EAAE;AAAA,EAC3B;AACA,QAAM,MAAM,KAAK,QAAQ,OAAO,UAAU,OAAO,OAAO;AACxD,MAAI;AACA,UAAM,KAAK,IAAI,WAAW,UAAU,GAAG,CAAC;AAC5C,SAAO,MAAM,KAAK,GAAG;AACzB;AACA,SAAS,UAAU,MAAM,KAAK,WAAW,aAAa;AA1FtD;AA2FI,MAAI,OAAO,IAAI;AACX,WAAO,KAAK,SAAS,KAAK,WAAW,WAAW;AACpD,MAAI,QAAQ,IAAI,GAAG;AACf,QAAI,IAAI,IAAI;AACR,aAAO,KAAK,SAAS,GAAG;AAC5B,SAAI,SAAI,oBAAJ,mBAAqB,IAAI,OAAO;AAChC,YAAM,IAAI,UAAU,yDAAyD;AAAA,IACjF,OACK;AACD,UAAI,IAAI;AACJ,YAAI,gBAAgB,IAAI,IAAI;AAAA;AAE5B,YAAI,kBAAkB,oBAAI,IAAI,CAAC,IAAI,CAAC;AACxC,aAAO,KAAK,QAAQ,IAAI,GAAG;AAAA,IAC/B;AAAA,EACJ;AACA,MAAI,SAAS;AACb,QAAM,OAAO,OAAO,IAAI,IAClB,OACA,IAAI,IAAI,WAAW,MAAM,EAAE,UAAU,OAAM,SAAS,EAAG,CAAC;AAC9D,aAAW,SAAS,aAAa,IAAI,IAAI,OAAO,MAAM,IAAI;AAC1D,QAAM,QAAQ,eAAe,MAAM,QAAQ,GAAG;AAC9C,MAAI,MAAM,SAAS;AACf,QAAI,iBAAiB,IAAI,iBAAiB,KAAK,MAAM,SAAS;AAClE,QAAM,MAAM,OAAO,OAAO,cAAc,aAClC,OAAO,UAAU,MAAM,KAAK,WAAW,WAAW,IAClD,SAAS,IAAI,IACT,gBAAgB,MAAM,KAAK,WAAW,WAAW,IACjD,KAAK,SAAS,KAAK,WAAW,WAAW;AACnD,MAAI,CAAC;AACD,WAAO;AACX,SAAO,SAAS,IAAI,KAAK,IAAI,CAAC,MAAM,OAAO,IAAI,CAAC,MAAM,MAChD,GAAG,KAAK,IAAI,GAAG,KACf,GAAG,KAAK;AAAA,EAAK,IAAI,MAAM,GAAG,GAAG;AACvC;;;ACxHA,SAAS,cAAc,EAAE,KAAK,MAAM,GAAG,KAAK,WAAW,aAAa;AAChE,QAAM,EAAE,eAAe,KAAK,QAAQ,YAAY,SAAS,EAAE,eAAe,WAAW,WAAW,EAAE,IAAI;AACtG,MAAI,aAAc,OAAO,GAAG,KAAK,IAAI,WAAY;AACjD,MAAI,YAAY;AACZ,QAAI,YAAY;AACZ,YAAM,IAAI,MAAM,kDAAkD;AAAA,IACtE;AACA,QAAI,aAAa,GAAG,KAAM,CAAC,OAAO,GAAG,KAAK,OAAO,QAAQ,UAAW;AAChE,YAAM,MAAM;AACZ,YAAM,IAAI,MAAM,GAAG;AAAA,IACvB;AAAA,EACJ;AACA,MAAI,cAAc,CAAC,eACd,CAAC,OACG,cAAc,SAAS,QAAQ,CAAC,IAAI,UACrC,aAAa,GAAG,MACf,SAAS,GAAG,IACP,IAAI,SAAS,OAAO,gBAAgB,IAAI,SAAS,OAAO,gBACxD,OAAO,QAAQ;AAC7B,QAAM,OAAO,OAAO,CAAC,GAAG,KAAK;AAAA,IACzB,eAAe;AAAA,IACf,aAAa,CAAC,gBAAgB,cAAc,CAAC;AAAA,IAC7C,QAAQ,SAAS;AAAA,EACrB,CAAC;AACD,MAAI,iBAAiB;AACrB,MAAI,YAAY;AAChB,MAAI,MAAM,UAAU,KAAK,KAAK,MAAO,iBAAiB,MAAO,MAAO,YAAY,IAAK;AACrF,MAAI,CAAC,eAAe,CAAC,IAAI,UAAU,IAAI,SAAS,MAAM;AAClD,QAAI;AACA,YAAM,IAAI,MAAM,8EAA8E;AAClG,kBAAc;AAAA,EAClB;AACA,MAAI,IAAI,QAAQ;AACZ,QAAI,iBAAiB,SAAS,MAAM;AAChC,UAAI,kBAAkB;AAClB,kBAAU;AACd,aAAO,QAAQ,KAAK,MAAM,cAAc,KAAK,GAAG,KAAK;AAAA,IACzD;AAAA,EACJ,WACU,iBAAiB,CAAC,cAAgB,SAAS,QAAQ,aAAc;AACvE,UAAM,KAAK,GAAG;AACd,QAAI,cAAc,CAAC,gBAAgB;AAC/B,aAAO,YAAY,KAAK,IAAI,QAAQ,cAAc,UAAU,CAAC;AAAA,IACjE,WACS,aAAa;AAClB,kBAAY;AAChB,WAAO;AAAA,EACX;AACA,MAAI;AACA,iBAAa;AACjB,MAAI,aAAa;AACb,QAAI;AACA,aAAO,YAAY,KAAK,IAAI,QAAQ,cAAc,UAAU,CAAC;AACjE,UAAM,KAAK,GAAG;AAAA,EAAK,MAAM;AAAA,EAC7B,OACK;AACD,UAAM,GAAG,GAAG;AACZ,QAAI;AACA,aAAO,YAAY,KAAK,IAAI,QAAQ,cAAc,UAAU,CAAC;AAAA,EACrE;AACA,MAAI,KAAK,KAAK;AACd,MAAI,OAAO,KAAK,GAAG;AACf,UAAM,CAAC,CAAC,MAAM;AACd,UAAM,MAAM;AACZ,mBAAe,MAAM;AAAA,EACzB,OACK;AACD,UAAM;AACN,UAAM;AACN,mBAAe;AACf,QAAI,SAAS,OAAO,UAAU;AAC1B,cAAQ,IAAI,WAAW,KAAK;AAAA,EACpC;AACA,MAAI,cAAc;AAClB,MAAI,CAAC,eAAe,CAAC,cAAc,SAAS,KAAK;AAC7C,QAAI,gBAAgB,IAAI,SAAS;AACrC,cAAY;AACZ,MAAI,CAAC,aACD,WAAW,UAAU,KACrB,CAAC,IAAI,UACL,CAAC,eACD,MAAM,KAAK,KACX,CAAC,MAAM,QACP,CAAC,MAAM,OACP,CAAC,MAAM,QAAQ;AAEf,QAAI,SAAS,IAAI,OAAO,UAAU,CAAC;AAAA,EACvC;AACA,MAAI,mBAAmB;AACvB,QAAM,WAAW,UAAU,OAAO,KAAK,MAAO,mBAAmB,MAAO,MAAO,YAAY,IAAK;AAChG,MAAI,KAAK;AACT,MAAI,cAAc,OAAO,KAAK;AAC1B,SAAK,MAAM,OAAO;AAClB,QAAI,KAAK;AACL,YAAM,KAAK,cAAc,GAAG;AAC5B,YAAM;AAAA,EAAK,cAAc,IAAI,IAAI,MAAM,CAAC;AAAA,IAC5C;AACA,QAAI,aAAa,MAAM,CAAC,IAAI,QAAQ;AAChC,UAAI,OAAO;AACP,aAAK;AAAA,IACb,OACK;AACD,YAAM;AAAA,EAAK,IAAI,MAAM;AAAA,IACzB;AAAA,EACJ,WACS,CAAC,eAAe,aAAa,KAAK,GAAG;AAC1C,UAAM,MAAM,SAAS,CAAC;AACtB,UAAM,MAAM,SAAS,QAAQ,IAAI;AACjC,UAAM,aAAa,QAAQ;AAC3B,UAAM,OAAO,IAAI,UAAU,MAAM,QAAQ,MAAM,MAAM,WAAW;AAChE,QAAI,cAAc,CAAC,MAAM;AACrB,UAAI,eAAe;AACnB,UAAI,eAAe,QAAQ,OAAO,QAAQ,MAAM;AAC5C,YAAI,MAAM,SAAS,QAAQ,GAAG;AAC9B,YAAI,QAAQ,OACR,QAAQ,MACR,MAAM,OACN,SAAS,MAAM,CAAC,MAAM,KAAK;AAC3B,gBAAM,SAAS,QAAQ,KAAK,MAAM,CAAC;AAAA,QACvC;AACA,YAAI,QAAQ,MAAM,MAAM;AACpB,yBAAe;AAAA,MACvB;AACA,UAAI,CAAC;AACD,aAAK;AAAA,EAAK,IAAI,MAAM;AAAA,IAC5B;AAAA,EACJ,WACS,aAAa,MAAM,SAAS,CAAC,MAAM,MAAM;AAC9C,SAAK;AAAA,EACT;AACA,SAAO,KAAK;AACZ,MAAI,IAAI,QAAQ;AACZ,QAAI,oBAAoB;AACpB,gBAAU;AAAA,EAClB,WACS,gBAAgB,CAAC,kBAAkB;AACxC,WAAO,YAAY,KAAK,IAAI,QAAQ,cAAc,YAAY,CAAC;AAAA,EACnE,WACS,aAAa,aAAa;AAC/B,gBAAY;AAAA,EAChB;AACA,SAAO;AACX;;;AC/IA,SAAS,KAAK,UAAU,SAAS;AAC7B,MAAI,aAAa,WAAW,aAAa,QAAQ;AAC7C,YAAQ,KAAK,OAAO;AAAA,EACxB;AACJ;;;ACEA,IAAM,YAAY;AAClB,IAAM,QAAQ;AAAA,EACV,UAAU,WAAS,UAAU,aACxB,OAAO,UAAU,YAAY,MAAM,gBAAgB;AAAA,EACxD,SAAS;AAAA,EACT,KAAK;AAAA,EACL,MAAM;AAAA,EACN,SAAS,MAAM,OAAO,OAAO,IAAI,OAAO,OAAO,SAAS,CAAC,GAAG;AAAA,IACxD,YAAY;AAAA,EAChB,CAAC;AAAA,EACD,WAAW,MAAM;AACrB;AACA,IAAM,aAAa,CAAC,KAAK,SAAS,MAAM,SAAS,GAAG,KAC/C,SAAS,GAAG,MACR,CAAC,IAAI,QAAQ,IAAI,SAAS,OAAO,UAClC,MAAM,SAAS,IAAI,KAAK,OAC5B,2BAAK,IAAI,OAAO,KAAK,KAAK,SAAO,IAAI,QAAQ,MAAM,OAAO,IAAI;AAClE,SAAS,gBAAgB,KAAKC,MAAK,OAAO;AACtC,UAAQ,OAAO,QAAQ,KAAK,IAAI,MAAM,QAAQ,IAAI,GAAG,IAAI;AACzD,MAAI,MAAM,KAAK;AACX,eAAW,MAAM,MAAM;AACnB,iBAAW,KAAKA,MAAK,EAAE;AAAA,WACtB,MAAM,QAAQ,KAAK;AACxB,eAAW,MAAM;AACb,iBAAW,KAAKA,MAAK,EAAE;AAAA;AAE3B,eAAW,KAAKA,MAAK,KAAK;AAClC;AACA,SAAS,WAAW,KAAKA,MAAK,OAAO;AACjC,QAAM,SAAS,OAAO,QAAQ,KAAK,IAAI,MAAM,QAAQ,IAAI,GAAG,IAAI;AAChE,MAAI,CAAC,MAAM,MAAM;AACb,UAAM,IAAI,MAAM,2CAA2C;AAC/D,QAAM,SAAS,OAAO,OAAO,MAAM,KAAK,GAAG;AAC3C,aAAW,CAAC,KAAKC,MAAK,KAAK,QAAQ;AAC/B,QAAID,gBAAe,KAAK;AACpB,UAAI,CAACA,KAAI,IAAI,GAAG;AACZ,QAAAA,KAAI,IAAI,KAAKC,MAAK;AAAA,IAC1B,WACSD,gBAAe,KAAK;AACzB,MAAAA,KAAI,IAAI,GAAG;AAAA,IACf,WACS,CAAC,OAAO,UAAU,eAAe,KAAKA,MAAK,GAAG,GAAG;AACtD,aAAO,eAAeA,MAAK,KAAK;AAAA,QAC5B,OAAAC;AAAA,QACA,UAAU;AAAA,QACV,YAAY;AAAA,QACZ,cAAc;AAAA,MAClB,CAAC;AAAA,IACL;AAAA,EACJ;AACA,SAAOD;AACX;;;ACvDA,SAAS,eAAe,KAAKE,MAAK,EAAE,KAAK,MAAM,GAAG;AAC9C,MAAI,OAAO,GAAG,KAAK,IAAI;AACnB,QAAI,WAAW,KAAKA,MAAK,KAAK;AAAA,WAEzB,WAAW,KAAK,GAAG;AACxB,oBAAgB,KAAKA,MAAK,KAAK;AAAA,OAC9B;AACD,UAAM,QAAQ,KAAK,KAAK,IAAI,GAAG;AAC/B,QAAIA,gBAAe,KAAK;AACpB,MAAAA,KAAI,IAAI,OAAO,KAAK,OAAO,OAAO,GAAG,CAAC;AAAA,IAC1C,WACSA,gBAAe,KAAK;AACzB,MAAAA,KAAI,IAAI,KAAK;AAAA,IACjB,OACK;AACD,YAAM,YAAY,aAAa,KAAK,OAAO,GAAG;AAC9C,YAAM,UAAU,KAAK,OAAO,WAAW,GAAG;AAC1C,UAAI,aAAaA;AACb,eAAO,eAAeA,MAAK,WAAW;AAAA,UAClC,OAAO;AAAA,UACP,UAAU;AAAA,UACV,YAAY;AAAA,UACZ,cAAc;AAAA,QAClB,CAAC;AAAA;AAED,QAAAA,KAAI,SAAS,IAAI;AAAA,IACzB;AAAA,EACJ;AACA,SAAOA;AACX;AACA,SAAS,aAAa,KAAK,OAAO,KAAK;AACnC,MAAI,UAAU;AACV,WAAO;AAEX,MAAI,OAAO,UAAU;AACjB,WAAO,OAAO,KAAK;AACvB,MAAI,OAAO,GAAG,MAAK,2BAAK,MAAK;AACzB,UAAM,SAAS,uBAAuB,IAAI,KAAK,CAAC,CAAC;AACjD,WAAO,UAAU,oBAAI,IAAI;AACzB,eAAW,QAAQ,IAAI,QAAQ,KAAK;AAChC,aAAO,QAAQ,IAAI,KAAK,MAAM;AAClC,WAAO,SAAS;AAChB,WAAO,iBAAiB;AACxB,UAAM,SAAS,IAAI,SAAS,MAAM;AAClC,QAAI,CAAC,IAAI,cAAc;AACnB,UAAI,UAAU,KAAK,UAAU,MAAM;AACnC,UAAI,QAAQ,SAAS;AACjB,kBAAU,QAAQ,UAAU,GAAG,EAAE,IAAI;AACzC,WAAK,IAAI,IAAI,QAAQ,UAAU,kFAAkF,OAAO,0CAA0C;AAClK,UAAI,eAAe;AAAA,IACvB;AACA,WAAO;AAAA,EACX;AACA,SAAO,KAAK,UAAU,KAAK;AAC/B;;;ACvDA,SAAS,WAAW,KAAK,OAAO,KAAK;AACjC,QAAM,IAAI,WAAW,KAAK,QAAW,GAAG;AACxC,QAAM,IAAI,WAAW,OAAO,QAAW,GAAG;AAC1C,SAAO,IAAI,KAAK,GAAG,CAAC;AACxB;AACA,IAAM,OAAN,MAAM,MAAK;AAAA,EACP,YAAY,KAAK,QAAQ,MAAM;AAC3B,WAAO,eAAe,MAAM,WAAW,EAAE,OAAO,KAAK,CAAC;AACtD,SAAK,MAAM;AACX,SAAK,QAAQ;AAAA,EACjB;AAAA,EACA,MAAMC,SAAQ;AACV,QAAI,EAAE,KAAK,MAAM,IAAI;AACrB,QAAI,OAAO,GAAG;AACV,YAAM,IAAI,MAAMA,OAAM;AAC1B,QAAI,OAAO,KAAK;AACZ,cAAQ,MAAM,MAAMA,OAAM;AAC9B,WAAO,IAAI,MAAK,KAAK,KAAK;AAAA,EAC9B;AAAA,EACA,OAAO,GAAG,KAAK;AACX,UAAM,QAAO,2BAAK,YAAW,oBAAI,IAAI,IAAI,CAAC;AAC1C,WAAO,eAAe,KAAK,MAAM,IAAI;AAAA,EACzC;AAAA,EACA,SAAS,KAAK,WAAW,aAAa;AAClC,YAAO,2BAAK,OACN,cAAc,MAAM,KAAK,WAAW,WAAW,IAC/C,KAAK,UAAU,IAAI;AAAA,EAC7B;AACJ;;;AC7BA,SAAS,oBAAoB,YAAY,KAAK,SAAS;AACnD,QAAM,OAAO,IAAI,UAAU,WAAW;AACtC,QAAMC,aAAY,OAAO,0BAA0B;AACnD,SAAOA,WAAU,YAAY,KAAK,OAAO;AAC7C;AACA,SAAS,yBAAyB,EAAE,SAAS,MAAM,GAAG,KAAK,EAAE,iBAAiB,WAAW,YAAY,aAAa,UAAU,GAAG;AAC3H,QAAM,EAAE,QAAQ,SAAS,EAAE,cAAc,EAAE,IAAI;AAC/C,QAAM,UAAU,OAAO,OAAO,CAAC,GAAG,KAAK,EAAE,QAAQ,YAAY,MAAM,KAAK,CAAC;AACzE,MAAI,YAAY;AAChB,QAAM,QAAQ,CAAC;AACf,WAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,EAAE,GAAG;AACnC,UAAM,OAAO,MAAM,CAAC;AACpB,QAAIC,WAAU;AACd,QAAI,OAAO,IAAI,GAAG;AACd,UAAI,CAAC,aAAa,KAAK;AACnB,cAAM,KAAK,EAAE;AACjB,uBAAiB,KAAK,OAAO,KAAK,eAAe,SAAS;AAC1D,UAAI,KAAK;AACL,QAAAA,WAAU,KAAK;AAAA,IACvB,WACS,OAAO,IAAI,GAAG;AACnB,YAAM,KAAK,OAAO,KAAK,GAAG,IAAI,KAAK,MAAM;AACzC,UAAI,IAAI;AACJ,YAAI,CAAC,aAAa,GAAG;AACjB,gBAAM,KAAK,EAAE;AACjB,yBAAiB,KAAK,OAAO,GAAG,eAAe,SAAS;AAAA,MAC5D;AAAA,IACJ;AACA,gBAAY;AACZ,QAAIC,OAAM,UAAU,MAAM,SAAS,MAAOD,WAAU,MAAO,MAAO,YAAY,IAAK;AACnF,QAAIA;AACA,MAAAC,QAAO,YAAYA,MAAK,YAAY,cAAcD,QAAO,CAAC;AAC9D,QAAI,aAAaA;AACb,kBAAY;AAChB,UAAM,KAAK,kBAAkBC,IAAG;AAAA,EACpC;AACA,MAAI;AACJ,MAAI,MAAM,WAAW,GAAG;AACpB,UAAM,UAAU,QAAQ,UAAU;AAAA,EACtC,OACK;AACD,UAAM,MAAM,CAAC;AACb,aAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,EAAE,GAAG;AACnC,YAAM,OAAO,MAAM,CAAC;AACpB,aAAO,OAAO;AAAA,EAAK,MAAM,GAAG,IAAI,KAAK;AAAA,IACzC;AAAA,EACJ;AACA,MAAI,SAAS;AACT,WAAO,OAAO,cAAc,cAAc,OAAO,GAAG,MAAM;AAC1D,QAAI;AACA,gBAAU;AAAA,EAClB,WACS,aAAa;AAClB,gBAAY;AAChB,SAAO;AACX;AACA,SAAS,wBAAwB,EAAE,MAAM,GAAG,KAAK,EAAE,WAAW,WAAW,GAAG;AACxE,QAAM,EAAE,QAAQ,YAAY,uBAAuB,WAAW,SAAS,EAAE,cAAc,EAAE,IAAI;AAC7F,gBAAc;AACd,QAAM,UAAU,OAAO,OAAO,CAAC,GAAG,KAAK;AAAA,IACnC,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,MAAM;AAAA,EACV,CAAC;AACD,MAAI,aAAa;AACjB,MAAI,eAAe;AACnB,QAAM,QAAQ,CAAC;AACf,WAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,EAAE,GAAG;AACnC,UAAM,OAAO,MAAM,CAAC;AACpB,QAAI,UAAU;AACd,QAAI,OAAO,IAAI,GAAG;AACd,UAAI,KAAK;AACL,cAAM,KAAK,EAAE;AACjB,uBAAiB,KAAK,OAAO,KAAK,eAAe,KAAK;AACtD,UAAI,KAAK;AACL,kBAAU,KAAK;AAAA,IACvB,WACS,OAAO,IAAI,GAAG;AACnB,YAAM,KAAK,OAAO,KAAK,GAAG,IAAI,KAAK,MAAM;AACzC,UAAI,IAAI;AACJ,YAAI,GAAG;AACH,gBAAM,KAAK,EAAE;AACjB,yBAAiB,KAAK,OAAO,GAAG,eAAe,KAAK;AACpD,YAAI,GAAG;AACH,uBAAa;AAAA,MACrB;AACA,YAAM,KAAK,OAAO,KAAK,KAAK,IAAI,KAAK,QAAQ;AAC7C,UAAI,IAAI;AACJ,YAAI,GAAG;AACH,oBAAU,GAAG;AACjB,YAAI,GAAG;AACH,uBAAa;AAAA,MACrB,WACS,KAAK,SAAS,SAAQ,yBAAI,UAAS;AACxC,kBAAU,GAAG;AAAA,MACjB;AAAA,IACJ;AACA,QAAI;AACA,mBAAa;AACjB,QAAI,MAAM,UAAU,MAAM,SAAS,MAAO,UAAU,IAAK;AACzD,QAAI,IAAI,MAAM,SAAS;AACnB,aAAO;AACX,QAAI;AACA,aAAO,YAAY,KAAK,YAAY,cAAc,OAAO,CAAC;AAC9D,QAAI,CAAC,eAAe,MAAM,SAAS,gBAAgB,IAAI,SAAS,IAAI;AAChE,mBAAa;AACjB,UAAM,KAAK,GAAG;AACd,mBAAe,MAAM;AAAA,EACzB;AACA,QAAM,EAAE,OAAO,IAAI,IAAI;AACvB,MAAI,MAAM,WAAW,GAAG;AACpB,WAAO,QAAQ;AAAA,EACnB,OACK;AACD,QAAI,CAAC,YAAY;AACb,YAAM,MAAM,MAAM,OAAO,CAAC,KAAK,SAAS,MAAM,KAAK,SAAS,GAAG,CAAC;AAChE,mBAAa,IAAI,QAAQ,YAAY,KAAK,MAAM,IAAI,QAAQ;AAAA,IAChE;AACA,QAAI,YAAY;AACZ,UAAI,MAAM;AACV,iBAAW,QAAQ;AACf,eAAO,OAAO;AAAA,EAAK,UAAU,GAAG,MAAM,GAAG,IAAI,KAAK;AACtD,aAAO,GAAG,GAAG;AAAA,EAAK,MAAM,GAAG,GAAG;AAAA,IAClC,OACK;AACD,aAAO,GAAG,KAAK,GAAG,SAAS,GAAG,MAAM,KAAK,GAAG,CAAC,GAAG,SAAS,GAAG,GAAG;AAAA,IACnE;AAAA,EACJ;AACJ;AACA,SAAS,iBAAiB,EAAE,QAAQ,SAAS,EAAE,cAAc,EAAE,GAAG,OAAO,SAAS,WAAW;AACzF,MAAI,WAAW;AACX,cAAU,QAAQ,QAAQ,QAAQ,EAAE;AACxC,MAAI,SAAS;AACT,UAAM,KAAK,cAAc,cAAc,OAAO,GAAG,MAAM;AACvD,UAAM,KAAK,GAAG,UAAU,CAAC;AAAA,EAC7B;AACJ;;;ACrIA,SAAS,SAAS,OAAO,KAAK;AAC1B,QAAM,IAAI,SAAS,GAAG,IAAI,IAAI,QAAQ;AACtC,aAAW,MAAM,OAAO;AACpB,QAAI,OAAO,EAAE,GAAG;AACZ,UAAI,GAAG,QAAQ,OAAO,GAAG,QAAQ;AAC7B,eAAO;AACX,UAAI,SAAS,GAAG,GAAG,KAAK,GAAG,IAAI,UAAU;AACrC,eAAO;AAAA,IACf;AAAA,EACJ;AACA,SAAO;AACX;AACA,IAAM,UAAN,cAAsB,WAAW;AAAA,EAC7B,WAAW,UAAU;AACjB,WAAO;AAAA,EACX;AAAA,EACA,YAAYC,SAAQ;AAChB,UAAM,KAAKA,OAAM;AACjB,SAAK,QAAQ,CAAC;AAAA,EAClB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,OAAO,KAAKA,SAAQ,KAAK,KAAK;AAC1B,UAAM,EAAE,eAAe,SAAS,IAAI;AACpC,UAAMC,OAAM,IAAI,KAAKD,OAAM;AAC3B,UAAM,MAAM,CAAC,KAAK,UAAU;AACxB,UAAI,OAAO,aAAa;AACpB,gBAAQ,SAAS,KAAK,KAAK,KAAK,KAAK;AAAA,eAChC,MAAM,QAAQ,QAAQ,KAAK,CAAC,SAAS,SAAS,GAAG;AACtD;AACJ,UAAI,UAAU,UAAa;AACvB,QAAAC,KAAI,MAAM,KAAK,WAAW,KAAK,OAAO,GAAG,CAAC;AAAA,IAClD;AACA,QAAI,eAAe,KAAK;AACpB,iBAAW,CAAC,KAAK,KAAK,KAAK;AACvB,YAAI,KAAK,KAAK;AAAA,IACtB,WACS,OAAO,OAAO,QAAQ,UAAU;AACrC,iBAAW,OAAO,OAAO,KAAK,GAAG;AAC7B,YAAI,KAAK,IAAI,GAAG,CAAC;AAAA,IACzB;AACA,QAAI,OAAOD,QAAO,mBAAmB,YAAY;AAC7C,MAAAC,KAAI,MAAM,KAAKD,QAAO,cAAc;AAAA,IACxC;AACA,WAAOC;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,IAAI,MAAM,WAAW;AA7DzB;AA8DQ,QAAI;AACJ,QAAI,OAAO,IAAI;AACX,cAAQ;AAAA,aACH,CAAC,QAAQ,OAAO,SAAS,YAAY,EAAE,SAAS,OAAO;AAE5D,cAAQ,IAAI,KAAK,MAAM,6BAAM,KAAK;AAAA,IACtC;AAEI,cAAQ,IAAI,KAAK,KAAK,KAAK,KAAK,KAAK;AACzC,UAAM,OAAO,SAAS,KAAK,OAAO,MAAM,GAAG;AAC3C,UAAM,eAAc,UAAK,WAAL,mBAAa;AACjC,QAAI,MAAM;AACN,UAAI,CAAC;AACD,cAAM,IAAI,MAAM,OAAO,MAAM,GAAG,cAAc;AAElD,UAAI,SAAS,KAAK,KAAK,KAAK,cAAc,MAAM,KAAK;AACjD,aAAK,MAAM,QAAQ,MAAM;AAAA;AAEzB,aAAK,QAAQ,MAAM;AAAA,IAC3B,WACS,aAAa;AAClB,YAAM,IAAI,KAAK,MAAM,UAAU,UAAQ,YAAY,OAAO,IAAI,IAAI,CAAC;AACnE,UAAI,MAAM;AACN,aAAK,MAAM,KAAK,KAAK;AAAA;AAErB,aAAK,MAAM,OAAO,GAAG,GAAG,KAAK;AAAA,IACrC,OACK;AACD,WAAK,MAAM,KAAK,KAAK;AAAA,IACzB;AAAA,EACJ;AAAA,EACA,OAAO,KAAK;AACR,UAAM,KAAK,SAAS,KAAK,OAAO,GAAG;AACnC,QAAI,CAAC;AACD,aAAO;AACX,UAAM,MAAM,KAAK,MAAM,OAAO,KAAK,MAAM,QAAQ,EAAE,GAAG,CAAC;AACvD,WAAO,IAAI,SAAS;AAAA,EACxB;AAAA,EACA,IAAI,KAAK,YAAY;AACjB,UAAM,KAAK,SAAS,KAAK,OAAO,GAAG;AACnC,UAAM,OAAO,yBAAI;AACjB,YAAQ,CAAC,cAAc,SAAS,IAAI,IAAI,KAAK,QAAQ,SAAS;AAAA,EAClE;AAAA,EACA,IAAI,KAAK;AACL,WAAO,CAAC,CAAC,SAAS,KAAK,OAAO,GAAG;AAAA,EACrC;AAAA,EACA,IAAI,KAAK,OAAO;AACZ,SAAK,IAAI,IAAI,KAAK,KAAK,KAAK,GAAG,IAAI;AAAA,EACvC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,OAAO,GAAG,KAAK,MAAM;AACjB,UAAMA,OAAM,OAAO,IAAI,KAAK,KAAI,2BAAK,YAAW,oBAAI,IAAI,IAAI,CAAC;AAC7D,QAAI,2BAAK;AACL,UAAI,SAASA,IAAG;AACpB,eAAW,QAAQ,KAAK;AACpB,qBAAe,KAAKA,MAAK,IAAI;AACjC,WAAOA;AAAA,EACX;AAAA,EACA,SAAS,KAAK,WAAW,aAAa;AAClC,QAAI,CAAC;AACD,aAAO,KAAK,UAAU,IAAI;AAC9B,eAAW,QAAQ,KAAK,OAAO;AAC3B,UAAI,CAAC,OAAO,IAAI;AACZ,cAAM,IAAI,MAAM,sCAAsC,KAAK,UAAU,IAAI,CAAC,UAAU;AAAA,IAC5F;AACA,QAAI,CAAC,IAAI,iBAAiB,KAAK,iBAAiB,KAAK;AACjD,YAAM,OAAO,OAAO,CAAC,GAAG,KAAK,EAAE,eAAe,KAAK,CAAC;AACxD,WAAO,oBAAoB,MAAM,KAAK;AAAA,MAClC,iBAAiB;AAAA,MACjB,WAAW,EAAE,OAAO,KAAK,KAAK,IAAI;AAAA,MAClC,YAAY,IAAI,UAAU;AAAA,MAC1B;AAAA,MACA;AAAA,IACJ,CAAC;AAAA,EACL;AACJ;;;AC1IA,IAAM,MAAM;AAAA,EACR,YAAY;AAAA,EACZ,SAAS;AAAA,EACT,WAAW;AAAA,EACX,KAAK;AAAA,EACL,QAAQC,MAAK,SAAS;AAClB,QAAI,CAAC,MAAMA,IAAG;AACV,cAAQ,iCAAiC;AAC7C,WAAOA;AAAA,EACX;AAAA,EACA,YAAY,CAACC,SAAQ,KAAK,QAAQ,QAAQ,KAAKA,SAAQ,KAAK,GAAG;AACnE;;;ACPA,IAAM,UAAN,cAAsB,WAAW;AAAA,EAC7B,WAAW,UAAU;AACjB,WAAO;AAAA,EACX;AAAA,EACA,YAAYC,SAAQ;AAChB,UAAM,KAAKA,OAAM;AACjB,SAAK,QAAQ,CAAC;AAAA,EAClB;AAAA,EACA,IAAI,OAAO;AACP,SAAK,MAAM,KAAK,KAAK;AAAA,EACzB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,OAAO,KAAK;AACR,UAAM,MAAM,YAAY,GAAG;AAC3B,QAAI,OAAO,QAAQ;AACf,aAAO;AACX,UAAM,MAAM,KAAK,MAAM,OAAO,KAAK,CAAC;AACpC,WAAO,IAAI,SAAS;AAAA,EACxB;AAAA,EACA,IAAI,KAAK,YAAY;AACjB,UAAM,MAAM,YAAY,GAAG;AAC3B,QAAI,OAAO,QAAQ;AACf,aAAO;AACX,UAAM,KAAK,KAAK,MAAM,GAAG;AACzB,WAAO,CAAC,cAAc,SAAS,EAAE,IAAI,GAAG,QAAQ;AAAA,EACpD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,IAAI,KAAK;AACL,UAAM,MAAM,YAAY,GAAG;AAC3B,WAAO,OAAO,QAAQ,YAAY,MAAM,KAAK,MAAM;AAAA,EACvD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,IAAI,KAAK,OAAO;AACZ,UAAM,MAAM,YAAY,GAAG;AAC3B,QAAI,OAAO,QAAQ;AACf,YAAM,IAAI,MAAM,+BAA+B,GAAG,GAAG;AACzD,UAAM,OAAO,KAAK,MAAM,GAAG;AAC3B,QAAI,SAAS,IAAI,KAAK,cAAc,KAAK;AACrC,WAAK,QAAQ;AAAA;AAEb,WAAK,MAAM,GAAG,IAAI;AAAA,EAC1B;AAAA,EACA,OAAO,GAAG,KAAK;AACX,UAAMC,OAAM,CAAC;AACb,QAAI,2BAAK;AACL,UAAI,SAASA,IAAG;AACpB,QAAI,IAAI;AACR,eAAW,QAAQ,KAAK;AACpB,MAAAA,KAAI,KAAK,KAAK,MAAM,OAAO,GAAG,GAAG,GAAG,CAAC;AACzC,WAAOA;AAAA,EACX;AAAA,EACA,SAAS,KAAK,WAAW,aAAa;AAClC,QAAI,CAAC;AACD,aAAO,KAAK,UAAU,IAAI;AAC9B,WAAO,oBAAoB,MAAM,KAAK;AAAA,MAClC,iBAAiB;AAAA,MACjB,WAAW,EAAE,OAAO,KAAK,KAAK,IAAI;AAAA,MAClC,aAAa,IAAI,UAAU,MAAM;AAAA,MACjC;AAAA,MACA;AAAA,IACJ,CAAC;AAAA,EACL;AAAA,EACA,OAAO,KAAKD,SAAQ,KAAK,KAAK;AAC1B,UAAM,EAAE,SAAS,IAAI;AACrB,UAAMC,OAAM,IAAI,KAAKD,OAAM;AAC3B,QAAI,OAAO,OAAO,YAAY,OAAO,GAAG,GAAG;AACvC,UAAI,IAAI;AACR,eAAS,MAAM,KAAK;AAChB,YAAI,OAAO,aAAa,YAAY;AAChC,gBAAM,MAAM,eAAe,MAAM,KAAK,OAAO,GAAG;AAChD,eAAK,SAAS,KAAK,KAAK,KAAK,EAAE;AAAA,QACnC;AACA,QAAAC,KAAI,MAAM,KAAK,WAAW,IAAI,QAAW,GAAG,CAAC;AAAA,MACjD;AAAA,IACJ;AACA,WAAOA;AAAA,EACX;AACJ;AACA,SAAS,YAAY,KAAK;AACtB,MAAI,MAAM,SAAS,GAAG,IAAI,IAAI,QAAQ;AACtC,MAAI,OAAO,OAAO,QAAQ;AACtB,UAAM,OAAO,GAAG;AACpB,SAAO,OAAO,QAAQ,YAAY,OAAO,UAAU,GAAG,KAAK,OAAO,IAC5D,MACA;AACV;;;AC3GA,IAAM,MAAM;AAAA,EACR,YAAY;AAAA,EACZ,SAAS;AAAA,EACT,WAAW;AAAA,EACX,KAAK;AAAA,EACL,QAAQC,MAAK,SAAS;AAClB,QAAI,CAAC,MAAMA,IAAG;AACV,cAAQ,kCAAkC;AAC9C,WAAOA;AAAA,EACX;AAAA,EACA,YAAY,CAACC,SAAQ,KAAK,QAAQ,QAAQ,KAAKA,SAAQ,KAAK,GAAG;AACnE;;;ACZA,IAAM,SAAS;AAAA,EACX,UAAU,WAAS,OAAO,UAAU;AAAA,EACpC,SAAS;AAAA,EACT,KAAK;AAAA,EACL,SAAS,SAAO;AAAA,EAChB,UAAU,MAAM,KAAK,WAAW,aAAa;AACzC,UAAM,OAAO,OAAO,EAAE,cAAc,KAAK,GAAG,GAAG;AAC/C,WAAO,gBAAgB,MAAM,KAAK,WAAW,WAAW;AAAA,EAC5D;AACJ;;;ACTA,IAAM,UAAU;AAAA,EACZ,UAAU,WAAS,SAAS;AAAA,EAC5B,YAAY,MAAM,IAAI,OAAO,IAAI;AAAA,EACjC,SAAS;AAAA,EACT,KAAK;AAAA,EACL,MAAM;AAAA,EACN,SAAS,MAAM,IAAI,OAAO,IAAI;AAAA,EAC9B,WAAW,CAAC,EAAE,OAAO,GAAG,QAAQ,OAAO,WAAW,YAAY,QAAQ,KAAK,KAAK,MAAM,IAChF,SACA,IAAI,QAAQ;AACtB;;;ACVA,IAAM,UAAU;AAAA,EACZ,UAAU,WAAS,OAAO,UAAU;AAAA,EACpC,SAAS;AAAA,EACT,KAAK;AAAA,EACL,MAAM;AAAA,EACN,SAAS,SAAO,IAAI,OAAO,IAAI,CAAC,MAAM,OAAO,IAAI,CAAC,MAAM,GAAG;AAAA,EAC3D,UAAU,EAAE,QAAQ,MAAM,GAAG,KAAK;AAC9B,QAAI,UAAU,QAAQ,KAAK,KAAK,MAAM,GAAG;AACrC,YAAM,KAAK,OAAO,CAAC,MAAM,OAAO,OAAO,CAAC,MAAM;AAC9C,UAAI,UAAU;AACV,eAAO;AAAA,IACf;AACA,WAAO,QAAQ,IAAI,QAAQ,UAAU,IAAI,QAAQ;AAAA,EACrD;AACJ;;;AChBA,SAAS,gBAAgB,EAAE,QAAQ,mBAAmB,KAAK,MAAM,GAAG;AAChE,MAAI,OAAO,UAAU;AACjB,WAAO,OAAO,KAAK;AACvB,QAAM,MAAM,OAAO,UAAU,WAAW,QAAQ,OAAO,KAAK;AAC5D,MAAI,CAAC,SAAS,GAAG;AACb,WAAO,MAAM,GAAG,IAAI,SAAS,MAAM,IAAI,UAAU;AACrD,MAAI,IAAI,KAAK,UAAU,KAAK;AAC5B,MAAI,CAAC,UACD,sBACC,CAAC,OAAO,QAAQ,8BACjB,MAAM,KAAK,CAAC,GAAG;AACf,QAAI,IAAI,EAAE,QAAQ,GAAG;AACrB,QAAI,IAAI,GAAG;AACP,UAAI,EAAE;AACN,WAAK;AAAA,IACT;AACA,QAAI,IAAI,qBAAqB,EAAE,SAAS,IAAI;AAC5C,WAAO,MAAM;AACT,WAAK;AAAA,EACb;AACA,SAAO;AACX;;;AClBA,IAAM,WAAW;AAAA,EACb,UAAU,WAAS,OAAO,UAAU;AAAA,EACpC,SAAS;AAAA,EACT,KAAK;AAAA,EACL,MAAM;AAAA,EACN,SAAS,SAAO,IAAI,MAAM,EAAE,EAAE,YAAY,MAAM,QAC1C,MACA,IAAI,CAAC,MAAM,MACP,OAAO,oBACP,OAAO;AAAA,EACjB,WAAW;AACf;AACA,IAAM,WAAW;AAAA,EACb,UAAU,WAAS,OAAO,UAAU;AAAA,EACpC,SAAS;AAAA,EACT,KAAK;AAAA,EACL,QAAQ;AAAA,EACR,MAAM;AAAA,EACN,SAAS,SAAO,WAAW,GAAG;AAAA,EAC9B,UAAU,MAAM;AACZ,UAAM,MAAM,OAAO,KAAK,KAAK;AAC7B,WAAO,SAAS,GAAG,IAAI,IAAI,cAAc,IAAI,gBAAgB,IAAI;AAAA,EACrE;AACJ;AACA,IAAM,QAAQ;AAAA,EACV,UAAU,WAAS,OAAO,UAAU;AAAA,EACpC,SAAS;AAAA,EACT,KAAK;AAAA,EACL,MAAM;AAAA,EACN,QAAQ,KAAK;AACT,UAAM,OAAO,IAAI,OAAO,WAAW,GAAG,CAAC;AACvC,UAAM,MAAM,IAAI,QAAQ,GAAG;AAC3B,QAAI,QAAQ,MAAM,IAAI,IAAI,SAAS,CAAC,MAAM;AACtC,WAAK,oBAAoB,IAAI,SAAS,MAAM;AAChD,WAAO;AAAA,EACX;AAAA,EACA,WAAW;AACf;;;ACtCA,IAAM,cAAc,CAAC,UAAU,OAAO,UAAU,YAAY,OAAO,UAAU,KAAK;AAClF,IAAM,aAAa,CAAC,KAAK,QAAQ,OAAO,EAAE,YAAY,MAAO,cAAc,OAAO,GAAG,IAAI,SAAS,IAAI,UAAU,MAAM,GAAG,KAAK;AAC9H,SAAS,aAAa,MAAM,OAAO,QAAQ;AACvC,QAAM,EAAE,MAAM,IAAI;AAClB,MAAI,YAAY,KAAK,KAAK,SAAS;AAC/B,WAAO,SAAS,MAAM,SAAS,KAAK;AACxC,SAAO,gBAAgB,IAAI;AAC/B;AACA,IAAM,SAAS;AAAA,EACX,UAAU,WAAS,YAAY,KAAK,KAAK,SAAS;AAAA,EAClD,SAAS;AAAA,EACT,KAAK;AAAA,EACL,QAAQ;AAAA,EACR,MAAM;AAAA,EACN,SAAS,CAAC,KAAK,UAAU,QAAQ,WAAW,KAAK,GAAG,GAAG,GAAG;AAAA,EAC1D,WAAW,UAAQ,aAAa,MAAM,GAAG,IAAI;AACjD;AACA,IAAM,MAAM;AAAA,EACR,UAAU;AAAA,EACV,SAAS;AAAA,EACT,KAAK;AAAA,EACL,MAAM;AAAA,EACN,SAAS,CAAC,KAAK,UAAU,QAAQ,WAAW,KAAK,GAAG,IAAI,GAAG;AAAA,EAC3D,WAAW;AACf;AACA,IAAM,SAAS;AAAA,EACX,UAAU,WAAS,YAAY,KAAK,KAAK,SAAS;AAAA,EAClD,SAAS;AAAA,EACT,KAAK;AAAA,EACL,QAAQ;AAAA,EACR,MAAM;AAAA,EACN,SAAS,CAAC,KAAK,UAAU,QAAQ,WAAW,KAAK,GAAG,IAAI,GAAG;AAAA,EAC3D,WAAW,UAAQ,aAAa,MAAM,IAAI,IAAI;AAClD;;;AC3BA,IAAM,SAAS;AAAA,EACX;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACJ;;;AChBA,SAASC,aAAY,OAAO;AACxB,SAAO,OAAO,UAAU,YAAY,OAAO,UAAU,KAAK;AAC9D;AACA,IAAM,gBAAgB,CAAC,EAAE,MAAM,MAAM,KAAK,UAAU,KAAK;AACzD,IAAM,cAAc;AAAA,EAChB;AAAA,IACI,UAAU,WAAS,OAAO,UAAU;AAAA,IACpC,SAAS;AAAA,IACT,KAAK;AAAA,IACL,SAAS,SAAO;AAAA,IAChB,WAAW;AAAA,EACf;AAAA,EACA;AAAA,IACI,UAAU,WAAS,SAAS;AAAA,IAC5B,YAAY,MAAM,IAAI,OAAO,IAAI;AAAA,IACjC,SAAS;AAAA,IACT,KAAK;AAAA,IACL,MAAM;AAAA,IACN,SAAS,MAAM;AAAA,IACf,WAAW;AAAA,EACf;AAAA,EACA;AAAA,IACI,UAAU,WAAS,OAAO,UAAU;AAAA,IACpC,SAAS;AAAA,IACT,KAAK;AAAA,IACL,MAAM;AAAA,IACN,SAAS,SAAO,QAAQ;AAAA,IACxB,WAAW;AAAA,EACf;AAAA,EACA;AAAA,IACI,UAAUA;AAAA,IACV,SAAS;AAAA,IACT,KAAK;AAAA,IACL,MAAM;AAAA,IACN,SAAS,CAAC,KAAK,UAAU,EAAE,YAAY,MAAM,cAAc,OAAO,GAAG,IAAI,SAAS,KAAK,EAAE;AAAA,IACzF,WAAW,CAAC,EAAE,MAAM,MAAMA,aAAY,KAAK,IAAI,MAAM,SAAS,IAAI,KAAK,UAAU,KAAK;AAAA,EAC1F;AAAA,EACA;AAAA,IACI,UAAU,WAAS,OAAO,UAAU;AAAA,IACpC,SAAS;AAAA,IACT,KAAK;AAAA,IACL,MAAM;AAAA,IACN,SAAS,SAAO,WAAW,GAAG;AAAA,IAC9B,WAAW;AAAA,EACf;AACJ;AACA,IAAM,YAAY;AAAA,EACd,SAAS;AAAA,EACT,KAAK;AAAA,EACL,MAAM;AAAA,EACN,QAAQ,KAAK,SAAS;AAClB,YAAQ,2BAA2B,KAAK,UAAU,GAAG,CAAC,EAAE;AACxD,WAAO;AAAA,EACX;AACJ;AACA,IAAMC,UAAS,CAAC,KAAK,GAAG,EAAE,OAAO,aAAa,SAAS;;;ACxDvD,IAAM,SAAS;AAAA,EACX,UAAU,WAAS,iBAAiB;AAAA;AAAA,EACpC,SAAS;AAAA,EACT,KAAK;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASL,QAAQ,KAAK,SAAS;AAClB,QAAI,OAAO,SAAS,YAAY;AAE5B,YAAM,MAAM,KAAK,IAAI,QAAQ,WAAW,EAAE,CAAC;AAC3C,YAAM,SAAS,IAAI,WAAW,IAAI,MAAM;AACxC,eAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,EAAE;AAC9B,eAAO,CAAC,IAAI,IAAI,WAAW,CAAC;AAChC,aAAO;AAAA,IACX,OACK;AACD,cAAQ,0FAA0F;AAClG,aAAO;AAAA,IACX;AAAA,EACJ;AAAA,EACA,UAAU,EAAE,SAAS,MAAM,MAAM,GAAG,KAAK,WAAW,aAAa;AAC7D,QAAI,CAAC;AACD,aAAO;AACX,UAAM,MAAM;AACZ,QAAI;AACJ,QAAI,OAAO,SAAS,YAAY;AAC5B,UAAI,IAAI;AACR,eAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,EAAE;AAC9B,aAAK,OAAO,aAAa,IAAI,CAAC,CAAC;AACnC,YAAM,KAAK,CAAC;AAAA,IAChB,OACK;AACD,YAAM,IAAI,MAAM,0FAA0F;AAAA,IAC9G;AACA,aAAS,OAAO,OAAO;AACvB,QAAI,SAAS,OAAO,cAAc;AAC9B,YAAM,YAAY,KAAK,IAAI,IAAI,QAAQ,YAAY,IAAI,OAAO,QAAQ,IAAI,QAAQ,eAAe;AACjG,YAAM,IAAI,KAAK,KAAK,IAAI,SAAS,SAAS;AAC1C,YAAM,QAAQ,IAAI,MAAM,CAAC;AACzB,eAAS,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG,KAAK,WAAW;AAC/C,cAAM,CAAC,IAAI,IAAI,OAAO,GAAG,SAAS;AAAA,MACtC;AACA,YAAM,MAAM,KAAK,SAAS,OAAO,gBAAgB,OAAO,GAAG;AAAA,IAC/D;AACA,WAAO,gBAAgB,EAAE,SAAS,MAAM,OAAO,IAAI,GAAG,KAAK,WAAW,WAAW;AAAA,EACrF;AACJ;;;AClDA,SAAS,aAAaC,MAAK,SAAS;AAChC,MAAI,MAAMA,IAAG,GAAG;AACZ,aAAS,IAAI,GAAG,IAAIA,KAAI,MAAM,QAAQ,EAAE,GAAG;AACvC,UAAI,OAAOA,KAAI,MAAM,CAAC;AACtB,UAAI,OAAO,IAAI;AACX;AAAA,eACK,MAAM,IAAI,GAAG;AAClB,YAAI,KAAK,MAAM,SAAS;AACpB,kBAAQ,gDAAgD;AAC5D,cAAM,OAAO,KAAK,MAAM,CAAC,KAAK,IAAI,KAAK,IAAI,OAAO,IAAI,CAAC;AACvD,YAAI,KAAK;AACL,eAAK,IAAI,gBAAgB,KAAK,IAAI,gBAC5B,GAAG,KAAK,aAAa;AAAA,EAAK,KAAK,IAAI,aAAa,KAChD,KAAK;AACf,YAAI,KAAK,SAAS;AACd,gBAAM,KAAK,KAAK,SAAS,KAAK;AAC9B,aAAG,UAAU,GAAG,UACV,GAAG,KAAK,OAAO;AAAA,EAAK,GAAG,OAAO,KAC9B,KAAK;AAAA,QACf;AACA,eAAO;AAAA,MACX;AACA,MAAAA,KAAI,MAAM,CAAC,IAAI,OAAO,IAAI,IAAI,OAAO,IAAI,KAAK,IAAI;AAAA,IACtD;AAAA,EACJ;AAEI,YAAQ,kCAAkC;AAC9C,SAAOA;AACX;AACA,SAAS,YAAYC,SAAQ,UAAU,KAAK;AACxC,QAAM,EAAE,SAAS,IAAI;AACrB,QAAMC,SAAQ,IAAI,QAAQD,OAAM;AAChC,EAAAC,OAAM,MAAM;AACZ,MAAI,IAAI;AACR,MAAI,YAAY,OAAO,YAAY,OAAO,QAAQ;AAC9C,aAAS,MAAM,UAAU;AACrB,UAAI,OAAO,aAAa;AACpB,aAAK,SAAS,KAAK,UAAU,OAAO,GAAG,GAAG,EAAE;AAChD,UAAI,KAAK;AACT,UAAI,MAAM,QAAQ,EAAE,GAAG;AACnB,YAAI,GAAG,WAAW,GAAG;AACjB,gBAAM,GAAG,CAAC;AACV,kBAAQ,GAAG,CAAC;AAAA,QAChB;AAEI,gBAAM,IAAI,UAAU,gCAAgC,EAAE,EAAE;AAAA,MAChE,WACS,MAAM,cAAc,QAAQ;AACjC,cAAM,OAAO,OAAO,KAAK,EAAE;AAC3B,YAAI,KAAK,WAAW,GAAG;AACnB,gBAAM,KAAK,CAAC;AACZ,kBAAQ,GAAG,GAAG;AAAA,QAClB,OACK;AACD,gBAAM,IAAI,UAAU,oCAAoC,KAAK,MAAM,OAAO;AAAA,QAC9E;AAAA,MACJ,OACK;AACD,cAAM;AAAA,MACV;AACA,MAAAA,OAAM,MAAM,KAAK,WAAW,KAAK,OAAO,GAAG,CAAC;AAAA,IAChD;AACJ,SAAOA;AACX;AACA,IAAM,QAAQ;AAAA,EACV,YAAY;AAAA,EACZ,SAAS;AAAA,EACT,KAAK;AAAA,EACL,SAAS;AAAA,EACT,YAAY;AAChB;;;ACrEA,IAAM,WAAN,MAAM,kBAAiB,QAAQ;AAAA,EAC3B,cAAc;AACV,UAAM;AACN,SAAK,MAAM,QAAQ,UAAU,IAAI,KAAK,IAAI;AAC1C,SAAK,SAAS,QAAQ,UAAU,OAAO,KAAK,IAAI;AAChD,SAAK,MAAM,QAAQ,UAAU,IAAI,KAAK,IAAI;AAC1C,SAAK,MAAM,QAAQ,UAAU,IAAI,KAAK,IAAI;AAC1C,SAAK,MAAM,QAAQ,UAAU,IAAI,KAAK,IAAI;AAC1C,SAAK,MAAM,UAAS;AAAA,EACxB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,OAAO,GAAG,KAAK;AACX,QAAI,CAAC;AACD,aAAO,MAAM,OAAO,CAAC;AACzB,UAAMC,OAAM,oBAAI,IAAI;AACpB,QAAI,2BAAK;AACL,UAAI,SAASA,IAAG;AACpB,eAAW,QAAQ,KAAK,OAAO;AAC3B,UAAI,KAAK;AACT,UAAI,OAAO,IAAI,GAAG;AACd,cAAM,KAAK,KAAK,KAAK,IAAI,GAAG;AAC5B,gBAAQ,KAAK,KAAK,OAAO,KAAK,GAAG;AAAA,MACrC,OACK;AACD,cAAM,KAAK,MAAM,IAAI,GAAG;AAAA,MAC5B;AACA,UAAIA,KAAI,IAAI,GAAG;AACX,cAAM,IAAI,MAAM,8CAA8C;AAClE,MAAAA,KAAI,IAAI,KAAK,KAAK;AAAA,IACtB;AACA,WAAOA;AAAA,EACX;AAAA,EACA,OAAO,KAAKC,SAAQ,UAAU,KAAK;AAC/B,UAAMC,SAAQ,YAAYD,SAAQ,UAAU,GAAG;AAC/C,UAAME,QAAO,IAAI,KAAK;AACtB,IAAAA,MAAK,QAAQD,OAAM;AACnB,WAAOC;AAAA,EACX;AACJ;AACA,SAAS,MAAM;AACf,IAAM,OAAO;AAAA,EACT,YAAY;AAAA,EACZ,UAAU,WAAS,iBAAiB;AAAA,EACpC,WAAW;AAAA,EACX,SAAS;AAAA,EACT,KAAK;AAAA,EACL,QAAQC,MAAK,SAAS;AAClB,UAAMF,SAAQ,aAAaE,MAAK,OAAO;AACvC,UAAM,WAAW,CAAC;AAClB,eAAW,EAAE,IAAI,KAAKF,OAAM,OAAO;AAC/B,UAAI,SAAS,GAAG,GAAG;AACf,YAAI,SAAS,SAAS,IAAI,KAAK,GAAG;AAC9B,kBAAQ,iDAAiD,IAAI,KAAK,EAAE;AAAA,QACxE,OACK;AACD,mBAAS,KAAK,IAAI,KAAK;AAAA,QAC3B;AAAA,MACJ;AAAA,IACJ;AACA,WAAO,OAAO,OAAO,IAAI,SAAS,GAAGA,MAAK;AAAA,EAC9C;AAAA,EACA,YAAY,CAACD,SAAQ,UAAU,QAAQ,SAAS,KAAKA,SAAQ,UAAU,GAAG;AAC9E;;;ACrEA,SAAS,cAAc,EAAE,OAAO,OAAO,GAAG,KAAK;AAC3C,QAAM,UAAU,QAAQ,UAAU;AAClC,MAAI,UAAU,QAAQ,KAAK,KAAK,MAAM;AAClC,WAAO;AACX,SAAO,QAAQ,IAAI,QAAQ,UAAU,IAAI,QAAQ;AACrD;AACA,IAAM,UAAU;AAAA,EACZ,UAAU,WAAS,UAAU;AAAA,EAC7B,SAAS;AAAA,EACT,KAAK;AAAA,EACL,MAAM;AAAA,EACN,SAAS,MAAM,IAAI,OAAO,IAAI;AAAA,EAC9B,WAAW;AACf;AACA,IAAM,WAAW;AAAA,EACb,UAAU,WAAS,UAAU;AAAA,EAC7B,SAAS;AAAA,EACT,KAAK;AAAA,EACL,MAAM;AAAA,EACN,SAAS,MAAM,IAAI,OAAO,KAAK;AAAA,EAC/B,WAAW;AACf;;;ACpBA,IAAMI,YAAW;AAAA,EACb,UAAU,WAAS,OAAO,UAAU;AAAA,EACpC,SAAS;AAAA,EACT,KAAK;AAAA,EACL,MAAM;AAAA,EACN,SAAS,CAAC,QAAQ,IAAI,MAAM,EAAE,EAAE,YAAY,MAAM,QAC5C,MACA,IAAI,CAAC,MAAM,MACP,OAAO,oBACP,OAAO;AAAA,EACjB,WAAW;AACf;AACA,IAAMC,YAAW;AAAA,EACb,UAAU,WAAS,OAAO,UAAU;AAAA,EACpC,SAAS;AAAA,EACT,KAAK;AAAA,EACL,QAAQ;AAAA,EACR,MAAM;AAAA,EACN,SAAS,CAAC,QAAQ,WAAW,IAAI,QAAQ,MAAM,EAAE,CAAC;AAAA,EAClD,UAAU,MAAM;AACZ,UAAM,MAAM,OAAO,KAAK,KAAK;AAC7B,WAAO,SAAS,GAAG,IAAI,IAAI,cAAc,IAAI,gBAAgB,IAAI;AAAA,EACrE;AACJ;AACA,IAAMC,SAAQ;AAAA,EACV,UAAU,WAAS,OAAO,UAAU;AAAA,EACpC,SAAS;AAAA,EACT,KAAK;AAAA,EACL,MAAM;AAAA,EACN,QAAQ,KAAK;AACT,UAAM,OAAO,IAAI,OAAO,WAAW,IAAI,QAAQ,MAAM,EAAE,CAAC,CAAC;AACzD,UAAM,MAAM,IAAI,QAAQ,GAAG;AAC3B,QAAI,QAAQ,IAAI;AACZ,YAAM,IAAI,IAAI,UAAU,MAAM,CAAC,EAAE,QAAQ,MAAM,EAAE;AACjD,UAAI,EAAE,EAAE,SAAS,CAAC,MAAM;AACpB,aAAK,oBAAoB,EAAE;AAAA,IACnC;AACA,WAAO;AAAA,EACX;AAAA,EACA,WAAW;AACf;;;ACzCA,IAAMC,eAAc,CAAC,UAAU,OAAO,UAAU,YAAY,OAAO,UAAU,KAAK;AAClF,SAASC,YAAW,KAAK,QAAQ,OAAO,EAAE,YAAY,GAAG;AACrD,QAAM,OAAO,IAAI,CAAC;AAClB,MAAI,SAAS,OAAO,SAAS;AACzB,cAAU;AACd,QAAM,IAAI,UAAU,MAAM,EAAE,QAAQ,MAAM,EAAE;AAC5C,MAAI,aAAa;AACb,YAAQ,OAAO;AAAA,MACX,KAAK;AACD,cAAM,KAAK,GAAG;AACd;AAAA,MACJ,KAAK;AACD,cAAM,KAAK,GAAG;AACd;AAAA,MACJ,KAAK;AACD,cAAM,KAAK,GAAG;AACd;AAAA,IACR;AACA,UAAMC,KAAI,OAAO,GAAG;AACpB,WAAO,SAAS,MAAM,OAAO,EAAE,IAAIA,KAAIA;AAAA,EAC3C;AACA,QAAM,IAAI,SAAS,KAAK,KAAK;AAC7B,SAAO,SAAS,MAAM,KAAK,IAAI;AACnC;AACA,SAASC,cAAa,MAAM,OAAO,QAAQ;AACvC,QAAM,EAAE,MAAM,IAAI;AAClB,MAAIH,aAAY,KAAK,GAAG;AACpB,UAAM,MAAM,MAAM,SAAS,KAAK;AAChC,WAAO,QAAQ,IAAI,MAAM,SAAS,IAAI,OAAO,CAAC,IAAI,SAAS;AAAA,EAC/D;AACA,SAAO,gBAAgB,IAAI;AAC/B;AACA,IAAM,SAAS;AAAA,EACX,UAAUA;AAAA,EACV,SAAS;AAAA,EACT,KAAK;AAAA,EACL,QAAQ;AAAA,EACR,MAAM;AAAA,EACN,SAAS,CAAC,KAAK,UAAU,QAAQC,YAAW,KAAK,GAAG,GAAG,GAAG;AAAA,EAC1D,WAAW,UAAQE,cAAa,MAAM,GAAG,IAAI;AACjD;AACA,IAAMC,UAAS;AAAA,EACX,UAAUJ;AAAA,EACV,SAAS;AAAA,EACT,KAAK;AAAA,EACL,QAAQ;AAAA,EACR,MAAM;AAAA,EACN,SAAS,CAAC,KAAK,UAAU,QAAQC,YAAW,KAAK,GAAG,GAAG,GAAG;AAAA,EAC1D,WAAW,UAAQE,cAAa,MAAM,GAAG,GAAG;AAChD;AACA,IAAME,OAAM;AAAA,EACR,UAAUL;AAAA,EACV,SAAS;AAAA,EACT,KAAK;AAAA,EACL,MAAM;AAAA,EACN,SAAS,CAAC,KAAK,UAAU,QAAQC,YAAW,KAAK,GAAG,IAAI,GAAG;AAAA,EAC3D,WAAW;AACf;AACA,IAAMK,UAAS;AAAA,EACX,UAAUN;AAAA,EACV,SAAS;AAAA,EACT,KAAK;AAAA,EACL,QAAQ;AAAA,EACR,MAAM;AAAA,EACN,SAAS,CAAC,KAAK,UAAU,QAAQC,YAAW,KAAK,GAAG,IAAI,GAAG;AAAA,EAC3D,WAAW,UAAQE,cAAa,MAAM,IAAI,IAAI;AAClD;;;AChEA,IAAM,UAAN,MAAM,iBAAgB,QAAQ;AAAA,EAC1B,YAAYI,SAAQ;AAChB,UAAMA,OAAM;AACZ,SAAK,MAAM,SAAQ;AAAA,EACvB;AAAA,EACA,IAAI,KAAK;AACL,QAAI;AACJ,QAAI,OAAO,GAAG;AACV,aAAO;AAAA,aACF,OACL,OAAO,QAAQ,YACf,SAAS,OACT,WAAW,OACX,IAAI,UAAU;AACd,aAAO,IAAI,KAAK,IAAI,KAAK,IAAI;AAAA;AAE7B,aAAO,IAAI,KAAK,KAAK,IAAI;AAC7B,UAAM,OAAO,SAAS,KAAK,OAAO,KAAK,GAAG;AAC1C,QAAI,CAAC;AACD,WAAK,MAAM,KAAK,IAAI;AAAA,EAC5B;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,KAAK,UAAU;AACf,UAAM,OAAO,SAAS,KAAK,OAAO,GAAG;AACrC,WAAO,CAAC,YAAY,OAAO,IAAI,IACzB,SAAS,KAAK,GAAG,IACb,KAAK,IAAI,QACT,KAAK,MACT;AAAA,EACV;AAAA,EACA,IAAI,KAAK,OAAO;AACZ,QAAI,OAAO,UAAU;AACjB,YAAM,IAAI,MAAM,iEAAiE,OAAO,KAAK,EAAE;AACnG,UAAM,OAAO,SAAS,KAAK,OAAO,GAAG;AACrC,QAAI,QAAQ,CAAC,OAAO;AAChB,WAAK,MAAM,OAAO,KAAK,MAAM,QAAQ,IAAI,GAAG,CAAC;AAAA,IACjD,WACS,CAAC,QAAQ,OAAO;AACrB,WAAK,MAAM,KAAK,IAAI,KAAK,GAAG,CAAC;AAAA,IACjC;AAAA,EACJ;AAAA,EACA,OAAO,GAAG,KAAK;AACX,WAAO,MAAM,OAAO,GAAG,KAAK,GAAG;AAAA,EACnC;AAAA,EACA,SAAS,KAAK,WAAW,aAAa;AAClC,QAAI,CAAC;AACD,aAAO,KAAK,UAAU,IAAI;AAC9B,QAAI,KAAK,iBAAiB,IAAI;AAC1B,aAAO,MAAM,SAAS,OAAO,OAAO,CAAC,GAAG,KAAK,EAAE,eAAe,KAAK,CAAC,GAAG,WAAW,WAAW;AAAA;AAE7F,YAAM,IAAI,MAAM,qCAAqC;AAAA,EAC7D;AAAA,EACA,OAAO,KAAKA,SAAQ,UAAU,KAAK;AAC/B,UAAM,EAAE,SAAS,IAAI;AACrB,UAAMC,OAAM,IAAI,KAAKD,OAAM;AAC3B,QAAI,YAAY,OAAO,YAAY,OAAO,QAAQ;AAC9C,eAAS,SAAS,UAAU;AACxB,YAAI,OAAO,aAAa;AACpB,kBAAQ,SAAS,KAAK,UAAU,OAAO,KAAK;AAChD,QAAAC,KAAI,MAAM,KAAK,WAAW,OAAO,MAAM,GAAG,CAAC;AAAA,MAC/C;AACJ,WAAOA;AAAA,EACX;AACJ;AACA,QAAQ,MAAM;AACd,IAAM,MAAM;AAAA,EACR,YAAY;AAAA,EACZ,UAAU,WAAS,iBAAiB;AAAA,EACpC,WAAW;AAAA,EACX,SAAS;AAAA,EACT,KAAK;AAAA,EACL,YAAY,CAACD,SAAQ,UAAU,QAAQ,QAAQ,KAAKA,SAAQ,UAAU,GAAG;AAAA,EACzE,QAAQE,MAAK,SAAS;AAClB,QAAI,MAAMA,IAAG,GAAG;AACZ,UAAIA,KAAI,iBAAiB,IAAI;AACzB,eAAO,OAAO,OAAO,IAAI,QAAQ,GAAGA,IAAG;AAAA;AAEvC,gBAAQ,qCAAqC;AAAA,IACrD;AAEI,cAAQ,iCAAiC;AAC7C,WAAOA;AAAA,EACX;AACJ;;;ACvFA,SAAS,iBAAiB,KAAK,UAAU;AACrC,QAAM,OAAO,IAAI,CAAC;AAClB,QAAM,QAAQ,SAAS,OAAO,SAAS,MAAM,IAAI,UAAU,CAAC,IAAI;AAChE,QAAM,MAAM,CAAC,MAAM,WAAW,OAAO,CAAC,IAAI,OAAO,CAAC;AAClD,QAAM,MAAM,MACP,QAAQ,MAAM,EAAE,EAChB,MAAM,GAAG,EACT,OAAO,CAACC,MAAK,MAAMA,OAAM,IAAI,EAAE,IAAI,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC;AACtD,SAAQ,SAAS,MAAM,IAAI,EAAE,IAAI,MAAM;AAC3C;AAMA,SAAS,qBAAqB,MAAM;AAChC,MAAI,EAAE,MAAM,IAAI;AAChB,MAAI,MAAM,CAAC,MAAM;AACjB,MAAI,OAAO,UAAU;AACjB,UAAM,OAAK,OAAO,CAAC;AAAA,WACd,MAAM,KAAK,KAAK,CAAC,SAAS,KAAK;AACpC,WAAO,gBAAgB,IAAI;AAC/B,MAAI,OAAO;AACX,MAAI,QAAQ,GAAG;AACX,WAAO;AACP,aAAS,IAAI,EAAE;AAAA,EACnB;AACA,QAAM,MAAM,IAAI,EAAE;AAClB,QAAM,QAAQ,CAAC,QAAQ,GAAG;AAC1B,MAAI,QAAQ,IAAI;AACZ,UAAM,QAAQ,CAAC;AAAA,EACnB,OACK;AACD,aAAS,QAAQ,MAAM,CAAC,KAAK;AAC7B,UAAM,QAAQ,QAAQ,GAAG;AACzB,QAAI,SAAS,IAAI;AACb,eAAS,QAAQ,MAAM,CAAC,KAAK;AAC7B,YAAM,QAAQ,KAAK;AAAA,IACvB;AAAA,EACJ;AACA,SAAQ,OACJ,MACK,IAAI,OAAK,OAAO,CAAC,EAAE,SAAS,GAAG,GAAG,CAAC,EACnC,KAAK,GAAG,EACR,QAAQ,cAAc,EAAE;AAErC;AACA,IAAM,UAAU;AAAA,EACZ,UAAU,WAAS,OAAO,UAAU,YAAY,OAAO,UAAU,KAAK;AAAA,EACtE,SAAS;AAAA,EACT,KAAK;AAAA,EACL,QAAQ;AAAA,EACR,MAAM;AAAA,EACN,SAAS,CAAC,KAAK,UAAU,EAAE,YAAY,MAAM,iBAAiB,KAAK,WAAW;AAAA,EAC9E,WAAW;AACf;AACA,IAAM,YAAY;AAAA,EACd,UAAU,WAAS,OAAO,UAAU;AAAA,EACpC,SAAS;AAAA,EACT,KAAK;AAAA,EACL,QAAQ;AAAA,EACR,MAAM;AAAA,EACN,SAAS,SAAO,iBAAiB,KAAK,KAAK;AAAA,EAC3C,WAAW;AACf;AACA,IAAM,YAAY;AAAA,EACd,UAAU,WAAS,iBAAiB;AAAA,EACpC,SAAS;AAAA,EACT,KAAK;AAAA;AAAA;AAAA;AAAA,EAIL,MAAM,OAAO,2JAKJ;AAAA,EACT,QAAQ,KAAK;AACT,UAAM,QAAQ,IAAI,MAAM,UAAU,IAAI;AACtC,QAAI,CAAC;AACD,YAAM,IAAI,MAAM,sDAAsD;AAC1E,UAAM,CAAC,EAAE,MAAM,OAAO,KAAK,MAAM,QAAQ,MAAM,IAAI,MAAM,IAAI,MAAM;AACnE,UAAM,WAAW,MAAM,CAAC,IAAI,QAAQ,MAAM,CAAC,IAAI,MAAM,OAAO,GAAG,CAAC,CAAC,IAAI;AACrE,QAAI,OAAO,KAAK,IAAI,MAAM,QAAQ,GAAG,KAAK,QAAQ,GAAG,UAAU,GAAG,UAAU,GAAG,QAAQ;AACvF,UAAM,KAAK,MAAM,CAAC;AAClB,QAAI,MAAM,OAAO,KAAK;AAClB,UAAI,IAAI,iBAAiB,IAAI,KAAK;AAClC,UAAI,KAAK,IAAI,CAAC,IAAI;AACd,aAAK;AACT,cAAQ,MAAQ;AAAA,IACpB;AACA,WAAO,IAAI,KAAK,IAAI;AAAA,EACxB;AAAA,EACA,WAAW,CAAC,EAAE,MAAM,OAAM,+BAAO,cAAc,QAAQ,uBAAuB,QAAO;AACzF;;;ACpFA,IAAMC,UAAS;AAAA,EACX;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACAC;AAAA,EACAC;AAAA,EACAC;AAAA,EACAC;AAAA,EACAC;AAAA,EACAC;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACJ;;;ACnBA,IAAM,UAAU,oBAAI,IAAI;AAAA,EACpB,CAAC,QAAQ,MAAM;AAAA,EACf,CAAC,YAAY,CAAC,KAAK,KAAK,MAAM,CAAC;AAAA,EAC/B,CAAC,QAAQC,OAAQ;AAAA,EACjB,CAAC,UAAUA,OAAQ;AAAA,EACnB,CAAC,YAAYA,OAAQ;AACzB,CAAC;AACD,IAAM,aAAa;AAAA,EACf;AAAA,EACA,MAAM;AAAA,EACN;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,MAAM;AAAA,EACN;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACJ;AACA,IAAM,gBAAgB;AAAA,EAClB,4BAA4B;AAAA,EAC5B,2BAA2B;AAAA,EAC3B,0BAA0B;AAAA,EAC1B,2BAA2B;AAAA,EAC3B,yBAAyB;AAAA,EACzB,+BAA+B;AACnC;AACA,SAAS,QAAQ,YAAY,YAAY,aAAa;AAClD,QAAM,aAAa,QAAQ,IAAI,UAAU;AACzC,MAAI,cAAc,CAAC,YAAY;AAC3B,WAAO,eAAe,CAAC,WAAW,SAAS,KAAK,IAC1C,WAAW,OAAO,KAAK,IACvB,WAAW,MAAM;AAAA,EAC3B;AACA,MAAI,OAAO;AACX,MAAI,CAAC,MAAM;AACP,QAAI,MAAM,QAAQ,UAAU;AACxB,aAAO,CAAC;AAAA,SACP;AACD,YAAM,OAAO,MAAM,KAAK,QAAQ,KAAK,CAAC,EACjC,OAAO,SAAO,QAAQ,QAAQ,EAC9B,IAAI,SAAO,KAAK,UAAU,GAAG,CAAC,EAC9B,KAAK,IAAI;AACd,YAAM,IAAI,MAAM,mBAAmB,UAAU,iBAAiB,IAAI,6BAA6B;AAAA,IACnG;AAAA,EACJ;AACA,MAAI,MAAM,QAAQ,UAAU,GAAG;AAC3B,eAAW,OAAO;AACd,aAAO,KAAK,OAAO,GAAG;AAAA,EAC9B,WACS,OAAO,eAAe,YAAY;AACvC,WAAO,WAAW,KAAK,MAAM,CAAC;AAAA,EAClC;AACA,MAAI;AACA,WAAO,KAAK,OAAO,KAAK;AAC5B,SAAO,KAAK,OAAO,CAACC,OAAM,QAAQ;AAC9B,UAAM,SAAS,OAAO,QAAQ,WAAW,WAAW,GAAG,IAAI;AAC3D,QAAI,CAAC,QAAQ;AACT,YAAM,UAAU,KAAK,UAAU,GAAG;AAClC,YAAM,OAAO,OAAO,KAAK,UAAU,EAC9B,IAAI,SAAO,KAAK,UAAU,GAAG,CAAC,EAC9B,KAAK,IAAI;AACd,YAAM,IAAI,MAAM,sBAAsB,OAAO,gBAAgB,IAAI,EAAE;AAAA,IACvE;AACA,QAAI,CAACA,MAAK,SAAS,MAAM;AACrB,MAAAA,MAAK,KAAK,MAAM;AACpB,WAAOA;AAAA,EACX,GAAG,CAAC,CAAC;AACT;;;ACvFA,IAAM,sBAAsB,CAAC,GAAG,MAAM,EAAE,MAAM,EAAE,MAAM,KAAK,EAAE,MAAM,EAAE,MAAM,IAAI;AAC/E,IAAM,SAAN,MAAM,QAAO;AAAA,EACT,YAAY,EAAE,QAAQ,YAAY,OAAAC,QAAO,kBAAkB,QAAAC,SAAQ,gBAAgB,iBAAiB,GAAG;AACnG,SAAK,SAAS,MAAM,QAAQ,MAAM,IAC5B,QAAQ,QAAQ,QAAQ,IACxB,SACI,QAAQ,MAAM,MAAM,IACpB;AACV,SAAK,OAAQ,OAAOA,YAAW,YAAYA,WAAW;AACtD,SAAK,YAAY,mBAAmB,gBAAgB,CAAC;AACrD,SAAK,OAAO,QAAQ,YAAY,KAAK,MAAMD,MAAK;AAChD,SAAK,kBAAkB,oBAAoB;AAC3C,WAAO,eAAe,MAAM,KAAK,EAAE,OAAO,IAAI,CAAC;AAC/C,WAAO,eAAe,MAAM,QAAQ,EAAE,OAAO,OAAO,CAAC;AACrD,WAAO,eAAe,MAAM,KAAK,EAAE,OAAO,IAAI,CAAC;AAE/C,SAAK,iBACD,OAAO,mBAAmB,aACpB,iBACA,mBAAmB,OACf,sBACA;AAAA,EAClB;AAAA,EACA,QAAQ;AACJ,UAAM,OAAO,OAAO,OAAO,QAAO,WAAW,OAAO,0BAA0B,IAAI,CAAC;AACnF,SAAK,OAAO,KAAK,KAAK,MAAM;AAC5B,WAAO;AAAA,EACX;AACJ;;;AC9BA,SAAS,kBAAkB,KAAK,SAAS;AAJzC;AAKI,QAAM,QAAQ,CAAC;AACf,MAAI,gBAAgB,QAAQ,eAAe;AAC3C,MAAI,QAAQ,eAAe,SAAS,IAAI,YAAY;AAChD,UAAM,MAAM,IAAI,WAAW,SAAS,GAAG;AACvC,QAAI,KAAK;AACL,YAAM,KAAK,GAAG;AACd,sBAAgB;AAAA,IACpB,WACS,IAAI,WAAW;AACpB,sBAAgB;AAAA,EACxB;AACA,MAAI;AACA,UAAM,KAAK,KAAK;AACpB,QAAM,MAAM,uBAAuB,KAAK,OAAO;AAC/C,QAAM,EAAE,cAAc,IAAI,IAAI;AAC9B,MAAI,IAAI,eAAe;AACnB,QAAI,MAAM,WAAW;AACjB,YAAM,QAAQ,EAAE;AACpB,UAAM,KAAK,cAAc,IAAI,aAAa;AAC1C,UAAM,QAAQ,cAAc,IAAI,EAAE,CAAC;AAAA,EACvC;AACA,MAAI,YAAY;AAChB,MAAI,iBAAiB;AACrB,MAAI,IAAI,UAAU;AACd,QAAI,OAAO,IAAI,QAAQ,GAAG;AACtB,UAAI,IAAI,SAAS,eAAe;AAC5B,cAAM,KAAK,EAAE;AACjB,UAAI,IAAI,SAAS,eAAe;AAC5B,cAAM,KAAK,cAAc,IAAI,SAAS,aAAa;AACnD,cAAM,KAAK,cAAc,IAAI,EAAE,CAAC;AAAA,MACpC;AAEA,UAAI,mBAAmB,CAAC,CAAC,IAAI;AAC7B,uBAAiB,IAAI,SAAS;AAAA,IAClC;AACA,UAAM,cAAc,iBAAiB,SAAY,MAAO,YAAY;AACpE,QAAI,OAAO,UAAU,IAAI,UAAU,KAAK,MAAO,iBAAiB,MAAO,WAAW;AAClF,QAAI;AACA,cAAQ,YAAY,MAAM,IAAI,cAAc,cAAc,CAAC;AAC/D,SAAK,KAAK,CAAC,MAAM,OAAO,KAAK,CAAC,MAAM,QAChC,MAAM,MAAM,SAAS,CAAC,MAAM,OAAO;AAGnC,YAAM,MAAM,SAAS,CAAC,IAAI,OAAO,IAAI;AAAA,IACzC;AAEI,YAAM,KAAK,IAAI;AAAA,EACvB,OACK;AACD,UAAM,KAAK,UAAU,IAAI,UAAU,GAAG,CAAC;AAAA,EAC3C;AACA,OAAI,SAAI,eAAJ,mBAAgB,QAAQ;AACxB,QAAI,IAAI,SAAS;AACb,YAAM,KAAK,cAAc,IAAI,OAAO;AACpC,UAAI,GAAG,SAAS,IAAI,GAAG;AACnB,cAAM,KAAK,KAAK;AAChB,cAAM,KAAK,cAAc,IAAI,EAAE,CAAC;AAAA,MACpC,OACK;AACD,cAAM,KAAK,OAAO,EAAE,EAAE;AAAA,MAC1B;AAAA,IACJ,OACK;AACD,YAAM,KAAK,KAAK;AAAA,IACpB;AAAA,EACJ,OACK;AACD,QAAI,KAAK,IAAI;AACb,QAAI,MAAM;AACN,WAAK,GAAG,QAAQ,QAAQ,EAAE;AAC9B,QAAI,IAAI;AACJ,WAAK,CAAC,aAAa,mBAAmB,MAAM,MAAM,SAAS,CAAC,MAAM;AAC9D,cAAM,KAAK,EAAE;AACjB,YAAM,KAAK,cAAc,cAAc,EAAE,GAAG,EAAE,CAAC;AAAA,IACnD;AAAA,EACJ;AACA,SAAO,MAAM,KAAK,IAAI,IAAI;AAC9B;;;ACtEA,IAAM,WAAN,MAAM,UAAS;AAAA,EACX,YAAY,OAAO,UAAU,SAAS;AAElC,SAAK,gBAAgB;AAErB,SAAK,UAAU;AAEf,SAAK,SAAS,CAAC;AAEf,SAAK,WAAW,CAAC;AACjB,WAAO,eAAe,MAAM,WAAW,EAAE,OAAO,IAAI,CAAC;AACrD,QAAI,YAAY;AAChB,QAAI,OAAO,aAAa,cAAc,MAAM,QAAQ,QAAQ,GAAG;AAC3D,kBAAY;AAAA,IAChB,WACS,YAAY,UAAa,UAAU;AACxC,gBAAU;AACV,iBAAW;AAAA,IACf;AACA,UAAM,MAAM,OAAO,OAAO;AAAA,MACtB,aAAa;AAAA,MACb,kBAAkB;AAAA,MAClB,UAAU;AAAA,MACV,cAAc;AAAA,MACd,QAAQ;AAAA,MACR,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,SAAS;AAAA,IACb,GAAG,OAAO;AACV,SAAK,UAAU;AACf,QAAI,EAAE,QAAQ,IAAI;AAClB,QAAI,mCAAS,aAAa;AACtB,WAAK,aAAa,QAAQ,YAAY,WAAW;AACjD,UAAI,KAAK,WAAW,KAAK;AACrB,kBAAU,KAAK,WAAW,KAAK;AAAA,IACvC;AAEI,WAAK,aAAa,IAAI,WAAW,EAAE,QAAQ,CAAC;AAChD,SAAK,UAAU,SAAS,OAAO;AAE/B,SAAK,WACD,UAAU,SAAY,OAAO,KAAK,WAAW,OAAO,WAAW,OAAO;AAAA,EAC9E;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,QAAQ;AACJ,UAAM,OAAO,OAAO,OAAO,UAAS,WAAW;AAAA,MAC3C,CAAC,SAAS,GAAG,EAAE,OAAO,IAAI;AAAA,IAC9B,CAAC;AACD,SAAK,gBAAgB,KAAK;AAC1B,SAAK,UAAU,KAAK;AACpB,SAAK,SAAS,KAAK,OAAO,MAAM;AAChC,SAAK,WAAW,KAAK,SAAS,MAAM;AACpC,SAAK,UAAU,OAAO,OAAO,CAAC,GAAG,KAAK,OAAO;AAC7C,QAAI,KAAK;AACL,WAAK,aAAa,KAAK,WAAW,MAAM;AAC5C,SAAK,SAAS,KAAK,OAAO,MAAM;AAEhC,SAAK,WAAW,OAAO,KAAK,QAAQ,IAC9B,KAAK,SAAS,MAAM,KAAK,MAAM,IAC/B,KAAK;AACX,QAAI,KAAK;AACL,WAAK,QAAQ,KAAK,MAAM,MAAM;AAClC,WAAO;AAAA,EACX;AAAA;AAAA,EAEA,IAAI,OAAO;AACP,QAAI,iBAAiB,KAAK,QAAQ;AAC9B,WAAK,SAAS,IAAI,KAAK;AAAA,EAC/B;AAAA;AAAA,EAEA,MAAM,MAAM,OAAO;AACf,QAAI,iBAAiB,KAAK,QAAQ;AAC9B,WAAK,SAAS,MAAM,MAAM,KAAK;AAAA,EACvC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,YAAY,MAAM,MAAM;AACpB,QAAI,CAAC,KAAK,QAAQ;AACd,YAAM,OAAO,YAAY,IAAI;AAC7B,WAAK;AAAA,MAED,CAAC,QAAQ,KAAK,IAAI,IAAI,IAAI,cAAc,QAAQ,KAAK,IAAI,IAAI;AAAA,IACrE;AACA,WAAO,IAAI,MAAM,KAAK,MAAM;AAAA,EAChC;AAAA,EACA,WAAW,OAAO,UAAU,SAAS;AACjC,QAAI,YAAY;AAChB,QAAI,OAAO,aAAa,YAAY;AAChC,cAAQ,SAAS,KAAK,EAAE,IAAI,MAAM,GAAG,IAAI,KAAK;AAC9C,kBAAY;AAAA,IAChB,WACS,MAAM,QAAQ,QAAQ,GAAG;AAC9B,YAAM,WAAW,CAAC,MAAM,OAAO,MAAM,YAAY,aAAa,UAAU,aAAa;AACrF,YAAM,QAAQ,SAAS,OAAO,QAAQ,EAAE,IAAI,MAAM;AAClD,UAAI,MAAM,SAAS;AACf,mBAAW,SAAS,OAAO,KAAK;AACpC,kBAAY;AAAA,IAChB,WACS,YAAY,UAAa,UAAU;AACxC,gBAAU;AACV,iBAAW;AAAA,IACf;AACA,UAAM,EAAE,uBAAuB,cAAc,MAAM,eAAe,UAAU,IAAI,IAAI,WAAW,CAAC;AAChG,UAAM,EAAE,UAAU,YAAY,cAAc,IAAI;AAAA,MAAkB;AAAA;AAAA,MAElE,gBAAgB;AAAA,IAAG;AACnB,UAAM,MAAM;AAAA,MACR,uBAAuB,yBAAyB;AAAA,MAChD,eAAe,iBAAiB;AAAA,MAChC;AAAA,MACA;AAAA,MACA,UAAU;AAAA,MACV,QAAQ,KAAK;AAAA,MACb;AAAA,IACJ;AACA,UAAM,OAAO,WAAW,OAAO,KAAK,GAAG;AACvC,QAAI,QAAQ,aAAa,IAAI;AACzB,WAAK,OAAO;AAChB,eAAW;AACX,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,WAAW,KAAK,OAAO,UAAU,CAAC,GAAG;AACjC,UAAM,IAAI,KAAK,WAAW,KAAK,MAAM,OAAO;AAC5C,UAAM,IAAI,KAAK,WAAW,OAAO,MAAM,OAAO;AAC9C,WAAO,IAAI,KAAK,GAAG,CAAC;AAAA,EACxB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,OAAO,KAAK;AACR,WAAO,iBAAiB,KAAK,QAAQ,IAAI,KAAK,SAAS,OAAO,GAAG,IAAI;AAAA,EACzE;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,SAAS,MAAM;AACX,QAAI,YAAY,IAAI,GAAG;AACnB,UAAI,KAAK,YAAY;AACjB,eAAO;AAEX,WAAK,WAAW;AAChB,aAAO;AAAA,IACX;AACA,WAAO,iBAAiB,KAAK,QAAQ,IAC/B,KAAK,SAAS,SAAS,IAAI,IAC3B;AAAA,EACV;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,IAAI,KAAK,YAAY;AACjB,WAAO,aAAa,KAAK,QAAQ,IAC3B,KAAK,SAAS,IAAI,KAAK,UAAU,IACjC;AAAA,EACV;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,MAAM,MAAM,YAAY;AACpB,QAAI,YAAY,IAAI;AAChB,aAAO,CAAC,cAAc,SAAS,KAAK,QAAQ,IACtC,KAAK,SAAS,QACd,KAAK;AACf,WAAO,aAAa,KAAK,QAAQ,IAC3B,KAAK,SAAS,MAAM,MAAM,UAAU,IACpC;AAAA,EACV;AAAA;AAAA;AAAA;AAAA,EAIA,IAAI,KAAK;AACL,WAAO,aAAa,KAAK,QAAQ,IAAI,KAAK,SAAS,IAAI,GAAG,IAAI;AAAA,EAClE;AAAA;AAAA;AAAA;AAAA,EAIA,MAAM,MAAM;AACR,QAAI,YAAY,IAAI;AAChB,aAAO,KAAK,aAAa;AAC7B,WAAO,aAAa,KAAK,QAAQ,IAAI,KAAK,SAAS,MAAM,IAAI,IAAI;AAAA,EACrE;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,KAAK,OAAO;AACZ,QAAI,KAAK,YAAY,MAAM;AAEvB,WAAK,WAAW,mBAAmB,KAAK,QAAQ,CAAC,GAAG,GAAG,KAAK;AAAA,IAChE,WACS,iBAAiB,KAAK,QAAQ,GAAG;AACtC,WAAK,SAAS,IAAI,KAAK,KAAK;AAAA,IAChC;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,MAAM,OAAO;AACf,QAAI,YAAY,IAAI,GAAG;AAEnB,WAAK,WAAW;AAAA,IACpB,WACS,KAAK,YAAY,MAAM;AAE5B,WAAK,WAAW,mBAAmB,KAAK,QAAQ,MAAM,KAAK,IAAI,GAAG,KAAK;AAAA,IAC3E,WACS,iBAAiB,KAAK,QAAQ,GAAG;AACtC,WAAK,SAAS,MAAM,MAAM,KAAK;AAAA,IACnC;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,UAAU,SAAS,UAAU,CAAC,GAAG;AAC7B,QAAI,OAAO,YAAY;AACnB,gBAAU,OAAO,OAAO;AAC5B,QAAI;AACJ,YAAQ,SAAS;AAAA,MACb,KAAK;AACD,YAAI,KAAK;AACL,eAAK,WAAW,KAAK,UAAU;AAAA;AAE/B,eAAK,aAAa,IAAI,WAAW,EAAE,SAAS,MAAM,CAAC;AACvD,cAAM,EAAE,kBAAkB,OAAO,QAAQ,WAAW;AACpD;AAAA,MACJ,KAAK;AAAA,MACL,KAAK;AACD,YAAI,KAAK;AACL,eAAK,WAAW,KAAK,UAAU;AAAA;AAE/B,eAAK,aAAa,IAAI,WAAW,EAAE,QAAQ,CAAC;AAChD,cAAM,EAAE,kBAAkB,MAAM,QAAQ,OAAO;AAC/C;AAAA,MACJ,KAAK;AACD,YAAI,KAAK;AACL,iBAAO,KAAK;AAChB,cAAM;AACN;AAAA,MACJ,SAAS;AACL,cAAM,KAAK,KAAK,UAAU,OAAO;AACjC,cAAM,IAAI,MAAM,+DAA+D,EAAE,EAAE;AAAA,MACvF;AAAA,IACJ;AAEA,QAAI,QAAQ,kBAAkB;AAC1B,WAAK,SAAS,QAAQ;AAAA,aACjB;AACL,WAAK,SAAS,IAAI,OAAO,OAAO,OAAO,KAAK,OAAO,CAAC;AAAA;AAEpD,YAAM,IAAI,MAAM,qEAAqE;AAAA,EAC7F;AAAA;AAAA,EAEA,KAAK,EAAE,MAAM,SAAS,UAAU,eAAe,UAAU,QAAQ,IAAI,CAAC,GAAG;AACrE,UAAM,MAAM;AAAA,MACR,SAAS,oBAAI,IAAI;AAAA,MACjB,KAAK;AAAA,MACL,MAAM,CAAC;AAAA,MACP,UAAU,aAAa;AAAA,MACvB,cAAc;AAAA,MACd,eAAe,OAAO,kBAAkB,WAAW,gBAAgB;AAAA,IACvE;AACA,UAAM,MAAM,KAAK,KAAK,UAAU,WAAW,IAAI,GAAG;AAClD,QAAI,OAAO,aAAa;AACpB,iBAAW,EAAE,OAAO,KAAAE,KAAI,KAAK,IAAI,QAAQ,OAAO;AAC5C,iBAASA,MAAK,KAAK;AAC3B,WAAO,OAAO,YAAY,aACpB,aAAa,SAAS,EAAE,IAAI,IAAI,GAAG,IAAI,GAAG,IAC1C;AAAA,EACV;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,OAAO,SAAS,UAAU;AACtB,WAAO,KAAK,KAAK,EAAE,MAAM,MAAM,SAAS,UAAU,OAAO,SAAS,CAAC;AAAA,EACvE;AAAA;AAAA,EAEA,SAAS,UAAU,CAAC,GAAG;AACnB,QAAI,KAAK,OAAO,SAAS;AACrB,YAAM,IAAI,MAAM,4CAA4C;AAChE,QAAI,YAAY,YACX,CAAC,OAAO,UAAU,QAAQ,MAAM,KAAK,OAAO,QAAQ,MAAM,KAAK,IAAI;AACpE,YAAM,IAAI,KAAK,UAAU,QAAQ,MAAM;AACvC,YAAM,IAAI,MAAM,mDAAmD,CAAC,EAAE;AAAA,IAC1E;AACA,WAAO,kBAAkB,MAAM,OAAO;AAAA,EAC1C;AACJ;AACA,SAAS,iBAAiB,UAAU;AAChC,MAAI,aAAa,QAAQ;AACrB,WAAO;AACX,QAAM,IAAI,MAAM,iDAAiD;AACrE;;;AC5UA,IAAM,YAAN,cAAwB,MAAM;AAAA,EAC1B,YAAY,MAAM,KAAK,MAAM,SAAS;AAClC,UAAM;AACN,SAAK,OAAO;AACZ,SAAK,OAAO;AACZ,SAAK,UAAU;AACf,SAAK,MAAM;AAAA,EACf;AACJ;AACA,IAAM,iBAAN,cAA6B,UAAU;AAAA,EACnC,YAAY,KAAK,MAAM,SAAS;AAC5B,UAAM,kBAAkB,KAAK,MAAM,OAAO;AAAA,EAC9C;AACJ;AACA,IAAM,cAAN,cAA0B,UAAU;AAAA,EAChC,YAAY,KAAK,MAAM,SAAS;AAC5B,UAAM,eAAe,KAAK,MAAM,OAAO;AAAA,EAC3C;AACJ;AACA,IAAM,gBAAgB,CAAC,KAAK,OAAO,CAAC,UAAU;AAC1C,MAAI,MAAM,IAAI,CAAC,MAAM;AACjB;AACJ,QAAM,UAAU,MAAM,IAAI,IAAI,SAAO,GAAG,QAAQ,GAAG,CAAC;AACpD,QAAM,EAAE,MAAM,IAAI,IAAI,MAAM,QAAQ,CAAC;AACrC,QAAM,WAAW,YAAY,IAAI,YAAY,GAAG;AAChD,MAAI,KAAK,MAAM;AACf,MAAI,UAAU,IACT,UAAU,GAAG,WAAW,OAAO,CAAC,GAAG,GAAG,WAAW,IAAI,CAAC,EACtD,QAAQ,YAAY,EAAE;AAE3B,MAAI,MAAM,MAAM,QAAQ,SAAS,IAAI;AACjC,UAAM,YAAY,KAAK,IAAI,KAAK,IAAI,QAAQ,SAAS,EAAE;AACvD,cAAU,MAAM,QAAQ,UAAU,SAAS;AAC3C,UAAM,YAAY;AAAA,EACtB;AACA,MAAI,QAAQ,SAAS;AACjB,cAAU,QAAQ,UAAU,GAAG,EAAE,IAAI;AAEzC,MAAI,OAAO,KAAK,OAAO,KAAK,QAAQ,UAAU,GAAG,EAAE,CAAC,GAAG;AAEnD,QAAI,OAAO,IAAI,UAAU,GAAG,WAAW,OAAO,CAAC,GAAG,GAAG,WAAW,OAAO,CAAC,CAAC;AACzE,QAAI,KAAK,SAAS;AACd,aAAO,KAAK,UAAU,GAAG,EAAE,IAAI;AACnC,cAAU,OAAO;AAAA,EACrB;AACA,MAAI,OAAO,KAAK,OAAO,GAAG;AACtB,QAAI,QAAQ;AACZ,UAAM,MAAM,MAAM,QAAQ,CAAC;AAC3B,QAAI,OAAO,IAAI,SAAS,QAAQ,IAAI,MAAM,KAAK;AAC3C,cAAQ,KAAK,IAAI,GAAG,KAAK,IAAI,IAAI,MAAM,KAAK,KAAK,EAAE,CAAC;AAAA,IACxD;AACA,UAAM,UAAU,IAAI,OAAO,EAAE,IAAI,IAAI,OAAO,KAAK;AACjD,UAAM,WAAW;AAAA;AAAA,EAAQ,OAAO;AAAA,EAAK,OAAO;AAAA;AAAA,EAChD;AACJ;;;ACtDA,SAAS,aAAa,QAAQ,EAAE,MAAM,WAAW,MAAM,QAAQ,SAAS,cAAc,eAAe,GAAG;AACpG,MAAI,cAAc;AAClB,MAAI,YAAY;AAChB,MAAI,WAAW;AACf,MAAI,UAAU;AACd,MAAI,aAAa;AACjB,MAAI,aAAa;AACjB,MAAI,WAAW;AACf,MAAI,MAAM;AACV,MAAI,SAAS;AACb,MAAI,MAAM;AACV,MAAI,mBAAmB;AACvB,MAAI,QAAQ;AACZ,MAAI,QAAQ;AACZ,MAAI,QAAQ;AACZ,aAAW,SAAS,QAAQ;AACxB,QAAI,UAAU;AACV,UAAI,MAAM,SAAS,WACf,MAAM,SAAS,aACf,MAAM,SAAS;AACf,gBAAQ,MAAM,QAAQ,gBAAgB,uEAAuE;AACjH,iBAAW;AAAA,IACf;AACA,QAAI,KAAK;AACL,UAAI,aAAa,MAAM,SAAS,aAAa,MAAM,SAAS,WAAW;AACnE,gBAAQ,KAAK,iBAAiB,qCAAqC;AAAA,MACvE;AACA,YAAM;AAAA,IACV;AACA,YAAQ,MAAM,MAAM;AAAA,MAChB,KAAK;AAID,YAAI,CAAC,SACA,cAAc,gBAAe,6BAAM,UAAS,sBAC7C,MAAM,OAAO,SAAS,GAAI,GAAG;AAC7B,gBAAM;AAAA,QACV;AACA,mBAAW;AACX;AAAA,MACJ,KAAK,WAAW;AACZ,YAAI,CAAC;AACD,kBAAQ,OAAO,gBAAgB,wEAAwE;AAC3G,cAAM,KAAK,MAAM,OAAO,UAAU,CAAC,KAAK;AACxC,YAAI,CAAC;AACD,oBAAU;AAAA;AAEV,qBAAW,aAAa;AAC5B,qBAAa;AACb,oBAAY;AACZ;AAAA,MACJ;AAAA,MACA,KAAK;AACD,YAAI,WAAW;AACX,cAAI;AACA,uBAAW,MAAM;AAAA,mBACZ,CAAC,SAAS,cAAc;AAC7B,0BAAc;AAAA,QACtB;AAEI,wBAAc,MAAM;AACxB,oBAAY;AACZ,qBAAa;AACb,YAAI,UAAU;AACV,6BAAmB;AACvB,mBAAW;AACX;AAAA,MACJ,KAAK;AACD,YAAI;AACA,kBAAQ,OAAO,oBAAoB,oCAAoC;AAC3E,YAAI,MAAM,OAAO,SAAS,GAAG;AACzB,kBAAQ,MAAM,SAAS,MAAM,OAAO,SAAS,GAAG,aAAa,mCAAmC,IAAI;AACxG,iBAAS;AACT,kBAAU,QAAQ,MAAM;AACxB,oBAAY;AACZ,mBAAW;AACX,mBAAW;AACX;AAAA,MACJ,KAAK,OAAO;AACR,YAAI;AACA,kBAAQ,OAAO,iBAAiB,iCAAiC;AACrE,cAAM;AACN,kBAAU,QAAQ,MAAM;AACxB,oBAAY;AACZ,mBAAW;AACX,mBAAW;AACX;AAAA,MACJ;AAAA,MACA,KAAK;AAED,YAAI,UAAU;AACV,kBAAQ,OAAO,kBAAkB,sCAAsC,MAAM,MAAM,YAAY;AACnG,YAAI;AACA,kBAAQ,OAAO,oBAAoB,cAAc,MAAM,MAAM,OAAO,QAAQ,YAAY,EAAE;AAC9F,gBAAQ;AACR,oBACI,cAAc,kBAAkB,cAAc;AAClD,mBAAW;AACX;AAAA,MACJ,KAAK;AACD,YAAI,MAAM;AACN,cAAI;AACA,oBAAQ,OAAO,oBAAoB,mBAAmB,IAAI,EAAE;AAChE,kBAAQ;AACR,sBAAY;AACZ,qBAAW;AACX;AAAA,QACJ;AAAA,MAEJ;AACI,gBAAQ,OAAO,oBAAoB,cAAc,MAAM,IAAI,QAAQ;AACnE,oBAAY;AACZ,mBAAW;AAAA,IACnB;AAAA,EACJ;AACA,QAAM,OAAO,OAAO,OAAO,SAAS,CAAC;AACrC,QAAM,MAAM,OAAO,KAAK,SAAS,KAAK,OAAO,SAAS;AACtD,MAAI,YACA,QACA,KAAK,SAAS,WACd,KAAK,SAAS,aACd,KAAK,SAAS,YACb,KAAK,SAAS,YAAY,KAAK,WAAW,KAAK;AAChD,YAAQ,KAAK,QAAQ,gBAAgB,uEAAuE;AAAA,EAChH;AACA,MAAI,QACE,aAAa,IAAI,UAAU,iBACzB,6BAAM,UAAS,gBACf,6BAAM,UAAS;AACnB,YAAQ,KAAK,iBAAiB,qCAAqC;AACvE,SAAO;AAAA,IACH;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,OAAO,SAAS;AAAA,EACpB;AACJ;;;AC/IA,SAAS,gBAAgB,KAAK;AAC1B,MAAI,CAAC;AACD,WAAO;AACX,UAAQ,IAAI,MAAM;AAAA,IACd,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AACD,UAAI,IAAI,OAAO,SAAS,IAAI;AACxB,eAAO;AACX,UAAI,IAAI;AACJ,mBAAW,MAAM,IAAI;AACjB,cAAI,GAAG,SAAS;AACZ,mBAAO;AAAA;AACnB,aAAO;AAAA,IACX,KAAK;AACD,iBAAW,MAAM,IAAI,OAAO;AACxB,mBAAW,MAAM,GAAG;AAChB,cAAI,GAAG,SAAS;AACZ,mBAAO;AACf,YAAI,GAAG;AACH,qBAAW,MAAM,GAAG;AAChB,gBAAI,GAAG,SAAS;AACZ,qBAAO;AAAA;AACnB,YAAI,gBAAgB,GAAG,GAAG,KAAK,gBAAgB,GAAG,KAAK;AACnD,iBAAO;AAAA,MACf;AACA,aAAO;AAAA,IACX;AACI,aAAO;AAAA,EACf;AACJ;;;AC7BA,SAAS,gBAAgB,QAAQ,IAAI,SAAS;AAC1C,OAAI,yBAAI,UAAS,mBAAmB;AAChC,UAAM,MAAM,GAAG,IAAI,CAAC;AACpB,QAAI,IAAI,WAAW,WACd,IAAI,WAAW,OAAO,IAAI,WAAW,QACtC,gBAAgB,EAAE,GAAG;AACrB,YAAM,MAAM;AACZ,cAAQ,KAAK,cAAc,KAAK,IAAI;AAAA,IACxC;AAAA,EACJ;AACJ;;;ACVA,SAAS,YAAY,KAAK,OAAO,QAAQ;AACrC,QAAM,EAAE,WAAW,IAAI,IAAI;AAC3B,MAAI,eAAe;AACf,WAAO;AACX,QAAM,UAAU,OAAO,eAAe,aAChC,aACA,CAAC,GAAG,MAAM,MAAM,KAAM,SAAS,CAAC,KAAK,SAAS,CAAC,KAAK,EAAE,UAAU,EAAE;AACxE,SAAO,MAAM,KAAK,UAAQ,QAAQ,KAAK,KAAK,MAAM,CAAC;AACvD;;;ACHA,IAAM,cAAc;AACpB,SAAS,gBAAgB,EAAE,aAAAC,cAAa,kBAAAC,kBAAiB,GAAG,KAAK,IAAI,SAAS,KAAK;AARnF;AASI,QAAM,aAAY,2BAAK,cAAa;AACpC,QAAMC,OAAM,IAAI,UAAU,IAAI,MAAM;AACpC,MAAI,IAAI;AACJ,QAAI,SAAS;AACjB,MAAI,SAAS,GAAG;AAChB,MAAI,aAAa;AACjB,aAAW,YAAY,GAAG,OAAO;AAC7B,UAAM,EAAE,OAAO,KAAK,KAAK,MAAM,IAAI;AAEnC,UAAM,WAAW,aAAa,OAAO;AAAA,MACjC,WAAW;AAAA,MACX,MAAM,QAAO,2BAAM;AAAA,MACnB;AAAA,MACA;AAAA,MACA,cAAc,GAAG;AAAA,MACjB,gBAAgB;AAAA,IACpB,CAAC;AACD,UAAM,cAAc,CAAC,SAAS;AAC9B,QAAI,aAAa;AACb,UAAI,KAAK;AACL,YAAI,IAAI,SAAS;AACb,kBAAQ,QAAQ,yBAAyB,yDAAyD;AAAA,iBAC7F,YAAY,OAAO,IAAI,WAAW,GAAG;AAC1C,kBAAQ,QAAQ,cAAc,WAAW;AAAA,MACjD;AACA,UAAI,CAAC,SAAS,UAAU,CAAC,SAAS,OAAO,CAAC,KAAK;AAC3C,qBAAa,SAAS;AACtB,YAAI,SAAS,SAAS;AAClB,cAAIA,KAAI;AACJ,YAAAA,KAAI,WAAW,OAAO,SAAS;AAAA;AAE/B,YAAAA,KAAI,UAAU,SAAS;AAAA,QAC/B;AACA;AAAA,MACJ;AACA,UAAI,SAAS,oBAAoB,gBAAgB,GAAG,GAAG;AACnD,gBAAQ,OAAO,MAAM,MAAM,SAAS,CAAC,GAAG,0BAA0B,2CAA2C;AAAA,MACjH;AAAA,IACJ,aACS,cAAS,UAAT,mBAAgB,YAAW,GAAG,QAAQ;AAC3C,cAAQ,QAAQ,cAAc,WAAW;AAAA,IAC7C;AAEA,QAAI,QAAQ;AACZ,UAAM,WAAW,SAAS;AAC1B,UAAM,UAAU,MACVF,aAAY,KAAK,KAAK,UAAU,OAAO,IACvCC,kBAAiB,KAAK,UAAU,OAAO,MAAM,UAAU,OAAO;AACpE,QAAI,IAAI,OAAO;AACX,sBAAgB,GAAG,QAAQ,KAAK,OAAO;AAC3C,QAAI,QAAQ;AACZ,QAAI,YAAY,KAAKC,KAAI,OAAO,OAAO;AACnC,cAAQ,UAAU,iBAAiB,yBAAyB;AAEhE,UAAM,aAAa,aAAa,OAAO,CAAC,GAAG;AAAA,MACvC,WAAW;AAAA,MACX,MAAM;AAAA,MACN,QAAQ,QAAQ,MAAM,CAAC;AAAA,MACvB;AAAA,MACA,cAAc,GAAG;AAAA,MACjB,gBAAgB,CAAC,OAAO,IAAI,SAAS;AAAA,IACzC,CAAC;AACD,aAAS,WAAW;AACpB,QAAI,WAAW,OAAO;AAClB,UAAI,aAAa;AACb,aAAI,+BAAO,UAAS,eAAe,CAAC,WAAW;AAC3C,kBAAQ,QAAQ,yBAAyB,qDAAqD;AAClG,YAAI,IAAI,QAAQ,UACZ,SAAS,QAAQ,WAAW,MAAM,SAAS;AAC3C,kBAAQ,QAAQ,OAAO,uBAAuB,6FAA6F;AAAA,MACnJ;AAEA,YAAM,YAAY,QACZF,aAAY,KAAK,OAAO,YAAY,OAAO,IAC3CC,kBAAiB,KAAK,QAAQ,KAAK,MAAM,YAAY,OAAO;AAClE,UAAI,IAAI,OAAO;AACX,wBAAgB,GAAG,QAAQ,OAAO,OAAO;AAC7C,eAAS,UAAU,MAAM,CAAC;AAC1B,YAAM,OAAO,IAAI,KAAK,SAAS,SAAS;AACxC,UAAI,IAAI,QAAQ;AACZ,aAAK,WAAW;AACpB,MAAAC,KAAI,MAAM,KAAK,IAAI;AAAA,IACvB,OACK;AAED,UAAI;AACA,gBAAQ,QAAQ,OAAO,gBAAgB,qDAAqD;AAChG,UAAI,WAAW,SAAS;AACpB,YAAI,QAAQ;AACR,kBAAQ,WAAW,OAAO,WAAW;AAAA;AAErC,kBAAQ,UAAU,WAAW;AAAA,MACrC;AACA,YAAM,OAAO,IAAI,KAAK,OAAO;AAC7B,UAAI,IAAI,QAAQ;AACZ,aAAK,WAAW;AACpB,MAAAA,KAAI,MAAM,KAAK,IAAI;AAAA,IACvB;AAAA,EACJ;AACA,MAAI,cAAc,aAAa;AAC3B,YAAQ,YAAY,cAAc,mCAAmC;AACzE,EAAAA,KAAI,QAAQ,CAAC,GAAG,QAAQ,QAAQ,cAAc,MAAM;AACpD,SAAOA;AACX;;;AC5GA,SAAS,gBAAgB,EAAE,aAAAC,cAAa,kBAAAC,kBAAiB,GAAG,KAAK,IAAI,SAAS,KAAK;AAC/E,QAAM,aAAY,2BAAK,cAAa;AACpC,QAAMC,OAAM,IAAI,UAAU,IAAI,MAAM;AACpC,MAAI,IAAI;AACJ,QAAI,SAAS;AACjB,MAAI,IAAI;AACJ,QAAI,QAAQ;AAChB,MAAI,SAAS,GAAG;AAChB,MAAI,aAAa;AACjB,aAAW,EAAE,OAAO,MAAM,KAAK,GAAG,OAAO;AACrC,UAAM,QAAQ,aAAa,OAAO;AAAA,MAC9B,WAAW;AAAA,MACX,MAAM;AAAA,MACN;AAAA,MACA;AAAA,MACA,cAAc,GAAG;AAAA,MACjB,gBAAgB;AAAA,IACpB,CAAC;AACD,QAAI,CAAC,MAAM,OAAO;AACd,UAAI,MAAM,UAAU,MAAM,OAAO,OAAO;AACpC,YAAI,SAAS,MAAM,SAAS;AACxB,kBAAQ,MAAM,KAAK,cAAc,kDAAkD;AAAA;AAEnF,kBAAQ,QAAQ,gBAAgB,mCAAmC;AAAA,MAC3E,OACK;AACD,qBAAa,MAAM;AACnB,YAAI,MAAM;AACN,UAAAA,KAAI,UAAU,MAAM;AACxB;AAAA,MACJ;AAAA,IACJ;AACA,UAAM,OAAO,QACPF,aAAY,KAAK,OAAO,OAAO,OAAO,IACtCC,kBAAiB,KAAK,MAAM,KAAK,OAAO,MAAM,OAAO,OAAO;AAClE,QAAI,IAAI,OAAO;AACX,sBAAgB,GAAG,QAAQ,OAAO,OAAO;AAC7C,aAAS,KAAK,MAAM,CAAC;AACrB,IAAAC,KAAI,MAAM,KAAK,IAAI;AAAA,EACvB;AACA,EAAAA,KAAI,QAAQ,CAAC,GAAG,QAAQ,QAAQ,cAAc,MAAM;AACpD,SAAOA;AACX;;;AC9CA,SAAS,WAAW,KAAK,QAAQ,UAAU,SAAS;AAChD,MAAI,UAAU;AACd,MAAI,KAAK;AACL,QAAI,WAAW;AACf,QAAI,MAAM;AACV,eAAW,SAAS,KAAK;AACrB,YAAM,EAAE,QAAQ,KAAK,IAAI;AACzB,cAAQ,MAAM;AAAA,QACV,KAAK;AACD,qBAAW;AACX;AAAA,QACJ,KAAK,WAAW;AACZ,cAAI,YAAY,CAAC;AACb,oBAAQ,OAAO,gBAAgB,wEAAwE;AAC3G,gBAAM,KAAK,OAAO,UAAU,CAAC,KAAK;AAClC,cAAI,CAAC;AACD,sBAAU;AAAA;AAEV,uBAAW,MAAM;AACrB,gBAAM;AACN;AAAA,QACJ;AAAA,QACA,KAAK;AACD,cAAI;AACA,mBAAO;AACX,qBAAW;AACX;AAAA,QACJ;AACI,kBAAQ,OAAO,oBAAoB,cAAc,IAAI,cAAc;AAAA,MAC3E;AACA,gBAAU,OAAO;AAAA,IACrB;AAAA,EACJ;AACA,SAAO,EAAE,SAAS,OAAO;AAC7B;;;ACzBA,IAAM,WAAW;AACjB,IAAM,UAAU,CAAC,UAAU,UAAU,MAAM,SAAS,eAAe,MAAM,SAAS;AAClF,SAAS,sBAAsB,EAAE,aAAAC,cAAa,kBAAAC,kBAAiB,GAAG,KAAK,IAAI,SAAS,KAAK;AACrF,QAAMC,SAAQ,GAAG,MAAM,WAAW;AAClC,QAAM,SAASA,SAAQ,aAAa;AACpC,QAAM,aAAa,2BAAK,eAAcA,SAAQ,UAAU;AACxD,QAAM,OAAO,IAAI,UAAU,IAAI,MAAM;AACrC,OAAK,OAAO;AACZ,QAAM,SAAS,IAAI;AACnB,MAAI;AACA,QAAI,SAAS;AACjB,MAAI,IAAI;AACJ,QAAI,QAAQ;AAChB,MAAI,SAAS,GAAG,SAAS,GAAG,MAAM,OAAO;AACzC,WAAS,IAAI,GAAG,IAAI,GAAG,MAAM,QAAQ,EAAE,GAAG;AACtC,UAAM,WAAW,GAAG,MAAM,CAAC;AAC3B,UAAM,EAAE,OAAO,KAAK,KAAK,MAAM,IAAI;AACnC,UAAM,QAAQ,aAAa,OAAO;AAAA,MAC9B,MAAM;AAAA,MACN,WAAW;AAAA,MACX,MAAM,QAAO,2BAAM;AAAA,MACnB;AAAA,MACA;AAAA,MACA,cAAc,GAAG;AAAA,MACjB,gBAAgB;AAAA,IACpB,CAAC;AACD,QAAI,CAAC,MAAM,OAAO;AACd,UAAI,CAAC,MAAM,UAAU,CAAC,MAAM,OAAO,CAAC,OAAO,CAAC,OAAO;AAC/C,YAAI,MAAM,KAAK,MAAM;AACjB,kBAAQ,MAAM,OAAO,oBAAoB,mBAAmB,MAAM,EAAE;AAAA,iBAC/D,IAAI,GAAG,MAAM,SAAS;AAC3B,kBAAQ,MAAM,OAAO,oBAAoB,4BAA4B,MAAM,EAAE;AACjF,YAAI,MAAM,SAAS;AACf,cAAI,KAAK;AACL,iBAAK,WAAW,OAAO,MAAM;AAAA;AAE7B,iBAAK,UAAU,MAAM;AAAA,QAC7B;AACA,iBAAS,MAAM;AACf;AAAA,MACJ;AACA,UAAI,CAACA,UAAS,IAAI,QAAQ,UAAU,gBAAgB,GAAG;AACnD;AAAA,UAAQ;AAAA;AAAA,UACR;AAAA,UAA0B;AAAA,QAAkE;AAAA,IACpG;AACA,QAAI,MAAM,GAAG;AACT,UAAI,MAAM;AACN,gBAAQ,MAAM,OAAO,oBAAoB,mBAAmB,MAAM,EAAE;AAAA,IAC5E,OACK;AACD,UAAI,CAAC,MAAM;AACP,gBAAQ,MAAM,OAAO,gBAAgB,qBAAqB,MAAM,QAAQ;AAC5E,UAAI,MAAM,SAAS;AACf,YAAI,kBAAkB;AACtB,aAAM,YAAW,MAAM,OAAO;AAC1B,kBAAQ,GAAG,MAAM;AAAA,YACb,KAAK;AAAA,YACL,KAAK;AACD;AAAA,YACJ,KAAK;AACD,gCAAkB,GAAG,OAAO,UAAU,CAAC;AACvC,oBAAM;AAAA,YACV;AACI,oBAAM;AAAA,UACd;AAAA,QACJ;AACA,YAAI,iBAAiB;AACjB,cAAI,OAAO,KAAK,MAAM,KAAK,MAAM,SAAS,CAAC;AAC3C,cAAI,OAAO,IAAI;AACX,mBAAO,KAAK,SAAS,KAAK;AAC9B,cAAI,KAAK;AACL,iBAAK,WAAW,OAAO;AAAA;AAEvB,iBAAK,UAAU;AACnB,gBAAM,UAAU,MAAM,QAAQ,UAAU,gBAAgB,SAAS,CAAC;AAAA,QACtE;AAAA,MACJ;AAAA,IACJ;AACA,QAAI,CAACA,UAAS,CAAC,OAAO,CAAC,MAAM,OAAO;AAGhC,YAAM,YAAY,QACZF,aAAY,KAAK,OAAO,OAAO,OAAO,IACtCC,kBAAiB,KAAK,MAAM,KAAK,KAAK,MAAM,OAAO,OAAO;AAChE,WAAK,MAAM,KAAK,SAAS;AACzB,eAAS,UAAU,MAAM,CAAC;AAC1B,UAAI,QAAQ,KAAK;AACb,gBAAQ,UAAU,OAAO,iBAAiB,QAAQ;AAAA,IAC1D,OACK;AAGD,UAAI,QAAQ;AACZ,YAAM,WAAW,MAAM;AACvB,YAAM,UAAU,MACVD,aAAY,KAAK,KAAK,OAAO,OAAO,IACpCC,kBAAiB,KAAK,UAAU,OAAO,MAAM,OAAO,OAAO;AACjE,UAAI,QAAQ,GAAG;AACX,gBAAQ,QAAQ,OAAO,iBAAiB,QAAQ;AACpD,UAAI,QAAQ;AAEZ,YAAM,aAAa,aAAa,OAAO,CAAC,GAAG;AAAA,QACvC,MAAM;AAAA,QACN,WAAW;AAAA,QACX,MAAM;AAAA,QACN,QAAQ,QAAQ,MAAM,CAAC;AAAA,QACvB;AAAA,QACA,cAAc,GAAG;AAAA,QACjB,gBAAgB;AAAA,MACpB,CAAC;AACD,UAAI,WAAW,OAAO;AAClB,YAAI,CAACC,UAAS,CAAC,MAAM,SAAS,IAAI,QAAQ,QAAQ;AAC9C,cAAI;AACA,uBAAW,MAAM,KAAK;AAClB,kBAAI,OAAO,WAAW;AAClB;AACJ,kBAAI,GAAG,SAAS,WAAW;AACvB,wBAAQ,IAAI,0BAA0B,kEAAkE;AACxG;AAAA,cACJ;AAAA,YACJ;AACJ,cAAI,MAAM,QAAQ,WAAW,MAAM,SAAS;AACxC,oBAAQ,WAAW,OAAO,uBAAuB,6FAA6F;AAAA,QACtJ;AAAA,MACJ,WACS,OAAO;AACZ,YAAI,YAAY,SAAS,MAAM,UAAU,MAAM,OAAO,CAAC,MAAM;AACzD,kBAAQ,OAAO,gBAAgB,4BAA4B,MAAM,EAAE;AAAA;AAEnE,kBAAQ,WAAW,OAAO,gBAAgB,0BAA0B,MAAM,QAAQ;AAAA,MAC1F;AAEA,YAAM,YAAY,QACZF,aAAY,KAAK,OAAO,YAAY,OAAO,IAC3C,WAAW,QACPC,kBAAiB,KAAK,WAAW,KAAK,KAAK,MAAM,YAAY,OAAO,IACpE;AACV,UAAI,WAAW;AACX,YAAI,QAAQ,KAAK;AACb,kBAAQ,UAAU,OAAO,iBAAiB,QAAQ;AAAA,MAC1D,WACS,WAAW,SAAS;AACzB,YAAI,QAAQ;AACR,kBAAQ,WAAW,OAAO,WAAW;AAAA;AAErC,kBAAQ,UAAU,WAAW;AAAA,MACrC;AACA,YAAM,OAAO,IAAI,KAAK,SAAS,SAAS;AACxC,UAAI,IAAI,QAAQ;AACZ,aAAK,WAAW;AACpB,UAAIC,QAAO;AACP,cAAMC,OAAM;AACZ,YAAI,YAAY,KAAKA,KAAI,OAAO,OAAO;AACnC,kBAAQ,UAAU,iBAAiB,yBAAyB;AAChE,QAAAA,KAAI,MAAM,KAAK,IAAI;AAAA,MACvB,OACK;AACD,cAAMA,OAAM,IAAI,QAAQ,IAAI,MAAM;AAClC,QAAAA,KAAI,OAAO;AACX,QAAAA,KAAI,MAAM,KAAK,IAAI;AACnB,cAAM,YAAY,aAAa,SAAS;AACxC,QAAAA,KAAI,QAAQ,CAAC,QAAQ,MAAM,CAAC,GAAG,SAAS,CAAC,GAAG,SAAS,CAAC,CAAC;AACvD,aAAK,MAAM,KAAKA,IAAG;AAAA,MACvB;AACA,eAAS,YAAY,UAAU,MAAM,CAAC,IAAI,WAAW;AAAA,IACzD;AAAA,EACJ;AACA,QAAM,cAAcD,SAAQ,MAAM;AAClC,QAAM,CAAC,IAAI,GAAG,EAAE,IAAI,GAAG;AACvB,MAAI,QAAQ;AACZ,MAAI,MAAM,GAAG,WAAW;AACpB,YAAQ,GAAG,SAAS,GAAG,OAAO;AAAA,OAC7B;AACD,UAAM,OAAO,OAAO,CAAC,EAAE,YAAY,IAAI,OAAO,UAAU,CAAC;AACzD,UAAM,MAAM,SACN,GAAG,IAAI,oBAAoB,WAAW,KACtC,GAAG,IAAI,qEAAqE,WAAW;AAC7F,YAAQ,QAAQ,SAAS,iBAAiB,cAAc,GAAG;AAC3D,QAAI,MAAM,GAAG,OAAO,WAAW;AAC3B,SAAG,QAAQ,EAAE;AAAA,EACrB;AACA,MAAI,GAAG,SAAS,GAAG;AACf,UAAM,MAAM,WAAW,IAAI,OAAO,IAAI,QAAQ,QAAQ,OAAO;AAC7D,QAAI,IAAI,SAAS;AACb,UAAI,KAAK;AACL,aAAK,WAAW,OAAO,IAAI;AAAA;AAE3B,aAAK,UAAU,IAAI;AAAA,IAC3B;AACA,SAAK,QAAQ,CAAC,GAAG,QAAQ,OAAO,IAAI,MAAM;AAAA,EAC9C,OACK;AACD,SAAK,QAAQ,CAAC,GAAG,QAAQ,OAAO,KAAK;AAAA,EACzC;AACA,SAAO;AACX;;;ACpMA,SAAS,kBAAkBE,KAAI,KAAK,OAAO,SAAS,SAAS,KAAK;AAC9D,QAAM,OAAO,MAAM,SAAS,cACtB,gBAAgBA,KAAI,KAAK,OAAO,SAAS,GAAG,IAC5C,MAAM,SAAS,cACX,gBAAgBA,KAAI,KAAK,OAAO,SAAS,GAAG,IAC5C,sBAAsBA,KAAI,KAAK,OAAO,SAAS,GAAG;AAC5D,QAAM,OAAO,KAAK;AAGlB,MAAI,YAAY,OAAO,YAAY,KAAK,SAAS;AAC7C,SAAK,MAAM,KAAK;AAChB,WAAO;AAAA,EACX;AACA,MAAI;AACA,SAAK,MAAM;AACf,SAAO;AACX;AACA,SAAS,kBAAkBA,KAAI,KAAK,OAAO,OAAO,SAAS;AAzB3D;AA0BI,QAAM,WAAW,MAAM;AACvB,QAAM,UAAU,CAAC,WACX,OACA,IAAI,WAAW,QAAQ,SAAS,QAAQ,SAAO,QAAQ,UAAU,sBAAsB,GAAG,CAAC;AACjG,MAAI,MAAM,SAAS,aAAa;AAC5B,UAAM,EAAE,QAAQ,kBAAkB,GAAG,IAAI;AACzC,UAAM,WAAW,UAAU,WACrB,OAAO,SAAS,SAAS,SACrB,SACA,WACH,UAAU;AACjB,QAAI,aAAa,CAAC,MAAM,GAAG,SAAS,SAAS,SAAS;AAClD,YAAM,UAAU;AAChB,cAAQ,UAAU,gBAAgB,OAAO;AAAA,IAC7C;AAAA,EACJ;AACA,QAAM,UAAU,MAAM,SAAS,cACzB,QACA,MAAM,SAAS,cACX,QACA,MAAM,MAAM,WAAW,MACnB,QACA;AAGd,MAAI,CAAC,YACD,CAAC,WACD,YAAY,OACX,YAAY,QAAQ,WAAW,YAAY,SAC3C,YAAY,QAAQ,WAAW,YAAY,OAAQ;AACpD,WAAO,kBAAkBA,KAAI,KAAK,OAAO,SAAS,OAAO;AAAA,EAC7D;AACA,MAAI,MAAM,IAAI,OAAO,KAAK,KAAK,OAAK,EAAE,QAAQ,WAAW,EAAE,eAAe,OAAO;AACjF,MAAI,CAAC,KAAK;AACN,UAAM,KAAK,IAAI,OAAO,UAAU,OAAO;AACvC,QAAI,MAAM,GAAG,eAAe,SAAS;AACjC,UAAI,OAAO,KAAK,KAAK,OAAO,OAAO,CAAC,GAAG,IAAI,EAAE,SAAS,MAAM,CAAC,CAAC;AAC9D,YAAM;AAAA,IACV,OACK;AACD,UAAI,IAAI;AACJ,gBAAQ,UAAU,uBAAuB,GAAG,GAAG,GAAG,aAAa,OAAO,4BAA4B,GAAG,cAAc,QAAQ,IAAI,IAAI;AAAA,MACvI,OACK;AACD,gBAAQ,UAAU,sBAAsB,mBAAmB,OAAO,IAAI,IAAI;AAAA,MAC9E;AACA,aAAO,kBAAkBA,KAAI,KAAK,OAAO,SAAS,OAAO;AAAA,IAC7D;AAAA,EACJ;AACA,QAAM,OAAO,kBAAkBA,KAAI,KAAK,OAAO,SAAS,SAAS,GAAG;AACpE,QAAM,QAAM,SAAI,YAAJ,6BAAc,MAAM,SAAO,QAAQ,UAAU,sBAAsB,GAAG,GAAG,IAAI,aAAY;AACrG,QAAM,OAAO,OAAO,GAAG,IACjB,MACA,IAAI,OAAO,GAAG;AACpB,OAAK,QAAQ,KAAK;AAClB,OAAK,MAAM;AACX,MAAI,2BAAK;AACL,SAAK,SAAS,IAAI;AACtB,SAAO;AACX;;;ACnFA,SAAS,mBAAmB,KAAK,QAAQ,SAAS;AAC9C,QAAM,QAAQ,OAAO;AACrB,QAAM,SAAS,uBAAuB,QAAQ,IAAI,QAAQ,QAAQ,OAAO;AACzE,MAAI,CAAC;AACD,WAAO,EAAE,OAAO,IAAI,MAAM,MAAM,SAAS,IAAI,OAAO,CAAC,OAAO,OAAO,KAAK,EAAE;AAC9E,QAAM,OAAO,OAAO,SAAS,MAAM,OAAO,eAAe,OAAO;AAChE,QAAM,QAAQ,OAAO,SAAS,WAAW,OAAO,MAAM,IAAI,CAAC;AAE3D,MAAI,aAAa,MAAM;AACvB,WAAS,IAAI,MAAM,SAAS,GAAG,KAAK,GAAG,EAAE,GAAG;AACxC,UAAM,UAAU,MAAM,CAAC,EAAE,CAAC;AAC1B,QAAI,YAAY,MAAM,YAAY;AAC9B,mBAAa;AAAA;AAEb;AAAA,EACR;AAEA,MAAI,eAAe,GAAG;AAClB,UAAMC,SAAQ,OAAO,UAAU,OAAO,MAAM,SAAS,IAC/C,KAAK,OAAO,KAAK,IAAI,GAAG,MAAM,SAAS,CAAC,CAAC,IACzC;AACN,QAAIC,OAAM,QAAQ,OAAO;AACzB,QAAI,OAAO;AACP,MAAAA,QAAO,OAAO,OAAO;AACzB,WAAO,EAAE,OAAAD,QAAO,MAAM,SAAS,OAAO,SAAS,OAAO,CAAC,OAAOC,MAAKA,IAAG,EAAE;AAAA,EAC5E;AAEA,MAAI,aAAa,OAAO,SAAS,OAAO;AACxC,MAAI,SAAS,OAAO,SAAS,OAAO;AACpC,MAAI,eAAe;AACnB,WAAS,IAAI,GAAG,IAAI,YAAY,EAAE,GAAG;AACjC,UAAM,CAAC,QAAQ,OAAO,IAAI,MAAM,CAAC;AACjC,QAAI,YAAY,MAAM,YAAY,MAAM;AACpC,UAAI,OAAO,WAAW,KAAK,OAAO,SAAS;AACvC,qBAAa,OAAO;AAAA,IAC5B,OACK;AACD,UAAI,OAAO,SAAS,YAAY;AAC5B,cAAM,UAAU;AAChB,gBAAQ,SAAS,OAAO,QAAQ,gBAAgB,OAAO;AAAA,MAC3D;AACA,UAAI,OAAO,WAAW;AAClB,qBAAa,OAAO;AACxB,qBAAe;AACf,UAAI,eAAe,KAAK,CAAC,IAAI,QAAQ;AACjC,cAAM,UAAU;AAChB,gBAAQ,QAAQ,cAAc,OAAO;AAAA,MACzC;AACA;AAAA,IACJ;AACA,cAAU,OAAO,SAAS,QAAQ,SAAS;AAAA,EAC/C;AAEA,WAAS,IAAI,MAAM,SAAS,GAAG,KAAK,YAAY,EAAE,GAAG;AACjD,QAAI,MAAM,CAAC,EAAE,CAAC,EAAE,SAAS;AACrB,mBAAa,IAAI;AAAA,EACzB;AACA,MAAI,QAAQ;AACZ,MAAI,MAAM;AACV,MAAI,mBAAmB;AAEvB,WAAS,IAAI,GAAG,IAAI,cAAc,EAAE;AAChC,aAAS,MAAM,CAAC,EAAE,CAAC,EAAE,MAAM,UAAU,IAAI;AAC7C,WAAS,IAAI,cAAc,IAAI,YAAY,EAAE,GAAG;AAC5C,QAAI,CAAC,QAAQ,OAAO,IAAI,MAAM,CAAC;AAC/B,cAAU,OAAO,SAAS,QAAQ,SAAS;AAC3C,UAAM,OAAO,QAAQ,QAAQ,SAAS,CAAC,MAAM;AAC7C,QAAI;AACA,gBAAU,QAAQ,MAAM,GAAG,EAAE;AAEjC,QAAI,WAAW,OAAO,SAAS,YAAY;AACvC,YAAM,MAAM,OAAO,SACb,mCACA;AACN,YAAM,UAAU,2DAA2D,GAAG;AAC9E,cAAQ,SAAS,QAAQ,UAAU,OAAO,IAAI,IAAI,cAAc,OAAO;AACvE,eAAS;AAAA,IACb;AACA,QAAI,SAAS,OAAO,eAAe;AAC/B,eAAS,MAAM,OAAO,MAAM,UAAU,IAAI;AAC1C,YAAM;AAAA,IACV,WACS,OAAO,SAAS,cAAc,QAAQ,CAAC,MAAM,KAAM;AAExD,UAAI,QAAQ;AACR,cAAM;AAAA,eACD,CAAC,oBAAoB,QAAQ;AAClC,cAAM;AACV,eAAS,MAAM,OAAO,MAAM,UAAU,IAAI;AAC1C,YAAM;AACN,yBAAmB;AAAA,IACvB,WACS,YAAY,IAAI;AAErB,UAAI,QAAQ;AACR,iBAAS;AAAA;AAET,cAAM;AAAA,IACd,OACK;AACD,eAAS,MAAM;AACf,YAAM;AACN,yBAAmB;AAAA,IACvB;AAAA,EACJ;AACA,UAAQ,OAAO,OAAO;AAAA,IAClB,KAAK;AACD;AAAA,IACJ,KAAK;AACD,eAAS,IAAI,YAAY,IAAI,MAAM,QAAQ,EAAE;AACzC,iBAAS,OAAO,MAAM,CAAC,EAAE,CAAC,EAAE,MAAM,UAAU;AAChD,UAAI,MAAM,MAAM,SAAS,CAAC,MAAM;AAC5B,iBAAS;AACb;AAAA,IACJ;AACI,eAAS;AAAA,EACjB;AACA,QAAM,MAAM,QAAQ,OAAO,SAAS,OAAO,OAAO;AAClD,SAAO,EAAE,OAAO,MAAM,SAAS,OAAO,SAAS,OAAO,CAAC,OAAO,KAAK,GAAG,EAAE;AAC5E;AACA,SAAS,uBAAuB,EAAE,QAAQ,MAAM,GAAG,QAAQ,SAAS;AAEhE,MAAI,MAAM,CAAC,EAAE,SAAS,uBAAuB;AACzC,YAAQ,MAAM,CAAC,GAAG,cAAc,+BAA+B;AAC/D,WAAO;AAAA,EACX;AACA,QAAM,EAAE,OAAO,IAAI,MAAM,CAAC;AAC1B,QAAM,OAAO,OAAO,CAAC;AACrB,MAAI,SAAS;AACb,MAAI,QAAQ;AACZ,MAAI,QAAQ;AACZ,WAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,EAAE,GAAG;AACpC,UAAM,KAAK,OAAO,CAAC;AACnB,QAAI,CAAC,UAAU,OAAO,OAAO,OAAO;AAChC,cAAQ;AAAA,SACP;AACD,YAAM,IAAI,OAAO,EAAE;AACnB,UAAI,CAAC,UAAU;AACX,iBAAS;AAAA,eACJ,UAAU;AACf,gBAAQ,SAAS;AAAA,IACzB;AAAA,EACJ;AACA,MAAI,UAAU;AACV,YAAQ,OAAO,oBAAoB,kDAAkD,MAAM,EAAE;AACjG,MAAI,WAAW;AACf,MAAI,UAAU;AACd,MAAI,SAAS,OAAO;AACpB,WAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,EAAE,GAAG;AACnC,UAAM,QAAQ,MAAM,CAAC;AACrB,YAAQ,MAAM,MAAM;AAAA,MAChB,KAAK;AACD,mBAAW;AAAA,MAEf,KAAK;AACD,kBAAU,MAAM,OAAO;AACvB;AAAA,MACJ,KAAK;AACD,YAAI,UAAU,CAAC,UAAU;AACrB,gBAAM,UAAU;AAChB,kBAAQ,OAAO,gBAAgB,OAAO;AAAA,QAC1C;AACA,kBAAU,MAAM,OAAO;AACvB,kBAAU,MAAM,OAAO,UAAU,CAAC;AAClC;AAAA,MACJ,KAAK;AACD,gBAAQ,OAAO,oBAAoB,MAAM,OAAO;AAChD,kBAAU,MAAM,OAAO;AACvB;AAAA,MAEJ,SAAS;AACL,cAAM,UAAU,4CAA4C,MAAM,IAAI;AACtE,gBAAQ,OAAO,oBAAoB,OAAO;AAC1C,cAAM,KAAK,MAAM;AACjB,YAAI,MAAM,OAAO,OAAO;AACpB,oBAAU,GAAG;AAAA,MACrB;AAAA,IACJ;AAAA,EACJ;AACA,SAAO,EAAE,MAAM,QAAQ,OAAO,SAAS,OAAO;AAClD;AAEA,SAAS,WAAW,QAAQ;AACxB,QAAM,QAAQ,OAAO,MAAM,QAAQ;AACnC,QAAM,QAAQ,MAAM,CAAC;AACrB,QAAM,IAAI,MAAM,MAAM,OAAO;AAC7B,QAAM,SAAQ,uBAAI,MACZ,CAAC,EAAE,CAAC,GAAG,MAAM,MAAM,EAAE,CAAC,EAAE,MAAM,CAAC,IAC/B,CAAC,IAAI,KAAK;AAChB,QAAM,QAAQ,CAAC,KAAK;AACpB,WAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACnC,UAAM,KAAK,CAAC,MAAM,CAAC,GAAG,MAAM,IAAI,CAAC,CAAC,CAAC;AACvC,SAAO;AACX;;;AChMA,SAAS,kBAAkB,QAAQ,QAAQ,SAAS;AAChD,QAAM,EAAE,QAAQ,MAAM,QAAQ,IAAI,IAAI;AACtC,MAAI;AACJ,MAAI;AACJ,QAAM,WAAW,CAAC,KAAK,MAAM,QAAQ,QAAQ,SAAS,KAAK,MAAM,GAAG;AACpE,UAAQ,MAAM;AAAA,IACV,KAAK;AACD,cAAQ,OAAO;AACf,cAAQ,WAAW,QAAQ,QAAQ;AACnC;AAAA,IACJ,KAAK;AACD,cAAQ,OAAO;AACf,cAAQ,kBAAkB,QAAQ,QAAQ;AAC1C;AAAA,IACJ,KAAK;AACD,cAAQ,OAAO;AACf,cAAQ,kBAAkB,QAAQ,QAAQ;AAC1C;AAAA,IAEJ;AACI,cAAQ,QAAQ,oBAAoB,4CAA4C,IAAI,EAAE;AACtF,aAAO;AAAA,QACH,OAAO;AAAA,QACP,MAAM;AAAA,QACN,SAAS;AAAA,QACT,OAAO,CAAC,QAAQ,SAAS,OAAO,QAAQ,SAAS,OAAO,MAAM;AAAA,MAClE;AAAA,EACR;AACA,QAAM,WAAW,SAAS,OAAO;AACjC,QAAM,KAAK,WAAW,KAAK,UAAU,QAAQ,OAAO;AACpD,SAAO;AAAA,IACH;AAAA,IACA,MAAM;AAAA,IACN,SAAS,GAAG;AAAA,IACZ,OAAO,CAAC,QAAQ,UAAU,GAAG,MAAM;AAAA,EACvC;AACJ;AACA,SAAS,WAAW,QAAQ,SAAS;AACjC,MAAI,UAAU;AACd,UAAQ,OAAO,CAAC,GAAG;AAAA,IAEf,KAAK;AACD,gBAAU;AACV;AAAA,IACJ,KAAK;AACD,gBAAU;AACV;AAAA,IACJ,KAAK;AACD,gBAAU;AACV;AAAA,IACJ,KAAK;AAAA,IACL,KAAK,KAAK;AACN,gBAAU,0BAA0B,OAAO,CAAC,CAAC;AAC7C;AAAA,IACJ;AAAA,IACA,KAAK;AAAA,IACL,KAAK,KAAK;AACN,gBAAU,sBAAsB,OAAO,CAAC,CAAC;AACzC;AAAA,IACJ;AAAA,EACJ;AACA,MAAI;AACA,YAAQ,GAAG,oBAAoB,iCAAiC,OAAO,EAAE;AAC7E,SAAO,UAAU,MAAM;AAC3B;AACA,SAAS,kBAAkB,QAAQ,SAAS;AACxC,MAAI,OAAO,OAAO,SAAS,CAAC,MAAM,OAAO,OAAO,WAAW;AACvD,YAAQ,OAAO,QAAQ,gBAAgB,wBAAwB;AACnE,SAAO,UAAU,OAAO,MAAM,GAAG,EAAE,CAAC,EAAE,QAAQ,OAAO,GAAG;AAC5D;AACA,SAAS,UAAU,QAAQ;AAQvB,MAAI,OAAO;AACX,MAAI;AACA,YAAQ,IAAI,OAAO,4BAA8B,IAAI;AACrD,WAAO,IAAI,OAAO,sCAAyC,IAAI;AAAA,EACnE,QACM;AACF,YAAQ;AACR,WAAO;AAAA,EACX;AACA,MAAI,QAAQ,MAAM,KAAK,MAAM;AAC7B,MAAI,CAAC;AACD,WAAO;AACX,MAAI,MAAM,MAAM,CAAC;AACjB,MAAI,MAAM;AACV,MAAI,MAAM,MAAM;AAChB,OAAK,YAAY;AACjB,SAAQ,QAAQ,KAAK,KAAK,MAAM,GAAI;AAChC,QAAI,MAAM,CAAC,MAAM,IAAI;AACjB,UAAI,QAAQ;AACR,eAAO;AAAA;AAEP,cAAM;AAAA,IACd,OACK;AACD,aAAO,MAAM,MAAM,CAAC;AACpB,YAAM;AAAA,IACV;AACA,UAAM,KAAK;AAAA,EACf;AACA,QAAM,OAAO;AACb,OAAK,YAAY;AACjB,UAAQ,KAAK,KAAK,MAAM;AACxB,SAAO,MAAM,QAAO,+BAAQ,OAAM;AACtC;AACA,SAAS,kBAAkB,QAAQ,SAAS;AACxC,MAAI,MAAM;AACV,WAAS,IAAI,GAAG,IAAI,OAAO,SAAS,GAAG,EAAE,GAAG;AACxC,UAAM,KAAK,OAAO,CAAC;AACnB,QAAI,OAAO,QAAQ,OAAO,IAAI,CAAC,MAAM;AACjC;AACJ,QAAI,OAAO,MAAM;AACb,YAAM,EAAE,MAAM,OAAO,IAAI,YAAY,QAAQ,CAAC;AAC9C,aAAO;AACP,UAAI;AAAA,IACR,WACS,OAAO,MAAM;AAClB,UAAI,OAAO,OAAO,EAAE,CAAC;AACrB,YAAM,KAAK,YAAY,IAAI;AAC3B,UAAI;AACA,eAAO;AAAA,eACF,SAAS,MAAM;AAEpB,eAAO,OAAO,IAAI,CAAC;AACnB,eAAO,SAAS,OAAO,SAAS;AAC5B,iBAAO,OAAO,EAAE,IAAI,CAAC;AAAA,MAC7B,WACS,SAAS,QAAQ,OAAO,IAAI,CAAC,MAAM,MAAM;AAE9C,eAAO,OAAO,EAAE,IAAI,CAAC;AACrB,eAAO,SAAS,OAAO,SAAS;AAC5B,iBAAO,OAAO,EAAE,IAAI,CAAC;AAAA,MAC7B,WACS,SAAS,OAAO,SAAS,OAAO,SAAS,KAAK;AACnD,cAAM,SAAS,EAAE,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,EAAE,IAAI;AACxC,eAAO,cAAc,QAAQ,IAAI,GAAG,QAAQ,OAAO;AACnD,aAAK;AAAA,MACT,OACK;AACD,cAAM,MAAM,OAAO,OAAO,IAAI,GAAG,CAAC;AAClC,gBAAQ,IAAI,GAAG,iBAAiB,2BAA2B,GAAG,EAAE;AAChE,eAAO;AAAA,MACX;AAAA,IACJ,WACS,OAAO,OAAO,OAAO,KAAM;AAEhC,YAAM,UAAU;AAChB,UAAI,OAAO,OAAO,IAAI,CAAC;AACvB,aAAO,SAAS,OAAO,SAAS;AAC5B,eAAO,OAAO,EAAE,IAAI,CAAC;AACzB,UAAI,SAAS,QAAQ,EAAE,SAAS,QAAQ,OAAO,IAAI,CAAC,MAAM;AACtD,eAAO,IAAI,UAAU,OAAO,MAAM,SAAS,IAAI,CAAC,IAAI;AAAA,IAC5D,OACK;AACD,aAAO;AAAA,IACX;AAAA,EACJ;AACA,MAAI,OAAO,OAAO,SAAS,CAAC,MAAM,OAAO,OAAO,WAAW;AACvD,YAAQ,OAAO,QAAQ,gBAAgB,wBAAwB;AACnE,SAAO;AACX;AAKA,SAAS,YAAY,QAAQ,QAAQ;AACjC,MAAI,OAAO;AACX,MAAI,KAAK,OAAO,SAAS,CAAC;AAC1B,SAAO,OAAO,OAAO,OAAO,OAAQ,OAAO,QAAQ,OAAO,MAAM;AAC5D,QAAI,OAAO,QAAQ,OAAO,SAAS,CAAC,MAAM;AACtC;AACJ,QAAI,OAAO;AACP,cAAQ;AACZ,cAAU;AACV,SAAK,OAAO,SAAS,CAAC;AAAA,EAC1B;AACA,MAAI,CAAC;AACD,WAAO;AACX,SAAO,EAAE,MAAM,OAAO;AAC1B;AACA,IAAM,cAAc;AAAA,EAChB,KAAK;AAAA;AAAA,EACL,GAAG;AAAA;AAAA,EACH,GAAG;AAAA;AAAA,EACH,GAAG;AAAA;AAAA,EACH,GAAG;AAAA;AAAA,EACH,GAAG;AAAA;AAAA,EACH,GAAG;AAAA;AAAA,EACH,GAAG;AAAA;AAAA,EACH,GAAG;AAAA;AAAA,EACH,GAAG;AAAA;AAAA,EACH,GAAG;AAAA;AAAA,EACH,GAAG;AAAA;AAAA,EACH,GAAG;AAAA;AAAA,EACH,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,MAAM;AAAA,EACN,KAAM;AACV;AACA,SAAS,cAAc,QAAQ,QAAQ,QAAQ,SAAS;AACpD,QAAM,KAAK,OAAO,OAAO,QAAQ,MAAM;AACvC,QAAM,KAAK,GAAG,WAAW,UAAU,iBAAiB,KAAK,EAAE;AAC3D,QAAM,OAAO,KAAK,SAAS,IAAI,EAAE,IAAI;AACrC,MAAI,MAAM,IAAI,GAAG;AACb,UAAM,MAAM,OAAO,OAAO,SAAS,GAAG,SAAS,CAAC;AAChD,YAAQ,SAAS,GAAG,iBAAiB,2BAA2B,GAAG,EAAE;AACrE,WAAO;AAAA,EACX;AACA,SAAO,OAAO,cAAc,IAAI;AACpC;;;ACvNA,SAAS,cAAc,KAAK,OAAO,UAAU,SAAS;AAClD,QAAM,EAAE,OAAO,MAAM,SAAS,MAAM,IAAI,MAAM,SAAS,iBACjD,mBAAmB,KAAK,OAAO,OAAO,IACtC,kBAAkB,OAAO,IAAI,QAAQ,QAAQ,OAAO;AAC1D,QAAM,UAAU,WACV,IAAI,WAAW,QAAQ,SAAS,QAAQ,SAAO,QAAQ,UAAU,sBAAsB,GAAG,CAAC,IAC3F;AACN,MAAI;AACJ,MAAI,IAAI,QAAQ,cAAc,IAAI,OAAO;AACrC,UAAM,IAAI,OAAO,MAAM;AAAA,EAC3B,WACS;AACL,UAAM,oBAAoB,IAAI,QAAQ,OAAO,SAAS,UAAU,OAAO;AAAA,WAClE,MAAM,SAAS;AACpB,UAAM,oBAAoB,KAAK,OAAO,OAAO,OAAO;AAAA;AAEpD,UAAM,IAAI,OAAO,MAAM;AAC3B,MAAI;AACJ,MAAI;AACA,UAAM,MAAM,IAAI,QAAQ,OAAO,SAAO,QAAQ,YAAY,OAAO,sBAAsB,GAAG,GAAG,IAAI,OAAO;AACxG,aAAS,SAAS,GAAG,IAAI,MAAM,IAAI,OAAO,GAAG;AAAA,EACjD,SACO,OAAO;AACV,UAAM,MAAM,iBAAiB,QAAQ,MAAM,UAAU,OAAO,KAAK;AACjE,YAAQ,YAAY,OAAO,sBAAsB,GAAG;AACpD,aAAS,IAAI,OAAO,KAAK;AAAA,EAC7B;AACA,SAAO,QAAQ;AACf,SAAO,SAAS;AAChB,MAAI;AACA,WAAO,OAAO;AAClB,MAAI;AACA,WAAO,MAAM;AACjB,MAAI,IAAI;AACJ,WAAO,SAAS,IAAI;AACxB,MAAI;AACA,WAAO,UAAU;AACrB,SAAO;AACX;AACA,SAAS,oBAAoBC,SAAQ,OAAO,SAAS,UAAU,SAAS;AA5CxE;AA6CI,MAAI,YAAY;AACZ,WAAOA,QAAO,MAAM;AACxB,QAAM,gBAAgB,CAAC;AACvB,aAAW,OAAOA,QAAO,MAAM;AAC3B,QAAI,CAAC,IAAI,cAAc,IAAI,QAAQ,SAAS;AACxC,UAAI,IAAI,WAAW,IAAI;AACnB,sBAAc,KAAK,GAAG;AAAA;AAEtB,eAAO;AAAA,IACf;AAAA,EACJ;AACA,aAAW,OAAO;AACd,SAAI,SAAI,SAAJ,mBAAU,KAAK;AACf,aAAO;AACf,QAAM,KAAKA,QAAO,UAAU,OAAO;AACnC,MAAI,MAAM,CAAC,GAAG,YAAY;AAGtB,IAAAA,QAAO,KAAK,KAAK,OAAO,OAAO,CAAC,GAAG,IAAI,EAAE,SAAS,OAAO,MAAM,OAAU,CAAC,CAAC;AAC3E,WAAO;AAAA,EACX;AACA,UAAQ,UAAU,sBAAsB,mBAAmB,OAAO,IAAI,YAAY,uBAAuB;AACzG,SAAOA,QAAO,MAAM;AACxB;AACA,SAAS,oBAAoB,EAAE,OAAO,YAAY,QAAAA,QAAO,GAAG,OAAO,OAAO,SAAS;AAC/E,QAAM,MAAMA,QAAO,KAAK,KAAK,CAAAC,SAAI;AAtErC;AAsEyC,YAAAA,KAAI,YAAY,QAAS,SAASA,KAAI,YAAY,YACnF,KAAAA,KAAI,SAAJ,mBAAU,KAAK;AAAA,GAAM,KAAKD,QAAO,MAAM;AAC3C,MAAIA,QAAO,QAAQ;AACf,UAAM,SAASA,QAAO,OAAO,KAAK,CAAAC,SAAI;AAzE9C;AAyEiD,aAAAA,KAAI,aAAW,KAAAA,KAAI,SAAJ,mBAAU,KAAK;AAAA,KAAM,KACzED,QAAO,MAAM;AACjB,QAAI,IAAI,QAAQ,OAAO,KAAK;AACxB,YAAM,KAAK,WAAW,UAAU,IAAI,GAAG;AACvC,YAAM,KAAK,WAAW,UAAU,OAAO,GAAG;AAC1C,YAAM,MAAM,iCAAiC,EAAE,OAAO,EAAE;AACxD,cAAQ,OAAO,sBAAsB,KAAK,IAAI;AAAA,IAClD;AAAA,EACJ;AACA,SAAO;AACX;;;ACnFA,SAAS,oBAAoB,QAAQ,QAAQ,KAAK;AAC9C,MAAI,QAAQ;AACR,YAAQ,MAAM,OAAO;AACrB,aAAS,IAAI,MAAM,GAAG,KAAK,GAAG,EAAE,GAAG;AAC/B,UAAI,KAAK,OAAO,CAAC;AACjB,cAAQ,GAAG,MAAM;AAAA,QACb,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AACD,oBAAU,GAAG,OAAO;AACpB;AAAA,MACR;AAGA,WAAK,OAAO,EAAE,CAAC;AACf,cAAO,yBAAI,UAAS,SAAS;AACzB,kBAAU,GAAG,OAAO;AACpB,aAAK,OAAO,EAAE,CAAC;AAAA,MACnB;AACA;AAAA,IACJ;AAAA,EACJ;AACA,SAAO;AACX;;;AChBA,IAAM,KAAK,EAAE,aAAa,iBAAiB;AAC3C,SAAS,YAAY,KAAK,OAAO,OAAO,SAAS;AAC7C,QAAM,QAAQ,IAAI;AAClB,QAAM,EAAE,aAAa,SAAS,QAAQ,IAAI,IAAI;AAC9C,MAAI;AACJ,MAAI,aAAa;AACjB,UAAQ,MAAM,MAAM;AAAA,IAChB,KAAK;AACD,aAAO,aAAa,KAAK,OAAO,OAAO;AACvC,UAAI,UAAU;AACV,gBAAQ,OAAO,eAAe,+CAA+C;AACjF;AAAA,IACJ,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AACD,aAAO,cAAc,KAAK,OAAO,KAAK,OAAO;AAC7C,UAAI;AACA,aAAK,SAAS,OAAO,OAAO,UAAU,CAAC;AAC3C;AAAA,IACJ,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AACD,aAAO,kBAAkB,IAAI,KAAK,OAAO,OAAO,OAAO;AACvD,UAAI;AACA,aAAK,SAAS,OAAO,OAAO,UAAU,CAAC;AAC3C;AAAA,IACJ,SAAS;AACL,YAAM,UAAU,MAAM,SAAS,UACzB,MAAM,UACN,4BAA4B,MAAM,IAAI;AAC5C,cAAQ,OAAO,oBAAoB,OAAO;AAC1C,aAAO,iBAAiB,KAAK,MAAM,QAAQ,QAAW,MAAM,OAAO,OAAO;AAC1E,mBAAa;AAAA,IACjB;AAAA,EACJ;AACA,MAAI,UAAU,KAAK,WAAW;AAC1B,YAAQ,QAAQ,aAAa,kCAAkC;AACnE,MAAI,SACA,IAAI,QAAQ,eACX,CAAC,SAAS,IAAI,KACX,OAAO,KAAK,UAAU,YACrB,KAAK,OAAO,KAAK,QAAQ,0BAA2B;AACzD,UAAM,MAAM;AACZ,YAAQ,OAAO,OAAO,kBAAkB,GAAG;AAAA,EAC/C;AACA,MAAI;AACA,SAAK,cAAc;AACvB,MAAI,SAAS;AACT,QAAI,MAAM,SAAS,YAAY,MAAM,WAAW;AAC5C,WAAK,UAAU;AAAA;AAEf,WAAK,gBAAgB;AAAA,EAC7B;AAEA,MAAI,IAAI,QAAQ,oBAAoB;AAChC,SAAK,WAAW;AACpB,SAAO;AACX;AACA,SAAS,iBAAiB,KAAK,QAAQ,QAAQ,KAAK,EAAE,aAAa,SAAS,QAAQ,KAAK,IAAI,GAAG,SAAS;AACrG,QAAM,QAAQ;AAAA,IACV,MAAM;AAAA,IACN,QAAQ,oBAAoB,QAAQ,QAAQ,GAAG;AAAA,IAC/C,QAAQ;AAAA,IACR,QAAQ;AAAA,EACZ;AACA,QAAM,OAAO,cAAc,KAAK,OAAO,KAAK,OAAO;AACnD,MAAI,QAAQ;AACR,SAAK,SAAS,OAAO,OAAO,UAAU,CAAC;AACvC,QAAI,KAAK,WAAW;AAChB,cAAQ,QAAQ,aAAa,kCAAkC;AAAA,EACvE;AACA,MAAI;AACA,SAAK,cAAc;AACvB,MAAI,SAAS;AACT,SAAK,UAAU;AACf,SAAK,MAAM,CAAC,IAAI;AAAA,EACpB;AACA,SAAO;AACX;AACA,SAAS,aAAa,EAAE,QAAQ,GAAG,EAAE,QAAQ,QAAQ,IAAI,GAAG,SAAS;AACjE,QAAM,QAAQ,IAAI,MAAM,OAAO,UAAU,CAAC,CAAC;AAC3C,MAAI,MAAM,WAAW;AACjB,YAAQ,QAAQ,aAAa,iCAAiC;AAClE,MAAI,MAAM,OAAO,SAAS,GAAG;AACzB,YAAQ,SAAS,OAAO,SAAS,GAAG,aAAa,kCAAkC,IAAI;AAC3F,QAAM,WAAW,SAAS,OAAO;AACjC,QAAM,KAAK,WAAW,KAAK,UAAU,QAAQ,QAAQ,OAAO;AAC5D,QAAM,QAAQ,CAAC,QAAQ,UAAU,GAAG,MAAM;AAC1C,MAAI,GAAG;AACH,UAAM,UAAU,GAAG;AACvB,SAAO;AACX;;;AC9FA,SAAS,WAAW,SAAS,YAAY,EAAE,QAAQ,OAAO,OAAO,IAAI,GAAG,SAAS;AAC7E,QAAM,OAAO,OAAO,OAAO,EAAE,aAAa,WAAW,GAAG,OAAO;AAC/D,QAAM,MAAM,IAAI,SAAS,QAAW,IAAI;AACxC,QAAM,MAAM;AAAA,IACR,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,YAAY,IAAI;AAAA,IAChB,SAAS,IAAI;AAAA,IACb,QAAQ,IAAI;AAAA,EAChB;AACA,QAAM,QAAQ,aAAa,OAAO;AAAA,IAC9B,WAAW;AAAA,IACX,MAAM,UAAS,2BAAM;AAAA,IACrB;AAAA,IACA;AAAA,IACA,cAAc;AAAA,IACd,gBAAgB;AAAA,EACpB,CAAC;AACD,MAAI,MAAM,OAAO;AACb,QAAI,WAAW,WAAW;AAC1B,QAAI,UACC,MAAM,SAAS,eAAe,MAAM,SAAS,gBAC9C,CAAC,MAAM;AACP,cAAQ,MAAM,KAAK,gBAAgB,uEAAuE;AAAA,EAClH;AAEA,MAAI,WAAW,QACT,YAAY,KAAK,OAAO,OAAO,OAAO,IACtC,iBAAiB,KAAK,MAAM,KAAK,OAAO,MAAM,OAAO,OAAO;AAClE,QAAM,aAAa,IAAI,SAAS,MAAM,CAAC;AACvC,QAAM,KAAK,WAAW,KAAK,YAAY,OAAO,OAAO;AACrD,MAAI,GAAG;AACH,QAAI,UAAU,GAAG;AACrB,MAAI,QAAQ,CAAC,QAAQ,YAAY,GAAG,MAAM;AAC1C,SAAO;AACX;;;ACjCA,SAAS,YAAY,KAAK;AACtB,MAAI,OAAO,QAAQ;AACf,WAAO,CAAC,KAAK,MAAM,CAAC;AACxB,MAAI,MAAM,QAAQ,GAAG;AACjB,WAAO,IAAI,WAAW,IAAI,MAAM,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC;AACnD,QAAM,EAAE,QAAQ,OAAO,IAAI;AAC3B,SAAO,CAAC,QAAQ,UAAU,OAAO,WAAW,WAAW,OAAO,SAAS,EAAE;AAC7E;AACA,SAAS,aAAa,SAAS;AAf/B;AAgBI,MAAI,UAAU;AACd,MAAI,YAAY;AAChB,MAAI,iBAAiB;AACrB,WAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,EAAE,GAAG;AACrC,UAAM,SAAS,QAAQ,CAAC;AACxB,YAAQ,OAAO,CAAC,GAAG;AAAA,MACf,KAAK;AACD,oBACK,YAAY,KAAK,KAAK,iBAAiB,SAAS,SAC5C,OAAO,UAAU,CAAC,KAAK;AAChC,oBAAY;AACZ,yBAAiB;AACjB;AAAA,MACJ,KAAK;AACD,cAAI,aAAQ,IAAI,CAAC,MAAb,mBAAiB,QAAO;AACxB,eAAK;AACT,oBAAY;AACZ;AAAA,MACJ;AAEI,YAAI,CAAC;AACD,2BAAiB;AACrB,oBAAY;AAAA,IACpB;AAAA,EACJ;AACA,SAAO,EAAE,SAAS,eAAe;AACrC;AAYA,IAAM,WAAN,MAAe;AAAA,EACX,YAAY,UAAU,CAAC,GAAG;AACtB,SAAK,MAAM;AACX,SAAK,eAAe;AACpB,SAAK,UAAU,CAAC;AAChB,SAAK,SAAS,CAAC;AACf,SAAK,WAAW,CAAC;AACjB,SAAK,UAAU,CAAC,QAAQ,MAAM,SAAS,YAAY;AAC/C,YAAM,MAAM,YAAY,MAAM;AAC9B,UAAI;AACA,aAAK,SAAS,KAAK,IAAI,YAAY,KAAK,MAAM,OAAO,CAAC;AAAA;AAEtD,aAAK,OAAO,KAAK,IAAI,eAAe,KAAK,MAAM,OAAO,CAAC;AAAA,IAC/D;AAEA,SAAK,aAAa,IAAI,WAAW,EAAE,SAAS,QAAQ,WAAW,MAAM,CAAC;AACtE,SAAK,UAAU;AAAA,EACnB;AAAA,EACA,SAAS,KAAK,UAAU;AACpB,UAAM,EAAE,SAAS,eAAe,IAAI,aAAa,KAAK,OAAO;AAE7D,QAAI,SAAS;AACT,YAAM,KAAK,IAAI;AACf,UAAI,UAAU;AACV,YAAI,UAAU,IAAI,UAAU,GAAG,IAAI,OAAO;AAAA,EAAK,OAAO,KAAK;AAAA,MAC/D,WACS,kBAAkB,IAAI,WAAW,YAAY,CAAC,IAAI;AACvD,YAAI,gBAAgB;AAAA,MACxB,WACS,aAAa,EAAE,KAAK,CAAC,GAAG,QAAQ,GAAG,MAAM,SAAS,GAAG;AAC1D,YAAI,KAAK,GAAG,MAAM,CAAC;AACnB,YAAI,OAAO,EAAE;AACT,eAAK,GAAG;AACZ,cAAM,KAAK,GAAG;AACd,WAAG,gBAAgB,KAAK,GAAG,OAAO;AAAA,EAAK,EAAE,KAAK;AAAA,MAClD,OACK;AACD,cAAM,KAAK,GAAG;AACd,WAAG,gBAAgB,KAAK,GAAG,OAAO;AAAA,EAAK,EAAE,KAAK;AAAA,MAClD;AAAA,IACJ;AACA,QAAI,UAAU;AACV,YAAM,UAAU,KAAK,MAAM,IAAI,QAAQ,KAAK,MAAM;AAClD,YAAM,UAAU,KAAK,MAAM,IAAI,UAAU,KAAK,QAAQ;AAAA,IAC1D,OACK;AACD,UAAI,SAAS,KAAK;AAClB,UAAI,WAAW,KAAK;AAAA,IACxB;AACA,SAAK,UAAU,CAAC;AAChB,SAAK,SAAS,CAAC;AACf,SAAK,WAAW,CAAC;AAAA,EACrB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,aAAa;AACT,WAAO;AAAA,MACH,SAAS,aAAa,KAAK,OAAO,EAAE;AAAA,MACpC,YAAY,KAAK;AAAA,MACjB,QAAQ,KAAK;AAAA,MACb,UAAU,KAAK;AAAA,IACnB;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,CAAC,QAAQ,QAAQ,WAAW,OAAO,YAAY,IAAI;AAC/C,eAAW,SAAS;AAChB,aAAO,KAAK,KAAK,KAAK;AAC1B,WAAO,KAAK,IAAI,UAAU,SAAS;AAAA,EACvC;AAAA;AAAA,EAEA,CAAC,KAAK,OAAO;AACT,YAAQ,MAAM,MAAM;AAAA,MAChB,KAAK;AACD,aAAK,WAAW,IAAI,MAAM,QAAQ,CAAC,QAAQ,SAAS,YAAY;AAC5D,gBAAM,MAAM,YAAY,KAAK;AAC7B,cAAI,CAAC,KAAK;AACV,eAAK,QAAQ,KAAK,iBAAiB,SAAS,OAAO;AAAA,QACvD,CAAC;AACD,aAAK,QAAQ,KAAK,MAAM,MAAM;AAC9B,aAAK,eAAe;AACpB;AAAA,MACJ,KAAK,YAAY;AACb,cAAM,MAAM,WAAW,KAAK,SAAS,KAAK,YAAY,OAAO,KAAK,OAAO;AACzE,YAAI,KAAK,gBAAgB,CAAC,IAAI,WAAW;AACrC,eAAK,QAAQ,OAAO,gBAAgB,iDAAiD;AACzF,aAAK,SAAS,KAAK,KAAK;AACxB,YAAI,KAAK;AACL,gBAAM,KAAK;AACf,aAAK,MAAM;AACX,aAAK,eAAe;AACpB;AAAA,MACJ;AAAA,MACA,KAAK;AAAA,MACL,KAAK;AACD;AAAA,MACJ,KAAK;AAAA,MACL,KAAK;AACD,aAAK,QAAQ,KAAK,MAAM,MAAM;AAC9B;AAAA,MACJ,KAAK,SAAS;AACV,cAAM,MAAM,MAAM,SACZ,GAAG,MAAM,OAAO,KAAK,KAAK,UAAU,MAAM,MAAM,CAAC,KACjD,MAAM;AACZ,cAAM,QAAQ,IAAI,eAAe,YAAY,KAAK,GAAG,oBAAoB,GAAG;AAC5E,YAAI,KAAK,gBAAgB,CAAC,KAAK;AAC3B,eAAK,OAAO,KAAK,KAAK;AAAA;AAEtB,eAAK,IAAI,OAAO,KAAK,KAAK;AAC9B;AAAA,MACJ;AAAA,MACA,KAAK,WAAW;AACZ,YAAI,CAAC,KAAK,KAAK;AACX,gBAAM,MAAM;AACZ,eAAK,OAAO,KAAK,IAAI,eAAe,YAAY,KAAK,GAAG,oBAAoB,GAAG,CAAC;AAChF;AAAA,QACJ;AACA,aAAK,IAAI,WAAW,SAAS;AAC7B,cAAM,MAAM,WAAW,MAAM,KAAK,MAAM,SAAS,MAAM,OAAO,QAAQ,KAAK,IAAI,QAAQ,QAAQ,KAAK,OAAO;AAC3G,aAAK,SAAS,KAAK,KAAK,IAAI;AAC5B,YAAI,IAAI,SAAS;AACb,gBAAM,KAAK,KAAK,IAAI;AACpB,eAAK,IAAI,UAAU,KAAK,GAAG,EAAE;AAAA,EAAK,IAAI,OAAO,KAAK,IAAI;AAAA,QAC1D;AACA,aAAK,IAAI,MAAM,CAAC,IAAI,IAAI;AACxB;AAAA,MACJ;AAAA,MACA;AACI,aAAK,OAAO,KAAK,IAAI,eAAe,YAAY,KAAK,GAAG,oBAAoB,qBAAqB,MAAM,IAAI,EAAE,CAAC;AAAA,IACtH;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,CAAC,IAAI,WAAW,OAAO,YAAY,IAAI;AACnC,QAAI,KAAK,KAAK;AACV,WAAK,SAAS,KAAK,KAAK,IAAI;AAC5B,YAAM,KAAK;AACX,WAAK,MAAM;AAAA,IACf,WACS,UAAU;AACf,YAAM,OAAO,OAAO,OAAO,EAAE,aAAa,KAAK,WAAW,GAAG,KAAK,OAAO;AACzE,YAAM,MAAM,IAAI,SAAS,QAAW,IAAI;AACxC,UAAI,KAAK;AACL,aAAK,QAAQ,WAAW,gBAAgB,uCAAuC;AACnF,UAAI,QAAQ,CAAC,GAAG,WAAW,SAAS;AACpC,WAAK,SAAS,KAAK,KAAK;AACxB,YAAM;AAAA,IACV;AAAA,EACJ;AACJ;;;ACtNA;AAAA;AAAA;AAAA;AAAA;AAAA,gBAAAE;AAAA,EAAA;AAAA,sBAAAC;AAAA,EAAA,gBAAAC;AAAA,EAAA;AAAA;AAAA;AAAA,mBAAAC;AAAA,EAAA;AAAA,eAAAC;AAAA;;;ACKA,SAAS,gBAAgB,OAAO,SAAS,MAAM,SAAS;AACpD,MAAI,OAAO;AACP,UAAM,WAAW,CAAC,KAAK,MAAM,YAAY;AACrC,YAAM,SAAS,OAAO,QAAQ,WAAW,MAAM,MAAM,QAAQ,GAAG,IAAI,IAAI,CAAC,IAAI,IAAI;AACjF,UAAI;AACA,gBAAQ,QAAQ,MAAM,OAAO;AAAA;AAE7B,cAAM,IAAI,eAAe,CAAC,QAAQ,SAAS,CAAC,GAAG,MAAM,OAAO;AAAA,IACpE;AACA,YAAQ,MAAM,MAAM;AAAA,MAChB,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AACD,eAAO,kBAAkB,OAAO,QAAQ,QAAQ;AAAA,MACpD,KAAK;AACD,eAAO,mBAAmB,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,OAAO,QAAQ;AAAA,IAC1E;AAAA,EACJ;AACA,SAAO;AACX;AAeA,SAAS,kBAAkB,OAAO,SAAS;AACvC,QAAM,EAAE,cAAc,OAAO,QAAQ,SAAS,OAAO,SAAS,IAAI,OAAO,QAAQ,IAAI;AACrF,QAAM,SAAS,gBAAgB,EAAE,MAAM,MAAM,GAAG;AAAA,IAC5C;AAAA,IACA,QAAQ,SAAS,IAAI,IAAI,OAAO,MAAM,IAAI;AAAA,IAC1C;AAAA,IACA,SAAS,EAAE,YAAY,MAAM,WAAW,GAAG;AAAA,EAC/C,CAAC;AACD,QAAM,MAAM,QAAQ,OAAO;AAAA,IACvB,EAAE,MAAM,WAAW,QAAQ,IAAI,QAAQ,QAAQ,KAAK;AAAA,EACxD;AACA,UAAQ,OAAO,CAAC,GAAG;AAAA,IACf,KAAK;AAAA,IACL,KAAK,KAAK;AACN,YAAM,KAAK,OAAO,QAAQ,IAAI;AAC9B,YAAM,OAAO,OAAO,UAAU,GAAG,EAAE;AACnC,YAAM,OAAO,OAAO,UAAU,KAAK,CAAC,IAAI;AACxC,YAAM,QAAQ;AAAA,QACV,EAAE,MAAM,uBAAuB,QAAQ,QAAQ,QAAQ,KAAK;AAAA,MAChE;AACA,UAAI,CAAC,mBAAmB,OAAO,GAAG;AAC9B,cAAM,KAAK,EAAE,MAAM,WAAW,QAAQ,IAAI,QAAQ,QAAQ,KAAK,CAAC;AACpE,aAAO,EAAE,MAAM,gBAAgB,QAAQ,QAAQ,OAAO,QAAQ,KAAK;AAAA,IACvE;AAAA,IACA,KAAK;AACD,aAAO,EAAE,MAAM,wBAAwB,QAAQ,QAAQ,QAAQ,IAAI;AAAA,IACvE,KAAK;AACD,aAAO,EAAE,MAAM,wBAAwB,QAAQ,QAAQ,QAAQ,IAAI;AAAA,IACvE;AACI,aAAO,EAAE,MAAM,UAAU,QAAQ,QAAQ,QAAQ,IAAI;AAAA,EAC7D;AACJ;AAiBA,SAAS,eAAe,OAAO,OAAO,UAAU,CAAC,GAAG;AAChD,MAAI,EAAE,WAAW,OAAO,cAAc,OAAO,SAAS,OAAO,KAAK,IAAI;AACtE,MAAI,SAAS,YAAY,QAAQ,MAAM,SAAS;AAChD,MAAI,YAAY,OAAO,WAAW;AAC9B,cAAU;AACd,MAAI,CAAC;AACD,YAAQ,MAAM,MAAM;AAAA,MAChB,KAAK;AACD,eAAO;AACP;AAAA,MACJ,KAAK;AACD,eAAO;AACP;AAAA,MACJ,KAAK,gBAAgB;AACjB,cAAM,SAAS,MAAM,MAAM,CAAC;AAC5B,YAAI,OAAO,SAAS;AAChB,gBAAM,IAAI,MAAM,6BAA6B;AACjD,eAAO,OAAO,OAAO,CAAC,MAAM,MAAM,iBAAiB;AACnD;AAAA,MACJ;AAAA,MACA;AACI,eAAO;AAAA,IACf;AACJ,QAAM,SAAS,gBAAgB,EAAE,MAAM,MAAM,GAAG;AAAA,IAC5C,aAAa,eAAe,WAAW;AAAA,IACvC,QAAQ,WAAW,QAAQ,SAAS,IAAI,IAAI,OAAO,MAAM,IAAI;AAAA,IAC7D;AAAA,IACA,SAAS,EAAE,YAAY,MAAM,WAAW,GAAG;AAAA,EAC/C,CAAC;AACD,UAAQ,OAAO,CAAC,GAAG;AAAA,IACf,KAAK;AAAA,IACL,KAAK;AACD,0BAAoB,OAAO,MAAM;AACjC;AAAA,IACJ,KAAK;AACD,yBAAmB,OAAO,QAAQ,sBAAsB;AACxD;AAAA,IACJ,KAAK;AACD,yBAAmB,OAAO,QAAQ,sBAAsB;AACxD;AAAA,IACJ;AACI,yBAAmB,OAAO,QAAQ,QAAQ;AAAA,EAClD;AACJ;AACA,SAAS,oBAAoB,OAAO,QAAQ;AACxC,QAAM,KAAK,OAAO,QAAQ,IAAI;AAC9B,QAAM,OAAO,OAAO,UAAU,GAAG,EAAE;AACnC,QAAM,OAAO,OAAO,UAAU,KAAK,CAAC,IAAI;AACxC,MAAI,MAAM,SAAS,gBAAgB;AAC/B,UAAM,SAAS,MAAM,MAAM,CAAC;AAC5B,QAAI,OAAO,SAAS;AAChB,YAAM,IAAI,MAAM,6BAA6B;AACjD,WAAO,SAAS;AAChB,UAAM,SAAS;AAAA,EACnB,OACK;AACD,UAAM,EAAE,OAAO,IAAI;AACnB,UAAM,SAAS,YAAY,QAAQ,MAAM,SAAS;AAClD,UAAM,QAAQ;AAAA,MACV,EAAE,MAAM,uBAAuB,QAAQ,QAAQ,QAAQ,KAAK;AAAA,IAChE;AACA,QAAI,CAAC,mBAAmB,OAAO,SAAS,QAAQ,MAAM,MAAM,MAAS;AACjE,YAAM,KAAK,EAAE,MAAM,WAAW,QAAQ,IAAI,QAAQ,QAAQ,KAAK,CAAC;AACpE,eAAW,OAAO,OAAO,KAAK,KAAK;AAC/B,UAAI,QAAQ,UAAU,QAAQ;AAC1B,eAAO,MAAM,GAAG;AACxB,WAAO,OAAO,OAAO,EAAE,MAAM,gBAAgB,QAAQ,OAAO,QAAQ,KAAK,CAAC;AAAA,EAC9E;AACJ;AAEA,SAAS,mBAAmB,OAAO,KAAK;AACpC,MAAI;AACA,eAAW,MAAM;AACb,cAAQ,GAAG,MAAM;AAAA,QACb,KAAK;AAAA,QACL,KAAK;AACD,gBAAM,KAAK,EAAE;AACb;AAAA,QACJ,KAAK;AACD,gBAAM,KAAK,EAAE;AACb,iBAAO;AAAA,MACf;AACR,SAAO;AACX;AACA,SAAS,mBAAmB,OAAO,QAAQ,MAAM;AAC7C,UAAQ,MAAM,MAAM;AAAA,IAChB,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AACD,YAAM,OAAO;AACb,YAAM,SAAS;AACf;AAAA,IACJ,KAAK,gBAAgB;AACjB,YAAM,MAAM,MAAM,MAAM,MAAM,CAAC;AAC/B,UAAI,KAAK,OAAO;AAChB,UAAI,MAAM,MAAM,CAAC,EAAE,SAAS;AACxB,cAAM,MAAM,MAAM,CAAC,EAAE,OAAO;AAChC,iBAAW,OAAO;AACd,YAAI,UAAU;AAClB,aAAO,MAAM;AACb,aAAO,OAAO,OAAO,EAAE,MAAM,QAAQ,IAAI,CAAC;AAC1C;AAAA,IACJ;AAAA,IACA,KAAK;AAAA,IACL,KAAK,aAAa;AACd,YAAM,SAAS,MAAM,SAAS,OAAO;AACrC,YAAM,KAAK,EAAE,MAAM,WAAW,QAAQ,QAAQ,MAAM,QAAQ,QAAQ,KAAK;AACzE,aAAO,MAAM;AACb,aAAO,OAAO,OAAO,EAAE,MAAM,QAAQ,KAAK,CAAC,EAAE,EAAE,CAAC;AAChD;AAAA,IACJ;AAAA,IACA,SAAS;AACL,YAAM,SAAS,YAAY,QAAQ,MAAM,SAAS;AAClD,YAAM,MAAM,SAAS,SAAS,MAAM,QAAQ,MAAM,GAAG,IAC/C,MAAM,IAAI,OAAO,QAAM,GAAG,SAAS,WACjC,GAAG,SAAS,aACZ,GAAG,SAAS,SAAS,IACvB,CAAC;AACP,iBAAW,OAAO,OAAO,KAAK,KAAK;AAC/B,YAAI,QAAQ,UAAU,QAAQ;AAC1B,iBAAO,MAAM,GAAG;AACxB,aAAO,OAAO,OAAO,EAAE,MAAM,QAAQ,QAAQ,IAAI,CAAC;AAAA,IACtD;AAAA,EACJ;AACJ;;;AC7MA,IAAMC,aAAY,CAAC,QAAQ,UAAU,MAAM,eAAe,GAAG,IAAI,cAAc,GAAG;AAClF,SAAS,eAAe,OAAO;AAC3B,UAAQ,MAAM,MAAM;AAAA,IAChB,KAAK,gBAAgB;AACjB,UAAI,MAAM;AACV,iBAAW,OAAO,MAAM;AACpB,eAAO,eAAe,GAAG;AAC7B,aAAO,MAAM,MAAM;AAAA,IACvB;AAAA,IACA,KAAK;AAAA,IACL,KAAK,aAAa;AACd,UAAI,MAAM;AACV,iBAAW,QAAQ,MAAM;AACrB,eAAO,cAAc,IAAI;AAC7B,aAAO;AAAA,IACX;AAAA,IACA,KAAK,mBAAmB;AACpB,UAAI,MAAM,MAAM,MAAM;AACtB,iBAAW,QAAQ,MAAM;AACrB,eAAO,cAAc,IAAI;AAC7B,iBAAW,MAAM,MAAM;AACnB,eAAO,GAAG;AACd,aAAO;AAAA,IACX;AAAA,IACA,KAAK,YAAY;AACb,UAAI,MAAM,cAAc,KAAK;AAC7B,UAAI,MAAM;AACN,mBAAW,MAAM,MAAM;AACnB,iBAAO,GAAG;AAClB,aAAO;AAAA,IACX;AAAA,IACA,SAAS;AACL,UAAI,MAAM,MAAM;AAChB,UAAI,SAAS,SAAS,MAAM;AACxB,mBAAW,MAAM,MAAM;AACnB,iBAAO,GAAG;AAClB,aAAO;AAAA,IACX;AAAA,EACJ;AACJ;AACA,SAAS,cAAc,EAAE,OAAO,KAAK,KAAK,MAAM,GAAG;AAC/C,MAAI,MAAM;AACV,aAAW,MAAM;AACb,WAAO,GAAG;AACd,MAAI;AACA,WAAO,eAAe,GAAG;AAC7B,MAAI;AACA,eAAW,MAAM;AACb,aAAO,GAAG;AAClB,MAAI;AACA,WAAO,eAAe,KAAK;AAC/B,SAAO;AACX;;;AC1DA,IAAMC,SAAQ,OAAO,aAAa;AAClC,IAAMC,QAAO,OAAO,eAAe;AACnC,IAAMC,UAAS,OAAO,aAAa;AA6BnC,SAASC,OAAM,KAAK,SAAS;AACzB,MAAI,UAAU,OAAO,IAAI,SAAS;AAC9B,UAAM,EAAE,OAAO,IAAI,OAAO,OAAO,IAAI,MAAM;AAC/C,SAAO,OAAO,OAAO,CAAC,CAAC,GAAG,KAAK,OAAO;AAC1C;AAKAA,OAAM,QAAQH;AAEdG,OAAM,OAAOF;AAEbE,OAAM,SAASD;AAEfC,OAAM,aAAa,CAAC,KAAK,SAAS;AAC9B,MAAI,OAAO;AACX,aAAW,CAAC,OAAO,KAAK,KAAK,MAAM;AAC/B,UAAM,MAAM,6BAAO;AACnB,QAAI,OAAO,WAAW,KAAK;AACvB,aAAO,IAAI,MAAM,KAAK;AAAA,IAC1B;AAEI,aAAO;AAAA,EACf;AACA,SAAO;AACX;AAMAA,OAAM,mBAAmB,CAAC,KAAK,SAAS;AACpC,QAAM,SAASA,OAAM,WAAW,KAAK,KAAK,MAAM,GAAG,EAAE,CAAC;AACtD,QAAM,QAAQ,KAAK,KAAK,SAAS,CAAC,EAAE,CAAC;AACrC,QAAM,OAAO,iCAAS;AACtB,MAAI,QAAQ,WAAW;AACnB,WAAO;AACX,QAAM,IAAI,MAAM,6BAA6B;AACjD;AACA,SAAS,OAAO,MAAM,MAAM,SAAS;AACjC,MAAI,OAAO,QAAQ,MAAM,IAAI;AAC7B,MAAI,OAAO,SAAS;AAChB,WAAO;AACX,aAAW,SAAS,CAAC,OAAO,OAAO,GAAG;AAClC,UAAM,QAAQ,KAAK,KAAK;AACxB,QAAI,SAAS,WAAW,OAAO;AAC3B,eAAS,IAAI,GAAG,IAAI,MAAM,MAAM,QAAQ,EAAE,GAAG;AACzC,cAAM,KAAK,OAAO,OAAO,OAAO,KAAK,OAAO,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,GAAG,MAAM,MAAM,CAAC,GAAG,OAAO;AACnF,YAAI,OAAO,OAAO;AACd,cAAI,KAAK;AAAA,iBACJ,OAAOH;AACZ,iBAAOA;AAAA,iBACF,OAAOE,SAAQ;AACpB,gBAAM,MAAM,OAAO,GAAG,CAAC;AACvB,eAAK;AAAA,QACT;AAAA,MACJ;AACA,UAAI,OAAO,SAAS,cAAc,UAAU;AACxC,eAAO,KAAK,MAAM,IAAI;AAAA,IAC9B;AAAA,EACJ;AACA,SAAO,OAAO,SAAS,aAAa,KAAK,MAAM,IAAI,IAAI;AAC3D;;;AHzFA,IAAM,MAAM;AAEZ,IAAM,WAAW;AAEjB,IAAM,WAAW;AAEjB,IAAME,UAAS;AAEf,IAAMC,gBAAe,CAAC,UAAU,CAAC,CAAC,SAAS,WAAW;AAEtD,IAAMC,YAAW,CAAC,UAAU,CAAC,CAAC,UACzB,MAAM,SAAS,YACZ,MAAM,SAAS,0BACf,MAAM,SAAS,0BACf,MAAM,SAAS;AAGvB,SAAS,YAAY,OAAO;AACxB,UAAQ,OAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAKF;AACD,aAAO;AAAA,IACX;AACI,aAAO,KAAK,UAAU,KAAK;AAAA,EACnC;AACJ;AAEA,SAAS,UAAU,QAAQ;AACvB,UAAQ,QAAQ;AAAA,IACZ,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAKA;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,EACf;AACA,UAAQ,OAAO,CAAC,GAAG;AAAA,IACf,KAAK;AAAA,IACL,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AAAA,IACL,KAAK;AACD,aAAO;AAAA,EACf;AACA,SAAO;AACX;;;AI1BA,SAAS,QAAQ,IAAI;AACjB,UAAQ,IAAI;AAAA,IACR,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AACD,aAAO;AAAA,IACX;AACI,aAAO;AAAA,EACf;AACJ;AACA,IAAM,YAAY,IAAI,IAAI,wBAAwB;AAClD,IAAM,WAAW,IAAI,IAAI,mFAAmF;AAC5G,IAAM,qBAAqB,IAAI,IAAI,OAAO;AAC1C,IAAM,qBAAqB,IAAI,IAAI,aAAc;AACjD,IAAM,kBAAkB,CAAC,OAAO,CAAC,MAAM,mBAAmB,IAAI,EAAE;AAgBhE,IAAM,QAAN,MAAY;AAAA,EACR,cAAc;AAKV,SAAK,QAAQ;AAMb,SAAK,oBAAoB;AAMzB,SAAK,kBAAkB;AAEvB,SAAK,SAAS;AAKd,SAAK,UAAU;AAEf,SAAK,YAAY;AAKjB,SAAK,aAAa;AAElB,SAAK,cAAc;AAEnB,SAAK,aAAa;AAElB,SAAK,OAAO;AAEZ,SAAK,MAAM;AAAA,EACf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,CAAC,IAAI,QAAQ,aAAa,OAAO;AAC7B,QAAI,QAAQ;AACR,UAAI,OAAO,WAAW;AAClB,cAAM,UAAU,wBAAwB;AAC5C,WAAK,SAAS,KAAK,SAAS,KAAK,SAAS,SAAS;AACnD,WAAK,aAAa;AAAA,IACtB;AACA,SAAK,QAAQ,CAAC;AACd,QAAI,OAAO,KAAK,QAAQ;AACxB,WAAO,SAAS,cAAc,KAAK,SAAS,CAAC;AACzC,aAAO,OAAO,KAAK,UAAU,IAAI;AAAA,EACzC;AAAA,EACA,YAAY;AACR,QAAI,IAAI,KAAK;AACb,QAAI,KAAK,KAAK,OAAO,CAAC;AACtB,WAAO,OAAO,OAAO,OAAO;AACxB,WAAK,KAAK,OAAO,EAAE,CAAC;AACxB,QAAI,CAAC,MAAM,OAAO,OAAO,OAAO;AAC5B,aAAO;AACX,QAAI,OAAO;AACP,aAAO,KAAK,OAAO,IAAI,CAAC,MAAM;AAClC,WAAO;AAAA,EACX;AAAA,EACA,OAAO,GAAG;AACN,WAAO,KAAK,OAAO,KAAK,MAAM,CAAC;AAAA,EACnC;AAAA,EACA,eAAe,QAAQ;AACnB,QAAI,KAAK,KAAK,OAAO,MAAM;AAC3B,QAAI,KAAK,aAAa,GAAG;AACrB,UAAI,SAAS;AACb,aAAO,OAAO;AACV,aAAK,KAAK,OAAO,EAAE,SAAS,MAAM;AACtC,UAAI,OAAO,MAAM;AACb,cAAM,OAAO,KAAK,OAAO,SAAS,SAAS,CAAC;AAC5C,YAAI,SAAS,QAAS,CAAC,QAAQ,CAAC,KAAK;AACjC,iBAAO,SAAS,SAAS;AAAA,MACjC;AACA,aAAO,OAAO,QAAQ,UAAU,KAAK,cAAe,CAAC,MAAM,CAAC,KAAK,QAC3D,SAAS,SACT;AAAA,IACV;AACA,QAAI,OAAO,OAAO,OAAO,KAAK;AAC1B,YAAM,KAAK,KAAK,OAAO,OAAO,QAAQ,CAAC;AACvC,WAAK,OAAO,SAAS,OAAO,UAAU,QAAQ,KAAK,OAAO,SAAS,CAAC,CAAC;AACjE,eAAO;AAAA,IACf;AACA,WAAO;AAAA,EACX;AAAA,EACA,UAAU;AACN,QAAI,MAAM,KAAK;AACf,QAAI,OAAO,QAAQ,YAAa,QAAQ,MAAM,MAAM,KAAK,KAAM;AAC3D,YAAM,KAAK,OAAO,QAAQ,MAAM,KAAK,GAAG;AACxC,WAAK,aAAa;AAAA,IACtB;AACA,QAAI,QAAQ;AACR,aAAO,KAAK,QAAQ,KAAK,OAAO,UAAU,KAAK,GAAG,IAAI;AAC1D,QAAI,KAAK,OAAO,MAAM,CAAC,MAAM;AACzB,aAAO;AACX,WAAO,KAAK,OAAO,UAAU,KAAK,KAAK,GAAG;AAAA,EAC9C;AAAA,EACA,SAAS,GAAG;AACR,WAAO,KAAK,MAAM,KAAK,KAAK,OAAO;AAAA,EACvC;AAAA,EACA,QAAQ,OAAO;AACX,SAAK,SAAS,KAAK,OAAO,UAAU,KAAK,GAAG;AAC5C,SAAK,MAAM;AACX,SAAK,aAAa;AAClB,SAAK,OAAO;AACZ,WAAO;AAAA,EACX;AAAA,EACA,KAAK,GAAG;AACJ,WAAO,KAAK,OAAO,OAAO,KAAK,KAAK,CAAC;AAAA,EACzC;AAAA,EACA,CAAC,UAAU,MAAM;AACb,YAAQ,MAAM;AAAA,MACV,KAAK;AACD,eAAO,OAAO,KAAK,YAAY;AAAA,MACnC,KAAK;AACD,eAAO,OAAO,KAAK,eAAe;AAAA,MACtC,KAAK;AACD,eAAO,OAAO,KAAK,gBAAgB;AAAA,MACvC,KAAK;AACD,eAAO,OAAO,KAAK,cAAc;AAAA,MACrC,KAAK;AACD,eAAO,OAAO,KAAK,oBAAoB;AAAA,MAC3C,KAAK;AACD,eAAO,OAAO,KAAK,kBAAkB;AAAA,MACzC,KAAK;AACD,eAAO,OAAO,KAAK,iBAAiB;AAAA,MACxC,KAAK;AACD,eAAO,OAAO,KAAK,iBAAiB;AAAA,IAC5C;AAAA,EACJ;AAAA,EACA,CAAC,cAAc;AACX,QAAI,OAAO,KAAK,QAAQ;AACxB,QAAI,SAAS;AACT,aAAO,KAAK,QAAQ,QAAQ;AAChC,QAAI,KAAK,CAAC,MAAM,KAAK;AACjB,aAAO,KAAK,UAAU,CAAC;AACvB,aAAO,KAAK,UAAU,CAAC;AAAA,IAC3B;AACA,QAAI,KAAK,CAAC,MAAM,KAAK;AACjB,UAAI,SAAS,KAAK;AAClB,UAAI,KAAK,KAAK,QAAQ,GAAG;AACzB,aAAO,OAAO,IAAI;AACd,cAAM,KAAK,KAAK,KAAK,CAAC;AACtB,YAAI,OAAO,OAAO,OAAO,KAAM;AAC3B,mBAAS,KAAK;AACd;AAAA,QACJ,OACK;AACD,eAAK,KAAK,QAAQ,KAAK,KAAK,CAAC;AAAA,QACjC;AAAA,MACJ;AACA,aAAO,MAAM;AACT,cAAM,KAAK,KAAK,SAAS,CAAC;AAC1B,YAAI,OAAO,OAAO,OAAO;AACrB,oBAAU;AAAA;AAEV;AAAA,MACR;AACA,YAAM,KAAK,OAAO,KAAK,UAAU,MAAM,MAAM,OAAO,KAAK,WAAW,IAAI;AACxE,aAAO,KAAK,UAAU,KAAK,SAAS,CAAC;AACrC,WAAK,YAAY;AACjB,aAAO;AAAA,IACX;AACA,QAAI,KAAK,UAAU,GAAG;AAClB,YAAM,KAAK,OAAO,KAAK,WAAW,IAAI;AACtC,aAAO,KAAK,UAAU,KAAK,SAAS,EAAE;AACtC,aAAO,KAAK,YAAY;AACxB,aAAO;AAAA,IACX;AACA,UAAM;AACN,WAAO,OAAO,KAAK,eAAe;AAAA,EACtC;AAAA,EACA,CAAC,iBAAiB;AACd,UAAM,KAAK,KAAK,OAAO,CAAC;AACxB,QAAI,CAAC,MAAM,CAAC,KAAK;AACb,aAAO,KAAK,QAAQ,YAAY;AACpC,QAAI,OAAO,OAAO,OAAO,KAAK;AAC1B,UAAI,CAAC,KAAK,SAAS,CAAC,KAAK,SAAS,CAAC;AAC/B,eAAO,KAAK,QAAQ,YAAY;AACpC,YAAM,IAAI,KAAK,KAAK,CAAC;AACrB,WAAK,MAAM,SAAS,MAAM,UAAU,QAAQ,KAAK,OAAO,CAAC,CAAC,GAAG;AACzD,eAAO,KAAK,UAAU,CAAC;AACvB,aAAK,cAAc;AACnB,aAAK,aAAa;AAClB,eAAO,MAAM,QAAQ,QAAQ;AAAA,MACjC;AAAA,IACJ;AACA,SAAK,cAAc,OAAO,KAAK,WAAW,KAAK;AAC/C,QAAI,KAAK,aAAa,KAAK,eAAe,CAAC,QAAQ,KAAK,OAAO,CAAC,CAAC;AAC7D,WAAK,aAAa,KAAK;AAC3B,WAAO,OAAO,KAAK,gBAAgB;AAAA,EACvC;AAAA,EACA,CAAC,kBAAkB;AACf,UAAM,CAAC,KAAK,GAAG,IAAI,KAAK,KAAK,CAAC;AAC9B,QAAI,CAAC,OAAO,CAAC,KAAK;AACd,aAAO,KAAK,QAAQ,aAAa;AACrC,SAAK,QAAQ,OAAO,QAAQ,OAAO,QAAQ,QAAQ,QAAQ,GAAG,GAAG;AAC7D,YAAM,KAAK,OAAO,KAAK,UAAU,CAAC,MAAM,OAAO,KAAK,WAAW,IAAI;AACnE,WAAK,aAAa,KAAK,cAAc;AACrC,WAAK,eAAe;AACpB,aAAO,OAAO,KAAK,gBAAgB;AAAA,IACvC;AACA,WAAO;AAAA,EACX;AAAA,EACA,CAAC,gBAAgB;AACb,WAAO,KAAK,WAAW,IAAI;AAC3B,UAAM,OAAO,KAAK,QAAQ;AAC1B,QAAI,SAAS;AACT,aAAO,KAAK,QAAQ,KAAK;AAC7B,QAAI,IAAI,OAAO,KAAK,eAAe;AACnC,YAAQ,KAAK,CAAC,GAAG;AAAA,MACb,KAAK;AACD,eAAO,KAAK,UAAU,KAAK,SAAS,CAAC;AAAA,MAEzC,KAAK;AACD,eAAO,KAAK,YAAY;AACxB,eAAO,OAAO,KAAK,eAAe;AAAA,MACtC,KAAK;AAAA,MACL,KAAK;AACD,eAAO,KAAK,UAAU,CAAC;AACvB,aAAK,UAAU;AACf,aAAK,YAAY;AACjB,eAAO;AAAA,MACX,KAAK;AAAA,MACL,KAAK;AAED,eAAO,KAAK,UAAU,CAAC;AACvB,eAAO;AAAA,MACX,KAAK;AACD,eAAO,KAAK,UAAU,eAAe;AACrC,eAAO;AAAA,MACX,KAAK;AAAA,MACL,KAAK;AACD,eAAO,OAAO,KAAK,kBAAkB;AAAA,MACzC,KAAK;AAAA,MACL,KAAK;AACD,aAAK,OAAO,KAAK,uBAAuB;AACxC,aAAK,OAAO,KAAK,WAAW,IAAI;AAChC,eAAO,KAAK,UAAU,KAAK,SAAS,CAAC;AACrC,eAAO,KAAK,YAAY;AACxB,eAAO,OAAO,KAAK,iBAAiB;AAAA,MACxC;AACI,eAAO,OAAO,KAAK,iBAAiB;AAAA,IAC5C;AAAA,EACJ;AAAA,EACA,CAAC,sBAAsB;AACnB,QAAI,IAAI;AACR,QAAI,SAAS;AACb,OAAG;AACC,WAAK,OAAO,KAAK,YAAY;AAC7B,UAAI,KAAK,GAAG;AACR,aAAK,OAAO,KAAK,WAAW,KAAK;AACjC,aAAK,cAAc,SAAS;AAAA,MAChC,OACK;AACD,aAAK;AAAA,MACT;AACA,YAAM,OAAO,KAAK,WAAW,IAAI;AAAA,IACrC,SAAS,KAAK,KAAK;AACnB,UAAM,OAAO,KAAK,QAAQ;AAC1B,QAAI,SAAS;AACT,aAAO,KAAK,QAAQ,MAAM;AAC9B,QAAK,WAAW,MAAM,SAAS,KAAK,cAAc,KAAK,CAAC,MAAM,OACzD,WAAW,MACP,KAAK,WAAW,KAAK,KAAK,KAAK,WAAW,KAAK,MAChD,QAAQ,KAAK,CAAC,CAAC,GAAI;AAIvB,YAAM,kBAAkB,WAAW,KAAK,aAAa,KACjD,KAAK,cAAc,MAClB,KAAK,CAAC,MAAM,OAAO,KAAK,CAAC,MAAM;AACpC,UAAI,CAAC,iBAAiB;AAElB,aAAK,YAAY;AACjB,cAAM;AACN,eAAO,OAAO,KAAK,eAAe;AAAA,MACtC;AAAA,IACJ;AACA,QAAI,IAAI;AACR,WAAO,KAAK,CAAC,MAAM,KAAK;AACpB,WAAK,OAAO,KAAK,UAAU,CAAC;AAC5B,WAAK,OAAO,KAAK,WAAW,IAAI;AAChC,WAAK,UAAU;AAAA,IACnB;AACA,SAAK,OAAO,KAAK,eAAe;AAChC,YAAQ,KAAK,CAAC,GAAG;AAAA,MACb,KAAK;AACD,eAAO;AAAA,MACX,KAAK;AACD,eAAO,KAAK,UAAU,KAAK,SAAS,CAAC;AACrC,eAAO;AAAA,MACX,KAAK;AAAA,MACL,KAAK;AACD,eAAO,KAAK,UAAU,CAAC;AACvB,aAAK,UAAU;AACf,aAAK,aAAa;AAClB,eAAO;AAAA,MACX,KAAK;AAAA,MACL,KAAK;AACD,eAAO,KAAK,UAAU,CAAC;AACvB,aAAK,UAAU;AACf,aAAK,aAAa;AAClB,eAAO,KAAK,YAAY,SAAS;AAAA,MACrC,KAAK;AACD,eAAO,KAAK,UAAU,eAAe;AACrC,eAAO;AAAA,MACX,KAAK;AAAA,MACL,KAAK;AACD,aAAK,UAAU;AACf,eAAO,OAAO,KAAK,kBAAkB;AAAA,MACzC,KAAK,KAAK;AACN,cAAM,OAAO,KAAK,OAAO,CAAC;AAC1B,YAAI,KAAK,WAAW,QAAQ,IAAI,KAAK,SAAS,KAAK;AAC/C,eAAK,UAAU;AACf,iBAAO,KAAK,UAAU,CAAC;AACvB,iBAAO,KAAK,WAAW,IAAI;AAC3B,iBAAO;AAAA,QACX;AAAA,MACJ;AAAA,MAEA;AACI,aAAK,UAAU;AACf,eAAO,OAAO,KAAK,iBAAiB;AAAA,IAC5C;AAAA,EACJ;AAAA,EACA,CAAC,oBAAoB;AACjB,UAAM,QAAQ,KAAK,OAAO,CAAC;AAC3B,QAAI,MAAM,KAAK,OAAO,QAAQ,OAAO,KAAK,MAAM,CAAC;AACjD,QAAI,UAAU,KAAK;AACf,aAAO,QAAQ,MAAM,KAAK,OAAO,MAAM,CAAC,MAAM;AAC1C,cAAM,KAAK,OAAO,QAAQ,KAAK,MAAM,CAAC;AAAA,IAC9C,OACK;AAED,aAAO,QAAQ,IAAI;AACf,YAAI,IAAI;AACR,eAAO,KAAK,OAAO,MAAM,IAAI,CAAC,MAAM;AAChC,eAAK;AACT,YAAI,IAAI,MAAM;AACV;AACJ,cAAM,KAAK,OAAO,QAAQ,KAAK,MAAM,CAAC;AAAA,MAC1C;AAAA,IACJ;AAEA,UAAM,KAAK,KAAK,OAAO,UAAU,GAAG,GAAG;AACvC,QAAI,KAAK,GAAG,QAAQ,MAAM,KAAK,GAAG;AAClC,QAAI,OAAO,IAAI;AACX,aAAO,OAAO,IAAI;AACd,cAAM,KAAK,KAAK,eAAe,KAAK,CAAC;AACrC,YAAI,OAAO;AACP;AACJ,aAAK,GAAG,QAAQ,MAAM,EAAE;AAAA,MAC5B;AACA,UAAI,OAAO,IAAI;AAEX,cAAM,MAAM,GAAG,KAAK,CAAC,MAAM,OAAO,IAAI;AAAA,MAC1C;AAAA,IACJ;AACA,QAAI,QAAQ,IAAI;AACZ,UAAI,CAAC,KAAK;AACN,eAAO,KAAK,QAAQ,eAAe;AACvC,YAAM,KAAK,OAAO;AAAA,IACtB;AACA,WAAO,KAAK,YAAY,MAAM,GAAG,KAAK;AACtC,WAAO,KAAK,YAAY,SAAS;AAAA,EACrC;AAAA,EACA,CAAC,yBAAyB;AACtB,SAAK,oBAAoB;AACzB,SAAK,kBAAkB;AACvB,QAAI,IAAI,KAAK;AACb,WAAO,MAAM;AACT,YAAM,KAAK,KAAK,OAAO,EAAE,CAAC;AAC1B,UAAI,OAAO;AACP,aAAK,kBAAkB;AAAA,eAClB,KAAK,OAAO,MAAM;AACvB,aAAK,oBAAoB,OAAO,EAAE,IAAI;AAAA,eACjC,OAAO;AACZ;AAAA,IACR;AACA,WAAO,OAAO,KAAK,UAAU,QAAM,QAAQ,EAAE,KAAK,OAAO,GAAG;AAAA,EAChE;AAAA,EACA,CAAC,mBAAmB;AAChB,QAAI,KAAK,KAAK,MAAM;AACpB,QAAI,SAAS;AACb,QAAI;AACJ,SAAM,UAASG,KAAI,KAAK,KAAM,KAAK,KAAK,OAAOA,EAAC,GAAI,EAAEA,IAAG;AACrD,cAAQ,IAAI;AAAA,QACR,KAAK;AACD,oBAAU;AACV;AAAA,QACJ,KAAK;AACD,eAAKA;AACL,mBAAS;AACT;AAAA,QACJ,KAAK,MAAM;AACP,gBAAM,OAAO,KAAK,OAAOA,KAAI,CAAC;AAC9B,cAAI,CAAC,QAAQ,CAAC,KAAK;AACf,mBAAO,KAAK,QAAQ,cAAc;AACtC,cAAI,SAAS;AACT;AAAA,QACR;AAAA,QACA;AACI,gBAAM;AAAA,MACd;AAAA,IACJ;AACA,QAAI,CAAC,MAAM,CAAC,KAAK;AACb,aAAO,KAAK,QAAQ,cAAc;AACtC,QAAI,UAAU,KAAK,YAAY;AAC3B,UAAI,KAAK,sBAAsB;AAC3B,aAAK,aAAa;AAAA,WACjB;AACD,aAAK,aACD,KAAK,qBAAqB,KAAK,eAAe,IAAI,IAAI,KAAK;AAAA,MACnE;AACA,SAAG;AACC,cAAM,KAAK,KAAK,eAAe,KAAK,CAAC;AACrC,YAAI,OAAO;AACP;AACJ,aAAK,KAAK,OAAO,QAAQ,MAAM,EAAE;AAAA,MACrC,SAAS,OAAO;AAChB,UAAI,OAAO,IAAI;AACX,YAAI,CAAC,KAAK;AACN,iBAAO,KAAK,QAAQ,cAAc;AACtC,aAAK,KAAK,OAAO;AAAA,MACrB;AAAA,IACJ;AAGA,QAAI,IAAI,KAAK;AACb,SAAK,KAAK,OAAO,CAAC;AAClB,WAAO,OAAO;AACV,WAAK,KAAK,OAAO,EAAE,CAAC;AACxB,QAAI,OAAO,KAAM;AACb,aAAO,OAAO,OAAQ,OAAO,OAAO,OAAO,QAAQ,OAAO;AACtD,aAAK,KAAK,OAAO,EAAE,CAAC;AACxB,WAAK,IAAI;AAAA,IACb,WACS,CAAC,KAAK,iBAAiB;AAC5B,SAAG;AACC,YAAIA,KAAI,KAAK;AACb,YAAIC,MAAK,KAAK,OAAOD,EAAC;AACtB,YAAIC,QAAO;AACP,UAAAA,MAAK,KAAK,OAAO,EAAED,EAAC;AACxB,cAAM,WAAWA;AACjB,eAAOC,QAAO;AACV,UAAAA,MAAK,KAAK,OAAO,EAAED,EAAC;AACxB,YAAIC,QAAO,QAAQD,MAAK,KAAK,OAAOA,KAAI,IAAI,SAAS;AACjD,eAAKA;AAAA;AAEL;AAAA,MACR,SAAS;AAAA,IACb;AACA,UAAME;AACN,WAAO,KAAK,YAAY,KAAK,GAAG,IAAI;AACpC,WAAO,OAAO,KAAK,eAAe;AAAA,EACtC;AAAA,EACA,CAAC,mBAAmB;AAChB,UAAM,SAAS,KAAK,YAAY;AAChC,QAAI,MAAM,KAAK,MAAM;AACrB,QAAI,IAAI,KAAK,MAAM;AACnB,QAAI;AACJ,WAAQ,KAAK,KAAK,OAAO,EAAE,CAAC,GAAI;AAC5B,UAAI,OAAO,KAAK;AACZ,cAAM,OAAO,KAAK,OAAO,IAAI,CAAC;AAC9B,YAAI,QAAQ,IAAI,KAAM,UAAU,mBAAmB,IAAI,IAAI;AACvD;AACJ,cAAM;AAAA,MACV,WACS,QAAQ,EAAE,GAAG;AAClB,YAAI,OAAO,KAAK,OAAO,IAAI,CAAC;AAC5B,YAAI,OAAO,MAAM;AACb,cAAI,SAAS,MAAM;AACf,iBAAK;AACL,iBAAK;AACL,mBAAO,KAAK,OAAO,IAAI,CAAC;AAAA,UAC5B;AAEI,kBAAM;AAAA,QACd;AACA,YAAI,SAAS,OAAQ,UAAU,mBAAmB,IAAI,IAAI;AACtD;AACJ,YAAI,OAAO,MAAM;AACb,gBAAM,KAAK,KAAK,eAAe,IAAI,CAAC;AACpC,cAAI,OAAO;AACP;AACJ,cAAI,KAAK,IAAI,GAAG,KAAK,CAAC;AAAA,QAC1B;AAAA,MACJ,OACK;AACD,YAAI,UAAU,mBAAmB,IAAI,EAAE;AACnC;AACJ,cAAM;AAAA,MACV;AAAA,IACJ;AACA,QAAI,CAAC,MAAM,CAAC,KAAK;AACb,aAAO,KAAK,QAAQ,cAAc;AACtC,UAAMA;AACN,WAAO,KAAK,YAAY,MAAM,GAAG,IAAI;AACrC,WAAO,SAAS,SAAS;AAAA,EAC7B;AAAA,EACA,CAAC,UAAU,GAAG;AACV,QAAI,IAAI,GAAG;AACP,YAAM,KAAK,OAAO,OAAO,KAAK,KAAK,CAAC;AACpC,WAAK,OAAO;AACZ,aAAO;AAAA,IACX;AACA,WAAO;AAAA,EACX;AAAA,EACA,CAAC,YAAY,GAAG,YAAY;AACxB,UAAM,IAAI,KAAK,OAAO,MAAM,KAAK,KAAK,CAAC;AACvC,QAAI,GAAG;AACH,YAAM;AACN,WAAK,OAAO,EAAE;AACd,aAAO,EAAE;AAAA,IACb,WACS;AACL,YAAM;AACV,WAAO;AAAA,EACX;AAAA,EACA,CAAC,iBAAiB;AACd,YAAQ,KAAK,OAAO,CAAC,GAAG;AAAA,MACpB,KAAK;AACD,gBAAS,OAAO,KAAK,QAAQ,MACxB,OAAO,KAAK,WAAW,IAAI,MAC3B,OAAO,KAAK,eAAe;AAAA,MACpC,KAAK;AACD,gBAAS,OAAO,KAAK,UAAU,eAAe,MACzC,OAAO,KAAK,WAAW,IAAI,MAC3B,OAAO,KAAK,eAAe;AAAA,MACpC,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK,KAAK;AACN,cAAM,SAAS,KAAK,YAAY;AAChC,cAAM,MAAM,KAAK,OAAO,CAAC;AACzB,YAAI,QAAQ,GAAG,KAAM,UAAU,mBAAmB,IAAI,GAAG,GAAI;AACzD,cAAI,CAAC;AACD,iBAAK,aAAa,KAAK,cAAc;AAAA,mBAChC,KAAK;AACV,iBAAK,UAAU;AACnB,kBAAS,OAAO,KAAK,UAAU,CAAC,MAC3B,OAAO,KAAK,WAAW,IAAI,MAC3B,OAAO,KAAK,eAAe;AAAA,QACpC;AAAA,MACJ;AAAA,IACJ;AACA,WAAO;AAAA,EACX;AAAA,EACA,CAAC,UAAU;AACP,QAAI,KAAK,OAAO,CAAC,MAAM,KAAK;AACxB,UAAI,IAAI,KAAK,MAAM;AACnB,UAAI,KAAK,KAAK,OAAO,CAAC;AACtB,aAAO,CAAC,QAAQ,EAAE,KAAK,OAAO;AAC1B,aAAK,KAAK,OAAO,EAAE,CAAC;AACxB,aAAO,OAAO,KAAK,YAAY,OAAO,MAAM,IAAI,IAAI,GAAG,KAAK;AAAA,IAChE,OACK;AACD,UAAI,IAAI,KAAK,MAAM;AACnB,UAAI,KAAK,KAAK,OAAO,CAAC;AACtB,aAAO,IAAI;AACP,YAAI,SAAS,IAAI,EAAE;AACf,eAAK,KAAK,OAAO,EAAE,CAAC;AAAA,iBACf,OAAO,OACZ,UAAU,IAAI,KAAK,OAAO,IAAI,CAAC,CAAC,KAChC,UAAU,IAAI,KAAK,OAAO,IAAI,CAAC,CAAC,GAAG;AACnC,eAAK,KAAK,OAAQ,KAAK,CAAE;AAAA,QAC7B;AAEI;AAAA,MACR;AACA,aAAO,OAAO,KAAK,YAAY,GAAG,KAAK;AAAA,IAC3C;AAAA,EACJ;AAAA,EACA,CAAC,cAAc;AACX,UAAM,KAAK,KAAK,OAAO,KAAK,GAAG;AAC/B,QAAI,OAAO;AACP,aAAO,OAAO,KAAK,UAAU,CAAC;AAAA,aACzB,OAAO,QAAQ,KAAK,OAAO,CAAC,MAAM;AACvC,aAAO,OAAO,KAAK,UAAU,CAAC;AAAA;AAE9B,aAAO;AAAA,EACf;AAAA,EACA,CAAC,WAAW,WAAW;AACnB,QAAI,IAAI,KAAK,MAAM;AACnB,QAAI;AACJ,OAAG;AACC,WAAK,KAAK,OAAO,EAAE,CAAC;AAAA,IACxB,SAAS,OAAO,OAAQ,aAAa,OAAO;AAC5C,UAAM,IAAI,IAAI,KAAK;AACnB,QAAI,IAAI,GAAG;AACP,YAAM,KAAK,OAAO,OAAO,KAAK,KAAK,CAAC;AACpC,WAAK,MAAM;AAAA,IACf;AACA,WAAO;AAAA,EACX;AAAA,EACA,CAAC,UAAU,MAAM;AACb,QAAI,IAAI,KAAK;AACb,QAAI,KAAK,KAAK,OAAO,CAAC;AACtB,WAAO,CAAC,KAAK,EAAE;AACX,WAAK,KAAK,OAAO,EAAE,CAAC;AACxB,WAAO,OAAO,KAAK,YAAY,GAAG,KAAK;AAAA,EAC3C;AACJ;;;ACrsBA,IAAM,cAAN,MAAkB;AAAA,EACd,cAAc;AACV,SAAK,aAAa,CAAC;AAKnB,SAAK,aAAa,CAAC,WAAW,KAAK,WAAW,KAAK,MAAM;AAMzD,SAAK,UAAU,CAAC,WAAW;AACvB,UAAI,MAAM;AACV,UAAI,OAAO,KAAK,WAAW;AAC3B,aAAO,MAAM,MAAM;AACf,cAAM,MAAO,MAAM,QAAS;AAC5B,YAAI,KAAK,WAAW,GAAG,IAAI;AACvB,gBAAM,MAAM;AAAA;AAEZ,iBAAO;AAAA,MACf;AACA,UAAI,KAAK,WAAW,GAAG,MAAM;AACzB,eAAO,EAAE,MAAM,MAAM,GAAG,KAAK,EAAE;AACnC,UAAI,QAAQ;AACR,eAAO,EAAE,MAAM,GAAG,KAAK,OAAO;AAClC,YAAM,QAAQ,KAAK,WAAW,MAAM,CAAC;AACrC,aAAO,EAAE,MAAM,KAAK,KAAK,SAAS,QAAQ,EAAE;AAAA,IAChD;AAAA,EACJ;AACJ;;;ACjCA,SAAS,cAAc,MAAM,MAAM;AAC/B,WAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,EAAE;AAC/B,QAAI,KAAK,CAAC,EAAE,SAAS;AACjB,aAAO;AACf,SAAO;AACX;AACA,SAAS,kBAAkB,MAAM;AAC7B,WAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,EAAE,GAAG;AAClC,YAAQ,KAAK,CAAC,EAAE,MAAM;AAAA,MAClB,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AACD;AAAA,MACJ;AACI,eAAO;AAAA,IACf;AAAA,EACJ;AACA,SAAO;AACX;AACA,SAAS,YAAY,OAAO;AACxB,UAAQ,+BAAO,MAAM;AAAA,IACjB,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AACD,aAAO;AAAA,IACX;AACI,aAAO;AAAA,EACf;AACJ;AACA,SAAS,aAAa,QAAQ;AAC1B,UAAQ,OAAO,MAAM;AAAA,IACjB,KAAK;AACD,aAAO,OAAO;AAAA,IAClB,KAAK,aAAa;AACd,YAAM,KAAK,OAAO,MAAM,OAAO,MAAM,SAAS,CAAC;AAC/C,aAAO,GAAG,OAAO,GAAG;AAAA,IACxB;AAAA,IACA,KAAK;AACD,aAAO,OAAO,MAAM,OAAO,MAAM,SAAS,CAAC,EAAE;AAAA,IAEjD;AACI,aAAO,CAAC;AAAA,EAChB;AACJ;AAEA,SAAS,sBAAsB,MAAM;AAlDrC;AAmDI,MAAI,KAAK,WAAW;AAChB,WAAO,CAAC;AACZ,MAAI,IAAI,KAAK;AACb,OAAM,QAAO,EAAE,KAAK,GAAG;AACnB,YAAQ,KAAK,CAAC,EAAE,MAAM;AAAA,MAClB,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AACD,cAAM;AAAA,IACd;AAAA,EACJ;AACA,WAAO,UAAK,EAAE,CAAC,MAAR,mBAAW,UAAS,SAAS;AAAA,EAEpC;AACA,SAAO,KAAK,OAAO,GAAG,KAAK,MAAM;AACrC;AACA,SAAS,gBAAgB,IAAI;AACzB,MAAI,GAAG,MAAM,SAAS,kBAAkB;AACpC,eAAW,MAAM,GAAG,OAAO;AACvB,UAAI,GAAG,OACH,CAAC,GAAG,SACJ,CAAC,cAAc,GAAG,OAAO,kBAAkB,KAC3C,CAAC,cAAc,GAAG,KAAK,eAAe,GAAG;AACzC,YAAI,GAAG;AACH,aAAG,QAAQ,GAAG;AAClB,eAAO,GAAG;AACV,YAAI,YAAY,GAAG,KAAK,GAAG;AACvB,cAAI,GAAG,MAAM;AACT,kBAAM,UAAU,KAAK,MAAM,GAAG,MAAM,KAAK,GAAG,GAAG;AAAA;AAE/C,eAAG,MAAM,MAAM,GAAG;AAAA,QAC1B;AAEI,gBAAM,UAAU,KAAK,MAAM,GAAG,OAAO,GAAG,GAAG;AAC/C,eAAO,GAAG;AAAA,MACd;AAAA,IACJ;AAAA,EACJ;AACJ;AA4BA,IAAM,SAAN,MAAa;AAAA;AAAA;AAAA;AAAA;AAAA,EAKT,YAAY,WAAW;AAEnB,SAAK,YAAY;AAEjB,SAAK,WAAW;AAEhB,SAAK,SAAS;AAEd,SAAK,SAAS;AAEd,SAAK,YAAY;AAEjB,SAAK,QAAQ,CAAC;AAEd,SAAK,SAAS;AAEd,SAAK,OAAO;AAEZ,SAAK,QAAQ,IAAI,MAAM;AACvB,SAAK,YAAY;AAAA,EACrB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,CAAC,MAAM,QAAQ,aAAa,OAAO;AAC/B,QAAI,KAAK,aAAa,KAAK,WAAW;AAClC,WAAK,UAAU,CAAC;AACpB,eAAW,UAAU,KAAK,MAAM,IAAI,QAAQ,UAAU;AAClD,aAAO,KAAK,KAAK,MAAM;AAC3B,QAAI,CAAC;AACD,aAAO,KAAK,IAAI;AAAA,EACxB;AAAA;AAAA;AAAA;AAAA,EAIA,CAAC,KAAK,QAAQ;AACV,SAAK,SAAS;AACd,QAAI,KAAK,UAAU;AACf,WAAK,WAAW;AAChB,aAAO,KAAK,KAAK;AACjB,WAAK,UAAU,OAAO;AACtB;AAAA,IACJ;AACA,UAAM,OAAO,UAAU,MAAM;AAC7B,QAAI,CAAC,MAAM;AACP,YAAM,UAAU,qBAAqB,MAAM;AAC3C,aAAO,KAAK,IAAI,EAAE,MAAM,SAAS,QAAQ,KAAK,QAAQ,SAAS,OAAO,CAAC;AACvE,WAAK,UAAU,OAAO;AAAA,IAC1B,WACS,SAAS,UAAU;AACxB,WAAK,YAAY;AACjB,WAAK,WAAW;AAChB,WAAK,OAAO;AAAA,IAChB,OACK;AACD,WAAK,OAAO;AACZ,aAAO,KAAK,KAAK;AACjB,cAAQ,MAAM;AAAA,QACV,KAAK;AACD,eAAK,YAAY;AACjB,eAAK,SAAS;AACd,cAAI,KAAK;AACL,iBAAK,UAAU,KAAK,SAAS,OAAO,MAAM;AAC9C;AAAA,QACJ,KAAK;AACD,cAAI,KAAK,aAAa,OAAO,CAAC,MAAM;AAChC,iBAAK,UAAU,OAAO;AAC1B;AAAA,QACJ,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AACD,cAAI,KAAK;AACL,iBAAK,UAAU,OAAO;AAC1B;AAAA,QACJ,KAAK;AAAA,QACL,KAAK;AACD;AAAA,QACJ;AACI,eAAK,YAAY;AAAA,MACzB;AACA,WAAK,UAAU,OAAO;AAAA,IAC1B;AAAA,EACJ;AAAA;AAAA,EAEA,CAAC,MAAM;AACH,WAAO,KAAK,MAAM,SAAS;AACvB,aAAO,KAAK,IAAI;AAAA,EACxB;AAAA,EACA,IAAI,cAAc;AACd,UAAM,KAAK;AAAA,MACP,MAAM,KAAK;AAAA,MACX,QAAQ,KAAK;AAAA,MACb,QAAQ,KAAK;AAAA,MACb,QAAQ,KAAK;AAAA,IACjB;AACA,WAAO;AAAA,EACX;AAAA,EACA,CAAC,OAAO;AACJ,UAAM,MAAM,KAAK,KAAK,CAAC;AACvB,QAAI,KAAK,SAAS,cAAc,CAAC,OAAO,IAAI,SAAS,YAAY;AAC7D,aAAO,KAAK,MAAM,SAAS;AACvB,eAAO,KAAK,IAAI;AACpB,WAAK,MAAM,KAAK;AAAA,QACZ,MAAM;AAAA,QACN,QAAQ,KAAK;AAAA,QACb,QAAQ,KAAK;AAAA,MACjB,CAAC;AACD;AAAA,IACJ;AACA,QAAI,CAAC;AACD,aAAO,OAAO,KAAK,OAAO;AAC9B,YAAQ,IAAI,MAAM;AAAA,MACd,KAAK;AACD,eAAO,OAAO,KAAK,SAAS,GAAG;AAAA,MACnC,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AACD,eAAO,OAAO,KAAK,OAAO,GAAG;AAAA,MACjC,KAAK;AACD,eAAO,OAAO,KAAK,YAAY,GAAG;AAAA,MACtC,KAAK;AACD,eAAO,OAAO,KAAK,SAAS,GAAG;AAAA,MACnC,KAAK;AACD,eAAO,OAAO,KAAK,cAAc,GAAG;AAAA,MACxC,KAAK;AACD,eAAO,OAAO,KAAK,eAAe,GAAG;AAAA,MACzC,KAAK;AACD,eAAO,OAAO,KAAK,YAAY,GAAG;AAAA,IAC1C;AAEA,WAAO,KAAK,IAAI;AAAA,EACpB;AAAA,EACA,KAAK,GAAG;AACJ,WAAO,KAAK,MAAM,KAAK,MAAM,SAAS,CAAC;AAAA,EAC3C;AAAA,EACA,CAAC,IAAI,OAAO;AACR,UAAM,QAAQ,SAAS,KAAK,MAAM,IAAI;AAEtC,QAAI,CAAC,OAAO;AACR,YAAM,UAAU;AAChB,YAAM,EAAE,MAAM,SAAS,QAAQ,KAAK,QAAQ,QAAQ,IAAI,QAAQ;AAAA,IACpE,WACS,KAAK,MAAM,WAAW,GAAG;AAC9B,YAAM;AAAA,IACV,OACK;AACD,YAAM,MAAM,KAAK,KAAK,CAAC;AACvB,UAAI,MAAM,SAAS,gBAAgB;AAE/B,cAAM,SAAS,YAAY,MAAM,IAAI,SAAS;AAAA,MAClD,WACS,MAAM,SAAS,qBAAqB,IAAI,SAAS,YAAY;AAElE,cAAM,SAAS;AAAA,MACnB;AACA,UAAI,MAAM,SAAS;AACf,wBAAgB,KAAK;AACzB,cAAQ,IAAI,MAAM;AAAA,QACd,KAAK;AACD,cAAI,QAAQ;AACZ;AAAA,QACJ,KAAK;AACD,cAAI,MAAM,KAAK,KAAK;AACpB;AAAA,QACJ,KAAK,aAAa;AACd,gBAAM,KAAK,IAAI,MAAM,IAAI,MAAM,SAAS,CAAC;AACzC,cAAI,GAAG,OAAO;AACV,gBAAI,MAAM,KAAK,EAAE,OAAO,CAAC,GAAG,KAAK,OAAO,KAAK,CAAC,EAAE,CAAC;AACjD,iBAAK,YAAY;AACjB;AAAA,UACJ,WACS,GAAG,KAAK;AACb,eAAG,QAAQ;AAAA,UACf,OACK;AACD,mBAAO,OAAO,IAAI,EAAE,KAAK,OAAO,KAAK,CAAC,EAAE,CAAC;AACzC,iBAAK,YAAY,CAAC,GAAG;AACrB;AAAA,UACJ;AACA;AAAA,QACJ;AAAA,QACA,KAAK,aAAa;AACd,gBAAM,KAAK,IAAI,MAAM,IAAI,MAAM,SAAS,CAAC;AACzC,cAAI,GAAG;AACH,gBAAI,MAAM,KAAK,EAAE,OAAO,CAAC,GAAG,OAAO,MAAM,CAAC;AAAA;AAE1C,eAAG,QAAQ;AACf;AAAA,QACJ;AAAA,QACA,KAAK,mBAAmB;AACpB,gBAAM,KAAK,IAAI,MAAM,IAAI,MAAM,SAAS,CAAC;AACzC,cAAI,CAAC,MAAM,GAAG;AACV,gBAAI,MAAM,KAAK,EAAE,OAAO,CAAC,GAAG,KAAK,OAAO,KAAK,CAAC,EAAE,CAAC;AAAA,mBAC5C,GAAG;AACR,eAAG,QAAQ;AAAA;AAEX,mBAAO,OAAO,IAAI,EAAE,KAAK,OAAO,KAAK,CAAC,EAAE,CAAC;AAC7C;AAAA,QACJ;AAAA,QAEA;AACI,iBAAO,KAAK,IAAI;AAChB,iBAAO,KAAK,IAAI,KAAK;AAAA,MAC7B;AACA,WAAK,IAAI,SAAS,cACd,IAAI,SAAS,eACb,IAAI,SAAS,iBACZ,MAAM,SAAS,eAAe,MAAM,SAAS,cAAc;AAC5D,cAAM,OAAO,MAAM,MAAM,MAAM,MAAM,SAAS,CAAC;AAC/C,YAAI,QACA,CAAC,KAAK,OACN,CAAC,KAAK,SACN,KAAK,MAAM,SAAS,KACpB,kBAAkB,KAAK,KAAK,MAAM,OACjC,MAAM,WAAW,KACd,KAAK,MAAM,MAAM,QAAM,GAAG,SAAS,aAAa,GAAG,SAAS,MAAM,MAAM,IAAI;AAChF,cAAI,IAAI,SAAS;AACb,gBAAI,MAAM,KAAK;AAAA;AAEf,gBAAI,MAAM,KAAK,EAAE,OAAO,KAAK,MAAM,CAAC;AACxC,gBAAM,MAAM,OAAO,IAAI,CAAC;AAAA,QAC5B;AAAA,MACJ;AAAA,IACJ;AAAA,EACJ;AAAA,EACA,CAAC,SAAS;AACN,YAAQ,KAAK,MAAM;AAAA,MACf,KAAK;AACD,cAAM,EAAE,MAAM,aAAa,QAAQ,KAAK,QAAQ,QAAQ,KAAK,OAAO;AACpE;AAAA,MACJ,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AACD,cAAM,KAAK;AACX;AAAA,MACJ,KAAK;AAAA,MACL,KAAK,aAAa;AACd,cAAM,MAAM;AAAA,UACR,MAAM;AAAA,UACN,QAAQ,KAAK;AAAA,UACb,OAAO,CAAC;AAAA,QACZ;AACA,YAAI,KAAK,SAAS;AACd,cAAI,MAAM,KAAK,KAAK,WAAW;AACnC,aAAK,MAAM,KAAK,GAAG;AACnB;AAAA,MACJ;AAAA,IACJ;AACA,UAAM;AAAA,MACF,MAAM;AAAA,MACN,QAAQ,KAAK;AAAA,MACb,SAAS,cAAc,KAAK,IAAI;AAAA,MAChC,QAAQ,KAAK;AAAA,IACjB;AAAA,EACJ;AAAA,EACA,CAAC,SAAS,KAAK;AACX,QAAI,IAAI;AACJ,aAAO,OAAO,KAAK,QAAQ,GAAG;AAClC,YAAQ,KAAK,MAAM;AAAA,MACf,KAAK,aAAa;AACd,YAAI,kBAAkB,IAAI,KAAK,MAAM,IAAI;AACrC,iBAAO,KAAK,IAAI;AAChB,iBAAO,KAAK,KAAK;AAAA,QACrB;AAEI,cAAI,MAAM,KAAK,KAAK,WAAW;AACnC;AAAA,MACJ;AAAA,MACA,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AACD,YAAI,MAAM,KAAK,KAAK,WAAW;AAC/B;AAAA,IACR;AACA,UAAM,KAAK,KAAK,gBAAgB,GAAG;AACnC,QAAI;AACA,WAAK,MAAM,KAAK,EAAE;AAAA,SACjB;AACD,YAAM;AAAA,QACF,MAAM;AAAA,QACN,QAAQ,KAAK;AAAA,QACb,SAAS,cAAc,KAAK,IAAI;AAAA,QAChC,QAAQ,KAAK;AAAA,MACjB;AAAA,IACJ;AAAA,EACJ;AAAA,EACA,CAAC,OAAO,QAAQ;AACZ,QAAI,KAAK,SAAS,iBAAiB;AAC/B,YAAM,OAAO,aAAa,KAAK,KAAK,CAAC,CAAC;AACtC,YAAM,QAAQ,sBAAsB,IAAI;AACxC,UAAI;AACJ,UAAI,OAAO,KAAK;AACZ,cAAM,OAAO;AACb,YAAI,KAAK,KAAK,WAAW;AACzB,eAAO,OAAO;AAAA,MAClB;AAEI,cAAM,CAAC,KAAK,WAAW;AAC3B,YAAMC,OAAM;AAAA,QACR,MAAM;AAAA,QACN,QAAQ,OAAO;AAAA,QACf,QAAQ,OAAO;AAAA,QACf,OAAO,CAAC,EAAE,OAAO,KAAK,QAAQ,IAAI,CAAC;AAAA,MACvC;AACA,WAAK,YAAY;AACjB,WAAK,MAAM,KAAK,MAAM,SAAS,CAAC,IAAIA;AAAA,IACxC;AAEI,aAAO,KAAK,QAAQ,MAAM;AAAA,EAClC;AAAA,EACA,CAAC,YAAY,QAAQ;AACjB,YAAQ,KAAK,MAAM;AAAA,MACf,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AACD,eAAO,MAAM,KAAK,KAAK,WAAW;AAClC;AAAA,MACJ,KAAK;AACD,eAAO,SAAS,KAAK;AAErB,aAAK,YAAY;AACjB,aAAK,SAAS;AACd,YAAI,KAAK,WAAW;AAChB,cAAI,KAAK,KAAK,OAAO,QAAQ,IAAI,IAAI;AACrC,iBAAO,OAAO,GAAG;AACb,iBAAK,UAAU,KAAK,SAAS,EAAE;AAC/B,iBAAK,KAAK,OAAO,QAAQ,MAAM,EAAE,IAAI;AAAA,UACzC;AAAA,QACJ;AACA,eAAO,KAAK,IAAI;AAChB;AAAA,MAEJ;AACI,eAAO,KAAK,IAAI;AAChB,eAAO,KAAK,KAAK;AAAA,IACzB;AAAA,EACJ;AAAA,EACA,CAAC,SAASA,MAAK;AAtdnB;AAudQ,UAAM,KAAKA,KAAI,MAAMA,KAAI,MAAM,SAAS,CAAC;AAEzC,YAAQ,KAAK,MAAM;AAAA,MACf,KAAK;AACD,aAAK,YAAY;AACjB,YAAI,GAAG,OAAO;AACV,gBAAM,MAAM,SAAS,GAAG,QAAQ,GAAG,MAAM,MAAM;AAC/C,gBAAM,OAAO,MAAM,QAAQ,GAAG,IAAI,IAAI,IAAI,SAAS,CAAC,IAAI;AACxD,eAAI,6BAAM,UAAS;AACf,uCAAK,KAAK,KAAK;AAAA;AAEf,YAAAA,KAAI,MAAM,KAAK,EAAE,OAAO,CAAC,KAAK,WAAW,EAAE,CAAC;AAAA,QACpD,WACS,GAAG,KAAK;AACb,aAAG,IAAI,KAAK,KAAK,WAAW;AAAA,QAChC,OACK;AACD,aAAG,MAAM,KAAK,KAAK,WAAW;AAAA,QAClC;AACA;AAAA,MACJ,KAAK;AAAA,MACL,KAAK;AACD,YAAI,GAAG,OAAO;AACV,UAAAA,KAAI,MAAM,KAAK,EAAE,OAAO,CAAC,KAAK,WAAW,EAAE,CAAC;AAAA,QAChD,WACS,GAAG,KAAK;AACb,aAAG,IAAI,KAAK,KAAK,WAAW;AAAA,QAChC,OACK;AACD,cAAI,KAAK,kBAAkB,GAAG,OAAOA,KAAI,MAAM,GAAG;AAC9C,kBAAM,OAAOA,KAAI,MAAMA,KAAI,MAAM,SAAS,CAAC;AAC3C,kBAAM,OAAM,kCAAM,UAAN,mBAAa;AACzB,gBAAI,MAAM,QAAQ,GAAG,GAAG;AACpB,oBAAM,UAAU,KAAK,MAAM,KAAK,GAAG,KAAK;AACxC,kBAAI,KAAK,KAAK,WAAW;AACzB,cAAAA,KAAI,MAAM,IAAI;AACd;AAAA,YACJ;AAAA,UACJ;AACA,aAAG,MAAM,KAAK,KAAK,WAAW;AAAA,QAClC;AACA;AAAA,IACR;AACA,QAAI,KAAK,UAAUA,KAAI,QAAQ;AAC3B,YAAM,cAAc,CAAC,KAAK,aAAa,KAAK,WAAWA,KAAI;AAC3D,YAAM,aAAa,gBACd,GAAG,OAAO,GAAG,gBACd,KAAK,SAAS;AAElB,UAAI,QAAQ,CAAC;AACb,UAAI,cAAc,GAAG,OAAO,CAAC,GAAG,OAAO;AACnC,cAAM,KAAK,CAAC;AACZ,iBAAS,IAAI,GAAG,IAAI,GAAG,IAAI,QAAQ,EAAE,GAAG;AACpC,gBAAM,KAAK,GAAG,IAAI,CAAC;AACnB,kBAAQ,GAAG,MAAM;AAAA,YACb,KAAK;AACD,iBAAG,KAAK,CAAC;AACT;AAAA,YACJ,KAAK;AACD;AAAA,YACJ,KAAK;AACD,kBAAI,GAAG,SAASA,KAAI;AAChB,mBAAG,SAAS;AAChB;AAAA,YACJ;AACI,iBAAG,SAAS;AAAA,UACpB;AAAA,QACJ;AACA,YAAI,GAAG,UAAU;AACb,kBAAQ,GAAG,IAAI,OAAO,GAAG,CAAC,CAAC;AAAA,MACnC;AACA,cAAQ,KAAK,MAAM;AAAA,QACf,KAAK;AAAA,QACL,KAAK;AACD,cAAI,cAAc,GAAG,OAAO;AACxB,kBAAM,KAAK,KAAK,WAAW;AAC3B,YAAAA,KAAI,MAAM,KAAK,EAAE,MAAM,CAAC;AACxB,iBAAK,YAAY;AAAA,UACrB,WACS,GAAG,KAAK;AACb,eAAG,IAAI,KAAK,KAAK,WAAW;AAAA,UAChC,OACK;AACD,eAAG,MAAM,KAAK,KAAK,WAAW;AAAA,UAClC;AACA;AAAA,QACJ,KAAK;AACD,cAAI,CAAC,GAAG,OAAO,CAAC,GAAG,aAAa;AAC5B,eAAG,MAAM,KAAK,KAAK,WAAW;AAC9B,eAAG,cAAc;AAAA,UACrB,WACS,cAAc,GAAG,OAAO;AAC7B,kBAAM,KAAK,KAAK,WAAW;AAC3B,YAAAA,KAAI,MAAM,KAAK,EAAE,OAAO,aAAa,KAAK,CAAC;AAAA,UAC/C,OACK;AACD,iBAAK,MAAM,KAAK;AAAA,cACZ,MAAM;AAAA,cACN,QAAQ,KAAK;AAAA,cACb,QAAQ,KAAK;AAAA,cACb,OAAO,CAAC,EAAE,OAAO,CAAC,KAAK,WAAW,GAAG,aAAa,KAAK,CAAC;AAAA,YAC5D,CAAC;AAAA,UACL;AACA,eAAK,YAAY;AACjB;AAAA,QACJ,KAAK;AACD,cAAI,GAAG,aAAa;AAChB,gBAAI,CAAC,GAAG,KAAK;AACT,kBAAI,cAAc,GAAG,OAAO,SAAS,GAAG;AACpC,uBAAO,OAAO,IAAI,EAAE,KAAK,MAAM,KAAK,CAAC,KAAK,WAAW,EAAE,CAAC;AAAA,cAC5D,OACK;AACD,sBAAMC,SAAQ,sBAAsB,GAAG,KAAK;AAC5C,qBAAK,MAAM,KAAK;AAAA,kBACZ,MAAM;AAAA,kBACN,QAAQ,KAAK;AAAA,kBACb,QAAQ,KAAK;AAAA,kBACb,OAAO,CAAC,EAAE,OAAAA,QAAO,KAAK,MAAM,KAAK,CAAC,KAAK,WAAW,EAAE,CAAC;AAAA,gBACzD,CAAC;AAAA,cACL;AAAA,YACJ,WACS,GAAG,OAAO;AACf,cAAAD,KAAI,MAAM,KAAK,EAAE,OAAO,CAAC,GAAG,KAAK,MAAM,KAAK,CAAC,KAAK,WAAW,EAAE,CAAC;AAAA,YACpE,WACS,cAAc,GAAG,KAAK,eAAe,GAAG;AAC7C,mBAAK,MAAM,KAAK;AAAA,gBACZ,MAAM;AAAA,gBACN,QAAQ,KAAK;AAAA,gBACb,QAAQ,KAAK;AAAA,gBACb,OAAO,CAAC,EAAE,OAAO,KAAK,MAAM,KAAK,CAAC,KAAK,WAAW,EAAE,CAAC;AAAA,cACzD,CAAC;AAAA,YACL,WACS,YAAY,GAAG,GAAG,KACvB,CAAC,cAAc,GAAG,KAAK,SAAS,GAAG;AACnC,oBAAMC,SAAQ,sBAAsB,GAAG,KAAK;AAC5C,oBAAM,MAAM,GAAG;AACf,oBAAM,MAAM,GAAG;AACf,kBAAI,KAAK,KAAK,WAAW;AAEzB,qBAAO,GAAG;AAEV,qBAAO,GAAG;AACV,mBAAK,MAAM,KAAK;AAAA,gBACZ,MAAM;AAAA,gBACN,QAAQ,KAAK;AAAA,gBACb,QAAQ,KAAK;AAAA,gBACb,OAAO,CAAC,EAAE,OAAAA,QAAO,KAAK,IAAI,CAAC;AAAA,cAC/B,CAAC;AAAA,YACL,WACS,MAAM,SAAS,GAAG;AAEvB,iBAAG,MAAM,GAAG,IAAI,OAAO,OAAO,KAAK,WAAW;AAAA,YAClD,OACK;AACD,iBAAG,IAAI,KAAK,KAAK,WAAW;AAAA,YAChC;AAAA,UACJ,OACK;AACD,gBAAI,CAAC,GAAG,KAAK;AACT,qBAAO,OAAO,IAAI,EAAE,KAAK,MAAM,KAAK,CAAC,KAAK,WAAW,EAAE,CAAC;AAAA,YAC5D,WACS,GAAG,SAAS,YAAY;AAC7B,cAAAD,KAAI,MAAM,KAAK,EAAE,OAAO,KAAK,MAAM,KAAK,CAAC,KAAK,WAAW,EAAE,CAAC;AAAA,YAChE,WACS,cAAc,GAAG,KAAK,eAAe,GAAG;AAC7C,mBAAK,MAAM,KAAK;AAAA,gBACZ,MAAM;AAAA,gBACN,QAAQ,KAAK;AAAA,gBACb,QAAQ,KAAK;AAAA,gBACb,OAAO,CAAC,EAAE,OAAO,CAAC,GAAG,KAAK,MAAM,KAAK,CAAC,KAAK,WAAW,EAAE,CAAC;AAAA,cAC7D,CAAC;AAAA,YACL,OACK;AACD,iBAAG,IAAI,KAAK,KAAK,WAAW;AAAA,YAChC;AAAA,UACJ;AACA,eAAK,YAAY;AACjB;AAAA,QACJ,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK,wBAAwB;AACzB,gBAAM,KAAK,KAAK,WAAW,KAAK,IAAI;AACpC,cAAI,cAAc,GAAG,OAAO;AACxB,YAAAA,KAAI,MAAM,KAAK,EAAE,OAAO,KAAK,IAAI,KAAK,CAAC,EAAE,CAAC;AAC1C,iBAAK,YAAY;AAAA,UACrB,WACS,GAAG,KAAK;AACb,iBAAK,MAAM,KAAK,EAAE;AAAA,UACtB,OACK;AACD,mBAAO,OAAO,IAAI,EAAE,KAAK,IAAI,KAAK,CAAC,EAAE,CAAC;AACtC,iBAAK,YAAY;AAAA,UACrB;AACA;AAAA,QACJ;AAAA,QACA,SAAS;AACL,gBAAM,KAAK,KAAK,gBAAgBA,IAAG;AACnC,cAAI,IAAI;AACJ,gBAAI,GAAG,SAAS,aAAa;AACzB,kBAAI,CAAC,GAAG,eACJ,GAAG,OACH,CAAC,cAAc,GAAG,KAAK,SAAS,GAAG;AACnC,uBAAO,KAAK,IAAI;AAAA,kBACZ,MAAM;AAAA,kBACN,QAAQ,KAAK;AAAA,kBACb,SAAS;AAAA,kBACT,QAAQ,KAAK;AAAA,gBACjB,CAAC;AACD;AAAA,cACJ;AAAA,YACJ,WACS,aAAa;AAClB,cAAAA,KAAI,MAAM,KAAK,EAAE,MAAM,CAAC;AAAA,YAC5B;AACA,iBAAK,MAAM,KAAK,EAAE;AAClB;AAAA,UACJ;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ;AACA,WAAO,KAAK,IAAI;AAChB,WAAO,KAAK,KAAK;AAAA,EACrB;AAAA,EACA,CAAC,cAAcE,MAAK;AAvrBxB;AAwrBQ,UAAM,KAAKA,KAAI,MAAMA,KAAI,MAAM,SAAS,CAAC;AACzC,YAAQ,KAAK,MAAM;AAAA,MACf,KAAK;AACD,YAAI,GAAG,OAAO;AACV,gBAAM,MAAM,SAAS,GAAG,QAAQ,GAAG,MAAM,MAAM;AAC/C,gBAAM,OAAO,MAAM,QAAQ,GAAG,IAAI,IAAI,IAAI,SAAS,CAAC,IAAI;AACxD,eAAI,6BAAM,UAAS;AACf,uCAAK,KAAK,KAAK;AAAA;AAEf,YAAAA,KAAI,MAAM,KAAK,EAAE,OAAO,CAAC,KAAK,WAAW,EAAE,CAAC;AAAA,QACpD;AAEI,aAAG,MAAM,KAAK,KAAK,WAAW;AAClC;AAAA,MACJ,KAAK;AAAA,MACL,KAAK;AACD,YAAI,GAAG;AACH,UAAAA,KAAI,MAAM,KAAK,EAAE,OAAO,CAAC,KAAK,WAAW,EAAE,CAAC;AAAA,aAC3C;AACD,cAAI,KAAK,kBAAkB,GAAG,OAAOA,KAAI,MAAM,GAAG;AAC9C,kBAAM,OAAOA,KAAI,MAAMA,KAAI,MAAM,SAAS,CAAC;AAC3C,kBAAM,OAAM,kCAAM,UAAN,mBAAa;AACzB,gBAAI,MAAM,QAAQ,GAAG,GAAG;AACpB,oBAAM,UAAU,KAAK,MAAM,KAAK,GAAG,KAAK;AACxC,kBAAI,KAAK,KAAK,WAAW;AACzB,cAAAA,KAAI,MAAM,IAAI;AACd;AAAA,YACJ;AAAA,UACJ;AACA,aAAG,MAAM,KAAK,KAAK,WAAW;AAAA,QAClC;AACA;AAAA,MACJ,KAAK;AAAA,MACL,KAAK;AACD,YAAI,GAAG,SAAS,KAAK,UAAUA,KAAI;AAC/B;AACJ,WAAG,MAAM,KAAK,KAAK,WAAW;AAC9B;AAAA,MACJ,KAAK;AACD,YAAI,KAAK,WAAWA,KAAI;AACpB;AACJ,YAAI,GAAG,SAAS,cAAc,GAAG,OAAO,cAAc;AAClD,UAAAA,KAAI,MAAM,KAAK,EAAE,OAAO,CAAC,KAAK,WAAW,EAAE,CAAC;AAAA;AAE5C,aAAG,MAAM,KAAK,KAAK,WAAW;AAClC;AAAA,IACR;AACA,QAAI,KAAK,SAASA,KAAI,QAAQ;AAC1B,YAAM,KAAK,KAAK,gBAAgBA,IAAG;AACnC,UAAI,IAAI;AACJ,aAAK,MAAM,KAAK,EAAE;AAClB;AAAA,MACJ;AAAA,IACJ;AACA,WAAO,KAAK,IAAI;AAChB,WAAO,KAAK,KAAK;AAAA,EACrB;AAAA,EACA,CAAC,eAAe,IAAI;AAChB,UAAM,KAAK,GAAG,MAAM,GAAG,MAAM,SAAS,CAAC;AACvC,QAAI,KAAK,SAAS,kBAAkB;AAChC,UAAI;AACJ,SAAG;AACC,eAAO,KAAK,IAAI;AAChB,cAAM,KAAK,KAAK,CAAC;AAAA,MACrB,SAAS,OAAO,IAAI,SAAS;AAAA,IACjC,WACS,GAAG,IAAI,WAAW,GAAG;AAC1B,cAAQ,KAAK,MAAM;AAAA,QACf,KAAK;AAAA,QACL,KAAK;AACD,cAAI,CAAC,MAAM,GAAG;AACV,eAAG,MAAM,KAAK,EAAE,OAAO,CAAC,KAAK,WAAW,EAAE,CAAC;AAAA;AAE3C,eAAG,MAAM,KAAK,KAAK,WAAW;AAClC;AAAA,QACJ,KAAK;AACD,cAAI,CAAC,MAAM,GAAG;AACV,eAAG,MAAM,KAAK,EAAE,OAAO,CAAC,GAAG,KAAK,MAAM,KAAK,CAAC,KAAK,WAAW,EAAE,CAAC;AAAA,mBAC1D,GAAG;AACR,eAAG,IAAI,KAAK,KAAK,WAAW;AAAA;AAE5B,mBAAO,OAAO,IAAI,EAAE,KAAK,MAAM,KAAK,CAAC,KAAK,WAAW,EAAE,CAAC;AAC5D;AAAA,QACJ,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AACD,cAAI,CAAC,MAAM,GAAG;AACV,eAAG,MAAM,KAAK,EAAE,OAAO,CAAC,KAAK,WAAW,EAAE,CAAC;AAAA,mBACtC,GAAG;AACR,eAAG,IAAI,KAAK,KAAK,WAAW;AAAA;AAE5B,eAAG,MAAM,KAAK,KAAK,WAAW;AAClC;AAAA,QACJ,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK,wBAAwB;AACzB,gBAAM,KAAK,KAAK,WAAW,KAAK,IAAI;AACpC,cAAI,CAAC,MAAM,GAAG;AACV,eAAG,MAAM,KAAK,EAAE,OAAO,CAAC,GAAG,KAAK,IAAI,KAAK,CAAC,EAAE,CAAC;AAAA,mBACxC,GAAG;AACR,iBAAK,MAAM,KAAK,EAAE;AAAA;AAElB,mBAAO,OAAO,IAAI,EAAE,KAAK,IAAI,KAAK,CAAC,EAAE,CAAC;AAC1C;AAAA,QACJ;AAAA,QACA,KAAK;AAAA,QACL,KAAK;AACD,aAAG,IAAI,KAAK,KAAK,WAAW;AAC5B;AAAA,MACR;AACA,YAAM,KAAK,KAAK,gBAAgB,EAAE;AAElC,UAAI;AACA,aAAK,MAAM,KAAK,EAAE;AAAA,WACjB;AACD,eAAO,KAAK,IAAI;AAChB,eAAO,KAAK,KAAK;AAAA,MACrB;AAAA,IACJ,OACK;AACD,YAAM,SAAS,KAAK,KAAK,CAAC;AAC1B,UAAI,OAAO,SAAS,gBACd,KAAK,SAAS,mBAAmB,OAAO,WAAW,GAAG,UACnD,KAAK,SAAS,aACX,CAAC,OAAO,MAAM,OAAO,MAAM,SAAS,CAAC,EAAE,MAAO;AACtD,eAAO,KAAK,IAAI;AAChB,eAAO,KAAK,KAAK;AAAA,MACrB,WACS,KAAK,SAAS,mBACnB,OAAO,SAAS,mBAAmB;AACnC,cAAM,OAAO,aAAa,MAAM;AAChC,cAAM,QAAQ,sBAAsB,IAAI;AACxC,wBAAgB,EAAE;AAClB,cAAM,MAAM,GAAG,IAAI,OAAO,GAAG,GAAG,IAAI,MAAM;AAC1C,YAAI,KAAK,KAAK,WAAW;AACzB,cAAMF,OAAM;AAAA,UACR,MAAM;AAAA,UACN,QAAQ,GAAG;AAAA,UACX,QAAQ,GAAG;AAAA,UACX,OAAO,CAAC,EAAE,OAAO,KAAK,IAAI,IAAI,CAAC;AAAA,QACnC;AACA,aAAK,YAAY;AACjB,aAAK,MAAM,KAAK,MAAM,SAAS,CAAC,IAAIA;AAAA,MACxC,OACK;AACD,eAAO,KAAK,QAAQ,EAAE;AAAA,MAC1B;AAAA,IACJ;AAAA,EACJ;AAAA,EACA,WAAW,MAAM;AACb,QAAI,KAAK,WAAW;AAChB,UAAI,KAAK,KAAK,OAAO,QAAQ,IAAI,IAAI;AACrC,aAAO,OAAO,GAAG;AACb,aAAK,UAAU,KAAK,SAAS,EAAE;AAC/B,aAAK,KAAK,OAAO,QAAQ,MAAM,EAAE,IAAI;AAAA,MACzC;AAAA,IACJ;AACA,WAAO;AAAA,MACH;AAAA,MACA,QAAQ,KAAK;AAAA,MACb,QAAQ,KAAK;AAAA,MACb,QAAQ,KAAK;AAAA,IACjB;AAAA,EACJ;AAAA,EACA,gBAAgB,QAAQ;AACpB,YAAQ,KAAK,MAAM;AAAA,MACf,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AACD,eAAO,KAAK,WAAW,KAAK,IAAI;AAAA,MACpC,KAAK;AACD,eAAO;AAAA,UACH,MAAM;AAAA,UACN,QAAQ,KAAK;AAAA,UACb,QAAQ,KAAK;AAAA,UACb,OAAO,CAAC,KAAK,WAAW;AAAA,UACxB,QAAQ;AAAA,QACZ;AAAA,MACJ,KAAK;AAAA,MACL,KAAK;AACD,eAAO;AAAA,UACH,MAAM;AAAA,UACN,QAAQ,KAAK;AAAA,UACb,QAAQ,KAAK;AAAA,UACb,OAAO,KAAK;AAAA,UACZ,OAAO,CAAC;AAAA,UACR,KAAK,CAAC;AAAA,QACV;AAAA,MACJ,KAAK;AACD,eAAO;AAAA,UACH,MAAM;AAAA,UACN,QAAQ,KAAK;AAAA,UACb,QAAQ,KAAK;AAAA,UACb,OAAO,CAAC,EAAE,OAAO,CAAC,KAAK,WAAW,EAAE,CAAC;AAAA,QACzC;AAAA,MACJ,KAAK,oBAAoB;AACrB,aAAK,YAAY;AACjB,cAAM,OAAO,aAAa,MAAM;AAChC,cAAM,QAAQ,sBAAsB,IAAI;AACxC,cAAM,KAAK,KAAK,WAAW;AAC3B,eAAO;AAAA,UACH,MAAM;AAAA,UACN,QAAQ,KAAK;AAAA,UACb,QAAQ,KAAK;AAAA,UACb,OAAO,CAAC,EAAE,OAAO,aAAa,KAAK,CAAC;AAAA,QACxC;AAAA,MACJ;AAAA,MACA,KAAK,iBAAiB;AAClB,aAAK,YAAY;AACjB,cAAM,OAAO,aAAa,MAAM;AAChC,cAAM,QAAQ,sBAAsB,IAAI;AACxC,eAAO;AAAA,UACH,MAAM;AAAA,UACN,QAAQ,KAAK;AAAA,UACb,QAAQ,KAAK;AAAA,UACb,OAAO,CAAC,EAAE,OAAO,KAAK,MAAM,KAAK,CAAC,KAAK,WAAW,EAAE,CAAC;AAAA,QACzD;AAAA,MACJ;AAAA,IACJ;AACA,WAAO;AAAA,EACX;AAAA,EACA,kBAAkB,OAAO,QAAQ;AAC7B,QAAI,KAAK,SAAS;AACd,aAAO;AACX,QAAI,KAAK,UAAU;AACf,aAAO;AACX,WAAO,MAAM,MAAM,QAAM,GAAG,SAAS,aAAa,GAAG,SAAS,OAAO;AAAA,EACzE;AAAA,EACA,CAAC,YAAY,QAAQ;AACjB,QAAI,KAAK,SAAS,YAAY;AAC1B,UAAI,OAAO;AACP,eAAO,IAAI,KAAK,KAAK,WAAW;AAAA;AAEhC,eAAO,MAAM,CAAC,KAAK,WAAW;AAClC,UAAI,KAAK,SAAS;AACd,eAAO,KAAK,IAAI;AAAA,IACxB;AAAA,EACJ;AAAA,EACA,CAAC,QAAQ,OAAO;AACZ,YAAQ,KAAK,MAAM;AAAA,MACf,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AACD,eAAO,KAAK,IAAI;AAChB,eAAO,KAAK,KAAK;AACjB;AAAA,MACJ,KAAK;AACD,aAAK,YAAY;AAAA,MAErB,KAAK;AAAA,MACL,KAAK;AAAA,MACL;AAEI,YAAI,MAAM;AACN,gBAAM,IAAI,KAAK,KAAK,WAAW;AAAA;AAE/B,gBAAM,MAAM,CAAC,KAAK,WAAW;AACjC,YAAI,KAAK,SAAS;AACd,iBAAO,KAAK,IAAI;AAAA,IAC5B;AAAA,EACJ;AACJ;;;AC57BA,SAAS,aAAa,SAAS;AAC3B,QAAM,eAAe,QAAQ,iBAAiB;AAC9C,QAAM,cAAc,QAAQ,eAAgB,gBAAgB,IAAI,YAAY,KAAM;AAClF,SAAO,EAAE,aAAa,aAAa;AACvC;AAUA,SAAS,kBAAkB,QAAQ,UAAU,CAAC,GAAG;AAC7C,QAAM,EAAE,aAAa,aAAa,IAAI,aAAa,OAAO;AAC1D,QAAM,SAAS,IAAI,OAAO,2CAAa,UAAU;AACjD,QAAM,WAAW,IAAI,SAAS,OAAO;AACrC,QAAM,OAAO,MAAM,KAAK,SAAS,QAAQ,OAAO,MAAM,MAAM,CAAC,CAAC;AAC9D,MAAI,gBAAgB;AAChB,eAAW,OAAO,MAAM;AACpB,UAAI,OAAO,QAAQ,cAAc,QAAQ,WAAW,CAAC;AACrD,UAAI,SAAS,QAAQ,cAAc,QAAQ,WAAW,CAAC;AAAA,IAC3D;AACJ,MAAI,KAAK,SAAS;AACd,WAAO;AACX,SAAO,OAAO,OAAO,CAAC,GAAG,EAAE,OAAO,KAAK,GAAG,SAAS,WAAW,CAAC;AACnE;AAEA,SAAS,cAAc,QAAQ,UAAU,CAAC,GAAG;AACzC,QAAM,EAAE,aAAa,aAAa,IAAI,aAAa,OAAO;AAC1D,QAAM,SAAS,IAAI,OAAO,2CAAa,UAAU;AACjD,QAAM,WAAW,IAAI,SAAS,OAAO;AAErC,MAAI,MAAM;AACV,aAAW,QAAQ,SAAS,QAAQ,OAAO,MAAM,MAAM,GAAG,MAAM,OAAO,MAAM,GAAG;AAC5E,QAAI,CAAC;AACD,YAAM;AAAA,aACD,IAAI,QAAQ,aAAa,UAAU;AACxC,UAAI,OAAO,KAAK,IAAI,eAAe,KAAK,MAAM,MAAM,GAAG,CAAC,GAAG,iBAAiB,yEAAyE,CAAC;AACtJ;AAAA,IACJ;AAAA,EACJ;AACA,MAAI,gBAAgB,aAAa;AAC7B,QAAI,OAAO,QAAQ,cAAc,QAAQ,WAAW,CAAC;AACrD,QAAI,SAAS,QAAQ,cAAc,QAAQ,WAAW,CAAC;AAAA,EAC3D;AACA,SAAO;AACX;AACA,SAAS,MAAM,KAAK,SAAS,SAAS;AAClC,MAAI,WAAW;AACf,MAAI,OAAO,YAAY,YAAY;AAC/B,eAAW;AAAA,EACf,WACS,YAAY,UAAa,WAAW,OAAO,YAAY,UAAU;AACtE,cAAU;AAAA,EACd;AACA,QAAM,MAAM,cAAc,KAAK,OAAO;AACtC,MAAI,CAAC;AACD,WAAO;AACX,MAAI,SAAS,QAAQ,aAAW,KAAK,IAAI,QAAQ,UAAU,OAAO,CAAC;AACnE,MAAI,IAAI,OAAO,SAAS,GAAG;AACvB,QAAI,IAAI,QAAQ,aAAa;AACzB,YAAM,IAAI,OAAO,CAAC;AAAA;AAElB,UAAI,SAAS,CAAC;AAAA,EACtB;AACA,SAAO,IAAI,KAAK,OAAO,OAAO,EAAE,SAAS,SAAS,GAAG,OAAO,CAAC;AACjE;AACA,SAASG,WAAU,OAAO,UAAU,SAAS;AACzC,MAAI,YAAY;AAChB,MAAI,OAAO,aAAa,cAAc,MAAM,QAAQ,QAAQ,GAAG;AAC3D,gBAAY;AAAA,EAChB,WACS,YAAY,UAAa,UAAU;AACxC,cAAU;AAAA,EACd;AACA,MAAI,OAAO,YAAY;AACnB,cAAU,QAAQ;AACtB,MAAI,OAAO,YAAY,UAAU;AAC7B,UAAM,SAAS,KAAK,MAAM,OAAO;AACjC,cAAU,SAAS,IAAI,SAAY,SAAS,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,OAAO;AAAA,EAC7E;AACA,MAAI,UAAU,QAAW;AACrB,UAAM,EAAE,cAAc,IAAI,WAAW,YAAY,CAAC;AAClD,QAAI,CAAC;AACD,aAAO;AAAA,EACf;AACA,MAAI,WAAW,KAAK,KAAK,CAAC;AACtB,WAAO,MAAM,SAAS,OAAO;AACjC,SAAO,IAAI,SAAS,OAAO,WAAW,OAAO,EAAE,SAAS,OAAO;AACnE;;;AChGA,IAAO,kBAAQ;", "names": ["stringify", "res", "res", "map", "schema", "node", "schema", "i", "end", "isBlock", "_a", "map", "value", "map", "schema", "stringify", "comment", "str", "schema", "map", "map", "schema", "schema", "seq", "seq", "schema", "intIdentify", "schema", "seq", "schema", "pairs", "map", "schema", "pairs", "omap", "seq", "floatNaN", "floatExp", "float", "intIdentify", "intResolve", "n", "intStringify", "intOct", "int", "intHex", "schema", "set", "map", "res", "schema", "intOct", "int", "intHex", "floatNaN", "floatExp", "float", "schema", "tags", "merge", "schema", "res", "composeNode", "composeEmptyNode", "map", "composeNode", "composeEmptyNode", "seq", "composeNode", "composeEmptyNode", "isMap", "map", "CN", "value", "end", "schema", "tag", "SCALAR", "isCollection", "isScalar", "stringify", "visit", "stringify", "BREAK", "SKIP", "REMOVE", "visit", "SCALAR", "isCollection", "isScalar", "i", "ch", "SCALAR", "map", "start", "seq", "stringify"]}