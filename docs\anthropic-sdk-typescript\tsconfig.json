{"include": ["src", "tests", "examples"], "exclude": [], "compilerOptions": {"target": "es2020", "lib": ["es2020"], "module": "commonjs", "moduleResolution": "node", "esModuleInterop": true, "baseUrl": "./", "paths": {"@anthropic-ai/sdk/*": ["src/*"], "@anthropic-ai/sdk": ["src/index.ts"]}, "noEmit": true, "resolveJsonModule": true, "forceConsistentCasingInFileNames": true, "strict": true, "noImplicitAny": true, "strictNullChecks": true, "strictFunctionTypes": true, "strictBindCallApply": true, "strictPropertyInitialization": true, "noImplicitThis": true, "noImplicitReturns": true, "alwaysStrict": true, "exactOptionalPropertyTypes": true, "noUncheckedIndexedAccess": true, "noImplicitOverride": true, "noPropertyAccessFromIndexSignature": true, "isolatedModules": false, "skipLibCheck": true}}