#!/usr/bin/env bash

errors=()

if [ -z "${STAINLESS_API_KEY}" ]; then
  errors+=("The STAINLESS_API_KEY secret has not been set. Please contact <PERSON><PERSON><PERSON> for an API key & set it in your organization secrets on GitHub.")
fi

if [ -z "${NPM_TOKEN}" ]; then
  errors+=("The NPM_TOKEN secret has not been set. Please set it in either this repository's secrets or your organization secrets")
fi

lenErrors=${#errors[@]}

if [[ lenErrors -gt 0 ]]; then
  echo -e "Found the following errors in the release environment:\n"

  for error in "${errors[@]}"; do
    echo -e "- $error\n"
  done

  exit 1
fi

echo "The environment is ready to push releases!"

