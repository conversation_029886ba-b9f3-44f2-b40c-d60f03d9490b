import Anthropic from "@anthropic-ai/sdk";
import { MessageParam } from "@anthropic-ai/sdk/resources";
import { Settings } from "../../../../service/Settings";
import { IAIEngine, ChatMessage, ChatOptions } from "../IAIEngine";
import { universalFetch } from "../../../../../utils/fetch";

export class AnthropicEngine implements IAIEngine {
  private anthropic = new Anthropic({
    apiKey: "",
    fetch: universalFetch,
  });

  private async updateConfig() {
    this.anthropic.apiKey = await Settings.get("anthropicApiKey");
  }

  async *chat(messages: ChatMessage[], options: ChatOptions): AsyncGenerator<string> {
    await this.updateConfig();

    // Adapt messages to the format required by the Anthropic SDK
    const anthropicMessages: MessageParam[] = messages.map((msg) => ({
      role: msg.role === "assistant" ? "assistant" : "user",
      content: msg.content,
    }));

    const stream = this.anthropic.messages.stream({
      messages: anthropicMessages,
      model: options.model,
      max_tokens: 4096, // Default max tokens
    });

    for await (const chunk of stream) {
      if (chunk.type === "content_block_delta" && chunk.delta.type === "text_delta") {
        yield chunk.delta.text;
      }
    }
  }

  async getModels(): Promise<string[]> {
    // Anthropic SDK does not provide a method to list models, so we return a hardcoded list.
    return ["claude-3-opus-20240229", "claude-3-sonnet-20240229", "claude-3-haiku-20240307"];
  }
}
