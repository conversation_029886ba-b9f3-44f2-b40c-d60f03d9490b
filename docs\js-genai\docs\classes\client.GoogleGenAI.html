<!DOCTYPE html><html class="default" lang="en" data-base=".."><head><meta charset="utf-8"/><meta http-equiv="x-ua-compatible" content="IE=edge"/><title>GoogleGenAI | @google/genai</title><meta name="description" content="Documentation for @google/genai"/><meta name="viewport" content="width=device-width, initial-scale=1"/><link rel="stylesheet" href="../assets/style.css"/><link rel="stylesheet" href="../assets/highlight.css"/><script defer src="../assets/main.js"></script><script async src="../assets/icons.js" id="tsd-icons-script"></script><script async src="../assets/search.js" id="tsd-search-script"></script><script async src="../assets/navigation.js" id="tsd-nav-script"></script></head><body><script>document.documentElement.dataset.theme = localStorage.getItem("tsd-theme") || "os";document.body.style.display="none";setTimeout(() => app?app.showPage():document.body.style.removeProperty("display"),500)</script><header class="tsd-page-toolbar"><div class="tsd-toolbar-contents container"><div class="table-cell" id="tsd-search"><div class="field"><label for="tsd-search-field" class="tsd-widget tsd-toolbar-icon search no-caption"><svg width="16" height="16" viewBox="0 0 16 16" fill="none" aria-hidden="true"><use href="../assets/icons.svg#icon-search"></use></svg></label><input type="text" id="tsd-search-field" aria-label="Search"/></div><div class="field"><div id="tsd-toolbar-links"></div></div><ul class="results"><li class="state loading">Preparing search index...</li><li class="state failure">The search index is not available</li></ul><a href="../index.html" class="title">@google/genai</a></div><div class="table-cell" id="tsd-widgets"><a href="#" class="tsd-widget tsd-toolbar-icon menu no-caption" data-toggle="menu" aria-label="Menu"><svg width="16" height="16" viewBox="0 0 16 16" fill="none" aria-hidden="true"><use href="../assets/icons.svg#icon-menu"></use></svg></a></div></div></header><div class="container container-main"><div class="col-content"><div class="tsd-page-title"><ul class="tsd-breadcrumb"><li><a href="../modules.html">@google/genai</a></li><li><a href="../modules/client.html">client</a></li><li><a href="client.GoogleGenAI.html">GoogleGenAI</a></li></ul><h1>Class GoogleGenAI</h1></div><section class="tsd-panel tsd-comment"><div class="tsd-comment tsd-typography"><p>The Google GenAI SDK.</p>
</div><div class="tsd-comment tsd-typography"><div class="tsd-tag-remarks"><h4 class="tsd-anchor-link"><a id="remarks" class="tsd-anchor"></a>Remarks<a href="#remarks" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h4><p>Provides access to the GenAI features through either the <a href="https://cloud.google.com/vertex-ai/docs/reference/rest">Gemini API</a>
or the <a href="https://cloud.google.com/vertex-ai/docs/reference/rest">Vertex AI API</a>.</p>
<p>The <a href="../interfaces/client.GoogleGenAIOptions.html#vertexai" class="tsd-kind-property">GoogleGenAIOptions.vertexai</a> value determines which of the API services to use.</p>
<p>When using the Gemini API, a <a href="../interfaces/client.GoogleGenAIOptions.html#apikey" class="tsd-kind-property">GoogleGenAIOptions.apiKey</a> must also be set,
when using Vertex AI <a href="../interfaces/client.GoogleGenAIOptions.html#project" class="tsd-kind-property">GoogleGenAIOptions.project</a> and <a href="../interfaces/client.GoogleGenAIOptions.html#location" class="tsd-kind-property">GoogleGenAIOptions.location</a> must also be set.</p>
</div><div class="tsd-tag-example"><h4 class="tsd-anchor-link"><a id="example" class="tsd-anchor"></a>Example<a href="#example" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h4><p>Initializing the SDK for using the Gemini API:</p>
<pre><code class="ts"><span class="hl-3">import</span><span class="hl-1"> {</span><span class="hl-4">GoogleGenAI</span><span class="hl-1">} </span><span class="hl-3">from</span><span class="hl-1"> </span><span class="hl-2">&#39;@google/genai&#39;</span><span class="hl-1">;</span><br/><span class="hl-5">const</span><span class="hl-1"> </span><span class="hl-6">ai</span><span class="hl-1"> = </span><span class="hl-5">new</span><span class="hl-1"> </span><span class="hl-0">GoogleGenAI</span><span class="hl-1">({</span><span class="hl-4">apiKey:</span><span class="hl-1"> </span><span class="hl-2">&#39;GEMINI_API_KEY&#39;</span><span class="hl-1">});</span>
</code><button type="button">Copy</button></pre>

</div><div class="tsd-tag-example"><h4 class="tsd-anchor-link"><a id="example-1" class="tsd-anchor"></a>Example<a href="#example-1" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h4><p>Initializing the SDK for using the Vertex AI API:</p>
<pre><code class="ts"><span class="hl-3">import</span><span class="hl-1"> {</span><span class="hl-4">GoogleGenAI</span><span class="hl-1">} </span><span class="hl-3">from</span><span class="hl-1"> </span><span class="hl-2">&#39;@google/genai&#39;</span><span class="hl-1">;</span><br/><span class="hl-5">const</span><span class="hl-1"> </span><span class="hl-6">ai</span><span class="hl-1"> = </span><span class="hl-5">new</span><span class="hl-1"> </span><span class="hl-0">GoogleGenAI</span><span class="hl-1">({</span><br/><span class="hl-1">  </span><span class="hl-4">vertexai:</span><span class="hl-1"> </span><span class="hl-5">true</span><span class="hl-1">,</span><br/><span class="hl-1">  </span><span class="hl-4">project:</span><span class="hl-1"> </span><span class="hl-2">&#39;PROJECT_ID&#39;</span><span class="hl-1">,</span><br/><span class="hl-1">  </span><span class="hl-4">location:</span><span class="hl-1"> </span><span class="hl-2">&#39;PROJECT_LOCATION&#39;</span><br/><span class="hl-1">});</span>
</code><button type="button">Copy</button></pre>

</div></div></section><aside class="tsd-sources"><ul><li>Defined in src/client.ts:122</li></ul></aside><section class="tsd-panel-group tsd-index-group"><section class="tsd-panel tsd-index-panel"><details class="tsd-index-content tsd-accordion" open><summary class="tsd-accordion-summary tsd-index-summary"><h5 class="tsd-index-heading uppercase" role="button" aria-expanded="false" tabIndex="0"><svg width="16" height="16" viewBox="0 0 16 16" fill="none" aria-hidden="true"><use href="../assets/icons.svg#icon-chevronSmall"></use></svg> Index</h5></summary><div class="tsd-accordion-details"><section class="tsd-index-section"><h3 class="tsd-index-heading">Constructors</h3><div class="tsd-index-list"><a href="client.GoogleGenAI.html#constructor" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Constructor"><use href="../assets/icons.svg#icon-512"></use></svg><span>constructor</span></a>
</div></section><section class="tsd-index-section"><h3 class="tsd-index-heading">Properties</h3><div class="tsd-index-list"><a href="client.GoogleGenAI.html#authtokens" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>auth<wbr/>Tokens</span></a>
<a href="client.GoogleGenAI.html#batches" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>batches</span></a>
<a href="client.GoogleGenAI.html#caches" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>caches</span></a>
<a href="client.GoogleGenAI.html#chats" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>chats</span></a>
<a href="client.GoogleGenAI.html#files" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>files</span></a>
<a href="client.GoogleGenAI.html#live" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>live</span></a>
<a href="client.GoogleGenAI.html#models" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>models</span></a>
<a href="client.GoogleGenAI.html#operations" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>operations</span></a>
<a href="client.GoogleGenAI.html#tunings" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>tunings</span></a>
<a href="client.GoogleGenAI.html#vertexai" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>vertexai</span></a>
</div></section></div></details></section></section><details class="tsd-panel-group tsd-member-group tsd-accordion" open><summary class="tsd-accordion-summary" data-key="section-Constructors"><h2><svg width="20" height="20" viewBox="0 0 24 24" fill="none" aria-hidden="true"><use href="../assets/icons.svg#icon-chevronDown"></use></svg> Constructors</h2></summary><section><section class="tsd-panel tsd-member"><a id="constructor" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><span>constructor</span><a href="#constructor" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><ul class="tsd-signatures"><li class=""><div class="tsd-signature tsd-anchor-link"><a id="constructorgooglegenai" class="tsd-anchor"></a><span class="tsd-signature-keyword">new</span> <span class="tsd-kind-constructor-signature">GoogleGenAI</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">options</span><span class="tsd-signature-symbol">:</span> <a href="../interfaces/client.GoogleGenAIOptions.html" class="tsd-signature-type tsd-kind-interface">GoogleGenAIOptions</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <a href="client.GoogleGenAI.html" class="tsd-signature-type tsd-kind-class">GoogleGenAI</a><a href="#constructorgooglegenai" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></div><div class="tsd-description"><div class="tsd-parameters"><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameter-list"><li><span><span class="tsd-kind-parameter">options</span>: <a href="../interfaces/client.GoogleGenAIOptions.html" class="tsd-signature-type tsd-kind-interface">GoogleGenAIOptions</a></span></li></ul></div><h4 class="tsd-returns-title">Returns <a href="client.GoogleGenAI.html" class="tsd-signature-type tsd-kind-class">GoogleGenAI</a></h4><aside class="tsd-sources"><ul><li>Defined in src/client.ts:137</li></ul></aside></div></li></ul></section></section></details><details class="tsd-panel-group tsd-member-group tsd-accordion" open><summary class="tsd-accordion-summary" data-key="section-Properties"><h2><svg width="20" height="20" viewBox="0 0 24 24" fill="none" aria-hidden="true"><use href="../assets/icons.svg#icon-chevronDown"></use></svg> Properties</h2></summary><section><section class="tsd-panel tsd-member"><a id="authtokens" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><code class="tsd-tag">Readonly</code><span>auth<wbr/>Tokens</span><a href="#authtokens" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-property">authTokens</span><span class="tsd-signature-symbol">:</span> <a href="tokens.Tokens.html" class="tsd-signature-type tsd-kind-class">Tokens</a></div><aside class="tsd-sources"><ul><li>Defined in src/client.ts:134</li></ul></aside></section><section class="tsd-panel tsd-member"><a id="batches" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><code class="tsd-tag">Readonly</code><span>batches</span><a href="#batches" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-property">batches</span><span class="tsd-signature-symbol">:</span> <a href="batches.Batches.html" class="tsd-signature-type tsd-kind-class">Batches</a></div><aside class="tsd-sources"><ul><li>Defined in src/client.ts:129</li></ul></aside></section><section class="tsd-panel tsd-member"><a id="caches" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><code class="tsd-tag">Readonly</code><span>caches</span><a href="#caches" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-property">caches</span><span class="tsd-signature-symbol">:</span> <a href="caches.Caches.html" class="tsd-signature-type tsd-kind-class">Caches</a></div><aside class="tsd-sources"><ul><li>Defined in src/client.ts:131</li></ul></aside></section><section class="tsd-panel tsd-member"><a id="chats" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><code class="tsd-tag">Readonly</code><span>chats</span><a href="#chats" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-property">chats</span><span class="tsd-signature-symbol">:</span> <a href="chats.Chats.html" class="tsd-signature-type tsd-kind-class">Chats</a></div><aside class="tsd-sources"><ul><li>Defined in src/client.ts:130</li></ul></aside></section><section class="tsd-panel tsd-member"><a id="files" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><code class="tsd-tag">Readonly</code><span>files</span><a href="#files" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-property">files</span><span class="tsd-signature-symbol">:</span> <a href="files.Files.html" class="tsd-signature-type tsd-kind-class">Files</a></div><aside class="tsd-sources"><ul><li>Defined in src/client.ts:132</li></ul></aside></section><section class="tsd-panel tsd-member"><a id="live" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><code class="tsd-tag">Readonly</code><span>live</span><a href="#live" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-property">live</span><span class="tsd-signature-symbol">:</span> <a href="live.Live.html" class="tsd-signature-type tsd-kind-class">Live</a></div><aside class="tsd-sources"><ul><li>Defined in src/client.ts:128</li></ul></aside></section><section class="tsd-panel tsd-member"><a id="models" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><code class="tsd-tag">Readonly</code><span>models</span><a href="#models" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-property">models</span><span class="tsd-signature-symbol">:</span> <a href="models.Models.html" class="tsd-signature-type tsd-kind-class">Models</a></div><aside class="tsd-sources"><ul><li>Defined in src/client.ts:127</li></ul></aside></section><section class="tsd-panel tsd-member"><a id="operations" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><code class="tsd-tag">Readonly</code><span>operations</span><a href="#operations" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-property">operations</span><span class="tsd-signature-symbol">:</span> <a href="operations.Operations.html" class="tsd-signature-type tsd-kind-class">Operations</a></div><aside class="tsd-sources"><ul><li>Defined in src/client.ts:133</li></ul></aside></section><section class="tsd-panel tsd-member"><a id="tunings" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><code class="tsd-tag">Readonly</code><span>tunings</span><a href="#tunings" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-property">tunings</span><span class="tsd-signature-symbol">:</span> <a href="tunings.Tunings.html" class="tsd-signature-type tsd-kind-class">Tunings</a></div><aside class="tsd-sources"><ul><li>Defined in src/client.ts:135</li></ul></aside></section><section class="tsd-panel tsd-member"><a id="vertexai" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><code class="tsd-tag">Readonly</code><span>vertexai</span><a href="#vertexai" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-property">vertexai</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">boolean</span></div><aside class="tsd-sources"><ul><li>Defined in src/client.ts:125</li></ul></aside></section></section></details></div><div class="col-sidebar"><div class="page-menu"><div class="tsd-navigation settings"><details class="tsd-accordion"><summary class="tsd-accordion-summary"><h3><svg width="20" height="20" viewBox="0 0 24 24" fill="none" aria-hidden="true"><use href="../assets/icons.svg#icon-chevronDown"></use></svg>Settings</h3></summary><div class="tsd-accordion-details"><div class="tsd-theme-toggle"><label class="settings-label" for="tsd-theme">Theme</label><select id="tsd-theme"><option value="os">OS</option><option value="light">Light</option><option value="dark">Dark</option></select></div></div></details></div><details open class="tsd-accordion tsd-page-navigation"><summary class="tsd-accordion-summary"><h3><svg width="20" height="20" viewBox="0 0 24 24" fill="none" aria-hidden="true"><use href="../assets/icons.svg#icon-chevronDown"></use></svg>On This Page</h3></summary><div class="tsd-accordion-details"><details open class="tsd-accordion tsd-page-navigation-section"><summary class="tsd-accordion-summary" data-key="section-Constructors"><svg width="20" height="20" viewBox="0 0 24 24" fill="none" aria-hidden="true"><use href="../assets/icons.svg#icon-chevronDown"></use></svg>Constructors</summary><div><a href="#constructor" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Constructor"><use href="../assets/icons.svg#icon-512"></use></svg><span>constructor</span></a></div></details><details open class="tsd-accordion tsd-page-navigation-section"><summary class="tsd-accordion-summary" data-key="section-Properties"><svg width="20" height="20" viewBox="0 0 24 24" fill="none" aria-hidden="true"><use href="../assets/icons.svg#icon-chevronDown"></use></svg>Properties</summary><div><a href="#authtokens" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>auth<wbr/>Tokens</span></a><a href="#batches" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>batches</span></a><a href="#caches" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>caches</span></a><a href="#chats" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>chats</span></a><a href="#files" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>files</span></a><a href="#live" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>live</span></a><a href="#models" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>models</span></a><a href="#operations" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>operations</span></a><a href="#tunings" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>tunings</span></a><a href="#vertexai" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>vertexai</span></a></div></details></div></details></div><div class="site-menu"><nav class="tsd-navigation"><a href="../modules.html">@google/genai</a><ul class="tsd-small-nested-navigation" id="tsd-nav-container"><li>Loading...</li></ul></nav></div></div></div><footer><p class="tsd-generator">Generated using <a href="https://typedoc.org/" target="_blank">TypeDoc</a></p></footer><div class="overlay"></div></body></html>
