/**
 * @file Defines the interface for an AI engine service provider.
 */

/**
 * Represents a single message in a chat conversation.
 */
export interface ChatMessage {
  /**
   * The role of the author of this message.
   */
  role: "user" | "assistant" | "system" | "tool";
  /**
   * The content of the message.
   */
  content: string;
}

/**
 * Represents the options for a chat request.
 */
export interface ChatOptions {
  /**
   * The name of the model to use for the chat.
   */
  model: string;
  /**
   * A list of tools the model can use.
   */
  tools?: any[]; // TODO: Define a more specific type for tools
  /**
   * Whether to stream the response.
   */
  stream?: boolean;
}

/**
 * Interface for an AI Engine, providing a unified contract for various AI service providers.
 */
export interface IAIEngine {
  /**
   * Sends a chat request to the AI model and streams the response.
   * @param messages - An array of chat messages representing the conversation history.
   * @param options - An object containing additional parameters for the chat request, such as the model name and tools.
   * @returns An async generator that yields the AI's response as a stream of strings.
   * @TDD-ANCHOR chat
   */
  chat(messages: ChatMessage[], options: ChatOptions): AsyncGenerator<string>;

  /**
   * Retrieves a list of available model names from the AI service provider.
   * @returns A promise that resolves to an array of strings, where each string is an available model name.
   * @TDD-ANCHOR getModels
   */
  getModels(): Promise<string[]>;
}
