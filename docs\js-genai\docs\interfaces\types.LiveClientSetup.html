<!DOCTYPE html><html class="default" lang="en" data-base=".."><head><meta charset="utf-8"/><meta http-equiv="x-ua-compatible" content="IE=edge"/><title>LiveClientSetup | @google/genai</title><meta name="description" content="Documentation for @google/genai"/><meta name="viewport" content="width=device-width, initial-scale=1"/><link rel="stylesheet" href="../assets/style.css"/><link rel="stylesheet" href="../assets/highlight.css"/><script defer src="../assets/main.js"></script><script async src="../assets/icons.js" id="tsd-icons-script"></script><script async src="../assets/search.js" id="tsd-search-script"></script><script async src="../assets/navigation.js" id="tsd-nav-script"></script></head><body><script>document.documentElement.dataset.theme = localStorage.getItem("tsd-theme") || "os";document.body.style.display="none";setTimeout(() => app?app.showPage():document.body.style.removeProperty("display"),500)</script><header class="tsd-page-toolbar"><div class="tsd-toolbar-contents container"><div class="table-cell" id="tsd-search"><div class="field"><label for="tsd-search-field" class="tsd-widget tsd-toolbar-icon search no-caption"><svg width="16" height="16" viewBox="0 0 16 16" fill="none" aria-hidden="true"><use href="../assets/icons.svg#icon-search"></use></svg></label><input type="text" id="tsd-search-field" aria-label="Search"/></div><div class="field"><div id="tsd-toolbar-links"></div></div><ul class="results"><li class="state loading">Preparing search index...</li><li class="state failure">The search index is not available</li></ul><a href="../index.html" class="title">@google/genai</a></div><div class="table-cell" id="tsd-widgets"><a href="#" class="tsd-widget tsd-toolbar-icon menu no-caption" data-toggle="menu" aria-label="Menu"><svg width="16" height="16" viewBox="0 0 16 16" fill="none" aria-hidden="true"><use href="../assets/icons.svg#icon-menu"></use></svg></a></div></div></header><div class="container container-main"><div class="col-content"><div class="tsd-page-title"><ul class="tsd-breadcrumb"><li><a href="../modules.html">@google/genai</a></li><li><a href="../modules/types.html">types</a></li><li><a href="types.LiveClientSetup.html">LiveClientSetup</a></li></ul><h1>Interface LiveClientSetup</h1></div><section class="tsd-panel tsd-comment"><div class="tsd-comment tsd-typography"><p>Message contains configuration that will apply for the duration of the streaming session.</p>
</div><div class="tsd-comment tsd-typography"></div></section><div class="tsd-signature"><span class="tsd-signature-keyword">interface</span> <span class="tsd-kind-interface">LiveClientSetup</span> <span class="tsd-signature-symbol">{</span><br/>    <a class="tsd-kind-property" href="types.LiveClientSetup.html#contextwindowcompression">contextWindowCompression</a><span class="tsd-signature-symbol">?:</span> <a href="types.ContextWindowCompressionConfig.html" class="tsd-signature-type tsd-kind-interface">ContextWindowCompressionConfig</a><span class="tsd-signature-symbol">;</span><br/>    <a class="tsd-kind-property" href="types.LiveClientSetup.html#generationconfig">generationConfig</a><span class="tsd-signature-symbol">?:</span> <a href="types.GenerationConfig.html" class="tsd-signature-type tsd-kind-interface">GenerationConfig</a><span class="tsd-signature-symbol">;</span><br/>    <a class="tsd-kind-property" href="types.LiveClientSetup.html#inputaudiotranscription">inputAudioTranscription</a><span class="tsd-signature-symbol">?:</span> <a href="types.AudioTranscriptionConfig.html" class="tsd-signature-type tsd-kind-interface">AudioTranscriptionConfig</a><span class="tsd-signature-symbol">;</span><br/>    <a class="tsd-kind-property" href="types.LiveClientSetup.html#model">model</a><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">;</span><br/>    <a class="tsd-kind-property" href="types.LiveClientSetup.html#outputaudiotranscription">outputAudioTranscription</a><span class="tsd-signature-symbol">?:</span> <a href="types.AudioTranscriptionConfig.html" class="tsd-signature-type tsd-kind-interface">AudioTranscriptionConfig</a><span class="tsd-signature-symbol">;</span><br/>    <a class="tsd-kind-property" href="types.LiveClientSetup.html#proactivity">proactivity</a><span class="tsd-signature-symbol">?:</span> <a href="types.ProactivityConfig.html" class="tsd-signature-type tsd-kind-interface">ProactivityConfig</a><span class="tsd-signature-symbol">;</span><br/>    <a class="tsd-kind-property" href="types.LiveClientSetup.html#realtimeinputconfig">realtimeInputConfig</a><span class="tsd-signature-symbol">?:</span> <a href="types.RealtimeInputConfig.html" class="tsd-signature-type tsd-kind-interface">RealtimeInputConfig</a><span class="tsd-signature-symbol">;</span><br/>    <a class="tsd-kind-property" href="types.LiveClientSetup.html#sessionresumption">sessionResumption</a><span class="tsd-signature-symbol">?:</span> <a href="types.SessionResumptionConfig.html" class="tsd-signature-type tsd-kind-interface">SessionResumptionConfig</a><span class="tsd-signature-symbol">;</span><br/>    <a class="tsd-kind-property" href="types.LiveClientSetup.html#systeminstruction">systemInstruction</a><span class="tsd-signature-symbol">?:</span> <a href="../types/types.ContentUnion.html" class="tsd-signature-type tsd-kind-type-alias">ContentUnion</a><span class="tsd-signature-symbol">;</span><br/>    <a class="tsd-kind-property" href="types.LiveClientSetup.html#tools">tools</a><span class="tsd-signature-symbol">?:</span> <a href="../types/types.ToolListUnion.html" class="tsd-signature-type tsd-kind-type-alias">ToolListUnion</a><span class="tsd-signature-symbol">;</span><br/><span class="tsd-signature-symbol">}</span></div><aside class="tsd-sources"><ul><li>Defined in src/types.ts:4796</li></ul></aside><section class="tsd-panel-group tsd-index-group"><section class="tsd-panel tsd-index-panel"><details class="tsd-index-content tsd-accordion" open><summary class="tsd-accordion-summary tsd-index-summary"><h5 class="tsd-index-heading uppercase" role="button" aria-expanded="false" tabIndex="0"><svg width="16" height="16" viewBox="0 0 16 16" fill="none" aria-hidden="true"><use href="../assets/icons.svg#icon-chevronSmall"></use></svg> Index</h5></summary><div class="tsd-accordion-details"><section class="tsd-index-section"><h3 class="tsd-index-heading">Properties</h3><div class="tsd-index-list"><a href="types.LiveClientSetup.html#contextwindowcompression" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>context<wbr/>Window<wbr/>Compression?</span></a>
<a href="types.LiveClientSetup.html#generationconfig" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>generation<wbr/>Config?</span></a>
<a href="types.LiveClientSetup.html#inputaudiotranscription" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>input<wbr/>Audio<wbr/>Transcription?</span></a>
<a href="types.LiveClientSetup.html#model" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>model?</span></a>
<a href="types.LiveClientSetup.html#outputaudiotranscription" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>output<wbr/>Audio<wbr/>Transcription?</span></a>
<a href="types.LiveClientSetup.html#proactivity" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>proactivity?</span></a>
<a href="types.LiveClientSetup.html#realtimeinputconfig" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>realtime<wbr/>Input<wbr/>Config?</span></a>
<a href="types.LiveClientSetup.html#sessionresumption" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>session<wbr/>Resumption?</span></a>
<a href="types.LiveClientSetup.html#systeminstruction" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>system<wbr/>Instruction?</span></a>
<a href="types.LiveClientSetup.html#tools" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>tools?</span></a>
</div></section></div></details></section></section><details class="tsd-panel-group tsd-member-group tsd-accordion" open><summary class="tsd-accordion-summary" data-key="section-Properties"><h2><svg width="20" height="20" viewBox="0 0 24 24" fill="none" aria-hidden="true"><use href="../assets/icons.svg#icon-chevronDown"></use></svg> Properties</h2></summary><section><section class="tsd-panel tsd-member"><a id="contextwindowcompression" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><code class="tsd-tag">Optional</code><span>context<wbr/>Window<wbr/>Compression</span><a href="#contextwindowcompression" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-property">contextWindowCompression</span><span class="tsd-signature-symbol">?:</span> <a href="types.ContextWindowCompressionConfig.html" class="tsd-signature-type tsd-kind-interface">ContextWindowCompressionConfig</a></div><div class="tsd-comment tsd-typography"><p>Configures context window compression mechanism.</p>
<p>If included, server will compress context window to fit into given length.</p>
</div><div class="tsd-comment tsd-typography"></div><aside class="tsd-sources"><ul><li>Defined in src/types.ts:4825</li></ul></aside></section><section class="tsd-panel tsd-member"><a id="generationconfig" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><code class="tsd-tag">Optional</code><span>generation<wbr/>Config</span><a href="#generationconfig" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-property">generationConfig</span><span class="tsd-signature-symbol">?:</span> <a href="types.GenerationConfig.html" class="tsd-signature-type tsd-kind-interface">GenerationConfig</a></div><div class="tsd-comment tsd-typography"><p>The generation configuration for the session.
Note: only a subset of fields are supported.</p>
</div><div class="tsd-comment tsd-typography"></div><aside class="tsd-sources"><ul><li>Defined in src/types.ts:4805</li></ul></aside></section><section class="tsd-panel tsd-member"><a id="inputaudiotranscription" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><code class="tsd-tag">Optional</code><span>input<wbr/>Audio<wbr/>Transcription</span><a href="#inputaudiotranscription" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-property">inputAudioTranscription</span><span class="tsd-signature-symbol">?:</span> <a href="types.AudioTranscriptionConfig.html" class="tsd-signature-type tsd-kind-interface">AudioTranscriptionConfig</a></div><div class="tsd-comment tsd-typography"><p>The transcription of the input aligns with the input audio language.</p>
</div><div class="tsd-comment tsd-typography"></div><aside class="tsd-sources"><ul><li>Defined in src/types.ts:4828</li></ul></aside></section><section class="tsd-panel tsd-member"><a id="model" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><code class="tsd-tag">Optional</code><span>model</span><a href="#model" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-property">model</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">string</span></div><div class="tsd-comment tsd-typography"><p>The fully qualified name of the publisher model or tuned model endpoint to
use.</p>
</div><div class="tsd-comment tsd-typography"></div><aside class="tsd-sources"><ul><li>Defined in src/types.ts:4801</li></ul></aside></section><section class="tsd-panel tsd-member"><a id="outputaudiotranscription" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><code class="tsd-tag">Optional</code><span>output<wbr/>Audio<wbr/>Transcription</span><a href="#outputaudiotranscription" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-property">outputAudioTranscription</span><span class="tsd-signature-symbol">?:</span> <a href="types.AudioTranscriptionConfig.html" class="tsd-signature-type tsd-kind-interface">AudioTranscriptionConfig</a></div><div class="tsd-comment tsd-typography"><p>The transcription of the output aligns with the language code
specified for the output audio.</p>
</div><div class="tsd-comment tsd-typography"></div><aside class="tsd-sources"><ul><li>Defined in src/types.ts:4832</li></ul></aside></section><section class="tsd-panel tsd-member"><a id="proactivity" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><code class="tsd-tag">Optional</code><span>proactivity</span><a href="#proactivity" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-property">proactivity</span><span class="tsd-signature-symbol">?:</span> <a href="types.ProactivityConfig.html" class="tsd-signature-type tsd-kind-interface">ProactivityConfig</a></div><div class="tsd-comment tsd-typography"><p>Configures the proactivity of the model. This allows the model to respond proactively to
the input and to ignore irrelevant input.</p>
</div><div class="tsd-comment tsd-typography"></div><aside class="tsd-sources"><ul><li>Defined in src/types.ts:4835</li></ul></aside></section><section class="tsd-panel tsd-member"><a id="realtimeinputconfig" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><code class="tsd-tag">Optional</code><span>realtime<wbr/>Input<wbr/>Config</span><a href="#realtimeinputconfig" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-property">realtimeInputConfig</span><span class="tsd-signature-symbol">?:</span> <a href="types.RealtimeInputConfig.html" class="tsd-signature-type tsd-kind-interface">RealtimeInputConfig</a></div><div class="tsd-comment tsd-typography"><p>Configures the realtime input behavior in BidiGenerateContent.</p>
</div><div class="tsd-comment tsd-typography"></div><aside class="tsd-sources"><ul><li>Defined in src/types.ts:4817</li></ul></aside></section><section class="tsd-panel tsd-member"><a id="sessionresumption" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><code class="tsd-tag">Optional</code><span>session<wbr/>Resumption</span><a href="#sessionresumption" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-property">sessionResumption</span><span class="tsd-signature-symbol">?:</span> <a href="types.SessionResumptionConfig.html" class="tsd-signature-type tsd-kind-interface">SessionResumptionConfig</a></div><div class="tsd-comment tsd-typography"><p>Configures session resumption mechanism.</p>
<p>If included server will send SessionResumptionUpdate messages.</p>
</div><div class="tsd-comment tsd-typography"></div><aside class="tsd-sources"><ul><li>Defined in src/types.ts:4821</li></ul></aside></section><section class="tsd-panel tsd-member"><a id="systeminstruction" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><code class="tsd-tag">Optional</code><span>system<wbr/>Instruction</span><a href="#systeminstruction" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-property">systemInstruction</span><span class="tsd-signature-symbol">?:</span> <a href="../types/types.ContentUnion.html" class="tsd-signature-type tsd-kind-type-alias">ContentUnion</a></div><div class="tsd-comment tsd-typography"><p>The user provided system instructions for the model.
Note: only text should be used in parts and content in each part will be
in a separate paragraph.</p>
</div><div class="tsd-comment tsd-typography"></div><aside class="tsd-sources"><ul><li>Defined in src/types.ts:4809</li></ul></aside></section><section class="tsd-panel tsd-member"><a id="tools" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><code class="tsd-tag">Optional</code><span>tools</span><a href="#tools" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-property">tools</span><span class="tsd-signature-symbol">?:</span> <a href="../types/types.ToolListUnion.html" class="tsd-signature-type tsd-kind-type-alias">ToolListUnion</a></div><div class="tsd-comment tsd-typography"><p>A list of <code>Tools</code> the model may use to generate the next response.</p>
<p>A <code>Tool</code> is a piece of code that enables the system to interact with
external systems to perform an action, or set of actions, outside of
knowledge and scope of the model.</p>
</div><div class="tsd-comment tsd-typography"></div><aside class="tsd-sources"><ul><li>Defined in src/types.ts:4815</li></ul></aside></section></section></details></div><div class="col-sidebar"><div class="page-menu"><div class="tsd-navigation settings"><details class="tsd-accordion"><summary class="tsd-accordion-summary"><h3><svg width="20" height="20" viewBox="0 0 24 24" fill="none" aria-hidden="true"><use href="../assets/icons.svg#icon-chevronDown"></use></svg>Settings</h3></summary><div class="tsd-accordion-details"><div class="tsd-theme-toggle"><label class="settings-label" for="tsd-theme">Theme</label><select id="tsd-theme"><option value="os">OS</option><option value="light">Light</option><option value="dark">Dark</option></select></div></div></details></div><details open class="tsd-accordion tsd-page-navigation"><summary class="tsd-accordion-summary"><h3><svg width="20" height="20" viewBox="0 0 24 24" fill="none" aria-hidden="true"><use href="../assets/icons.svg#icon-chevronDown"></use></svg>On This Page</h3></summary><div class="tsd-accordion-details"><details open class="tsd-accordion tsd-page-navigation-section"><summary class="tsd-accordion-summary" data-key="section-Properties"><svg width="20" height="20" viewBox="0 0 24 24" fill="none" aria-hidden="true"><use href="../assets/icons.svg#icon-chevronDown"></use></svg>Properties</summary><div><a href="#contextwindowcompression" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>context<wbr/>Window<wbr/>Compression</span></a><a href="#generationconfig" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>generation<wbr/>Config</span></a><a href="#inputaudiotranscription" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>input<wbr/>Audio<wbr/>Transcription</span></a><a href="#model" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>model</span></a><a href="#outputaudiotranscription" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>output<wbr/>Audio<wbr/>Transcription</span></a><a href="#proactivity" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>proactivity</span></a><a href="#realtimeinputconfig" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>realtime<wbr/>Input<wbr/>Config</span></a><a href="#sessionresumption" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>session<wbr/>Resumption</span></a><a href="#systeminstruction" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>system<wbr/>Instruction</span></a><a href="#tools" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>tools</span></a></div></details></div></details></div><div class="site-menu"><nav class="tsd-navigation"><a href="../modules.html">@google/genai</a><ul class="tsd-small-nested-navigation" id="tsd-nav-container"><li>Loading...</li></ul></nav></div></div></div><footer><p class="tsd-generator">Generated using <a href="https://typedoc.org/" target="_blank">TypeDoc</a></p></footer><div class="overlay"></div></body></html>
