<!DOCTYPE html><html class="default" lang="en" data-base=".."><head><meta charset="utf-8"/><meta http-equiv="x-ua-compatible" content="IE=edge"/><title>LiveConnectConfig | @google/genai</title><meta name="description" content="Documentation for @google/genai"/><meta name="viewport" content="width=device-width, initial-scale=1"/><link rel="stylesheet" href="../assets/style.css"/><link rel="stylesheet" href="../assets/highlight.css"/><script defer src="../assets/main.js"></script><script async src="../assets/icons.js" id="tsd-icons-script"></script><script async src="../assets/search.js" id="tsd-search-script"></script><script async src="../assets/navigation.js" id="tsd-nav-script"></script></head><body><script>document.documentElement.dataset.theme = localStorage.getItem("tsd-theme") || "os";document.body.style.display="none";setTimeout(() => app?app.showPage():document.body.style.removeProperty("display"),500)</script><header class="tsd-page-toolbar"><div class="tsd-toolbar-contents container"><div class="table-cell" id="tsd-search"><div class="field"><label for="tsd-search-field" class="tsd-widget tsd-toolbar-icon search no-caption"><svg width="16" height="16" viewBox="0 0 16 16" fill="none" aria-hidden="true"><use href="../assets/icons.svg#icon-search"></use></svg></label><input type="text" id="tsd-search-field" aria-label="Search"/></div><div class="field"><div id="tsd-toolbar-links"></div></div><ul class="results"><li class="state loading">Preparing search index...</li><li class="state failure">The search index is not available</li></ul><a href="../index.html" class="title">@google/genai</a></div><div class="table-cell" id="tsd-widgets"><a href="#" class="tsd-widget tsd-toolbar-icon menu no-caption" data-toggle="menu" aria-label="Menu"><svg width="16" height="16" viewBox="0 0 16 16" fill="none" aria-hidden="true"><use href="../assets/icons.svg#icon-menu"></use></svg></a></div></div></header><div class="container container-main"><div class="col-content"><div class="tsd-page-title"><ul class="tsd-breadcrumb"><li><a href="../modules.html">@google/genai</a></li><li><a href="../modules/types.html">types</a></li><li><a href="types.LiveConnectConfig.html">LiveConnectConfig</a></li></ul><h1>Interface LiveConnectConfig</h1></div><section class="tsd-panel tsd-comment"><div class="tsd-comment tsd-typography"><p>Session config for the API connection.</p>
</div><div class="tsd-comment tsd-typography"></div></section><div class="tsd-signature"><span class="tsd-signature-keyword">interface</span> <span class="tsd-kind-interface">LiveConnectConfig</span> <span class="tsd-signature-symbol">{</span><br/>    <a class="tsd-kind-property" href="types.LiveConnectConfig.html#abortsignal">abortSignal</a><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">AbortSignal</span><span class="tsd-signature-symbol">;</span><br/>    <a class="tsd-kind-property" href="types.LiveConnectConfig.html#contextwindowcompression">contextWindowCompression</a><span class="tsd-signature-symbol">?:</span> <a href="types.ContextWindowCompressionConfig.html" class="tsd-signature-type tsd-kind-interface">ContextWindowCompressionConfig</a><span class="tsd-signature-symbol">;</span><br/>    <a class="tsd-kind-property" href="types.LiveConnectConfig.html#enableaffectivedialog">enableAffectiveDialog</a><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">;</span><br/>    <a class="tsd-kind-property" href="types.LiveConnectConfig.html#generationconfig">generationConfig</a><span class="tsd-signature-symbol">?:</span> <a href="types.GenerationConfig.html" class="tsd-signature-type tsd-kind-interface">GenerationConfig</a><span class="tsd-signature-symbol">;</span><br/>    <a class="tsd-kind-property" href="types.LiveConnectConfig.html#httpoptions">httpOptions</a><span class="tsd-signature-symbol">?:</span> <a href="types.HttpOptions.html" class="tsd-signature-type tsd-kind-interface">HttpOptions</a><span class="tsd-signature-symbol">;</span><br/>    <a class="tsd-kind-property" href="types.LiveConnectConfig.html#inputaudiotranscription">inputAudioTranscription</a><span class="tsd-signature-symbol">?:</span> <a href="types.AudioTranscriptionConfig.html" class="tsd-signature-type tsd-kind-interface">AudioTranscriptionConfig</a><span class="tsd-signature-symbol">;</span><br/>    <a class="tsd-kind-property" href="types.LiveConnectConfig.html#maxoutputtokens">maxOutputTokens</a><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">;</span><br/>    <a class="tsd-kind-property" href="types.LiveConnectConfig.html#mediaresolution">mediaResolution</a><span class="tsd-signature-symbol">?:</span> <a href="../enums/types.MediaResolution.html" class="tsd-signature-type tsd-kind-enum">MediaResolution</a><span class="tsd-signature-symbol">;</span><br/>    <a class="tsd-kind-property" href="types.LiveConnectConfig.html#outputaudiotranscription">outputAudioTranscription</a><span class="tsd-signature-symbol">?:</span> <a href="types.AudioTranscriptionConfig.html" class="tsd-signature-type tsd-kind-interface">AudioTranscriptionConfig</a><span class="tsd-signature-symbol">;</span><br/>    <a class="tsd-kind-property" href="types.LiveConnectConfig.html#proactivity">proactivity</a><span class="tsd-signature-symbol">?:</span> <a href="types.ProactivityConfig.html" class="tsd-signature-type tsd-kind-interface">ProactivityConfig</a><span class="tsd-signature-symbol">;</span><br/>    <a class="tsd-kind-property" href="types.LiveConnectConfig.html#realtimeinputconfig">realtimeInputConfig</a><span class="tsd-signature-symbol">?:</span> <a href="types.RealtimeInputConfig.html" class="tsd-signature-type tsd-kind-interface">RealtimeInputConfig</a><span class="tsd-signature-symbol">;</span><br/>    <a class="tsd-kind-property" href="types.LiveConnectConfig.html#responsemodalities">responseModalities</a><span class="tsd-signature-symbol">?:</span> <a href="../enums/types.Modality.html" class="tsd-signature-type tsd-kind-enum">Modality</a><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">;</span><br/>    <a class="tsd-kind-property" href="types.LiveConnectConfig.html#seed">seed</a><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">;</span><br/>    <a class="tsd-kind-property" href="types.LiveConnectConfig.html#sessionresumption">sessionResumption</a><span class="tsd-signature-symbol">?:</span> <a href="types.SessionResumptionConfig.html" class="tsd-signature-type tsd-kind-interface">SessionResumptionConfig</a><span class="tsd-signature-symbol">;</span><br/>    <a class="tsd-kind-property" href="types.LiveConnectConfig.html#speechconfig">speechConfig</a><span class="tsd-signature-symbol">?:</span> <a href="types.SpeechConfig.html" class="tsd-signature-type tsd-kind-interface">SpeechConfig</a><span class="tsd-signature-symbol">;</span><br/>    <a class="tsd-kind-property" href="types.LiveConnectConfig.html#systeminstruction">systemInstruction</a><span class="tsd-signature-symbol">?:</span> <a href="../types/types.ContentUnion.html" class="tsd-signature-type tsd-kind-type-alias">ContentUnion</a><span class="tsd-signature-symbol">;</span><br/>    <a class="tsd-kind-property" href="types.LiveConnectConfig.html#temperature">temperature</a><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">;</span><br/>    <a class="tsd-kind-property" href="types.LiveConnectConfig.html#tools">tools</a><span class="tsd-signature-symbol">?:</span> <a href="../types/types.ToolListUnion.html" class="tsd-signature-type tsd-kind-type-alias">ToolListUnion</a><span class="tsd-signature-symbol">;</span><br/>    <a class="tsd-kind-property" href="types.LiveConnectConfig.html#topk">topK</a><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">;</span><br/>    <a class="tsd-kind-property" href="types.LiveConnectConfig.html#topp">topP</a><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">;</span><br/><span class="tsd-signature-symbol">}</span></div><aside class="tsd-sources"><ul><li>Defined in src/types.ts:4967</li></ul></aside><section class="tsd-panel-group tsd-index-group"><section class="tsd-panel tsd-index-panel"><details class="tsd-index-content tsd-accordion" open><summary class="tsd-accordion-summary tsd-index-summary"><h5 class="tsd-index-heading uppercase" role="button" aria-expanded="false" tabIndex="0"><svg width="16" height="16" viewBox="0 0 16 16" fill="none" aria-hidden="true"><use href="../assets/icons.svg#icon-chevronSmall"></use></svg> Index</h5></summary><div class="tsd-accordion-details"><section class="tsd-index-section"><h3 class="tsd-index-heading">Properties</h3><div class="tsd-index-list"><a href="types.LiveConnectConfig.html#abortsignal" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>abort<wbr/>Signal?</span></a>
<a href="types.LiveConnectConfig.html#contextwindowcompression" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>context<wbr/>Window<wbr/>Compression?</span></a>
<a href="types.LiveConnectConfig.html#enableaffectivedialog" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>enable<wbr/>Affective<wbr/>Dialog?</span></a>
<a href="types.LiveConnectConfig.html#generationconfig" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>generation<wbr/>Config?</span></a>
<a href="types.LiveConnectConfig.html#httpoptions" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>http<wbr/>Options?</span></a>
<a href="types.LiveConnectConfig.html#inputaudiotranscription" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>input<wbr/>Audio<wbr/>Transcription?</span></a>
<a href="types.LiveConnectConfig.html#maxoutputtokens" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>max<wbr/>Output<wbr/>Tokens?</span></a>
<a href="types.LiveConnectConfig.html#mediaresolution" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>media<wbr/>Resolution?</span></a>
<a href="types.LiveConnectConfig.html#outputaudiotranscription" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>output<wbr/>Audio<wbr/>Transcription?</span></a>
<a href="types.LiveConnectConfig.html#proactivity" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>proactivity?</span></a>
<a href="types.LiveConnectConfig.html#realtimeinputconfig" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>realtime<wbr/>Input<wbr/>Config?</span></a>
<a href="types.LiveConnectConfig.html#responsemodalities" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>response<wbr/>Modalities?</span></a>
<a href="types.LiveConnectConfig.html#seed" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>seed?</span></a>
<a href="types.LiveConnectConfig.html#sessionresumption" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>session<wbr/>Resumption?</span></a>
<a href="types.LiveConnectConfig.html#speechconfig" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>speech<wbr/>Config?</span></a>
<a href="types.LiveConnectConfig.html#systeminstruction" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>system<wbr/>Instruction?</span></a>
<a href="types.LiveConnectConfig.html#temperature" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>temperature?</span></a>
<a href="types.LiveConnectConfig.html#tools" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>tools?</span></a>
<a href="types.LiveConnectConfig.html#topk" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>topK?</span></a>
<a href="types.LiveConnectConfig.html#topp" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>topP?</span></a>
</div></section></div></details></section></section><details class="tsd-panel-group tsd-member-group tsd-accordion" open><summary class="tsd-accordion-summary" data-key="section-Properties"><h2><svg width="20" height="20" viewBox="0 0 24 24" fill="none" aria-hidden="true"><use href="../assets/icons.svg#icon-chevronDown"></use></svg> Properties</h2></summary><section><section class="tsd-panel tsd-member"><a id="abortsignal" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><code class="tsd-tag">Optional</code><span>abort<wbr/>Signal</span><a href="#abortsignal" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-property">abortSignal</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">AbortSignal</span></div><div class="tsd-comment tsd-typography"><p>Abort signal which can be used to cancel the request.</p>
<p>NOTE: AbortSignal is a client-only operation. Using it to cancel an
operation will not cancel the request in the service. You will still
be charged usage for any applicable operations.</p>
</div><div class="tsd-comment tsd-typography"></div><aside class="tsd-sources"><ul><li>Defined in src/types.ts:4976</li></ul></aside></section><section class="tsd-panel tsd-member"><a id="contextwindowcompression" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><code class="tsd-tag">Optional</code><span>context<wbr/>Window<wbr/>Compression</span><a href="#contextwindowcompression" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-property">contextWindowCompression</span><span class="tsd-signature-symbol">?:</span> <a href="types.ContextWindowCompressionConfig.html" class="tsd-signature-type tsd-kind-interface">ContextWindowCompressionConfig</a></div><div class="tsd-comment tsd-typography"><p>Configures context window compression mechanism.</p>
<p>If included, server will compress context window to fit into given length.</p>
</div><div class="tsd-comment tsd-typography"></div><aside class="tsd-sources"><ul><li>Defined in src/types.ts:5043</li></ul></aside></section><section class="tsd-panel tsd-member"><a id="enableaffectivedialog" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><code class="tsd-tag">Optional</code><span>enable<wbr/>Affective<wbr/>Dialog</span><a href="#enableaffectivedialog" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-property">enableAffectiveDialog</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">boolean</span></div><div class="tsd-comment tsd-typography"><p>If enabled, the model will detect emotions and adapt its responses accordingly.</p>
</div><div class="tsd-comment tsd-typography"></div><aside class="tsd-sources"><ul><li>Defined in src/types.ts:5016</li></ul></aside></section><section class="tsd-panel tsd-member"><a id="generationconfig" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><code class="tsd-tag">Optional</code><span>generation<wbr/>Config</span><a href="#generationconfig" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-property">generationConfig</span><span class="tsd-signature-symbol">?:</span> <a href="types.GenerationConfig.html" class="tsd-signature-type tsd-kind-interface">GenerationConfig</a></div><div class="tsd-comment tsd-typography"><p>The generation configuration for the session.</p>
</div><div class="tsd-comment tsd-typography"></div><aside class="tsd-sources"><ul><li>Defined in src/types.ts:4978</li></ul></aside></section><section class="tsd-panel tsd-member"><a id="httpoptions" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><code class="tsd-tag">Optional</code><span>http<wbr/>Options</span><a href="#httpoptions" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-property">httpOptions</span><span class="tsd-signature-symbol">?:</span> <a href="types.HttpOptions.html" class="tsd-signature-type tsd-kind-interface">HttpOptions</a></div><div class="tsd-comment tsd-typography"><p>Used to override HTTP request options.</p>
</div><div class="tsd-comment tsd-typography"></div><aside class="tsd-sources"><ul><li>Defined in src/types.ts:4969</li></ul></aside></section><section class="tsd-panel tsd-member"><a id="inputaudiotranscription" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><code class="tsd-tag">Optional</code><span>input<wbr/>Audio<wbr/>Transcription</span><a href="#inputaudiotranscription" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-property">inputAudioTranscription</span><span class="tsd-signature-symbol">?:</span> <a href="types.AudioTranscriptionConfig.html" class="tsd-signature-type tsd-kind-interface">AudioTranscriptionConfig</a></div><div class="tsd-comment tsd-typography"><p>The transcription of the input aligns with the input audio language.</p>
</div><div class="tsd-comment tsd-typography"></div><aside class="tsd-sources"><ul><li>Defined in src/types.ts:5033</li></ul></aside></section><section class="tsd-panel tsd-member"><a id="maxoutputtokens" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><code class="tsd-tag">Optional</code><span>max<wbr/>Output<wbr/>Tokens</span><a href="#maxoutputtokens" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-property">maxOutputTokens</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">number</span></div><div class="tsd-comment tsd-typography"><p>Maximum number of tokens that can be generated in the response.</p>
</div><div class="tsd-comment tsd-typography"></div><aside class="tsd-sources"><ul><li>Defined in src/types.ts:5003</li></ul></aside></section><section class="tsd-panel tsd-member"><a id="mediaresolution" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><code class="tsd-tag">Optional</code><span>media<wbr/>Resolution</span><a href="#mediaresolution" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-property">mediaResolution</span><span class="tsd-signature-symbol">?:</span> <a href="../enums/types.MediaResolution.html" class="tsd-signature-type tsd-kind-enum">MediaResolution</a></div><div class="tsd-comment tsd-typography"><p>If specified, the media resolution specified will be used.</p>
</div><div class="tsd-comment tsd-typography"></div><aside class="tsd-sources"><ul><li>Defined in src/types.ts:5006</li></ul></aside></section><section class="tsd-panel tsd-member"><a id="outputaudiotranscription" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><code class="tsd-tag">Optional</code><span>output<wbr/>Audio<wbr/>Transcription</span><a href="#outputaudiotranscription" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-property">outputAudioTranscription</span><span class="tsd-signature-symbol">?:</span> <a href="types.AudioTranscriptionConfig.html" class="tsd-signature-type tsd-kind-interface">AudioTranscriptionConfig</a></div><div class="tsd-comment tsd-typography"><p>The transcription of the output aligns with the language code
specified for the output audio.</p>
</div><div class="tsd-comment tsd-typography"></div><aside class="tsd-sources"><ul><li>Defined in src/types.ts:5037</li></ul></aside></section><section class="tsd-panel tsd-member"><a id="proactivity" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><code class="tsd-tag">Optional</code><span>proactivity</span><a href="#proactivity" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-property">proactivity</span><span class="tsd-signature-symbol">?:</span> <a href="types.ProactivityConfig.html" class="tsd-signature-type tsd-kind-interface">ProactivityConfig</a></div><div class="tsd-comment tsd-typography"><p>Configures the proactivity of the model. This allows the model to respond proactively to
the input and to ignore irrelevant input.</p>
</div><div class="tsd-comment tsd-typography"></div><aside class="tsd-sources"><ul><li>Defined in src/types.ts:5046</li></ul></aside></section><section class="tsd-panel tsd-member"><a id="realtimeinputconfig" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><code class="tsd-tag">Optional</code><span>realtime<wbr/>Input<wbr/>Config</span><a href="#realtimeinputconfig" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-property">realtimeInputConfig</span><span class="tsd-signature-symbol">?:</span> <a href="types.RealtimeInputConfig.html" class="tsd-signature-type tsd-kind-interface">RealtimeInputConfig</a></div><div class="tsd-comment tsd-typography"><p>Configures the realtime input behavior in BidiGenerateContent.</p>
</div><div class="tsd-comment tsd-typography"></div><aside class="tsd-sources"><ul><li>Defined in src/types.ts:5039</li></ul></aside></section><section class="tsd-panel tsd-member"><a id="responsemodalities" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><code class="tsd-tag">Optional</code><span>response<wbr/>Modalities</span><a href="#responsemodalities" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-property">responseModalities</span><span class="tsd-signature-symbol">?:</span> <a href="../enums/types.Modality.html" class="tsd-signature-type tsd-kind-enum">Modality</a><span class="tsd-signature-symbol">[]</span></div><div class="tsd-comment tsd-typography"><p>The requested modalities of the response. Represents the set of
modalities that the model can return. Defaults to AUDIO if not specified.</p>
</div><div class="tsd-comment tsd-typography"></div><aside class="tsd-sources"><ul><li>Defined in src/types.ts:4982</li></ul></aside></section><section class="tsd-panel tsd-member"><a id="seed" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><code class="tsd-tag">Optional</code><span>seed</span><a href="#seed" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-property">seed</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">number</span></div><div class="tsd-comment tsd-typography"><p>When <code>seed</code> is fixed to a specific number, the model makes a best
effort to provide the same response for repeated requests. By default, a
random number is used.</p>
</div><div class="tsd-comment tsd-typography"></div><aside class="tsd-sources"><ul><li>Defined in src/types.ts:5011</li></ul></aside></section><section class="tsd-panel tsd-member"><a id="sessionresumption" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><code class="tsd-tag">Optional</code><span>session<wbr/>Resumption</span><a href="#sessionresumption" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-property">sessionResumption</span><span class="tsd-signature-symbol">?:</span> <a href="types.SessionResumptionConfig.html" class="tsd-signature-type tsd-kind-interface">SessionResumptionConfig</a></div><div class="tsd-comment tsd-typography"><p>Configures session resumption mechanism.</p>
<p>If included the server will send SessionResumptionUpdate messages.</p>
</div><div class="tsd-comment tsd-typography"></div><aside class="tsd-sources"><ul><li>Defined in src/types.ts:5030</li></ul></aside></section><section class="tsd-panel tsd-member"><a id="speechconfig" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><code class="tsd-tag">Optional</code><span>speech<wbr/>Config</span><a href="#speechconfig" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-property">speechConfig</span><span class="tsd-signature-symbol">?:</span> <a href="types.SpeechConfig.html" class="tsd-signature-type tsd-kind-interface">SpeechConfig</a></div><div class="tsd-comment tsd-typography"><p>The speech generation configuration.</p>
</div><div class="tsd-comment tsd-typography"></div><aside class="tsd-sources"><ul><li>Defined in src/types.ts:5014</li></ul></aside></section><section class="tsd-panel tsd-member"><a id="systeminstruction" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><code class="tsd-tag">Optional</code><span>system<wbr/>Instruction</span><a href="#systeminstruction" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-property">systemInstruction</span><span class="tsd-signature-symbol">?:</span> <a href="../types/types.ContentUnion.html" class="tsd-signature-type tsd-kind-type-alias">ContentUnion</a></div><div class="tsd-comment tsd-typography"><p>The user provided system instructions for the model.
Note: only text should be used in parts and content in each part will be
in a separate paragraph.</p>
</div><div class="tsd-comment tsd-typography"></div><aside class="tsd-sources"><ul><li>Defined in src/types.ts:5020</li></ul></aside></section><section class="tsd-panel tsd-member"><a id="temperature" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><code class="tsd-tag">Optional</code><span>temperature</span><a href="#temperature" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-property">temperature</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">number</span></div><div class="tsd-comment tsd-typography"><p>Value that controls the degree of randomness in token selection.
Lower temperatures are good for prompts that require a less open-ended or
creative response, while higher temperatures can lead to more diverse or
creative results.</p>
</div><div class="tsd-comment tsd-typography"></div><aside class="tsd-sources"><ul><li>Defined in src/types.ts:4988</li></ul></aside></section><section class="tsd-panel tsd-member"><a id="tools" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><code class="tsd-tag">Optional</code><span>tools</span><a href="#tools" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-property">tools</span><span class="tsd-signature-symbol">?:</span> <a href="../types/types.ToolListUnion.html" class="tsd-signature-type tsd-kind-type-alias">ToolListUnion</a></div><div class="tsd-comment tsd-typography"><p>A list of <code>Tools</code> the model may use to generate the next response.</p>
<p>A <code>Tool</code> is a piece of code that enables the system to interact with
external systems to perform an action, or set of actions, outside of
knowledge and scope of the model.</p>
</div><div class="tsd-comment tsd-typography"></div><aside class="tsd-sources"><ul><li>Defined in src/types.ts:5026</li></ul></aside></section><section class="tsd-panel tsd-member"><a id="topk" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><code class="tsd-tag">Optional</code><span>topK</span><a href="#topk" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-property">topK</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">number</span></div><div class="tsd-comment tsd-typography"><p>For each token selection step, the <code>top_k</code> tokens with the
highest probabilities are sampled. Then tokens are further filtered based
on <code>top_p</code> with the final token selected using temperature sampling. Use
a lower number for less random responses and a higher number for more
random responses.</p>
</div><div class="tsd-comment tsd-typography"></div><aside class="tsd-sources"><ul><li>Defined in src/types.ts:5000</li></ul></aside></section><section class="tsd-panel tsd-member"><a id="topp" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><code class="tsd-tag">Optional</code><span>topP</span><a href="#topp" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-property">topP</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">number</span></div><div class="tsd-comment tsd-typography"><p>Tokens are selected from the most to least probable until the sum
of their probabilities equals this value. Use a lower value for less
random responses and a higher value for more random responses.</p>
</div><div class="tsd-comment tsd-typography"></div><aside class="tsd-sources"><ul><li>Defined in src/types.ts:4993</li></ul></aside></section></section></details></div><div class="col-sidebar"><div class="page-menu"><div class="tsd-navigation settings"><details class="tsd-accordion"><summary class="tsd-accordion-summary"><h3><svg width="20" height="20" viewBox="0 0 24 24" fill="none" aria-hidden="true"><use href="../assets/icons.svg#icon-chevronDown"></use></svg>Settings</h3></summary><div class="tsd-accordion-details"><div class="tsd-theme-toggle"><label class="settings-label" for="tsd-theme">Theme</label><select id="tsd-theme"><option value="os">OS</option><option value="light">Light</option><option value="dark">Dark</option></select></div></div></details></div><details open class="tsd-accordion tsd-page-navigation"><summary class="tsd-accordion-summary"><h3><svg width="20" height="20" viewBox="0 0 24 24" fill="none" aria-hidden="true"><use href="../assets/icons.svg#icon-chevronDown"></use></svg>On This Page</h3></summary><div class="tsd-accordion-details"><details open class="tsd-accordion tsd-page-navigation-section"><summary class="tsd-accordion-summary" data-key="section-Properties"><svg width="20" height="20" viewBox="0 0 24 24" fill="none" aria-hidden="true"><use href="../assets/icons.svg#icon-chevronDown"></use></svg>Properties</summary><div><a href="#abortsignal" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>abort<wbr/>Signal</span></a><a href="#contextwindowcompression" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>context<wbr/>Window<wbr/>Compression</span></a><a href="#enableaffectivedialog" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>enable<wbr/>Affective<wbr/>Dialog</span></a><a href="#generationconfig" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>generation<wbr/>Config</span></a><a href="#httpoptions" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>http<wbr/>Options</span></a><a href="#inputaudiotranscription" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>input<wbr/>Audio<wbr/>Transcription</span></a><a href="#maxoutputtokens" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>max<wbr/>Output<wbr/>Tokens</span></a><a href="#mediaresolution" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>media<wbr/>Resolution</span></a><a href="#outputaudiotranscription" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>output<wbr/>Audio<wbr/>Transcription</span></a><a href="#proactivity" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>proactivity</span></a><a href="#realtimeinputconfig" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>realtime<wbr/>Input<wbr/>Config</span></a><a href="#responsemodalities" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>response<wbr/>Modalities</span></a><a href="#seed" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>seed</span></a><a href="#sessionresumption" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>session<wbr/>Resumption</span></a><a href="#speechconfig" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>speech<wbr/>Config</span></a><a href="#systeminstruction" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>system<wbr/>Instruction</span></a><a href="#temperature" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>temperature</span></a><a href="#tools" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>tools</span></a><a href="#topk" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>topK</span></a><a href="#topp" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>topP</span></a></div></details></div></details></div><div class="site-menu"><nav class="tsd-navigation"><a href="../modules.html">@google/genai</a><ul class="tsd-small-nested-navigation" id="tsd-nav-container"><li>Loading...</li></ul></nav></div></div></div><footer><p class="tsd-generator">Generated using <a href="https://typedoc.org/" target="_blank">TypeDoc</a></p></footer><div class="overlay"></div></body></html>
