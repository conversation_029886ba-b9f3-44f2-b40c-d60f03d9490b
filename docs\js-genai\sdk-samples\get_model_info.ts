/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */
import {GoogleGenAI} from '@google/genai';

const GEMINI_API_KEY = process.env.GEMINI_API_KEY;
const GOOGLE_CLOUD_PROJECT = process.env.GOOGLE_CLOUD_PROJECT;
const GOOGLE_CLOUD_LOCATION = process.env.GOOGLE_CLOUD_LOCATION;
const GOOGLE_GENAI_USE_VERTEXAI = process.env.GOOGLE_GENAI_USE_VERTEXAI;

function configureClient(): GoogleGenAI {
  if (GOOGLE_GENAI_USE_VERTEXAI) {
    return new GoogleGenAI({
      vertexai: true,
      project: GOOGLE_CLOUD_PROJECT,
      location: GOOGLE_CLOUD_LOCATION,
    });
  }
  return new GoogleGenAI({vertexai: false, apiKey: GEMINI_API_KEY});
}

async function main() {
  const ai = configureClient();
  const modelInfo = await ai.models.get({model: 'gemini-2.0-flash'});
  console.log(modelInfo);
}

main();
