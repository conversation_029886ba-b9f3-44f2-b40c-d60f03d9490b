<!DOCTYPE html><html class="default" lang="en" data-base=".."><head><meta charset="utf-8"/><meta http-equiv="x-ua-compatible" content="IE=edge"/><title>Part | @google/genai</title><meta name="description" content="Documentation for @google/genai"/><meta name="viewport" content="width=device-width, initial-scale=1"/><link rel="stylesheet" href="../assets/style.css"/><link rel="stylesheet" href="../assets/highlight.css"/><script defer src="../assets/main.js"></script><script async src="../assets/icons.js" id="tsd-icons-script"></script><script async src="../assets/search.js" id="tsd-search-script"></script><script async src="../assets/navigation.js" id="tsd-nav-script"></script></head><body><script>document.documentElement.dataset.theme = localStorage.getItem("tsd-theme") || "os";document.body.style.display="none";setTimeout(() => app?app.showPage():document.body.style.removeProperty("display"),500)</script><header class="tsd-page-toolbar"><div class="tsd-toolbar-contents container"><div class="table-cell" id="tsd-search"><div class="field"><label for="tsd-search-field" class="tsd-widget tsd-toolbar-icon search no-caption"><svg width="16" height="16" viewBox="0 0 16 16" fill="none" aria-hidden="true"><use href="../assets/icons.svg#icon-search"></use></svg></label><input type="text" id="tsd-search-field" aria-label="Search"/></div><div class="field"><div id="tsd-toolbar-links"></div></div><ul class="results"><li class="state loading">Preparing search index...</li><li class="state failure">The search index is not available</li></ul><a href="../index.html" class="title">@google/genai</a></div><div class="table-cell" id="tsd-widgets"><a href="#" class="tsd-widget tsd-toolbar-icon menu no-caption" data-toggle="menu" aria-label="Menu"><svg width="16" height="16" viewBox="0 0 16 16" fill="none" aria-hidden="true"><use href="../assets/icons.svg#icon-menu"></use></svg></a></div></div></header><div class="container container-main"><div class="col-content"><div class="tsd-page-title"><ul class="tsd-breadcrumb"><li><a href="../modules.html">@google/genai</a></li><li><a href="../modules/types.html">types</a></li><li><a href="types.Part.html">Part</a></li></ul><h1>Interface Part</h1></div><section class="tsd-panel tsd-comment"><div class="tsd-comment tsd-typography"><p>A datatype containing media content.</p>
<p>Exactly one field within a Part should be set, representing the specific type
of content being conveyed. Using multiple fields within the same <code>Part</code>
instance is considered invalid.</p>
</div><div class="tsd-comment tsd-typography"></div></section><div class="tsd-signature"><span class="tsd-signature-keyword">interface</span> <span class="tsd-kind-interface">Part</span> <span class="tsd-signature-symbol">{</span><br/>    <a class="tsd-kind-property" href="types.Part.html#codeexecutionresult">codeExecutionResult</a><span class="tsd-signature-symbol">?:</span> <a href="types.CodeExecutionResult.html" class="tsd-signature-type tsd-kind-interface">CodeExecutionResult</a><span class="tsd-signature-symbol">;</span><br/>    <a class="tsd-kind-property" href="types.Part.html#executablecode">executableCode</a><span class="tsd-signature-symbol">?:</span> <a href="types.ExecutableCode.html" class="tsd-signature-type tsd-kind-interface">ExecutableCode</a><span class="tsd-signature-symbol">;</span><br/>    <a class="tsd-kind-property" href="types.Part.html#filedata">fileData</a><span class="tsd-signature-symbol">?:</span> <a href="types.FileData.html" class="tsd-signature-type tsd-kind-interface">FileData</a><span class="tsd-signature-symbol">;</span><br/>    <a class="tsd-kind-property" href="types.Part.html#functioncall">functionCall</a><span class="tsd-signature-symbol">?:</span> <a href="types.FunctionCall.html" class="tsd-signature-type tsd-kind-interface">FunctionCall</a><span class="tsd-signature-symbol">;</span><br/>    <a class="tsd-kind-property" href="types.Part.html#functionresponse">functionResponse</a><span class="tsd-signature-symbol">?:</span> <a href="../classes/types.FunctionResponse.html" class="tsd-signature-type tsd-kind-class">FunctionResponse</a><span class="tsd-signature-symbol">;</span><br/>    <a class="tsd-kind-property" href="types.Part.html#inlinedata">inlineData</a><span class="tsd-signature-symbol">?:</span> <a href="types.Blob.html" class="tsd-signature-type tsd-kind-interface">Blob</a><span class="tsd-signature-symbol">;</span><br/>    <a class="tsd-kind-property" href="types.Part.html#text">text</a><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">;</span><br/>    <a class="tsd-kind-property" href="types.Part.html#thought">thought</a><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">;</span><br/>    <a class="tsd-kind-property" href="types.Part.html#thoughtsignature">thoughtSignature</a><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">;</span><br/>    <a class="tsd-kind-property" href="types.Part.html#videometadata">videoMetadata</a><span class="tsd-signature-symbol">?:</span> <a href="types.VideoMetadata.html" class="tsd-signature-type tsd-kind-interface">VideoMetadata</a><span class="tsd-signature-symbol">;</span><br/><span class="tsd-signature-symbol">}</span></div><aside class="tsd-sources"><ul><li>Defined in src/types.ts:982</li></ul></aside><section class="tsd-panel-group tsd-index-group"><section class="tsd-panel tsd-index-panel"><details class="tsd-index-content tsd-accordion" open><summary class="tsd-accordion-summary tsd-index-summary"><h5 class="tsd-index-heading uppercase" role="button" aria-expanded="false" tabIndex="0"><svg width="16" height="16" viewBox="0 0 16 16" fill="none" aria-hidden="true"><use href="../assets/icons.svg#icon-chevronSmall"></use></svg> Index</h5></summary><div class="tsd-accordion-details"><section class="tsd-index-section"><h3 class="tsd-index-heading">Properties</h3><div class="tsd-index-list"><a href="types.Part.html#codeexecutionresult" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>code<wbr/>Execution<wbr/>Result?</span></a>
<a href="types.Part.html#executablecode" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>executable<wbr/>Code?</span></a>
<a href="types.Part.html#filedata" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>file<wbr/>Data?</span></a>
<a href="types.Part.html#functioncall" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>function<wbr/>Call?</span></a>
<a href="types.Part.html#functionresponse" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>function<wbr/>Response?</span></a>
<a href="types.Part.html#inlinedata" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>inline<wbr/>Data?</span></a>
<a href="types.Part.html#text" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>text?</span></a>
<a href="types.Part.html#thought" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>thought?</span></a>
<a href="types.Part.html#thoughtsignature" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>thought<wbr/>Signature?</span></a>
<a href="types.Part.html#videometadata" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>video<wbr/>Metadata?</span></a>
</div></section></div></details></section></section><details class="tsd-panel-group tsd-member-group tsd-accordion" open><summary class="tsd-accordion-summary" data-key="section-Properties"><h2><svg width="20" height="20" viewBox="0 0 24 24" fill="none" aria-hidden="true"><use href="../assets/icons.svg#icon-chevronDown"></use></svg> Properties</h2></summary><section><section class="tsd-panel tsd-member"><a id="codeexecutionresult" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><code class="tsd-tag">Optional</code><span>code<wbr/>Execution<wbr/>Result</span><a href="#codeexecutionresult" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-property">codeExecutionResult</span><span class="tsd-signature-symbol">?:</span> <a href="types.CodeExecutionResult.html" class="tsd-signature-type tsd-kind-interface">CodeExecutionResult</a></div><div class="tsd-comment tsd-typography"><p>Optional. Result of executing the [ExecutableCode].</p>
</div><div class="tsd-comment tsd-typography"></div><aside class="tsd-sources"><ul><li>Defined in src/types.ts:995</li></ul></aside></section><section class="tsd-panel tsd-member"><a id="executablecode" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><code class="tsd-tag">Optional</code><span>executable<wbr/>Code</span><a href="#executablecode" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-property">executableCode</span><span class="tsd-signature-symbol">?:</span> <a href="types.ExecutableCode.html" class="tsd-signature-type tsd-kind-interface">ExecutableCode</a></div><div class="tsd-comment tsd-typography"><p>Optional. Code generated by the model that is meant to be executed.</p>
</div><div class="tsd-comment tsd-typography"></div><aside class="tsd-sources"><ul><li>Defined in src/types.ts:997</li></ul></aside></section><section class="tsd-panel tsd-member"><a id="filedata" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><code class="tsd-tag">Optional</code><span>file<wbr/>Data</span><a href="#filedata" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-property">fileData</span><span class="tsd-signature-symbol">?:</span> <a href="types.FileData.html" class="tsd-signature-type tsd-kind-interface">FileData</a></div><div class="tsd-comment tsd-typography"><p>Optional. URI based data.</p>
</div><div class="tsd-comment tsd-typography"></div><aside class="tsd-sources"><ul><li>Defined in src/types.ts:990</li></ul></aside></section><section class="tsd-panel tsd-member"><a id="functioncall" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><code class="tsd-tag">Optional</code><span>function<wbr/>Call</span><a href="#functioncall" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-property">functionCall</span><span class="tsd-signature-symbol">?:</span> <a href="types.FunctionCall.html" class="tsd-signature-type tsd-kind-interface">FunctionCall</a></div><div class="tsd-comment tsd-typography"><p>Optional. A predicted [FunctionCall] returned from the model that contains a string representing the [<a href="http://FunctionDeclaration.name">FunctionDeclaration.name</a>] with the parameters and their values.</p>
</div><div class="tsd-comment tsd-typography"></div><aside class="tsd-sources"><ul><li>Defined in src/types.ts:999</li></ul></aside></section><section class="tsd-panel tsd-member"><a id="functionresponse" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><code class="tsd-tag">Optional</code><span>function<wbr/>Response</span><a href="#functionresponse" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-property">functionResponse</span><span class="tsd-signature-symbol">?:</span> <a href="../classes/types.FunctionResponse.html" class="tsd-signature-type tsd-kind-class">FunctionResponse</a></div><div class="tsd-comment tsd-typography"><p>Optional. The result output of a [FunctionCall] that contains a string representing the [<a href="http://FunctionDeclaration.name">FunctionDeclaration.name</a>] and a structured JSON object containing any output from the function call. It is used as context to the model.</p>
</div><div class="tsd-comment tsd-typography"></div><aside class="tsd-sources"><ul><li>Defined in src/types.ts:1001</li></ul></aside></section><section class="tsd-panel tsd-member"><a id="inlinedata" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><code class="tsd-tag">Optional</code><span>inline<wbr/>Data</span><a href="#inlinedata" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-property">inlineData</span><span class="tsd-signature-symbol">?:</span> <a href="types.Blob.html" class="tsd-signature-type tsd-kind-interface">Blob</a></div><div class="tsd-comment tsd-typography"><p>Optional. Inlined bytes data.</p>
</div><div class="tsd-comment tsd-typography"></div><aside class="tsd-sources"><ul><li>Defined in src/types.ts:988</li></ul></aside></section><section class="tsd-panel tsd-member"><a id="text" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><code class="tsd-tag">Optional</code><span>text</span><a href="#text" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-property">text</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">string</span></div><div class="tsd-comment tsd-typography"><p>Optional. Text part (can be code).</p>
</div><div class="tsd-comment tsd-typography"></div><aside class="tsd-sources"><ul><li>Defined in src/types.ts:1003</li></ul></aside></section><section class="tsd-panel tsd-member"><a id="thought" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><code class="tsd-tag">Optional</code><span>thought</span><a href="#thought" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-property">thought</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">boolean</span></div><div class="tsd-comment tsd-typography"><p>Indicates if the part is thought from the model.</p>
</div><div class="tsd-comment tsd-typography"></div><aside class="tsd-sources"><ul><li>Defined in src/types.ts:986</li></ul></aside></section><section class="tsd-panel tsd-member"><a id="thoughtsignature" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><code class="tsd-tag">Optional</code><span>thought<wbr/>Signature</span><a href="#thoughtsignature" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-property">thoughtSignature</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">string</span></div><div class="tsd-comment tsd-typography"><p>An opaque signature for the thought so it can be reused in subsequent requests.</p>
</div><div class="tsd-comment tsd-typography"><div class="tsd-tag-remarks"><h4 class="tsd-anchor-link"><a id="remarks" class="tsd-anchor"></a>Remarks<a href="#remarks" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h4><p>Encoded as base64 string.</p>
</div></div><aside class="tsd-sources"><ul><li>Defined in src/types.ts:993</li></ul></aside></section><section class="tsd-panel tsd-member"><a id="videometadata" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><code class="tsd-tag">Optional</code><span>video<wbr/>Metadata</span><a href="#videometadata" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-property">videoMetadata</span><span class="tsd-signature-symbol">?:</span> <a href="types.VideoMetadata.html" class="tsd-signature-type tsd-kind-interface">VideoMetadata</a></div><div class="tsd-comment tsd-typography"><p>Metadata for a given video.</p>
</div><div class="tsd-comment tsd-typography"></div><aside class="tsd-sources"><ul><li>Defined in src/types.ts:984</li></ul></aside></section></section></details></div><div class="col-sidebar"><div class="page-menu"><div class="tsd-navigation settings"><details class="tsd-accordion"><summary class="tsd-accordion-summary"><h3><svg width="20" height="20" viewBox="0 0 24 24" fill="none" aria-hidden="true"><use href="../assets/icons.svg#icon-chevronDown"></use></svg>Settings</h3></summary><div class="tsd-accordion-details"><div class="tsd-theme-toggle"><label class="settings-label" for="tsd-theme">Theme</label><select id="tsd-theme"><option value="os">OS</option><option value="light">Light</option><option value="dark">Dark</option></select></div></div></details></div><details open class="tsd-accordion tsd-page-navigation"><summary class="tsd-accordion-summary"><h3><svg width="20" height="20" viewBox="0 0 24 24" fill="none" aria-hidden="true"><use href="../assets/icons.svg#icon-chevronDown"></use></svg>On This Page</h3></summary><div class="tsd-accordion-details"><details open class="tsd-accordion tsd-page-navigation-section"><summary class="tsd-accordion-summary" data-key="section-Properties"><svg width="20" height="20" viewBox="0 0 24 24" fill="none" aria-hidden="true"><use href="../assets/icons.svg#icon-chevronDown"></use></svg>Properties</summary><div><a href="#codeexecutionresult" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>code<wbr/>Execution<wbr/>Result</span></a><a href="#executablecode" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>executable<wbr/>Code</span></a><a href="#filedata" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>file<wbr/>Data</span></a><a href="#functioncall" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>function<wbr/>Call</span></a><a href="#functionresponse" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>function<wbr/>Response</span></a><a href="#inlinedata" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>inline<wbr/>Data</span></a><a href="#text" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>text</span></a><a href="#thought" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>thought</span></a><a href="#thoughtsignature" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>thought<wbr/>Signature</span></a><a href="#videometadata" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>video<wbr/>Metadata</span></a></div></details></div></details></div><div class="site-menu"><nav class="tsd-navigation"><a href="../modules.html">@google/genai</a><ul class="tsd-small-nested-navigation" id="tsd-nav-container"><li>Loading...</li></ul></nav></div></div></div><footer><p class="tsd-generator">Generated using <a href="https://typedoc.org/" target="_blank">TypeDoc</a></p></footer><div class="overlay"></div></body></html>
