import { GoogleEngine } from "./google";
import { GoogleGenerativeAI } from "@google/generative-ai";
import { ChatMessage } from "../IAIEngine";
import { jest } from "@jest/globals";

jest.mock("@google/generative-ai", () => {
  const mockStartChat = {
    sendMessageStream: jest.fn(),
    sendMessage: jest.fn(),
  };
  const mockGenerativeModel = {
    startChat: jest.fn(() => mockStartChat),
    getGenerativeModel: jest.fn(() => mockGenerativeModel),
  };
  return {
    GoogleGenerativeAI: jest.fn(() => mockGenerativeModel),
  };
});

describe("GoogleEngine", () => {
  let engine: GoogleEngine;
  let mockConsoleError: jest.SpiedFunction<any>;

  beforeEach(() => {
    engine = new GoogleEngine();
    mockConsoleError = jest.spyOn(console, "error").mockImplementation(() => {});
    (GoogleGenerativeAI as jest.Mock).mockClear();
  });

  afterEach(() => {
    mockConsoleError.mockRestore();
  });

  describe("updateConfig", () => {
    it("should create a GoogleGenerativeAI instance when a valid API key is provided", async () => {
      await engine.updateConfig("a-valid-api-key");
      expect(GoogleGenerativeAI).toHaveBeenCalledWith("a-valid-api-key");
      expect((engine as any).genAI).not.toBeNull();
    });

    it("should log an error and keep the genAI instance as null if the API key is missing", async () => {
      await engine.updateConfig();
      expect(mockConsoleError).toHaveBeenCalledWith("Google API Key not found. The GoogleEngine is disabled.");
      expect((engine as any).genAI).toBeNull();
    });
  });

  describe("formatMessagesForGoogle", () => {
    it("should correctly format a mix of user, assistant, and system messages", () => {
      const messages: ChatMessage[] = [
        { role: "system", content: "You are a helpful assistant." },
        { role: "user", content: "Hello!" },
        { role: "assistant", content: "Hi there!" },
        { role: "user", content: "How are you?" },
      ];
      const result = (engine as any).formatMessagesForGoogle(messages);
      expect(result).toEqual([
        { role: "user", parts: [{ text: "Hello!" }] },
        { role: "model", parts: [{ text: "Hi there!" }] },
        { role: "user", parts: [{ text: "How are you?" }] },
      ]);
    });

    it("should merge consecutive messages from the same role", () => {
      const messages: ChatMessage[] = [
        { role: "user", content: "Message 1" },
        { role: "user", content: "Message 2" },
        { role: "assistant", content: "Response 1" },
      ];
      const result = (engine as any).formatMessagesForGoogle(messages);
      expect(result).toEqual([
        { role: "user", parts: [{ text: "Message 1\n\nMessage 2" }] },
        { role: "model", parts: [{ text: "Response 1" }] },
      ]);
    });
  });

  describe("chat", () => {
    it("should throw an error if the engine is not initialized", async () => {
      const messages: ChatMessage[] = [{ role: "user", content: "Hello" }];
      const options = { model: "gemini-pro", stream: true };
      await expect(engine.chat(messages, options)).rejects.toThrow(
        "Google AI Engine is not initialized. Please check your API key.",
      );
    });

    it("should handle streaming responses correctly", async () => {
      await engine.updateConfig("a-valid-api-key");
      const messages: ChatMessage[] = [{ role: "user", content: "Stream test" }];
      const options = { model: "gemini-pro", stream: true };

      const mockStream = (async function* () {
        yield { text: () => "Hello " };
        yield { text: () => "world" };
      })();

      const mockSendMessageStream = jest.fn().mockResolvedValue({ stream: mockStream });
      ((engine as any).genAI.getGenerativeModel().startChat as jest.Mock).mockReturnValue({
        sendMessageStream: mockSendMessageStream,
      });

      const result = engine.chat(messages, options);
      const chunks = [];
      for await (const chunk of result) {
        chunks.push(chunk);
      }
      expect(chunks).toEqual(["Hello ", "world"]);
    });
  });

  describe("getModels", () => {
    it("should return a list of available models", async () => {
      const models = await engine.getModels();
      expect(models).toEqual(["gemini-pro", "gemini-1.0-pro", "gemini-1.5-pro-latest", "gemini-1.5-flash-latest"]);
    });
  });
});
