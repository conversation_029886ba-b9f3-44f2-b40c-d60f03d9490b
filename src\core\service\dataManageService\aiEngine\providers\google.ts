import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>age, ChatOptions } from "../IAIEngine";
import { GoogleGenerativeAI, HarmCategory, HarmBlockThreshold, Content } from "@google/generative-ai";

export class GoogleEngine implements IAIEngine {
  private genAI: GoogleGenerativeAI | null = null;

  public async updateConfig(apiKey?: string) {
    if (apiKey && apiKey !== "YOUR_GOOGLE_API_KEY") {
      this.genAI = new GoogleGenerativeAI(apiKey);
    } else {
      console.error("Google API Key not found. The GoogleEngine is disabled.");
      this.genAI = null;
    }
  }

  private formatMessagesForGoogle(messages: ChatMessage[]): Content[] {
    // Filter out system messages and map roles
    const filteredMessages = messages
      .filter((message) => message.role !== "system")
      .map((message) => ({
        ...message,
        role: message.role === "assistant" ? "model" : "user",
      }));

    if (filteredMessages.length === 0) {
      return [];
    }

    const mergedMessages: { role: "user" | "model"; content: string }[] = [];
    let currentRole = filteredMessages[0].role;
    let currentContent: string[] = [];

    for (const message of filteredMessages) {
      if (message.role === currentRole) {
        currentContent.push(message.content);
      } else {
        mergedMessages.push({
          role: currentRole as "user" | "model",
          content: currentContent.join("\n\n"),
        });
        currentRole = message.role;
        currentContent = [message.content];
      }
    }
    mergedMessages.push({
      role: currentRole as "user" | "model",
      content: currentContent.join("\n\n"),
    });

    // Ensure history starts with a user message
    const finalMessages = mergedMessages;
    if (finalMessages.length > 0 && finalMessages[0].role === "model") {
      finalMessages.shift(); // Remove leading model message
    }

    // Ensure user and model roles alternate, starting with user.
    const alternatingMessages: { role: "user" | "model"; content: string }[] = [];
    let lastRole: "user" | "model" | null = null;

    // Find the first user message to start the conversation
    const firstUserIndex = finalMessages.findIndex((m) => m.role === "user");

    if (firstUserIndex === -1) {
      return []; // No user messages, history is invalid
    }

    for (let i = firstUserIndex; i < finalMessages.length; i++) {
      const message = finalMessages[i];
      if (message.role !== lastRole) {
        alternatingMessages.push(message);
        lastRole = message.role;
      }
    }

    return alternatingMessages.map((message) => ({
      role: message.role,
      parts: [{ text: message.content }],
    }));
  }

  async *chat(messages: ChatMessage[], options: ChatOptions): AsyncGenerator<string> {
    // The check for genAI is now the responsibility of the caller.
    // We assume updateConfig has been called.
    if (!this.genAI) {
      throw new Error("Google AI Engine is not initialized. Please check your API key.");
    }

    const enableStream = options.stream !== false;

    const model = this.genAI.getGenerativeModel({
      model: options.model,
      safetySettings: [
        {
          category: HarmCategory.HARM_CATEGORY_HARASSMENT,
          threshold: HarmBlockThreshold.BLOCK_NONE,
        },
        {
          category: HarmCategory.HARM_CATEGORY_HATE_SPEECH,
          threshold: HarmBlockThreshold.BLOCK_NONE,
        },
        {
          category: HarmCategory.HARM_CATEGORY_SEXUALLY_EXPLICIT,
          threshold: HarmBlockThreshold.BLOCK_NONE,
        },
        {
          category: HarmCategory.HARM_CATEGORY_DANGEROUS_CONTENT,
          threshold: HarmBlockThreshold.BLOCK_NONE,
        },
      ],
    });

    const history = this.formatMessagesForGoogle(messages.slice(0, -1));
    const chatSession = model.startChat({
      history: history,
    });
    const lastMessage = messages[messages.length - 1].content;
    const result = await chatSession.sendMessageStream(lastMessage);

    if (enableStream) {
      for await (const chunk of result.stream) {
        const text = chunk.text();
        if (text) {
          yield text;
        }
      }
    } else {
      const response = await result.response;
      yield response.text() || "";
    }
  }

  async getModels(): Promise<string[]> {
    return Promise.resolve(["gemini-pro", "gemini-1.0-pro", "gemini-1.5-pro-latest", "gemini-1.5-flash-latest"]);
  }
}
