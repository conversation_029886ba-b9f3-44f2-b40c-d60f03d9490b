{"version": 3, "sources": ["../../../../node_modules/.pnpm/@tauri-apps+plugin-cli@2.4.0/node_modules/@tauri-apps/plugin-cli/dist-js/index.js"], "sourcesContent": ["import { invoke } from '@tauri-apps/api/core';\n\n// Copyright 2019-2023 Tauri Programme within The Commons Conservancy\n// SPDX-License-Identifier: Apache-2.0\n// SPDX-License-Identifier: MIT\n/**\n * Parse arguments from your Command Line Interface.\n *\n * @module\n */\n/**\n * Parse the arguments provided to the current process and get the matches using the configuration defined [`tauri.cli`](https://tauri.app/v1/api/config/#tauriconfig.cli) in `tauri.conf.json`\n *\n * @example\n * ```typescript\n * import { getMatches } from '@tauri-apps/plugin-cli';\n * const matches = await getMatches();\n * if (matches.subcommand?.name === 'run') {\n *   // `./your-app run $ARGS` was executed\n *   const args = matches.subcommand?.matches.args\n *   if ('debug' in args) {\n *     // `./your-app run --debug` was executed\n *   }\n * } else {\n *   const args = matches.args\n *   // `./your-app $ARGS` was executed\n * }\n * ```\n *\n * @since 2.0.0\n */\nasync function getMatches() {\n    return await invoke('plugin:cli|cli_matches');\n}\n\nexport { getMatches };\n"], "mappings": ";;;;;;AA+BA,eAAe,aAAa;AACxB,SAAO,MAAM,OAAO,wBAAwB;AAChD;", "names": []}