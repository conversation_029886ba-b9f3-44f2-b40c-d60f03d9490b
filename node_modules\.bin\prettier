#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/d/ssh/learn/project-graph-origin/node_modules/.pnpm/prettier@3.6.2/node_modules/prettier/bin/node_modules:/mnt/d/ssh/learn/project-graph-origin/node_modules/.pnpm/prettier@3.6.2/node_modules/prettier/node_modules:/mnt/d/ssh/learn/project-graph-origin/node_modules/.pnpm/prettier@3.6.2/node_modules:/mnt/d/ssh/learn/project-graph-origin/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/d/ssh/learn/project-graph-origin/node_modules/.pnpm/prettier@3.6.2/node_modules/prettier/bin/node_modules:/mnt/d/ssh/learn/project-graph-origin/node_modules/.pnpm/prettier@3.6.2/node_modules/prettier/node_modules:/mnt/d/ssh/learn/project-graph-origin/node_modules/.pnpm/prettier@3.6.2/node_modules:/mnt/d/ssh/learn/project-graph-origin/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../../node_modules/.pnpm/prettier@3.6.2/node_modules/prettier/bin/prettier.cjs" "$@"
else
  exec node  "$basedir/../../../node_modules/.pnpm/prettier@3.6.2/node_modules/prettier/bin/prettier.cjs" "$@"
fi
