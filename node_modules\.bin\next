#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/d/ssh/learn/project-graph-origin/node_modules/.pnpm/next@15.4.3_@babel+core@7.2_9cb668e83455c3a04e9d2212d787f7cd/node_modules/next/dist/bin/node_modules:/mnt/d/ssh/learn/project-graph-origin/node_modules/.pnpm/next@15.4.3_@babel+core@7.2_9cb668e83455c3a04e9d2212d787f7cd/node_modules/next/dist/node_modules:/mnt/d/ssh/learn/project-graph-origin/node_modules/.pnpm/next@15.4.3_@babel+core@7.2_9cb668e83455c3a04e9d2212d787f7cd/node_modules/next/node_modules:/mnt/d/ssh/learn/project-graph-origin/node_modules/.pnpm/next@15.4.3_@babel+core@7.2_9cb668e83455c3a04e9d2212d787f7cd/node_modules:/mnt/d/ssh/learn/project-graph-origin/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/d/ssh/learn/project-graph-origin/node_modules/.pnpm/next@15.4.3_@babel+core@7.2_9cb668e83455c3a04e9d2212d787f7cd/node_modules/next/dist/bin/node_modules:/mnt/d/ssh/learn/project-graph-origin/node_modules/.pnpm/next@15.4.3_@babel+core@7.2_9cb668e83455c3a04e9d2212d787f7cd/node_modules/next/dist/node_modules:/mnt/d/ssh/learn/project-graph-origin/node_modules/.pnpm/next@15.4.3_@babel+core@7.2_9cb668e83455c3a04e9d2212d787f7cd/node_modules/next/node_modules:/mnt/d/ssh/learn/project-graph-origin/node_modules/.pnpm/next@15.4.3_@babel+core@7.2_9cb668e83455c3a04e9d2212d787f7cd/node_modules:/mnt/d/ssh/learn/project-graph-origin/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../../node_modules/.pnpm/next@15.4.3_@babel+core@7.2_9cb668e83455c3a04e9d2212d787f7cd/node_modules/next/dist/bin/next" "$@"
else
  exec node  "$basedir/../../../node_modules/.pnpm/next@15.4.3_@babel+core@7.2_9cb668e83455c3a04e9d2212d787f7cd/node_modules/next/dist/bin/next" "$@"
fi
