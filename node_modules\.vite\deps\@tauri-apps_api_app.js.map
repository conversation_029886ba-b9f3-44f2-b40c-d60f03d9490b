{"version": 3, "sources": ["../../../../node_modules/.pnpm/@tauri-apps+api@2.7.0/node_modules/@tauri-apps/api/app.js"], "sourcesContent": ["import { invoke } from './core.js';\nimport { Image } from './image.js';\n\n// Copyright 2019-2024 Tauri Programme within The Commons Conservancy\n// SPDX-License-Identifier: Apache-2.0\n// SPDX-License-Identifier: MIT\n/**\n * Bundle type of the current application.\n */\nvar BundleType;\n(function (BundleType) {\n    /** Windows NSIS */\n    BundleType[\"Nsis\"] = \"nsis\";\n    /** Windows MSI */\n    BundleType[\"Msi\"] = \"msi\";\n    /** Linux Debian package */\n    BundleType[\"Deb\"] = \"deb\";\n    /** Linux RPM */\n    BundleType[\"Rpm\"] = \"rpm\";\n    /** Linux AppImage */\n    BundleType[\"AppImage\"] = \"appimage\";\n    /** macOS app bundle */\n    BundleType[\"App\"] = \"app\";\n})(BundleType || (BundleType = {}));\n/**\n * Application metadata and related APIs.\n *\n * @module\n */\n/**\n * Gets the application version.\n * @example\n * ```typescript\n * import { getVersion } from '@tauri-apps/api/app';\n * const appVersion = await getVersion();\n * ```\n *\n * @since 1.0.0\n */\nasync function getVersion() {\n    return invoke('plugin:app|version');\n}\n/**\n * Gets the application name.\n * @example\n * ```typescript\n * import { getName } from '@tauri-apps/api/app';\n * const appName = await getName();\n * ```\n *\n * @since 1.0.0\n */\nasync function getName() {\n    return invoke('plugin:app|name');\n}\n/**\n * Gets the Tauri version.\n *\n * @example\n * ```typescript\n * import { getTauriVersion } from '@tauri-apps/api/app';\n * const tauriVersion = await getTauriVersion();\n * ```\n *\n * @since 1.0.0\n */\nasync function getTauriVersion() {\n    return invoke('plugin:app|tauri_version');\n}\n/**\n * Gets the application identifier.\n * @example\n * ```typescript\n * import { getIdentifier } from '@tauri-apps/api/app';\n * const identifier = await getIdentifier();\n * ```\n *\n * @returns The application identifier as configured in `tauri.conf.json`.\n *\n * @since 2.4.0\n */\nasync function getIdentifier() {\n    return invoke('plugin:app|identifier');\n}\n/**\n * Shows the application on macOS. This function does not automatically focus any specific app window.\n *\n * @example\n * ```typescript\n * import { show } from '@tauri-apps/api/app';\n * await show();\n * ```\n *\n * @since 1.2.0\n */\nasync function show() {\n    return invoke('plugin:app|app_show');\n}\n/**\n * Hides the application on macOS.\n *\n * @example\n * ```typescript\n * import { hide } from '@tauri-apps/api/app';\n * await hide();\n * ```\n *\n * @since 1.2.0\n */\nasync function hide() {\n    return invoke('plugin:app|app_hide');\n}\n/**\n * Fetches the data store identifiers on macOS and iOS.\n *\n * See https://developer.apple.com/documentation/webkit/wkwebsitedatastore for more information.\n *\n * @example\n * ```typescript\n * import { fetchDataStoreIdentifiers } from '@tauri-apps/api/app';\n * const ids = await fetchDataStoreIdentifiers();\n * ```\n *\n * @since 2.4.0\n */\nasync function fetchDataStoreIdentifiers() {\n    return invoke('plugin:app|fetch_data_store_identifiers');\n}\n/**\n * Removes the data store with the given identifier.\n *\n * Note that any webview using this data store should be closed before running this API.\n *\n * See https://developer.apple.com/documentation/webkit/wkwebsitedatastore for more information.\n *\n * @example\n * ```typescript\n * import { fetchDataStoreIdentifiers, removeDataStore } from '@tauri-apps/api/app';\n * for (const id of (await fetchDataStoreIdentifiers())) {\n *  await removeDataStore(id);\n * }\n * ```\n *\n * @since 2.4.0\n */\nasync function removeDataStore(uuid) {\n    return invoke('plugin:app|remove_data_store', { uuid });\n}\n/**\n * Get the default window icon.\n *\n * @example\n * ```typescript\n * import { defaultWindowIcon } from '@tauri-apps/api/app';\n * await defaultWindowIcon();\n * ```\n *\n * @since 2.0.0\n */\nasync function defaultWindowIcon() {\n    return invoke('plugin:app|default_window_icon').then((rid) => rid ? new Image(rid) : null);\n}\n/**\n * Set app's theme, pass in `null` or `undefined` to follow system theme\n *\n * @example\n * ```typescript\n * import { setTheme } from '@tauri-apps/api/app';\n * await setTheme('dark');\n * ```\n *\n * #### Platform-specific\n *\n * - **iOS / Android:** Unsupported.\n *\n * @since 2.0.0\n */\nasync function setTheme(theme) {\n    return invoke('plugin:app|set_app_theme', { theme });\n}\n/**\n * Sets the dock visibility for the application on macOS.\n *\n * @param visible whether the dock should be visible or not\n * @since 2.5.0\n */\nasync function setDockVisibility(visible) {\n    return invoke('plugin:app|set_dock_visibility', { visible });\n}\nasync function getBundleType() {\n    return invoke('plugin:app|bundle_type');\n}\n\nexport { BundleType, defaultWindowIcon, fetchDataStoreIdentifiers, getBundleType, getIdentifier, getName, getTauriVersion, getVersion, hide, removeDataStore, setDockVisibility, setTheme, show };\n"], "mappings": ";;;;;;;;;AASA,IAAI;AAAA,CACH,SAAUA,aAAY;AAEnB,EAAAA,YAAW,MAAM,IAAI;AAErB,EAAAA,YAAW,KAAK,IAAI;AAEpB,EAAAA,YAAW,KAAK,IAAI;AAEpB,EAAAA,YAAW,KAAK,IAAI;AAEpB,EAAAA,YAAW,UAAU,IAAI;AAEzB,EAAAA,YAAW,KAAK,IAAI;AACxB,GAAG,eAAe,aAAa,CAAC,EAAE;AAgBlC,eAAe,aAAa;AACxB,SAAO,OAAO,oBAAoB;AACtC;AAWA,eAAe,UAAU;AACrB,SAAO,OAAO,iBAAiB;AACnC;AAYA,eAAe,kBAAkB;AAC7B,SAAO,OAAO,0BAA0B;AAC5C;AAaA,eAAe,gBAAgB;AAC3B,SAAO,OAAO,uBAAuB;AACzC;AAYA,eAAe,OAAO;AAClB,SAAO,OAAO,qBAAqB;AACvC;AAYA,eAAe,OAAO;AAClB,SAAO,OAAO,qBAAqB;AACvC;AAcA,eAAe,4BAA4B;AACvC,SAAO,OAAO,yCAAyC;AAC3D;AAkBA,eAAe,gBAAgB,MAAM;AACjC,SAAO,OAAO,gCAAgC,EAAE,KAAK,CAAC;AAC1D;AAYA,eAAe,oBAAoB;AAC/B,SAAO,OAAO,gCAAgC,EAAE,KAAK,CAAC,QAAQ,MAAM,IAAI,MAAM,GAAG,IAAI,IAAI;AAC7F;AAgBA,eAAe,SAAS,OAAO;AAC3B,SAAO,OAAO,4BAA4B,EAAE,MAAM,CAAC;AACvD;AAOA,eAAe,kBAAkB,SAAS;AACtC,SAAO,OAAO,kCAAkC,EAAE,QAAQ,CAAC;AAC/D;AACA,eAAe,gBAAgB;AAC3B,SAAO,OAAO,wBAAwB;AAC1C;", "names": ["BundleType"]}