{"version": 3, "sources": ["../../../../node_modules/.pnpm/@tauri-apps+plugin-shell@2.3.0/node_modules/@tauri-apps/plugin-shell/dist-js/index.js"], "sourcesContent": ["import { invoke, Channel } from '@tauri-apps/api/core';\n\n// Copyright 2019-2023 Tauri Programme within The Commons Conservancy\n// SPDX-License-Identifier: Apache-2.0\n// SPDX-License-Identifier: MIT\n/**\n * Access the system shell.\n * Allows you to spawn child processes and manage files and URLs using their default application.\n *\n * ## Security\n *\n * This API has a scope configuration that forces you to restrict the programs and arguments that can be used.\n *\n * ### Restricting access to the {@link open | `open`} API\n *\n * On the configuration object, `open: true` means that the {@link open} API can be used with any URL,\n * as the argument is validated with the `^((mailto:\\w+)|(tel:\\w+)|(https?://\\w+)).+` regex.\n * You can change that regex by changing the boolean value to a string, e.g. `open: ^https://github.com/`.\n *\n * ### Restricting access to the {@link Command | `Command`} APIs\n *\n * The plugin permissions object has a `scope` field that defines an array of CLIs that can be used.\n * Each CLI is a configuration object `{ name: string, cmd: string, sidecar?: bool, args?: boolean | Arg[] }`.\n *\n * - `name`: the unique identifier of the command, passed to the {@link Command.create | Command.create function}.\n * If it's a sidecar, this must be the value defined on `tauri.conf.json > bundle > externalBin`.\n * - `cmd`: the program that is executed on this configuration. If it's a sidecar, this value is ignored.\n * - `sidecar`: whether the object configures a sidecar or a system program.\n * - `args`: the arguments that can be passed to the program. By default no arguments are allowed.\n *   - `true` means that any argument list is allowed.\n *   - `false` means that no arguments are allowed.\n *   - otherwise an array can be configured. Each item is either a string representing the fixed argument value\n *     or a `{ validator: string }` that defines a regex validating the argument value.\n *\n * #### Example scope configuration\n *\n * CLI: `git commit -m \"the commit message\"`\n *\n * Capability:\n * ```json\n * {\n *   \"permissions\": [\n *     {\n *       \"identifier\": \"shell:allow-execute\",\n *       \"allow\": [\n *         {\n *           \"name\": \"run-git-commit\",\n *           \"cmd\": \"git\",\n *           \"args\": [\"commit\", \"-m\", { \"validator\": \"\\\\S+\" }]\n *         }\n *       ]\n *     }\n *   ]\n * }\n * ```\n * Usage:\n * ```typescript\n * import { Command } from '@tauri-apps/plugin-shell'\n * Command.create('run-git-commit', ['commit', '-m', 'the commit message'])\n * ```\n *\n * Trying to execute any API with a program not configured on the scope results in a promise rejection due to denied access.\n *\n * @module\n */\n/**\n * @since 2.0.0\n */\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nclass EventEmitter {\n    constructor() {\n        /** @ignore */\n        // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment, @typescript-eslint/no-explicit-any\n        this.eventListeners = Object.create(null);\n    }\n    /**\n     * Alias for `emitter.on(eventName, listener)`.\n     *\n     * @since 2.0.0\n     */\n    addListener(eventName, listener) {\n        return this.on(eventName, listener);\n    }\n    /**\n     * Alias for `emitter.off(eventName, listener)`.\n     *\n     * @since 2.0.0\n     */\n    removeListener(eventName, listener) {\n        return this.off(eventName, listener);\n    }\n    /**\n     * Adds the `listener` function to the end of the listeners array for the\n     * event named `eventName`. No checks are made to see if the `listener` has\n     * already been added. Multiple calls passing the same combination of `eventName`and `listener` will result in the `listener` being added, and called, multiple\n     * times.\n     *\n     * Returns a reference to the `EventEmitter`, so that calls can be chained.\n     *\n     * @since 2.0.0\n     */\n    on(eventName, listener) {\n        if (eventName in this.eventListeners) {\n            // eslint-disable-next-line security/detect-object-injection\n            this.eventListeners[eventName].push(listener);\n        }\n        else {\n            // eslint-disable-next-line security/detect-object-injection\n            this.eventListeners[eventName] = [listener];\n        }\n        return this;\n    }\n    /**\n     * Adds a **one-time**`listener` function for the event named `eventName`. The\n     * next time `eventName` is triggered, this listener is removed and then invoked.\n     *\n     * Returns a reference to the `EventEmitter`, so that calls can be chained.\n     *\n     * @since 2.0.0\n     */\n    once(eventName, listener) {\n        const wrapper = (arg) => {\n            this.removeListener(eventName, wrapper);\n            listener(arg);\n        };\n        return this.addListener(eventName, wrapper);\n    }\n    /**\n     * Removes the all specified listener from the listener array for the event eventName\n     * Returns a reference to the `EventEmitter`, so that calls can be chained.\n     *\n     * @since 2.0.0\n     */\n    off(eventName, listener) {\n        if (eventName in this.eventListeners) {\n            // eslint-disable-next-line security/detect-object-injection\n            this.eventListeners[eventName] = this.eventListeners[eventName].filter((l) => l !== listener);\n        }\n        return this;\n    }\n    /**\n     * Removes all listeners, or those of the specified eventName.\n     *\n     * Returns a reference to the `EventEmitter`, so that calls can be chained.\n     *\n     * @since 2.0.0\n     */\n    removeAllListeners(event) {\n        if (event) {\n            // eslint-disable-next-line security/detect-object-injection\n            delete this.eventListeners[event];\n        }\n        else {\n            // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment\n            this.eventListeners = Object.create(null);\n        }\n        return this;\n    }\n    /**\n     * @ignore\n     * Synchronously calls each of the listeners registered for the event named`eventName`, in the order they were registered, passing the supplied arguments\n     * to each.\n     *\n     * @returns `true` if the event had listeners, `false` otherwise.\n     *\n     * @since 2.0.0\n     */\n    emit(eventName, arg) {\n        if (eventName in this.eventListeners) {\n            // eslint-disable-next-line security/detect-object-injection\n            const listeners = this.eventListeners[eventName];\n            for (const listener of listeners)\n                listener(arg);\n            return true;\n        }\n        return false;\n    }\n    /**\n     * Returns the number of listeners listening to the event named `eventName`.\n     *\n     * @since 2.0.0\n     */\n    listenerCount(eventName) {\n        if (eventName in this.eventListeners)\n            // eslint-disable-next-line security/detect-object-injection\n            return this.eventListeners[eventName].length;\n        return 0;\n    }\n    /**\n     * Adds the `listener` function to the _beginning_ of the listeners array for the\n     * event named `eventName`. No checks are made to see if the `listener` has\n     * already been added. Multiple calls passing the same combination of `eventName`and `listener` will result in the `listener` being added, and called, multiple\n     * times.\n     *\n     * Returns a reference to the `EventEmitter`, so that calls can be chained.\n     *\n     * @since 2.0.0\n     */\n    prependListener(eventName, listener) {\n        if (eventName in this.eventListeners) {\n            // eslint-disable-next-line security/detect-object-injection\n            this.eventListeners[eventName].unshift(listener);\n        }\n        else {\n            // eslint-disable-next-line security/detect-object-injection\n            this.eventListeners[eventName] = [listener];\n        }\n        return this;\n    }\n    /**\n     * Adds a **one-time**`listener` function for the event named `eventName` to the_beginning_ of the listeners array. The next time `eventName` is triggered, this\n     * listener is removed, and then invoked.\n     *\n     * Returns a reference to the `EventEmitter`, so that calls can be chained.\n     *\n     * @since 2.0.0\n     */\n    prependOnceListener(eventName, listener) {\n        // eslint-disable-next-line @typescript-eslint/no-explicit-any\n        const wrapper = (arg) => {\n            this.removeListener(eventName, wrapper);\n            // eslint-disable-next-line @typescript-eslint/no-unsafe-argument\n            listener(arg);\n        };\n        return this.prependListener(eventName, wrapper);\n    }\n}\n/**\n * @since 2.0.0\n */\nclass Child {\n    constructor(pid) {\n        this.pid = pid;\n    }\n    /**\n     * Writes `data` to the `stdin`.\n     *\n     * @param data The message to write, either a string or a byte array.\n     * @example\n     * ```typescript\n     * import { Command } from '@tauri-apps/plugin-shell';\n     * const command = Command.create('node');\n     * const child = await command.spawn();\n     * await child.write('message');\n     * await child.write([0, 1, 2, 3, 4, 5]);\n     * ```\n     *\n     * @returns A promise indicating the success or failure of the operation.\n     *\n     * @since 2.0.0\n     */\n    async write(data) {\n        await invoke('plugin:shell|stdin_write', {\n            pid: this.pid,\n            buffer: data\n        });\n    }\n    /**\n     * Kills the child process.\n     *\n     * @returns A promise indicating the success or failure of the operation.\n     *\n     * @since 2.0.0\n     */\n    async kill() {\n        await invoke('plugin:shell|kill', {\n            cmd: 'killChild',\n            pid: this.pid\n        });\n    }\n}\n/**\n * The entry point for spawning child processes.\n * It emits the `close` and `error` events.\n * @example\n * ```typescript\n * import { Command } from '@tauri-apps/plugin-shell';\n * const command = Command.create('node');\n * command.on('close', data => {\n *   console.log(`command finished with code ${data.code} and signal ${data.signal}`)\n * });\n * command.on('error', error => console.error(`command error: \"${error}\"`));\n * command.stdout.on('data', line => console.log(`command stdout: \"${line}\"`));\n * command.stderr.on('data', line => console.log(`command stderr: \"${line}\"`));\n *\n * const child = await command.spawn();\n * console.log('pid:', child.pid);\n * ```\n *\n * @since 2.0.0\n *\n */\nclass Command extends EventEmitter {\n    /**\n     * @ignore\n     * Creates a new `Command` instance.\n     *\n     * @param program The program name to execute.\n     * It must be configured on `tauri.conf.json > plugins > shell > scope`.\n     * @param args Program arguments.\n     * @param options Spawn options.\n     */\n    constructor(program, args = [], options) {\n        super();\n        /** Event emitter for the `stdout`. Emits the `data` event. */\n        this.stdout = new EventEmitter();\n        /** Event emitter for the `stderr`. Emits the `data` event. */\n        this.stderr = new EventEmitter();\n        this.program = program;\n        this.args = typeof args === 'string' ? [args] : args;\n        this.options = options ?? {};\n    }\n    /**\n     * Creates a command to execute the given program.\n     * @example\n     * ```typescript\n     * import { Command } from '@tauri-apps/plugin-shell';\n     * const command = Command.create('my-app', ['run', 'tauri']);\n     * const output = await command.execute();\n     * ```\n     *\n     * @param program The program to execute.\n     * It must be configured on `tauri.conf.json > plugins > shell > scope`.\n     */\n    static create(program, args = [], options) {\n        return new Command(program, args, options);\n    }\n    /**\n     * Creates a command to execute the given sidecar program.\n     * @example\n     * ```typescript\n     * import { Command } from '@tauri-apps/plugin-shell';\n     * const command = Command.sidecar('my-sidecar');\n     * const output = await command.execute();\n     * ```\n     *\n     * @param program The program to execute.\n     * It must be configured on `tauri.conf.json > plugins > shell > scope`.\n     */\n    static sidecar(program, args = [], options) {\n        const instance = new Command(program, args, options);\n        instance.options.sidecar = true;\n        return instance;\n    }\n    /**\n     * Executes the command as a child process, returning a handle to it.\n     *\n     * @returns A promise resolving to the child process handle.\n     *\n     * @since 2.0.0\n     */\n    async spawn() {\n        const program = this.program;\n        const args = this.args;\n        const options = this.options;\n        if (typeof args === 'object') {\n            Object.freeze(args);\n        }\n        const onEvent = new Channel();\n        onEvent.onmessage = (event) => {\n            switch (event.event) {\n                case 'Error':\n                    this.emit('error', event.payload);\n                    break;\n                case 'Terminated':\n                    this.emit('close', event.payload);\n                    break;\n                case 'Stdout':\n                    this.stdout.emit('data', event.payload);\n                    break;\n                case 'Stderr':\n                    this.stderr.emit('data', event.payload);\n                    break;\n            }\n        };\n        return await invoke('plugin:shell|spawn', {\n            program,\n            args,\n            options,\n            onEvent\n        }).then((pid) => new Child(pid));\n    }\n    /**\n     * Executes the command as a child process, waiting for it to finish and collecting all of its output.\n     * @example\n     * ```typescript\n     * import { Command } from '@tauri-apps/plugin-shell';\n     * const output = await Command.create('echo', 'message').execute();\n     * assert(output.code === 0);\n     * assert(output.signal === null);\n     * assert(output.stdout === 'message');\n     * assert(output.stderr === '');\n     * ```\n     *\n     * @returns A promise resolving to the child process output.\n     *\n     * @since 2.0.0\n     */\n    async execute() {\n        const program = this.program;\n        const args = this.args;\n        const options = this.options;\n        if (typeof args === 'object') {\n            Object.freeze(args);\n        }\n        return await invoke('plugin:shell|execute', {\n            program,\n            args,\n            options\n        });\n    }\n}\n/**\n * Opens a path or URL with the system's default app,\n * or the one specified with `openWith`.\n *\n * The `openWith` value must be one of `firefox`, `google chrome`, `chromium` `safari`,\n * `open`, `start`, `xdg-open`, `gio`, `gnome-open`, `kde-open` or `wslview`.\n *\n * @example\n * ```typescript\n * import { open } from '@tauri-apps/plugin-shell';\n * // opens the given URL on the default browser:\n * await open('https://github.com/tauri-apps/tauri');\n * // opens the given URL using `firefox`:\n * await open('https://github.com/tauri-apps/tauri', 'firefox');\n * // opens a file using the default program:\n * await open('/path/to/file');\n * ```\n *\n * @param path The path or URL to open.\n * This value is matched against the string regex defined on `tauri.conf.json > plugins > shell > open`,\n * which defaults to `^((mailto:\\w+)|(tel:\\w+)|(https?://\\w+)).+`.\n * @param openWith The app to open the file or URL with.\n * Defaults to the system default application for the specified path type.\n *\n * @since 2.0.0\n */\nasync function open(path, openWith) {\n    await invoke('plugin:shell|open', {\n        path,\n        with: openWith\n    });\n}\n\nexport { Child, Command, EventEmitter, open };\n"], "mappings": ";;;;;;;AAqEA,IAAM,eAAN,MAAmB;AAAA,EACf,cAAc;AAGV,SAAK,iBAAiB,uBAAO,OAAO,IAAI;AAAA,EAC5C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,YAAY,WAAW,UAAU;AAC7B,WAAO,KAAK,GAAG,WAAW,QAAQ;AAAA,EACtC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,eAAe,WAAW,UAAU;AAChC,WAAO,KAAK,IAAI,WAAW,QAAQ;AAAA,EACvC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,GAAG,WAAW,UAAU;AACpB,QAAI,aAAa,KAAK,gBAAgB;AAElC,WAAK,eAAe,SAAS,EAAE,KAAK,QAAQ;AAAA,IAChD,OACK;AAED,WAAK,eAAe,SAAS,IAAI,CAAC,QAAQ;AAAA,IAC9C;AACA,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,KAAK,WAAW,UAAU;AACtB,UAAM,UAAU,CAAC,QAAQ;AACrB,WAAK,eAAe,WAAW,OAAO;AACtC,eAAS,GAAG;AAAA,IAChB;AACA,WAAO,KAAK,YAAY,WAAW,OAAO;AAAA,EAC9C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,IAAI,WAAW,UAAU;AACrB,QAAI,aAAa,KAAK,gBAAgB;AAElC,WAAK,eAAe,SAAS,IAAI,KAAK,eAAe,SAAS,EAAE,OAAO,CAAC,MAAM,MAAM,QAAQ;AAAA,IAChG;AACA,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,mBAAmB,OAAO;AACtB,QAAI,OAAO;AAEP,aAAO,KAAK,eAAe,KAAK;AAAA,IACpC,OACK;AAED,WAAK,iBAAiB,uBAAO,OAAO,IAAI;AAAA,IAC5C;AACA,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,KAAK,WAAW,KAAK;AACjB,QAAI,aAAa,KAAK,gBAAgB;AAElC,YAAM,YAAY,KAAK,eAAe,SAAS;AAC/C,iBAAW,YAAY;AACnB,iBAAS,GAAG;AAChB,aAAO;AAAA,IACX;AACA,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,cAAc,WAAW;AACrB,QAAI,aAAa,KAAK;AAElB,aAAO,KAAK,eAAe,SAAS,EAAE;AAC1C,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,gBAAgB,WAAW,UAAU;AACjC,QAAI,aAAa,KAAK,gBAAgB;AAElC,WAAK,eAAe,SAAS,EAAE,QAAQ,QAAQ;AAAA,IACnD,OACK;AAED,WAAK,eAAe,SAAS,IAAI,CAAC,QAAQ;AAAA,IAC9C;AACA,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,oBAAoB,WAAW,UAAU;AAErC,UAAM,UAAU,CAAC,QAAQ;AACrB,WAAK,eAAe,WAAW,OAAO;AAEtC,eAAS,GAAG;AAAA,IAChB;AACA,WAAO,KAAK,gBAAgB,WAAW,OAAO;AAAA,EAClD;AACJ;AAIA,IAAM,QAAN,MAAY;AAAA,EACR,YAAY,KAAK;AACb,SAAK,MAAM;AAAA,EACf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAkBA,MAAM,MAAM,MAAM;AACd,UAAM,OAAO,4BAA4B;AAAA,MACrC,KAAK,KAAK;AAAA,MACV,QAAQ;AAAA,IACZ,CAAC;AAAA,EACL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,MAAM,OAAO;AACT,UAAM,OAAO,qBAAqB;AAAA,MAC9B,KAAK;AAAA,MACL,KAAK,KAAK;AAAA,IACd,CAAC;AAAA,EACL;AACJ;AAsBA,IAAM,UAAN,MAAM,iBAAgB,aAAa;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAU/B,YAAY,SAAS,OAAO,CAAC,GAAG,SAAS;AACrC,UAAM;AAEN,SAAK,SAAS,IAAI,aAAa;AAE/B,SAAK,SAAS,IAAI,aAAa;AAC/B,SAAK,UAAU;AACf,SAAK,OAAO,OAAO,SAAS,WAAW,CAAC,IAAI,IAAI;AAChD,SAAK,UAAU,WAAW,CAAC;AAAA,EAC/B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAaA,OAAO,OAAO,SAAS,OAAO,CAAC,GAAG,SAAS;AACvC,WAAO,IAAI,SAAQ,SAAS,MAAM,OAAO;AAAA,EAC7C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAaA,OAAO,QAAQ,SAAS,OAAO,CAAC,GAAG,SAAS;AACxC,UAAM,WAAW,IAAI,SAAQ,SAAS,MAAM,OAAO;AACnD,aAAS,QAAQ,UAAU;AAC3B,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,MAAM,QAAQ;AACV,UAAM,UAAU,KAAK;AACrB,UAAM,OAAO,KAAK;AAClB,UAAM,UAAU,KAAK;AACrB,QAAI,OAAO,SAAS,UAAU;AAC1B,aAAO,OAAO,IAAI;AAAA,IACtB;AACA,UAAM,UAAU,IAAI,QAAQ;AAC5B,YAAQ,YAAY,CAAC,UAAU;AAC3B,cAAQ,MAAM,OAAO;AAAA,QACjB,KAAK;AACD,eAAK,KAAK,SAAS,MAAM,OAAO;AAChC;AAAA,QACJ,KAAK;AACD,eAAK,KAAK,SAAS,MAAM,OAAO;AAChC;AAAA,QACJ,KAAK;AACD,eAAK,OAAO,KAAK,QAAQ,MAAM,OAAO;AACtC;AAAA,QACJ,KAAK;AACD,eAAK,OAAO,KAAK,QAAQ,MAAM,OAAO;AACtC;AAAA,MACR;AAAA,IACJ;AACA,WAAO,MAAM,OAAO,sBAAsB;AAAA,MACtC;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ,CAAC,EAAE,KAAK,CAAC,QAAQ,IAAI,MAAM,GAAG,CAAC;AAAA,EACnC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAiBA,MAAM,UAAU;AACZ,UAAM,UAAU,KAAK;AACrB,UAAM,OAAO,KAAK;AAClB,UAAM,UAAU,KAAK;AACrB,QAAI,OAAO,SAAS,UAAU;AAC1B,aAAO,OAAO,IAAI;AAAA,IACtB;AACA,WAAO,MAAM,OAAO,wBAAwB;AAAA,MACxC;AAAA,MACA;AAAA,MACA;AAAA,IACJ,CAAC;AAAA,EACL;AACJ;AA2BA,eAAe,KAAK,MAAM,UAAU;AAChC,QAAM,OAAO,qBAAqB;AAAA,IAC9B;AAAA,IACA,MAAM;AAAA,EACV,CAAC;AACL;", "names": []}