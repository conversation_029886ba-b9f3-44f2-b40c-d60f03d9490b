/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */

// Code generated by the Google Gen AI SDK generator DO NOT EDIT.

import * as common from '../_common.js';
import * as t from '../_transformers.js';
import * as types from '../types.js';

export function getTuningJobParametersToMldev(
  fromObject: types.GetTuningJobParameters,
): Record<string, unknown> {
  const toObject: Record<string, unknown> = {};

  const fromName = common.getValueByPath(fromObject, ['name']);
  if (fromName != null) {
    common.setValueByPath(toObject, ['_url', 'name'], fromName);
  }

  const fromConfig = common.getValueByPath(fromObject, ['config']);
  if (fromConfig != null) {
    common.setValueByPath(toObject, ['config'], fromConfig);
  }

  return toObject;
}

export function listTuningJobsConfigToMldev(
  fromObject: types.ListTuningJobsConfig,
  parentObject: Record<string, unknown>,
): Record<string, unknown> {
  const toObject: Record<string, unknown> = {};

  const fromPageSize = common.getValueByPath(fromObject, ['pageSize']);
  if (parentObject !== undefined && fromPageSize != null) {
    common.setValueByPath(parentObject, ['_query', 'pageSize'], fromPageSize);
  }

  const fromPageToken = common.getValueByPath(fromObject, ['pageToken']);
  if (parentObject !== undefined && fromPageToken != null) {
    common.setValueByPath(parentObject, ['_query', 'pageToken'], fromPageToken);
  }

  const fromFilter = common.getValueByPath(fromObject, ['filter']);
  if (parentObject !== undefined && fromFilter != null) {
    common.setValueByPath(parentObject, ['_query', 'filter'], fromFilter);
  }

  return toObject;
}

export function listTuningJobsParametersToMldev(
  fromObject: types.ListTuningJobsParameters,
): Record<string, unknown> {
  const toObject: Record<string, unknown> = {};

  const fromConfig = common.getValueByPath(fromObject, ['config']);
  if (fromConfig != null) {
    common.setValueByPath(
      toObject,
      ['config'],
      listTuningJobsConfigToMldev(fromConfig, toObject),
    );
  }

  return toObject;
}

export function tuningExampleToMldev(
  fromObject: types.TuningExample,
): Record<string, unknown> {
  const toObject: Record<string, unknown> = {};

  const fromTextInput = common.getValueByPath(fromObject, ['textInput']);
  if (fromTextInput != null) {
    common.setValueByPath(toObject, ['textInput'], fromTextInput);
  }

  const fromOutput = common.getValueByPath(fromObject, ['output']);
  if (fromOutput != null) {
    common.setValueByPath(toObject, ['output'], fromOutput);
  }

  return toObject;
}

export function tuningDatasetToMldev(
  fromObject: types.TuningDataset,
): Record<string, unknown> {
  const toObject: Record<string, unknown> = {};

  if (common.getValueByPath(fromObject, ['gcsUri']) !== undefined) {
    throw new Error('gcsUri parameter is not supported in Gemini API.');
  }

  if (
    common.getValueByPath(fromObject, ['vertexDatasetResource']) !== undefined
  ) {
    throw new Error(
      'vertexDatasetResource parameter is not supported in Gemini API.',
    );
  }

  const fromExamples = common.getValueByPath(fromObject, ['examples']);
  if (fromExamples != null) {
    let transformedList = fromExamples;
    if (Array.isArray(transformedList)) {
      transformedList = transformedList.map((item) => {
        return tuningExampleToMldev(item);
      });
    }
    common.setValueByPath(toObject, ['examples', 'examples'], transformedList);
  }

  return toObject;
}

export function createTuningJobConfigToMldev(
  fromObject: types.CreateTuningJobConfig,
  parentObject: Record<string, unknown>,
): Record<string, unknown> {
  const toObject: Record<string, unknown> = {};

  if (common.getValueByPath(fromObject, ['validationDataset']) !== undefined) {
    throw new Error(
      'validationDataset parameter is not supported in Gemini API.',
    );
  }

  const fromTunedModelDisplayName = common.getValueByPath(fromObject, [
    'tunedModelDisplayName',
  ]);
  if (parentObject !== undefined && fromTunedModelDisplayName != null) {
    common.setValueByPath(
      parentObject,
      ['displayName'],
      fromTunedModelDisplayName,
    );
  }

  if (common.getValueByPath(fromObject, ['description']) !== undefined) {
    throw new Error('description parameter is not supported in Gemini API.');
  }

  const fromEpochCount = common.getValueByPath(fromObject, ['epochCount']);
  if (parentObject !== undefined && fromEpochCount != null) {
    common.setValueByPath(
      parentObject,
      ['tuningTask', 'hyperparameters', 'epochCount'],
      fromEpochCount,
    );
  }

  const fromLearningRateMultiplier = common.getValueByPath(fromObject, [
    'learningRateMultiplier',
  ]);
  if (fromLearningRateMultiplier != null) {
    common.setValueByPath(
      toObject,
      ['tuningTask', 'hyperparameters', 'learningRateMultiplier'],
      fromLearningRateMultiplier,
    );
  }

  if (
    common.getValueByPath(fromObject, ['exportLastCheckpointOnly']) !==
    undefined
  ) {
    throw new Error(
      'exportLastCheckpointOnly parameter is not supported in Gemini API.',
    );
  }

  if (common.getValueByPath(fromObject, ['adapterSize']) !== undefined) {
    throw new Error('adapterSize parameter is not supported in Gemini API.');
  }

  const fromBatchSize = common.getValueByPath(fromObject, ['batchSize']);
  if (parentObject !== undefined && fromBatchSize != null) {
    common.setValueByPath(
      parentObject,
      ['tuningTask', 'hyperparameters', 'batchSize'],
      fromBatchSize,
    );
  }

  const fromLearningRate = common.getValueByPath(fromObject, ['learningRate']);
  if (parentObject !== undefined && fromLearningRate != null) {
    common.setValueByPath(
      parentObject,
      ['tuningTask', 'hyperparameters', 'learningRate'],
      fromLearningRate,
    );
  }

  return toObject;
}

export function createTuningJobParametersToMldev(
  fromObject: types.CreateTuningJobParameters,
): Record<string, unknown> {
  const toObject: Record<string, unknown> = {};

  const fromBaseModel = common.getValueByPath(fromObject, ['baseModel']);
  if (fromBaseModel != null) {
    common.setValueByPath(toObject, ['baseModel'], fromBaseModel);
  }

  const fromTrainingDataset = common.getValueByPath(fromObject, [
    'trainingDataset',
  ]);
  if (fromTrainingDataset != null) {
    common.setValueByPath(
      toObject,
      ['tuningTask', 'trainingData'],
      tuningDatasetToMldev(fromTrainingDataset),
    );
  }

  const fromConfig = common.getValueByPath(fromObject, ['config']);
  if (fromConfig != null) {
    common.setValueByPath(
      toObject,
      ['config'],
      createTuningJobConfigToMldev(fromConfig, toObject),
    );
  }

  return toObject;
}

export function getTuningJobParametersToVertex(
  fromObject: types.GetTuningJobParameters,
): Record<string, unknown> {
  const toObject: Record<string, unknown> = {};

  const fromName = common.getValueByPath(fromObject, ['name']);
  if (fromName != null) {
    common.setValueByPath(toObject, ['_url', 'name'], fromName);
  }

  const fromConfig = common.getValueByPath(fromObject, ['config']);
  if (fromConfig != null) {
    common.setValueByPath(toObject, ['config'], fromConfig);
  }

  return toObject;
}

export function listTuningJobsConfigToVertex(
  fromObject: types.ListTuningJobsConfig,
  parentObject: Record<string, unknown>,
): Record<string, unknown> {
  const toObject: Record<string, unknown> = {};

  const fromPageSize = common.getValueByPath(fromObject, ['pageSize']);
  if (parentObject !== undefined && fromPageSize != null) {
    common.setValueByPath(parentObject, ['_query', 'pageSize'], fromPageSize);
  }

  const fromPageToken = common.getValueByPath(fromObject, ['pageToken']);
  if (parentObject !== undefined && fromPageToken != null) {
    common.setValueByPath(parentObject, ['_query', 'pageToken'], fromPageToken);
  }

  const fromFilter = common.getValueByPath(fromObject, ['filter']);
  if (parentObject !== undefined && fromFilter != null) {
    common.setValueByPath(parentObject, ['_query', 'filter'], fromFilter);
  }

  return toObject;
}

export function listTuningJobsParametersToVertex(
  fromObject: types.ListTuningJobsParameters,
): Record<string, unknown> {
  const toObject: Record<string, unknown> = {};

  const fromConfig = common.getValueByPath(fromObject, ['config']);
  if (fromConfig != null) {
    common.setValueByPath(
      toObject,
      ['config'],
      listTuningJobsConfigToVertex(fromConfig, toObject),
    );
  }

  return toObject;
}

export function tuningDatasetToVertex(
  fromObject: types.TuningDataset,
  parentObject: Record<string, unknown>,
): Record<string, unknown> {
  const toObject: Record<string, unknown> = {};

  const fromGcsUri = common.getValueByPath(fromObject, ['gcsUri']);
  if (parentObject !== undefined && fromGcsUri != null) {
    common.setValueByPath(
      parentObject,
      ['supervisedTuningSpec', 'trainingDatasetUri'],
      fromGcsUri,
    );
  }

  const fromVertexDatasetResource = common.getValueByPath(fromObject, [
    'vertexDatasetResource',
  ]);
  if (parentObject !== undefined && fromVertexDatasetResource != null) {
    common.setValueByPath(
      parentObject,
      ['supervisedTuningSpec', 'trainingDatasetUri'],
      fromVertexDatasetResource,
    );
  }

  if (common.getValueByPath(fromObject, ['examples']) !== undefined) {
    throw new Error('examples parameter is not supported in Vertex AI.');
  }

  return toObject;
}

export function tuningValidationDatasetToVertex(
  fromObject: types.TuningValidationDataset,
  parentObject: Record<string, unknown>,
): Record<string, unknown> {
  const toObject: Record<string, unknown> = {};

  const fromGcsUri = common.getValueByPath(fromObject, ['gcsUri']);
  if (fromGcsUri != null) {
    common.setValueByPath(toObject, ['validationDatasetUri'], fromGcsUri);
  }

  const fromVertexDatasetResource = common.getValueByPath(fromObject, [
    'vertexDatasetResource',
  ]);
  if (parentObject !== undefined && fromVertexDatasetResource != null) {
    common.setValueByPath(
      parentObject,
      ['supervisedTuningSpec', 'trainingDatasetUri'],
      fromVertexDatasetResource,
    );
  }

  return toObject;
}

export function createTuningJobConfigToVertex(
  fromObject: types.CreateTuningJobConfig,
  parentObject: Record<string, unknown>,
): Record<string, unknown> {
  const toObject: Record<string, unknown> = {};

  const fromValidationDataset = common.getValueByPath(fromObject, [
    'validationDataset',
  ]);
  if (parentObject !== undefined && fromValidationDataset != null) {
    common.setValueByPath(
      parentObject,
      ['supervisedTuningSpec'],
      tuningValidationDatasetToVertex(fromValidationDataset, toObject),
    );
  }

  const fromTunedModelDisplayName = common.getValueByPath(fromObject, [
    'tunedModelDisplayName',
  ]);
  if (parentObject !== undefined && fromTunedModelDisplayName != null) {
    common.setValueByPath(
      parentObject,
      ['tunedModelDisplayName'],
      fromTunedModelDisplayName,
    );
  }

  const fromDescription = common.getValueByPath(fromObject, ['description']);
  if (parentObject !== undefined && fromDescription != null) {
    common.setValueByPath(parentObject, ['description'], fromDescription);
  }

  const fromEpochCount = common.getValueByPath(fromObject, ['epochCount']);
  if (parentObject !== undefined && fromEpochCount != null) {
    common.setValueByPath(
      parentObject,
      ['supervisedTuningSpec', 'hyperParameters', 'epochCount'],
      fromEpochCount,
    );
  }

  const fromLearningRateMultiplier = common.getValueByPath(fromObject, [
    'learningRateMultiplier',
  ]);
  if (parentObject !== undefined && fromLearningRateMultiplier != null) {
    common.setValueByPath(
      parentObject,
      ['supervisedTuningSpec', 'hyperParameters', 'learningRateMultiplier'],
      fromLearningRateMultiplier,
    );
  }

  const fromExportLastCheckpointOnly = common.getValueByPath(fromObject, [
    'exportLastCheckpointOnly',
  ]);
  if (parentObject !== undefined && fromExportLastCheckpointOnly != null) {
    common.setValueByPath(
      parentObject,
      ['supervisedTuningSpec', 'exportLastCheckpointOnly'],
      fromExportLastCheckpointOnly,
    );
  }

  const fromAdapterSize = common.getValueByPath(fromObject, ['adapterSize']);
  if (parentObject !== undefined && fromAdapterSize != null) {
    common.setValueByPath(
      parentObject,
      ['supervisedTuningSpec', 'hyperParameters', 'adapterSize'],
      fromAdapterSize,
    );
  }

  if (common.getValueByPath(fromObject, ['batchSize']) !== undefined) {
    throw new Error('batchSize parameter is not supported in Vertex AI.');
  }

  if (common.getValueByPath(fromObject, ['learningRate']) !== undefined) {
    throw new Error('learningRate parameter is not supported in Vertex AI.');
  }

  return toObject;
}

export function createTuningJobParametersToVertex(
  fromObject: types.CreateTuningJobParameters,
): Record<string, unknown> {
  const toObject: Record<string, unknown> = {};

  const fromBaseModel = common.getValueByPath(fromObject, ['baseModel']);
  if (fromBaseModel != null) {
    common.setValueByPath(toObject, ['baseModel'], fromBaseModel);
  }

  const fromTrainingDataset = common.getValueByPath(fromObject, [
    'trainingDataset',
  ]);
  if (fromTrainingDataset != null) {
    common.setValueByPath(
      toObject,
      ['supervisedTuningSpec', 'trainingDatasetUri'],
      tuningDatasetToVertex(fromTrainingDataset, toObject),
    );
  }

  const fromConfig = common.getValueByPath(fromObject, ['config']);
  if (fromConfig != null) {
    common.setValueByPath(
      toObject,
      ['config'],
      createTuningJobConfigToVertex(fromConfig, toObject),
    );
  }

  return toObject;
}

export function tunedModelFromMldev(
  fromObject: types.TunedModel,
): Record<string, unknown> {
  const toObject: Record<string, unknown> = {};

  const fromModel = common.getValueByPath(fromObject, ['name']);
  if (fromModel != null) {
    common.setValueByPath(toObject, ['model'], fromModel);
  }

  const fromEndpoint = common.getValueByPath(fromObject, ['name']);
  if (fromEndpoint != null) {
    common.setValueByPath(toObject, ['endpoint'], fromEndpoint);
  }

  return toObject;
}

export function tuningJobFromMldev(
  fromObject: types.TuningJob,
): Record<string, unknown> {
  const toObject: Record<string, unknown> = {};

  const fromSdkHttpResponse = common.getValueByPath(fromObject, [
    'sdkHttpResponse',
  ]);
  if (fromSdkHttpResponse != null) {
    common.setValueByPath(toObject, ['sdkHttpResponse'], fromSdkHttpResponse);
  }

  const fromName = common.getValueByPath(fromObject, ['name']);
  if (fromName != null) {
    common.setValueByPath(toObject, ['name'], fromName);
  }

  const fromState = common.getValueByPath(fromObject, ['state']);
  if (fromState != null) {
    common.setValueByPath(toObject, ['state'], t.tTuningJobStatus(fromState));
  }

  const fromCreateTime = common.getValueByPath(fromObject, ['createTime']);
  if (fromCreateTime != null) {
    common.setValueByPath(toObject, ['createTime'], fromCreateTime);
  }

  const fromStartTime = common.getValueByPath(fromObject, [
    'tuningTask',
    'startTime',
  ]);
  if (fromStartTime != null) {
    common.setValueByPath(toObject, ['startTime'], fromStartTime);
  }

  const fromEndTime = common.getValueByPath(fromObject, [
    'tuningTask',
    'completeTime',
  ]);
  if (fromEndTime != null) {
    common.setValueByPath(toObject, ['endTime'], fromEndTime);
  }

  const fromUpdateTime = common.getValueByPath(fromObject, ['updateTime']);
  if (fromUpdateTime != null) {
    common.setValueByPath(toObject, ['updateTime'], fromUpdateTime);
  }

  const fromDescription = common.getValueByPath(fromObject, ['description']);
  if (fromDescription != null) {
    common.setValueByPath(toObject, ['description'], fromDescription);
  }

  const fromBaseModel = common.getValueByPath(fromObject, ['baseModel']);
  if (fromBaseModel != null) {
    common.setValueByPath(toObject, ['baseModel'], fromBaseModel);
  }

  const fromTunedModel = common.getValueByPath(fromObject, ['_self']);
  if (fromTunedModel != null) {
    common.setValueByPath(
      toObject,
      ['tunedModel'],
      tunedModelFromMldev(fromTunedModel),
    );
  }

  const fromDistillationSpec = common.getValueByPath(fromObject, [
    'distillationSpec',
  ]);
  if (fromDistillationSpec != null) {
    common.setValueByPath(toObject, ['distillationSpec'], fromDistillationSpec);
  }

  const fromExperiment = common.getValueByPath(fromObject, ['experiment']);
  if (fromExperiment != null) {
    common.setValueByPath(toObject, ['experiment'], fromExperiment);
  }

  const fromLabels = common.getValueByPath(fromObject, ['labels']);
  if (fromLabels != null) {
    common.setValueByPath(toObject, ['labels'], fromLabels);
  }

  const fromPipelineJob = common.getValueByPath(fromObject, ['pipelineJob']);
  if (fromPipelineJob != null) {
    common.setValueByPath(toObject, ['pipelineJob'], fromPipelineJob);
  }

  const fromSatisfiesPzi = common.getValueByPath(fromObject, ['satisfiesPzi']);
  if (fromSatisfiesPzi != null) {
    common.setValueByPath(toObject, ['satisfiesPzi'], fromSatisfiesPzi);
  }

  const fromSatisfiesPzs = common.getValueByPath(fromObject, ['satisfiesPzs']);
  if (fromSatisfiesPzs != null) {
    common.setValueByPath(toObject, ['satisfiesPzs'], fromSatisfiesPzs);
  }

  const fromServiceAccount = common.getValueByPath(fromObject, [
    'serviceAccount',
  ]);
  if (fromServiceAccount != null) {
    common.setValueByPath(toObject, ['serviceAccount'], fromServiceAccount);
  }

  const fromTunedModelDisplayName = common.getValueByPath(fromObject, [
    'tunedModelDisplayName',
  ]);
  if (fromTunedModelDisplayName != null) {
    common.setValueByPath(
      toObject,
      ['tunedModelDisplayName'],
      fromTunedModelDisplayName,
    );
  }

  return toObject;
}

export function listTuningJobsResponseFromMldev(
  fromObject: types.ListTuningJobsResponse,
): Record<string, unknown> {
  const toObject: Record<string, unknown> = {};

  const fromSdkHttpResponse = common.getValueByPath(fromObject, [
    'sdkHttpResponse',
  ]);
  if (fromSdkHttpResponse != null) {
    common.setValueByPath(toObject, ['sdkHttpResponse'], fromSdkHttpResponse);
  }

  const fromNextPageToken = common.getValueByPath(fromObject, [
    'nextPageToken',
  ]);
  if (fromNextPageToken != null) {
    common.setValueByPath(toObject, ['nextPageToken'], fromNextPageToken);
  }

  const fromTuningJobs = common.getValueByPath(fromObject, ['tunedModels']);
  if (fromTuningJobs != null) {
    let transformedList = fromTuningJobs;
    if (Array.isArray(transformedList)) {
      transformedList = transformedList.map((item) => {
        return tuningJobFromMldev(item);
      });
    }
    common.setValueByPath(toObject, ['tuningJobs'], transformedList);
  }

  return toObject;
}

export function tuningOperationFromMldev(
  fromObject: types.TuningOperation,
): Record<string, unknown> {
  const toObject: Record<string, unknown> = {};

  const fromSdkHttpResponse = common.getValueByPath(fromObject, [
    'sdkHttpResponse',
  ]);
  if (fromSdkHttpResponse != null) {
    common.setValueByPath(toObject, ['sdkHttpResponse'], fromSdkHttpResponse);
  }

  const fromName = common.getValueByPath(fromObject, ['name']);
  if (fromName != null) {
    common.setValueByPath(toObject, ['name'], fromName);
  }

  const fromMetadata = common.getValueByPath(fromObject, ['metadata']);
  if (fromMetadata != null) {
    common.setValueByPath(toObject, ['metadata'], fromMetadata);
  }

  const fromDone = common.getValueByPath(fromObject, ['done']);
  if (fromDone != null) {
    common.setValueByPath(toObject, ['done'], fromDone);
  }

  const fromError = common.getValueByPath(fromObject, ['error']);
  if (fromError != null) {
    common.setValueByPath(toObject, ['error'], fromError);
  }

  return toObject;
}

export function tunedModelCheckpointFromVertex(
  fromObject: types.TunedModelCheckpoint,
): Record<string, unknown> {
  const toObject: Record<string, unknown> = {};

  const fromCheckpointId = common.getValueByPath(fromObject, ['checkpointId']);
  if (fromCheckpointId != null) {
    common.setValueByPath(toObject, ['checkpointId'], fromCheckpointId);
  }

  const fromEpoch = common.getValueByPath(fromObject, ['epoch']);
  if (fromEpoch != null) {
    common.setValueByPath(toObject, ['epoch'], fromEpoch);
  }

  const fromStep = common.getValueByPath(fromObject, ['step']);
  if (fromStep != null) {
    common.setValueByPath(toObject, ['step'], fromStep);
  }

  const fromEndpoint = common.getValueByPath(fromObject, ['endpoint']);
  if (fromEndpoint != null) {
    common.setValueByPath(toObject, ['endpoint'], fromEndpoint);
  }

  return toObject;
}

export function tunedModelFromVertex(
  fromObject: types.TunedModel,
): Record<string, unknown> {
  const toObject: Record<string, unknown> = {};

  const fromModel = common.getValueByPath(fromObject, ['model']);
  if (fromModel != null) {
    common.setValueByPath(toObject, ['model'], fromModel);
  }

  const fromEndpoint = common.getValueByPath(fromObject, ['endpoint']);
  if (fromEndpoint != null) {
    common.setValueByPath(toObject, ['endpoint'], fromEndpoint);
  }

  const fromCheckpoints = common.getValueByPath(fromObject, ['checkpoints']);
  if (fromCheckpoints != null) {
    let transformedList = fromCheckpoints;
    if (Array.isArray(transformedList)) {
      transformedList = transformedList.map((item) => {
        return tunedModelCheckpointFromVertex(item);
      });
    }
    common.setValueByPath(toObject, ['checkpoints'], transformedList);
  }

  return toObject;
}

export function tuningJobFromVertex(
  fromObject: types.TuningJob,
): Record<string, unknown> {
  const toObject: Record<string, unknown> = {};

  const fromSdkHttpResponse = common.getValueByPath(fromObject, [
    'sdkHttpResponse',
  ]);
  if (fromSdkHttpResponse != null) {
    common.setValueByPath(toObject, ['sdkHttpResponse'], fromSdkHttpResponse);
  }

  const fromName = common.getValueByPath(fromObject, ['name']);
  if (fromName != null) {
    common.setValueByPath(toObject, ['name'], fromName);
  }

  const fromState = common.getValueByPath(fromObject, ['state']);
  if (fromState != null) {
    common.setValueByPath(toObject, ['state'], t.tTuningJobStatus(fromState));
  }

  const fromCreateTime = common.getValueByPath(fromObject, ['createTime']);
  if (fromCreateTime != null) {
    common.setValueByPath(toObject, ['createTime'], fromCreateTime);
  }

  const fromStartTime = common.getValueByPath(fromObject, ['startTime']);
  if (fromStartTime != null) {
    common.setValueByPath(toObject, ['startTime'], fromStartTime);
  }

  const fromEndTime = common.getValueByPath(fromObject, ['endTime']);
  if (fromEndTime != null) {
    common.setValueByPath(toObject, ['endTime'], fromEndTime);
  }

  const fromUpdateTime = common.getValueByPath(fromObject, ['updateTime']);
  if (fromUpdateTime != null) {
    common.setValueByPath(toObject, ['updateTime'], fromUpdateTime);
  }

  const fromError = common.getValueByPath(fromObject, ['error']);
  if (fromError != null) {
    common.setValueByPath(toObject, ['error'], fromError);
  }

  const fromDescription = common.getValueByPath(fromObject, ['description']);
  if (fromDescription != null) {
    common.setValueByPath(toObject, ['description'], fromDescription);
  }

  const fromBaseModel = common.getValueByPath(fromObject, ['baseModel']);
  if (fromBaseModel != null) {
    common.setValueByPath(toObject, ['baseModel'], fromBaseModel);
  }

  const fromTunedModel = common.getValueByPath(fromObject, ['tunedModel']);
  if (fromTunedModel != null) {
    common.setValueByPath(
      toObject,
      ['tunedModel'],
      tunedModelFromVertex(fromTunedModel),
    );
  }

  const fromSupervisedTuningSpec = common.getValueByPath(fromObject, [
    'supervisedTuningSpec',
  ]);
  if (fromSupervisedTuningSpec != null) {
    common.setValueByPath(
      toObject,
      ['supervisedTuningSpec'],
      fromSupervisedTuningSpec,
    );
  }

  const fromTuningDataStats = common.getValueByPath(fromObject, [
    'tuningDataStats',
  ]);
  if (fromTuningDataStats != null) {
    common.setValueByPath(toObject, ['tuningDataStats'], fromTuningDataStats);
  }

  const fromEncryptionSpec = common.getValueByPath(fromObject, [
    'encryptionSpec',
  ]);
  if (fromEncryptionSpec != null) {
    common.setValueByPath(toObject, ['encryptionSpec'], fromEncryptionSpec);
  }

  const fromPartnerModelTuningSpec = common.getValueByPath(fromObject, [
    'partnerModelTuningSpec',
  ]);
  if (fromPartnerModelTuningSpec != null) {
    common.setValueByPath(
      toObject,
      ['partnerModelTuningSpec'],
      fromPartnerModelTuningSpec,
    );
  }

  const fromDistillationSpec = common.getValueByPath(fromObject, [
    'distillationSpec',
  ]);
  if (fromDistillationSpec != null) {
    common.setValueByPath(toObject, ['distillationSpec'], fromDistillationSpec);
  }

  const fromExperiment = common.getValueByPath(fromObject, ['experiment']);
  if (fromExperiment != null) {
    common.setValueByPath(toObject, ['experiment'], fromExperiment);
  }

  const fromLabels = common.getValueByPath(fromObject, ['labels']);
  if (fromLabels != null) {
    common.setValueByPath(toObject, ['labels'], fromLabels);
  }

  const fromPipelineJob = common.getValueByPath(fromObject, ['pipelineJob']);
  if (fromPipelineJob != null) {
    common.setValueByPath(toObject, ['pipelineJob'], fromPipelineJob);
  }

  const fromSatisfiesPzi = common.getValueByPath(fromObject, ['satisfiesPzi']);
  if (fromSatisfiesPzi != null) {
    common.setValueByPath(toObject, ['satisfiesPzi'], fromSatisfiesPzi);
  }

  const fromSatisfiesPzs = common.getValueByPath(fromObject, ['satisfiesPzs']);
  if (fromSatisfiesPzs != null) {
    common.setValueByPath(toObject, ['satisfiesPzs'], fromSatisfiesPzs);
  }

  const fromServiceAccount = common.getValueByPath(fromObject, [
    'serviceAccount',
  ]);
  if (fromServiceAccount != null) {
    common.setValueByPath(toObject, ['serviceAccount'], fromServiceAccount);
  }

  const fromTunedModelDisplayName = common.getValueByPath(fromObject, [
    'tunedModelDisplayName',
  ]);
  if (fromTunedModelDisplayName != null) {
    common.setValueByPath(
      toObject,
      ['tunedModelDisplayName'],
      fromTunedModelDisplayName,
    );
  }

  return toObject;
}

export function listTuningJobsResponseFromVertex(
  fromObject: types.ListTuningJobsResponse,
): Record<string, unknown> {
  const toObject: Record<string, unknown> = {};

  const fromSdkHttpResponse = common.getValueByPath(fromObject, [
    'sdkHttpResponse',
  ]);
  if (fromSdkHttpResponse != null) {
    common.setValueByPath(toObject, ['sdkHttpResponse'], fromSdkHttpResponse);
  }

  const fromNextPageToken = common.getValueByPath(fromObject, [
    'nextPageToken',
  ]);
  if (fromNextPageToken != null) {
    common.setValueByPath(toObject, ['nextPageToken'], fromNextPageToken);
  }

  const fromTuningJobs = common.getValueByPath(fromObject, ['tuningJobs']);
  if (fromTuningJobs != null) {
    let transformedList = fromTuningJobs;
    if (Array.isArray(transformedList)) {
      transformedList = transformedList.map((item) => {
        return tuningJobFromVertex(item);
      });
    }
    common.setValueByPath(toObject, ['tuningJobs'], transformedList);
  }

  return toObject;
}
