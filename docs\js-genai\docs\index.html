<!DOCTYPE html><html class="default" lang="en" data-base="."><head><meta charset="utf-8"/><meta http-equiv="x-ua-compatible" content="IE=edge"/><title>@google/genai</title><meta name="description" content="Documentation for @google/genai"/><meta name="viewport" content="width=device-width, initial-scale=1"/><link rel="stylesheet" href="assets/style.css"/><link rel="stylesheet" href="assets/highlight.css"/><script defer src="assets/main.js"></script><script async src="assets/icons.js" id="tsd-icons-script"></script><script async src="assets/search.js" id="tsd-search-script"></script><script async src="assets/navigation.js" id="tsd-nav-script"></script></head><body><script>document.documentElement.dataset.theme = localStorage.getItem("tsd-theme") || "os";document.body.style.display="none";setTimeout(() => app?app.showPage():document.body.style.removeProperty("display"),500)</script><header class="tsd-page-toolbar"><div class="tsd-toolbar-contents container"><div class="table-cell" id="tsd-search"><div class="field"><label for="tsd-search-field" class="tsd-widget tsd-toolbar-icon search no-caption"><svg width="16" height="16" viewBox="0 0 16 16" fill="none" aria-hidden="true"><use href="assets/icons.svg#icon-search"></use></svg></label><input type="text" id="tsd-search-field" aria-label="Search"/></div><div class="field"><div id="tsd-toolbar-links"></div></div><ul class="results"><li class="state loading">Preparing search index...</li><li class="state failure">The search index is not available</li></ul><a href="index.html" class="title">@google/genai</a></div><div class="table-cell" id="tsd-widgets"><a href="#" class="tsd-widget tsd-toolbar-icon menu no-caption" data-toggle="menu" aria-label="Menu"><svg width="16" height="16" viewBox="0 0 16 16" fill="none" aria-hidden="true"><use href="assets/icons.svg#icon-menu"></use></svg></a></div></div></header><div class="container container-main"><div class="col-content"><div class="tsd-page-title"><h1>@google/genai</h1></div><div class="tsd-panel tsd-typography"><a id="google-gen-ai-sdk-for-typescript-and-javascript" class="tsd-anchor"></a><h1 class="tsd-anchor-link">Google Gen AI SDK for TypeScript and JavaScript<a href="#google-gen-ai-sdk-for-typescript-and-javascript" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="assets/icons.svg#icon-anchor"></use></svg></a></h1><p><a href="https://www.npmjs.com/package/@google/genai"><img src="https://img.shields.io/npm/dw/%40google%2Fgenai" alt="NPM Downloads"></a>
<a href="https://www.npmjs.com/package/@google/genai"><img src="https://img.shields.io/node/v/%40google%2Fgenai" alt="Node Current"></a></p>
<hr>
<p><strong>Documentation:</strong> <a href="https://googleapis.github.io/js-genai/">https://googleapis.github.io/js-genai/</a></p>
<hr>
<p>The Google Gen AI JavaScript SDK is designed for
TypeScript and JavaScript developers to build applications powered by Gemini. The SDK
supports both the <a href="https://ai.google.dev/gemini-api/docs">Gemini Developer API</a>
and <a href="https://cloud.google.com/vertex-ai/generative-ai/docs/learn/overview">Vertex AI</a>.</p>
<p>The Google Gen AI SDK is designed to work with Gemini 2.0 features.</p>
<div class="tsd-alert tsd-alert-caution"><div class="tsd-alert-title"><svg width="16" height="16" viewBox="0 0 16 16" aria-hidden="true"><use href="assets/icons.svg#icon-alertCaution"></use></svg><span>Caution</span></div><p>
<strong>API Key Security:</strong> Avoid exposing API keys in client-side code.
Use server-side implementations in production environments.</p>
</div>
<a id="prerequisites" class="tsd-anchor"></a><h2 class="tsd-anchor-link">Prerequisites<a href="#prerequisites" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="assets/icons.svg#icon-anchor"></use></svg></a></h2><ol>
<li>Node.js version 20 or later</li>
</ol>
<a id="the-following-are-required-for-vertex-ai-users-excluding-vertex-ai-studio" class="tsd-anchor"></a><h3 class="tsd-anchor-link">The following are required for Vertex AI users (excluding Vertex AI Studio)<a href="#the-following-are-required-for-vertex-ai-users-excluding-vertex-ai-studio" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="assets/icons.svg#icon-anchor"></use></svg></a></h3><ol>
<li>
<p><a href="https://console.cloud.google.com/project">Select</a> or <a href="https://cloud.google.com/resource-manager/docs/creating-managing-projects#creating_a_project">create</a> a Google Cloud project.</p>
</li>
<li>
<p><a href="https://cloud.google.com/billing/docs/how-to/modify-project">Enable billing for your project</a>.</p>
</li>
<li>
<p><a href="https://console.cloud.google.com/flows/enableapi?apiid=aiplatform.googleapis.com">Enable the Vertex AI API</a>.</p>
</li>
<li>
<p><a href="https://cloud.google.com/docs/authentication">Configure authentication</a> for your project.</p>
<ul>
<li><a href="https://cloud.google.com/sdk/docs/install">Install the gcloud CLI</a>.</li>
<li><a href="https://cloud.google.com/sdk/docs/initializing">Initialize the gcloud CLI</a>.</li>
<li>Create local authentication credentials for your user account:</li>
</ul>
<pre><code class="sh"><span class="hl-0">gcloud</span><span class="hl-1"> </span><span class="hl-2">auth</span><span class="hl-1"> </span><span class="hl-2">application-default</span><span class="hl-1"> </span><span class="hl-2">login</span>
</code><button type="button">Copy</button></pre>

</li>
</ol>
<p>A list of accepted authentication options are listed in <a href="https://github.com/googleapis/google-auth-library-nodejs/blob/3ae120d0a45c95e36c59c9ac8286483938781f30/src/auth/googleauth.ts#L87">GoogleAuthOptions</a> interface of google-auth-library-node.js GitHub repo.</p>
<a id="installation" class="tsd-anchor"></a><h2 class="tsd-anchor-link">Installation<a href="#installation" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="assets/icons.svg#icon-anchor"></use></svg></a></h2><p>To install the SDK, run the following command:</p>
<pre><code class="shell"><span class="hl-0">npm</span><span class="hl-1"> </span><span class="hl-2">install</span><span class="hl-1"> </span><span class="hl-2">@google/genai</span>
</code><button type="button">Copy</button></pre>

<a id="quickstart" class="tsd-anchor"></a><h2 class="tsd-anchor-link">Quickstart<a href="#quickstart" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="assets/icons.svg#icon-anchor"></use></svg></a></h2><p>The simplest way to get started is to use an API key from
<a href="https://aistudio.google.com/apikey">Google AI Studio</a>:</p>
<pre><code class="typescript"><span class="hl-3">import</span><span class="hl-1"> {</span><span class="hl-4">GoogleGenAI</span><span class="hl-1">} </span><span class="hl-3">from</span><span class="hl-1"> </span><span class="hl-2">&#39;@google/genai&#39;</span><span class="hl-1">;</span><br/><span class="hl-5">const</span><span class="hl-1"> </span><span class="hl-6">GEMINI_API_KEY</span><span class="hl-1"> = </span><span class="hl-4">process</span><span class="hl-1">.</span><span class="hl-4">env</span><span class="hl-1">.</span><span class="hl-6">GEMINI_API_KEY</span><span class="hl-1">;</span><br/><br/><span class="hl-5">const</span><span class="hl-1"> </span><span class="hl-6">ai</span><span class="hl-1"> = </span><span class="hl-5">new</span><span class="hl-1"> </span><span class="hl-0">GoogleGenAI</span><span class="hl-1">({</span><span class="hl-4">apiKey:</span><span class="hl-1"> </span><span class="hl-6">GEMINI_API_KEY</span><span class="hl-1">});</span><br/><br/><span class="hl-5">async</span><span class="hl-1"> </span><span class="hl-5">function</span><span class="hl-1"> </span><span class="hl-0">main</span><span class="hl-1">() {</span><br/><span class="hl-1">  </span><span class="hl-5">const</span><span class="hl-1"> </span><span class="hl-6">response</span><span class="hl-1"> = </span><span class="hl-3">await</span><span class="hl-1"> </span><span class="hl-4">ai</span><span class="hl-1">.</span><span class="hl-4">models</span><span class="hl-1">.</span><span class="hl-0">generateContent</span><span class="hl-1">({</span><br/><span class="hl-1">    </span><span class="hl-4">model:</span><span class="hl-1"> </span><span class="hl-2">&#39;gemini-2.0-flash-001&#39;</span><span class="hl-1">,</span><br/><span class="hl-1">    </span><span class="hl-4">contents:</span><span class="hl-1"> </span><span class="hl-2">&#39;Why is the sky blue?&#39;</span><span class="hl-1">,</span><br/><span class="hl-1">  });</span><br/><span class="hl-1">  </span><span class="hl-4">console</span><span class="hl-1">.</span><span class="hl-0">log</span><span class="hl-1">(</span><span class="hl-4">response</span><span class="hl-1">.</span><span class="hl-4">text</span><span class="hl-1">);</span><br/><span class="hl-1">}</span><br/><br/><span class="hl-0">main</span><span class="hl-1">();</span>
</code><button type="button">Copy</button></pre>

<a id="initialization" class="tsd-anchor"></a><h2 class="tsd-anchor-link">Initialization<a href="#initialization" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="assets/icons.svg#icon-anchor"></use></svg></a></h2><p>The Google Gen AI SDK provides support for both the
<a href="https://ai.google.dev/gemini-api/docs">Google AI Studio</a> and
<a href="https://cloud.google.com/vertex-ai/generative-ai/docs/learn/overview">Vertex AI</a>
implementations of the Gemini API.</p>
<a id="gemini-developer-api" class="tsd-anchor"></a><h3 class="tsd-anchor-link">Gemini Developer API<a href="#gemini-developer-api" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="assets/icons.svg#icon-anchor"></use></svg></a></h3><p>For server-side applications, initialize using an API key, which can
be acquired from <a href="https://aistudio.google.com/apikey">Google AI Studio</a>:</p>
<pre><code class="typescript"><span class="hl-3">import</span><span class="hl-1"> { </span><span class="hl-4">GoogleGenAI</span><span class="hl-1"> } </span><span class="hl-3">from</span><span class="hl-1"> </span><span class="hl-2">&#39;@google/genai&#39;</span><span class="hl-1">;</span><br/><span class="hl-5">const</span><span class="hl-1"> </span><span class="hl-6">ai</span><span class="hl-1"> = </span><span class="hl-5">new</span><span class="hl-1"> </span><span class="hl-0">GoogleGenAI</span><span class="hl-1">({</span><span class="hl-4">apiKey:</span><span class="hl-1"> </span><span class="hl-2">&#39;GEMINI_API_KEY&#39;</span><span class="hl-1">});</span>
</code><button type="button">Copy</button></pre>

<a id="browser" class="tsd-anchor"></a><h4 class="tsd-anchor-link">Browser<a href="#browser" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="assets/icons.svg#icon-anchor"></use></svg></a></h4><div class="tsd-alert tsd-alert-caution"><div class="tsd-alert-title"><svg width="16" height="16" viewBox="0 0 16 16" aria-hidden="true"><use href="assets/icons.svg#icon-alertCaution"></use></svg><span>Caution</span></div><p>
<strong>API Key Security:</strong> Avoid exposing API keys in client-side code.
Use server-side implementations in production environments.</p>
</div>
<p>In the browser the initialization code is identical:</p>
<pre><code class="typescript"><span class="hl-3">import</span><span class="hl-1"> { </span><span class="hl-4">GoogleGenAI</span><span class="hl-1"> } </span><span class="hl-3">from</span><span class="hl-1"> </span><span class="hl-2">&#39;@google/genai&#39;</span><span class="hl-1">;</span><br/><span class="hl-5">const</span><span class="hl-1"> </span><span class="hl-6">ai</span><span class="hl-1"> = </span><span class="hl-5">new</span><span class="hl-1"> </span><span class="hl-0">GoogleGenAI</span><span class="hl-1">({</span><span class="hl-4">apiKey:</span><span class="hl-1"> </span><span class="hl-2">&#39;GEMINI_API_KEY&#39;</span><span class="hl-1">});</span>
</code><button type="button">Copy</button></pre>

<a id="vertex-ai" class="tsd-anchor"></a><h3 class="tsd-anchor-link">Vertex AI<a href="#vertex-ai" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="assets/icons.svg#icon-anchor"></use></svg></a></h3><p>Sample code for VertexAI initialization:</p>
<pre><code class="typescript"><span class="hl-3">import</span><span class="hl-1"> { </span><span class="hl-4">GoogleGenAI</span><span class="hl-1"> } </span><span class="hl-3">from</span><span class="hl-1"> </span><span class="hl-2">&#39;@google/genai&#39;</span><span class="hl-1">;</span><br/><br/><span class="hl-5">const</span><span class="hl-1"> </span><span class="hl-6">ai</span><span class="hl-1"> = </span><span class="hl-5">new</span><span class="hl-1"> </span><span class="hl-0">GoogleGenAI</span><span class="hl-1">({</span><br/><span class="hl-1">    </span><span class="hl-4">vertexai:</span><span class="hl-1"> </span><span class="hl-5">true</span><span class="hl-1">,</span><br/><span class="hl-1">    </span><span class="hl-4">project:</span><span class="hl-1"> </span><span class="hl-2">&#39;your_project&#39;</span><span class="hl-1">,</span><br/><span class="hl-1">    </span><span class="hl-4">location:</span><span class="hl-1"> </span><span class="hl-2">&#39;your_location&#39;</span><span class="hl-1">,</span><br/><span class="hl-1">});</span>
</code><button type="button">Copy</button></pre>

<a id="optional-nodejs-only-using-environment-variables" class="tsd-anchor"></a><h3 class="tsd-anchor-link">(Optional) (NodeJS only) Using environment variables:<a href="#optional-nodejs-only-using-environment-variables" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="assets/icons.svg#icon-anchor"></use></svg></a></h3><p>For NodeJS environments, you can create a client by configuring the necessary
environment variables. Configuration setup instructions depends on whether
you're using the Gemini Developer API or the Gemini API in Vertex AI.</p>
<p><strong>Gemini Developer API:</strong> Set <code>GOOGLE_API_KEY</code> as shown below:</p>
<pre><code class="bash"><span class="hl-5">export</span><span class="hl-1"> </span><span class="hl-4">GOOGLE_API_KEY</span><span class="hl-1">=</span><span class="hl-2">&#39;your-api-key&#39;</span>
</code><button type="button">Copy</button></pre>

<p><strong>Gemini API on Vertex AI:</strong> Set <code>GOOGLE_GENAI_USE_VERTEXAI</code>,
<code>GOOGLE_CLOUD_PROJECT</code> and <code>GOOGLE_CLOUD_LOCATION</code>, as shown below:</p>
<pre><code class="bash"><span class="hl-5">export</span><span class="hl-1"> </span><span class="hl-4">GOOGLE_GENAI_USE_VERTEXAI</span><span class="hl-1">=</span><span class="hl-4">true</span><br/><span class="hl-5">export</span><span class="hl-1"> </span><span class="hl-4">GOOGLE_CLOUD_PROJECT</span><span class="hl-1">=</span><span class="hl-2">&#39;your-project-id&#39;</span><br/><span class="hl-5">export</span><span class="hl-1"> </span><span class="hl-4">GOOGLE_CLOUD_LOCATION</span><span class="hl-1">=</span><span class="hl-2">&#39;us-central1&#39;</span>
</code><button type="button">Copy</button></pre>

<pre><code class="typescript"><span class="hl-3">import</span><span class="hl-1"> {</span><span class="hl-4">GoogleGenAI</span><span class="hl-1">} </span><span class="hl-3">from</span><span class="hl-1"> </span><span class="hl-2">&#39;@google/genai&#39;</span><span class="hl-1">;</span><br/><br/><span class="hl-5">const</span><span class="hl-1"> </span><span class="hl-6">ai</span><span class="hl-1"> = </span><span class="hl-5">new</span><span class="hl-1"> </span><span class="hl-0">GoogleGenAI</span><span class="hl-1">();</span>
</code><button type="button">Copy</button></pre>

<a id="api-selection" class="tsd-anchor"></a><h2 class="tsd-anchor-link">API Selection<a href="#api-selection" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="assets/icons.svg#icon-anchor"></use></svg></a></h2><p>By default, the SDK uses the beta API endpoints provided by Google to support
preview features in the APIs. The stable API endpoints can be selected by
setting the API version to <code>v1</code>.</p>
<p>To set the API version use <code>apiVersion</code>. For example, to set the API version to
<code>v1</code> for Vertex AI:</p>
<pre><code class="typescript"><span class="hl-5">const</span><span class="hl-1"> </span><span class="hl-6">ai</span><span class="hl-1"> = </span><span class="hl-5">new</span><span class="hl-1"> </span><span class="hl-0">GoogleGenAI</span><span class="hl-1">({</span><br/><span class="hl-1">    </span><span class="hl-4">vertexai:</span><span class="hl-1"> </span><span class="hl-5">true</span><span class="hl-1">,</span><br/><span class="hl-1">    </span><span class="hl-4">project:</span><span class="hl-1"> </span><span class="hl-2">&#39;your_project&#39;</span><span class="hl-1">,</span><br/><span class="hl-1">    </span><span class="hl-4">location:</span><span class="hl-1"> </span><span class="hl-2">&#39;your_location&#39;</span><span class="hl-1">,</span><br/><span class="hl-1">    </span><span class="hl-4">apiVersion:</span><span class="hl-1"> </span><span class="hl-2">&#39;v1&#39;</span><br/><span class="hl-1">});</span>
</code><button type="button">Copy</button></pre>

<p>To set the API version to <code>v1alpha</code> for the Gemini Developer API:</p>
<pre><code class="typescript"><span class="hl-5">const</span><span class="hl-1"> </span><span class="hl-6">ai</span><span class="hl-1"> = </span><span class="hl-5">new</span><span class="hl-1"> </span><span class="hl-0">GoogleGenAI</span><span class="hl-1">({</span><br/><span class="hl-1">    </span><span class="hl-4">apiKey:</span><span class="hl-1"> </span><span class="hl-2">&#39;GEMINI_API_KEY&#39;</span><span class="hl-1">,</span><br/><span class="hl-1">    </span><span class="hl-4">apiVersion:</span><span class="hl-1"> </span><span class="hl-2">&#39;v1alpha&#39;</span><br/><span class="hl-1">});</span>
</code><button type="button">Copy</button></pre>

<a id="googlegenai-overview" class="tsd-anchor"></a><h2 class="tsd-anchor-link">GoogleGenAI overview<a href="#googlegenai-overview" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="assets/icons.svg#icon-anchor"></use></svg></a></h2><p>All API features are accessed through an instance of the <code>GoogleGenAI</code> classes.
The submodules bundle together related API methods:</p>
<ul>
<li><a href="https://googleapis.github.io/js-genai/release_docs/classes/models.Models.html"><code>ai.models</code></a>:
Use <code>models</code> to query models (<code>generateContent</code>, <code>generateImages</code>, ...), or
examine their metadata.</li>
<li><a href="https://googleapis.github.io/js-genai/release_docs/classes/caches.Caches.html"><code>ai.caches</code></a>:
Create and manage <code>caches</code> to reduce costs when repeatedly using the same
large prompt prefix.</li>
<li><a href="https://googleapis.github.io/js-genai/release_docs/classes/chats.Chats.html"><code>ai.chats</code></a>:
Create local stateful <code>chat</code> objects to simplify multi turn interactions.</li>
<li><a href="https://googleapis.github.io/js-genai/release_docs/classes/files.Files.html"><code>ai.files</code></a>:
Upload <code>files</code> to the API and reference them in your prompts.
This reduces bandwidth if you use a file many times, and handles files too
large to fit inline with your prompt.</li>
<li><a href="https://googleapis.github.io/js-genai/release_docs/classes/live.Live.html"><code>ai.live</code></a>:
Start a <code>live</code> session for real time interaction, allows text + audio + video
input, and text or audio output.</li>
</ul>
<a id="samples" class="tsd-anchor"></a><h2 class="tsd-anchor-link">Samples<a href="#samples" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="assets/icons.svg#icon-anchor"></use></svg></a></h2><p>More samples can be found in the
<a href="https://github.com/googleapis/js-genai/tree/main/sdk-samples">github samples directory</a>.</p>
<a id="streaming" class="tsd-anchor"></a><h3 class="tsd-anchor-link">Streaming<a href="#streaming" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="assets/icons.svg#icon-anchor"></use></svg></a></h3><p>For quicker, more responsive API interactions use the <code>generateContentStream</code>
method which yields chunks as they're generated:</p>
<pre><code class="typescript"><span class="hl-3">import</span><span class="hl-1"> {</span><span class="hl-4">GoogleGenAI</span><span class="hl-1">} </span><span class="hl-3">from</span><span class="hl-1"> </span><span class="hl-2">&#39;@google/genai&#39;</span><span class="hl-1">;</span><br/><span class="hl-5">const</span><span class="hl-1"> </span><span class="hl-6">GEMINI_API_KEY</span><span class="hl-1"> = </span><span class="hl-4">process</span><span class="hl-1">.</span><span class="hl-4">env</span><span class="hl-1">.</span><span class="hl-6">GEMINI_API_KEY</span><span class="hl-1">;</span><br/><br/><span class="hl-5">const</span><span class="hl-1"> </span><span class="hl-6">ai</span><span class="hl-1"> = </span><span class="hl-5">new</span><span class="hl-1"> </span><span class="hl-0">GoogleGenAI</span><span class="hl-1">({</span><span class="hl-4">apiKey:</span><span class="hl-1"> </span><span class="hl-6">GEMINI_API_KEY</span><span class="hl-1">});</span><br/><br/><span class="hl-5">async</span><span class="hl-1"> </span><span class="hl-5">function</span><span class="hl-1"> </span><span class="hl-0">main</span><span class="hl-1">() {</span><br/><span class="hl-1">  </span><span class="hl-5">const</span><span class="hl-1"> </span><span class="hl-6">response</span><span class="hl-1"> = </span><span class="hl-3">await</span><span class="hl-1"> </span><span class="hl-4">ai</span><span class="hl-1">.</span><span class="hl-4">models</span><span class="hl-1">.</span><span class="hl-0">generateContentStream</span><span class="hl-1">({</span><br/><span class="hl-1">    </span><span class="hl-4">model:</span><span class="hl-1"> </span><span class="hl-2">&#39;gemini-2.0-flash-001&#39;</span><span class="hl-1">,</span><br/><span class="hl-1">    </span><span class="hl-4">contents:</span><span class="hl-1"> </span><span class="hl-2">&#39;Write a 100-word poem.&#39;</span><span class="hl-1">,</span><br/><span class="hl-1">  });</span><br/><span class="hl-1">  </span><span class="hl-3">for</span><span class="hl-1"> </span><span class="hl-3">await</span><span class="hl-1"> (</span><span class="hl-5">const</span><span class="hl-1"> </span><span class="hl-6">chunk</span><span class="hl-1"> </span><span class="hl-5">of</span><span class="hl-1"> </span><span class="hl-4">response</span><span class="hl-1">) {</span><br/><span class="hl-1">    </span><span class="hl-4">console</span><span class="hl-1">.</span><span class="hl-0">log</span><span class="hl-1">(</span><span class="hl-4">chunk</span><span class="hl-1">.</span><span class="hl-4">text</span><span class="hl-1">);</span><br/><span class="hl-1">  }</span><br/><span class="hl-1">}</span><br/><br/><span class="hl-0">main</span><span class="hl-1">();</span>
</code><button type="button">Copy</button></pre>

<a id="function-calling" class="tsd-anchor"></a><h3 class="tsd-anchor-link">Function Calling<a href="#function-calling" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="assets/icons.svg#icon-anchor"></use></svg></a></h3><p>To let Gemini to interact with external systems, you can provide
<code>functionDeclaration</code> objects as <code>tools</code>. To use these tools it's a 4 step</p>
<ol>
<li><strong>Declare the function name, description, and parametersJsonSchema</strong></li>
<li><strong>Call <code>generateContent</code> with function calling enabled</strong></li>
<li><strong>Use the returned <code>FunctionCall</code> parameters to call your actual function</strong></li>
<li><strong>Send the result back to the model (with history, easier in <code>ai.chat</code>)
as a <code>FunctionResponse</code></strong></li>
</ol>
<pre><code class="typescript"><span class="hl-3">import</span><span class="hl-1"> {</span><span class="hl-4">GoogleGenAI</span><span class="hl-1">, </span><span class="hl-4">FunctionCallingConfigMode</span><span class="hl-1">, </span><span class="hl-4">FunctionDeclaration</span><span class="hl-1">, </span><span class="hl-4">Type</span><span class="hl-1">} </span><span class="hl-3">from</span><span class="hl-1"> </span><span class="hl-2">&#39;@google/genai&#39;</span><span class="hl-1">;</span><br/><span class="hl-5">const</span><span class="hl-1"> </span><span class="hl-6">GEMINI_API_KEY</span><span class="hl-1"> = </span><span class="hl-4">process</span><span class="hl-1">.</span><span class="hl-4">env</span><span class="hl-1">.</span><span class="hl-6">GEMINI_API_KEY</span><span class="hl-1">;</span><br/><br/><span class="hl-5">async</span><span class="hl-1"> </span><span class="hl-5">function</span><span class="hl-1"> </span><span class="hl-0">main</span><span class="hl-1">() {</span><br/><span class="hl-1">  </span><span class="hl-5">const</span><span class="hl-1"> </span><span class="hl-6">controlLightDeclaration</span><span class="hl-1">: </span><span class="hl-7">FunctionDeclaration</span><span class="hl-1"> = {</span><br/><span class="hl-1">    </span><span class="hl-4">name:</span><span class="hl-1"> </span><span class="hl-2">&#39;controlLight&#39;</span><span class="hl-1">,</span><br/><span class="hl-1">    </span><span class="hl-4">parametersJsonSchema:</span><span class="hl-1"> {</span><br/><span class="hl-1">      </span><span class="hl-4">type:</span><span class="hl-1"> </span><span class="hl-2">&#39;object&#39;</span><span class="hl-1">,</span><br/><span class="hl-1">      </span><span class="hl-4">properties:</span><span class="hl-1">{</span><br/><span class="hl-1">        </span><span class="hl-4">brightness:</span><span class="hl-1"> {</span><br/><span class="hl-1">          </span><span class="hl-4">type:</span><span class="hl-2">&#39;number&#39;</span><span class="hl-1">,</span><br/><span class="hl-1">        },</span><br/><span class="hl-1">        </span><span class="hl-4">colorTemperature:</span><span class="hl-1"> {</span><br/><span class="hl-1">          </span><span class="hl-4">type:</span><span class="hl-2">&#39;string&#39;</span><span class="hl-1">,</span><br/><span class="hl-1">        },</span><br/><span class="hl-1">      },</span><br/><span class="hl-1">      </span><span class="hl-4">required:</span><span class="hl-1"> [</span><span class="hl-2">&#39;brightness&#39;</span><span class="hl-1">, </span><span class="hl-2">&#39;colorTemperature&#39;</span><span class="hl-1">],</span><br/><span class="hl-1">    },</span><br/><span class="hl-1">  };</span><br/><br/><span class="hl-1">  </span><span class="hl-5">const</span><span class="hl-1"> </span><span class="hl-6">ai</span><span class="hl-1"> = </span><span class="hl-5">new</span><span class="hl-1"> </span><span class="hl-0">GoogleGenAI</span><span class="hl-1">({</span><span class="hl-4">apiKey:</span><span class="hl-1"> </span><span class="hl-6">GEMINI_API_KEY</span><span class="hl-1">});</span><br/><span class="hl-1">  </span><span class="hl-5">const</span><span class="hl-1"> </span><span class="hl-6">response</span><span class="hl-1"> = </span><span class="hl-3">await</span><span class="hl-1"> </span><span class="hl-4">ai</span><span class="hl-1">.</span><span class="hl-4">models</span><span class="hl-1">.</span><span class="hl-0">generateContent</span><span class="hl-1">({</span><br/><span class="hl-1">    </span><span class="hl-4">model:</span><span class="hl-1"> </span><span class="hl-2">&#39;gemini-2.0-flash-001&#39;</span><span class="hl-1">,</span><br/><span class="hl-1">    </span><span class="hl-4">contents:</span><span class="hl-1"> </span><span class="hl-2">&#39;Dim the lights so the room feels cozy and warm.&#39;</span><span class="hl-1">,</span><br/><span class="hl-1">    </span><span class="hl-4">config:</span><span class="hl-1"> {</span><br/><span class="hl-1">      </span><span class="hl-4">toolConfig:</span><span class="hl-1"> {</span><br/><span class="hl-1">        </span><span class="hl-4">functionCallingConfig:</span><span class="hl-1"> {</span><br/><span class="hl-1">          </span><span class="hl-8">// Force it to call any function</span><br/><span class="hl-1">          </span><span class="hl-4">mode:</span><span class="hl-1"> </span><span class="hl-4">FunctionCallingConfigMode</span><span class="hl-1">.</span><span class="hl-6">ANY</span><span class="hl-1">,</span><br/><span class="hl-1">          </span><span class="hl-4">allowedFunctionNames:</span><span class="hl-1"> [</span><span class="hl-2">&#39;controlLight&#39;</span><span class="hl-1">],</span><br/><span class="hl-1">        }</span><br/><span class="hl-1">      },</span><br/><span class="hl-1">      </span><span class="hl-4">tools:</span><span class="hl-1"> [{</span><span class="hl-4">functionDeclarations:</span><span class="hl-1"> [</span><span class="hl-4">controlLightDeclaration</span><span class="hl-1">]}]</span><br/><span class="hl-1">    }</span><br/><span class="hl-1">  });</span><br/><br/><span class="hl-1">  </span><span class="hl-4">console</span><span class="hl-1">.</span><span class="hl-0">log</span><span class="hl-1">(</span><span class="hl-4">response</span><span class="hl-1">.</span><span class="hl-4">functionCalls</span><span class="hl-1">);</span><br/><span class="hl-1">}</span><br/><br/><span class="hl-0">main</span><span class="hl-1">();</span>
</code><button type="button">Copy</button></pre>

<a id="model-context-protocol-mcp-support-experimental" class="tsd-anchor"></a><h4 class="tsd-anchor-link">Model Context Protocol (MCP) support (experimental)<a href="#model-context-protocol-mcp-support-experimental" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="assets/icons.svg#icon-anchor"></use></svg></a></h4><p>Built-in <a href="https://modelcontextprotocol.io/introduction">MCP</a> support is an
experimental feature. You can pass a local MCP server as a tool directly.</p>
<pre><code class="javascript"><span class="hl-3">import</span><span class="hl-1"> { </span><span class="hl-4">GoogleGenAI</span><span class="hl-1">, </span><span class="hl-4">FunctionCallingConfigMode</span><span class="hl-1"> , </span><span class="hl-4">mcpToTool</span><span class="hl-1">} </span><span class="hl-3">from</span><span class="hl-1"> </span><span class="hl-2">&#39;@google/genai&#39;</span><span class="hl-1">;</span><br/><span class="hl-3">import</span><span class="hl-1"> { </span><span class="hl-4">Client</span><span class="hl-1"> } </span><span class="hl-3">from</span><span class="hl-1"> </span><span class="hl-2">&quot;@modelcontextprotocol/sdk/client/index.js&quot;</span><span class="hl-1">;</span><br/><span class="hl-3">import</span><span class="hl-1"> { </span><span class="hl-4">StdioClientTransport</span><span class="hl-1"> } </span><span class="hl-3">from</span><span class="hl-1"> </span><span class="hl-2">&quot;@modelcontextprotocol/sdk/client/stdio.js&quot;</span><span class="hl-1">;</span><br/><br/><span class="hl-8">// Create server parameters for stdio connection</span><br/><span class="hl-5">const</span><span class="hl-1"> </span><span class="hl-6">serverParams</span><span class="hl-1"> = </span><span class="hl-5">new</span><span class="hl-1"> </span><span class="hl-0">StdioClientTransport</span><span class="hl-1">({</span><br/><span class="hl-1">  </span><span class="hl-4">command:</span><span class="hl-1"> </span><span class="hl-2">&quot;npx&quot;</span><span class="hl-1">, </span><span class="hl-8">// Executable</span><br/><span class="hl-1">  </span><span class="hl-4">args:</span><span class="hl-1"> [</span><span class="hl-2">&quot;-y&quot;</span><span class="hl-1">, </span><span class="hl-2">&quot;@philschmid/weather-mcp&quot;</span><span class="hl-1">] </span><span class="hl-8">// MCP Server</span><br/><span class="hl-1">});</span><br/><br/><span class="hl-5">const</span><span class="hl-1"> </span><span class="hl-6">client</span><span class="hl-1"> = </span><span class="hl-5">new</span><span class="hl-1"> </span><span class="hl-0">Client</span><span class="hl-1">(</span><br/><span class="hl-1">  {</span><br/><span class="hl-1">    </span><span class="hl-4">name:</span><span class="hl-1"> </span><span class="hl-2">&quot;example-client&quot;</span><span class="hl-1">,</span><br/><span class="hl-1">    </span><span class="hl-4">version:</span><span class="hl-1"> </span><span class="hl-2">&quot;1.0.0&quot;</span><br/><span class="hl-1">  }</span><br/><span class="hl-1">);</span><br/><br/><span class="hl-8">// Configure the client</span><br/><span class="hl-5">const</span><span class="hl-1"> </span><span class="hl-6">ai</span><span class="hl-1"> = </span><span class="hl-5">new</span><span class="hl-1"> </span><span class="hl-0">GoogleGenAI</span><span class="hl-1">({});</span><br/><br/><span class="hl-8">// Initialize the connection between client and server</span><br/><span class="hl-3">await</span><span class="hl-1"> </span><span class="hl-4">client</span><span class="hl-1">.</span><span class="hl-0">connect</span><span class="hl-1">(</span><span class="hl-4">serverParams</span><span class="hl-1">);</span><br/><br/><span class="hl-8">// Send request to the model with MCP tools</span><br/><span class="hl-5">const</span><span class="hl-1"> </span><span class="hl-6">response</span><span class="hl-1"> = </span><span class="hl-3">await</span><span class="hl-1"> </span><span class="hl-4">ai</span><span class="hl-1">.</span><span class="hl-4">models</span><span class="hl-1">.</span><span class="hl-0">generateContent</span><span class="hl-1">({</span><br/><span class="hl-1">  </span><span class="hl-4">model:</span><span class="hl-1"> </span><span class="hl-2">&quot;gemini-2.5-flash&quot;</span><span class="hl-1">,</span><br/><span class="hl-1">  </span><span class="hl-4">contents:</span><span class="hl-1"> </span><span class="hl-2">`What is the weather in London in </span><span class="hl-5">${</span><span class="hl-5">new</span><span class="hl-9"> </span><span class="hl-0">Date</span><span class="hl-9">().</span><span class="hl-0">toLocaleDateString</span><span class="hl-9">()</span><span class="hl-5">}</span><span class="hl-2">?`</span><span class="hl-1">,</span><br/><span class="hl-1">  </span><span class="hl-4">config:</span><span class="hl-1"> {</span><br/><span class="hl-1">    </span><span class="hl-4">tools:</span><span class="hl-1"> [</span><span class="hl-0">mcpToTool</span><span class="hl-1">(</span><span class="hl-4">client</span><span class="hl-1">)],  </span><span class="hl-8">// uses the session, will automatically call the tool using automatic function calling</span><br/><span class="hl-1">  },</span><br/><span class="hl-1">});</span><br/><span class="hl-4">console</span><span class="hl-1">.</span><span class="hl-0">log</span><span class="hl-1">(</span><span class="hl-4">response</span><span class="hl-1">.</span><span class="hl-4">text</span><span class="hl-1">);</span><br/><br/><span class="hl-8">// Close the connection</span><br/><span class="hl-3">await</span><span class="hl-1"> </span><span class="hl-4">client</span><span class="hl-1">.</span><span class="hl-0">close</span><span class="hl-1">();</span>
</code><button type="button">Copy</button></pre>

<a id="generate-content" class="tsd-anchor"></a><h3 class="tsd-anchor-link">Generate Content<a href="#generate-content" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="assets/icons.svg#icon-anchor"></use></svg></a></h3><a id="how-to-structure-contents-argument-for-generatecontent" class="tsd-anchor"></a><h4 class="tsd-anchor-link">How to structure <code>contents</code> argument for <code>generateContent</code><a href="#how-to-structure-contents-argument-for-generatecontent" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="assets/icons.svg#icon-anchor"></use></svg></a></h4><p>The SDK allows you to specify the following types in the <code>contents</code> parameter:</p>
<a id="content" class="tsd-anchor"></a><h4 class="tsd-anchor-link">Content<a href="#content" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="assets/icons.svg#icon-anchor"></use></svg></a></h4><ul>
<li><code>Content</code>: The SDK will wrap the singular <code>Content</code> instance in an array which
contains only the given content instance</li>
<li><code>Content[]</code>: No transformation happens</li>
</ul>
<a id="part" class="tsd-anchor"></a><h4 class="tsd-anchor-link">Part<a href="#part" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="assets/icons.svg#icon-anchor"></use></svg></a></h4><p>Parts will be aggregated on a singular Content, with role 'user'.</p>
<ul>
<li><code>Part | string</code>: The SDK will wrap the <code>string</code> or <code>Part</code> in a <code>Content</code>
instance with role 'user'.</li>
<li><code>Part[] | string[]</code>: The SDK will wrap the full provided list into a single
<code>Content</code> with role 'user'.</li>
</ul>
<p><strong><em>NOTE:</em></strong> This doesn't apply to <code>FunctionCall</code> and <code>FunctionResponse</code> parts,
if you are specifying those, you need to explicitly provide the full
<code>Content[]</code> structure making it explicit which Parts are 'spoken' by the model,
or the user. The SDK will throw an exception if you try this.</p>
<a id="how-is-this-different-from-the-other-google-ai-sdks" class="tsd-anchor"></a><h2 class="tsd-anchor-link">How is this different from the other Google AI SDKs<a href="#how-is-this-different-from-the-other-google-ai-sdks" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="assets/icons.svg#icon-anchor"></use></svg></a></h2><p>This SDK (<code>@google/genai</code>) is Google Deepmind’s &quot;vanilla&quot; SDK for its generative
AI offerings, and is where Google Deepmind adds new AI features.</p>
<p>Models hosted either on the <a href="https://cloud.google.com/vertex-ai/generative-ai/docs/learn/overview">Vertex AI platform</a> or the <a href="https://ai.google.dev/gemini-api/docs">Gemini Developer platform</a> are accessible through this SDK.</p>
<p>Other SDKs may be offering additional AI frameworks on top of this SDK, or may
be targeting specific project environments (like Firebase).</p>
<p>The <code>@google/generative_language</code> and <code>@google-cloud/vertexai</code> SDKs are previous
iterations of this SDK and are no longer receiving new Gemini 2.0+ features.</p>
</div></div><div class="col-sidebar"><div class="page-menu"><div class="tsd-navigation settings"><details class="tsd-accordion"><summary class="tsd-accordion-summary"><h3><svg width="20" height="20" viewBox="0 0 24 24" fill="none" aria-hidden="true"><use href="assets/icons.svg#icon-chevronDown"></use></svg>Settings</h3></summary><div class="tsd-accordion-details"><div class="tsd-theme-toggle"><label class="settings-label" for="tsd-theme">Theme</label><select id="tsd-theme"><option value="os">OS</option><option value="light">Light</option><option value="dark">Dark</option></select></div></div></details></div><details open class="tsd-accordion tsd-page-navigation"><summary class="tsd-accordion-summary"><h3><svg width="20" height="20" viewBox="0 0 24 24" fill="none" aria-hidden="true"><use href="assets/icons.svg#icon-chevronDown"></use></svg>On This Page</h3></summary><div class="tsd-accordion-details"><a href="#google-gen-ai-sdk-for-typescript-and-javascript"><span>Google <wbr/>Gen AI SDK for <wbr/>Type<wbr/>Script and <wbr/>Java<wbr/>Script</span></a><ul><li><a href="#prerequisites"><span>Prerequisites</span></a></li><li><ul><li><a href="#the-following-are-required-for-vertex-ai-users-excluding-vertex-ai-studio"><span>The following are required for <wbr/>Vertex AI users (excluding <wbr/>Vertex AI <wbr/>Studio)</span></a></li></ul></li><li><a href="#installation"><span>Installation</span></a></li><li><a href="#quickstart"><span>Quickstart</span></a></li><li><a href="#initialization"><span>Initialization</span></a></li><li><ul><li><a href="#gemini-developer-api"><span>Gemini <wbr/>Developer API</span></a></li><li><ul><li><a href="#browser"><span>Browser</span></a></li></ul></li><li><a href="#vertex-ai"><span>Vertex AI</span></a></li><li><a href="#optional-nodejs-only-using-environment-variables"><span>(<wbr/>Optional) (<wbr/>NodeJS only) <wbr/>Using environment variables:</span></a></li></ul></li><li><a href="#api-selection"><span>API <wbr/>Selection</span></a></li><li><a href="#googlegenai-overview"><span>Google<wbr/>GenAI overview</span></a></li><li><a href="#samples"><span>Samples</span></a></li><li><ul><li><a href="#streaming"><span>Streaming</span></a></li><li><a href="#function-calling"><span>Function <wbr/>Calling</span></a></li><li><ul><li><a href="#model-context-protocol-mcp-support-experimental"><span>Model <wbr/>Context <wbr/>Protocol (MCP) support (experimental)</span></a></li></ul></li><li><a href="#generate-content"><span>Generate <wbr/>Content</span></a></li><li><ul><li><a href="#how-to-structure-contents-argument-for-generatecontent"><span>How to structure contents argument for generate<wbr/>Content</span></a></li><li><a href="#content"><span>Content</span></a></li><li><a href="#part"><span>Part</span></a></li></ul></li></ul></li><li><a href="#how-is-this-different-from-the-other-google-ai-sdks"><span>How is this different from the other <wbr/>Google AI SDKs</span></a></li></ul></div></details></div><div class="site-menu"><nav class="tsd-navigation"><a href="modules.html">@google/genai</a><ul class="tsd-small-nested-navigation" id="tsd-nav-container"><li>Loading...</li></ul></nav></div></div></div><footer><p class="tsd-generator">Generated using <a href="https://typedoc.org/" target="_blank">TypeDoc</a></p></footer><div class="overlay"></div></body></html>
