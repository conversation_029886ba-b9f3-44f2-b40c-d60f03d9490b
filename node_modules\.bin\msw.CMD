@SETLOCAL
@IF NOT DEFINED NODE_PATH (
  @SET "NODE_PATH=D:\ssh\learn\project-graph-origin\node_modules\.pnpm\msw@2.7.3_@types+node@24.1.0_typescript@5.8.3\node_modules\msw\cli\node_modules;D:\ssh\learn\project-graph-origin\node_modules\.pnpm\msw@2.7.3_@types+node@24.1.0_typescript@5.8.3\node_modules\msw\node_modules;D:\ssh\learn\project-graph-origin\node_modules\.pnpm\msw@2.7.3_@types+node@24.1.0_typescript@5.8.3\node_modules;D:\ssh\learn\project-graph-origin\node_modules\.pnpm\node_modules"
) ELSE (
  @SET "NODE_PATH=D:\ssh\learn\project-graph-origin\node_modules\.pnpm\msw@2.7.3_@types+node@24.1.0_typescript@5.8.3\node_modules\msw\cli\node_modules;D:\ssh\learn\project-graph-origin\node_modules\.pnpm\msw@2.7.3_@types+node@24.1.0_typescript@5.8.3\node_modules\msw\node_modules;D:\ssh\learn\project-graph-origin\node_modules\.pnpm\msw@2.7.3_@types+node@24.1.0_typescript@5.8.3\node_modules;D:\ssh\learn\project-graph-origin\node_modules\.pnpm\node_modules;%NODE_PATH%"
)
@IF EXIST "%~dp0\node.exe" (
  "%~dp0\node.exe"  "%~dp0\..\..\..\node_modules\.pnpm\msw@2.7.3_@types+node@24.1.0_typescript@5.8.3\node_modules\msw\cli\index.js" %*
) ELSE (
  @SET PATHEXT=%PATHEXT:;.JS;=;%
  node  "%~dp0\..\..\..\node_modules\.pnpm\msw@2.7.3_@types+node@24.1.0_typescript@5.8.3\node_modules\msw\cli\index.js" %*
)
