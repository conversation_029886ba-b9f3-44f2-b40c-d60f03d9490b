{"version": 3, "sources": ["../../../../node_modules/.pnpm/react-router@7.7.1_react-do_a895d082fdef8e02def914d2d9dfb881/node_modules/react-router/dist/development/dom-export.mjs"], "sourcesContent": ["/**\n * react-router v7.7.1\n *\n * Copyright (c) Remix Software Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE.md file in the root directory of this source tree.\n *\n * @license MIT\n */\n\"use client\";\nimport {\n  deserializeErrors,\n  getHydrationData\n} from \"./chunk-KIUJAIYX.mjs\";\nimport {\n  CRITICAL_CSS_DATA_ATTRIBUTE,\n  FrameworkContext,\n  RemixErrorBoundary,\n  RouterProvider,\n  createBrowserHistory,\n  createClientRoutes,\n  createClientRoutesWithHMRRevalidationOptOut,\n  createRouter,\n  decodeViaTurboStream,\n  getPatchRoutesOnNavigationFunction,\n  getTurboStreamSingleFetchDataStrategy,\n  hydrationRouteProperties,\n  invariant,\n  mapRouteProperties,\n  useFogOFWarDiscovery\n} from \"./chunk-C37GKA54.mjs\";\n\n// lib/dom-export/dom-router-provider.tsx\nimport * as React from \"react\";\nimport * as ReactDOM from \"react-dom\";\nfunction RouterProvider2(props) {\n  return /* @__PURE__ */ React.createElement(RouterProvider, { flushSync: ReactDOM.flushSync, ...props });\n}\n\n// lib/dom-export/hydrated-router.tsx\nimport * as React2 from \"react\";\nvar ssrInfo = null;\nvar router = null;\nfunction initSsrInfo() {\n  if (!ssrInfo && window.__reactRouterContext && window.__reactRouterManifest && window.__reactRouterRouteModules) {\n    if (window.__reactRouterManifest.sri === true) {\n      const importMap = document.querySelector(\"script[rr-importmap]\");\n      if (importMap?.textContent) {\n        try {\n          window.__reactRouterManifest.sri = JSON.parse(\n            importMap.textContent\n          ).integrity;\n        } catch (err) {\n          console.error(\"Failed to parse import map\", err);\n        }\n      }\n    }\n    ssrInfo = {\n      context: window.__reactRouterContext,\n      manifest: window.__reactRouterManifest,\n      routeModules: window.__reactRouterRouteModules,\n      stateDecodingPromise: void 0,\n      router: void 0,\n      routerInitialized: false\n    };\n  }\n}\nfunction createHydratedRouter({\n  unstable_getContext\n}) {\n  initSsrInfo();\n  if (!ssrInfo) {\n    throw new Error(\n      \"You must be using the SSR features of React Router in order to skip passing a `router` prop to `<RouterProvider>`\"\n    );\n  }\n  let localSsrInfo = ssrInfo;\n  if (!ssrInfo.stateDecodingPromise) {\n    let stream = ssrInfo.context.stream;\n    invariant(stream, \"No stream found for single fetch decoding\");\n    ssrInfo.context.stream = void 0;\n    ssrInfo.stateDecodingPromise = decodeViaTurboStream(stream, window).then((value) => {\n      ssrInfo.context.state = value.value;\n      localSsrInfo.stateDecodingPromise.value = true;\n    }).catch((e) => {\n      localSsrInfo.stateDecodingPromise.error = e;\n    });\n  }\n  if (ssrInfo.stateDecodingPromise.error) {\n    throw ssrInfo.stateDecodingPromise.error;\n  }\n  if (!ssrInfo.stateDecodingPromise.value) {\n    throw ssrInfo.stateDecodingPromise;\n  }\n  let routes = createClientRoutes(\n    ssrInfo.manifest.routes,\n    ssrInfo.routeModules,\n    ssrInfo.context.state,\n    ssrInfo.context.ssr,\n    ssrInfo.context.isSpaMode\n  );\n  let hydrationData = void 0;\n  if (ssrInfo.context.isSpaMode) {\n    let { loaderData } = ssrInfo.context.state;\n    if (ssrInfo.manifest.routes.root?.hasLoader && loaderData && \"root\" in loaderData) {\n      hydrationData = {\n        loaderData: {\n          root: loaderData.root\n        }\n      };\n    }\n  } else {\n    hydrationData = getHydrationData(\n      ssrInfo.context.state,\n      routes,\n      (routeId) => ({\n        clientLoader: ssrInfo.routeModules[routeId]?.clientLoader,\n        hasLoader: ssrInfo.manifest.routes[routeId]?.hasLoader === true,\n        hasHydrateFallback: ssrInfo.routeModules[routeId]?.HydrateFallback != null\n      }),\n      window.location,\n      window.__reactRouterContext?.basename,\n      ssrInfo.context.isSpaMode\n    );\n    if (hydrationData && hydrationData.errors) {\n      hydrationData.errors = deserializeErrors(hydrationData.errors);\n    }\n  }\n  let router2 = createRouter({\n    routes,\n    history: createBrowserHistory(),\n    basename: ssrInfo.context.basename,\n    unstable_getContext,\n    hydrationData,\n    hydrationRouteProperties,\n    mapRouteProperties,\n    future: {\n      unstable_middleware: ssrInfo.context.future.unstable_middleware\n    },\n    dataStrategy: getTurboStreamSingleFetchDataStrategy(\n      () => router2,\n      ssrInfo.manifest,\n      ssrInfo.routeModules,\n      ssrInfo.context.ssr,\n      ssrInfo.context.basename\n    ),\n    patchRoutesOnNavigation: getPatchRoutesOnNavigationFunction(\n      ssrInfo.manifest,\n      ssrInfo.routeModules,\n      ssrInfo.context.ssr,\n      ssrInfo.context.routeDiscovery,\n      ssrInfo.context.isSpaMode,\n      ssrInfo.context.basename\n    )\n  });\n  ssrInfo.router = router2;\n  if (router2.state.initialized) {\n    ssrInfo.routerInitialized = true;\n    router2.initialize();\n  }\n  router2.createRoutesForHMR = /* spacer so ts-ignore does not affect the right hand of the assignment */\n  createClientRoutesWithHMRRevalidationOptOut;\n  window.__reactRouterDataRouter = router2;\n  return router2;\n}\nfunction HydratedRouter(props) {\n  if (!router) {\n    router = createHydratedRouter({\n      unstable_getContext: props.unstable_getContext\n    });\n  }\n  let [criticalCss, setCriticalCss] = React2.useState(\n    process.env.NODE_ENV === \"development\" ? ssrInfo?.context.criticalCss : void 0\n  );\n  React2.useEffect(() => {\n    if (process.env.NODE_ENV === \"development\") {\n      setCriticalCss(void 0);\n    }\n  }, []);\n  React2.useEffect(() => {\n    if (process.env.NODE_ENV === \"development\" && criticalCss === void 0) {\n      document.querySelectorAll(`[${CRITICAL_CSS_DATA_ATTRIBUTE}]`).forEach((element) => element.remove());\n    }\n  }, [criticalCss]);\n  let [location, setLocation] = React2.useState(router.state.location);\n  React2.useLayoutEffect(() => {\n    if (ssrInfo && ssrInfo.router && !ssrInfo.routerInitialized) {\n      ssrInfo.routerInitialized = true;\n      ssrInfo.router.initialize();\n    }\n  }, []);\n  React2.useLayoutEffect(() => {\n    if (ssrInfo && ssrInfo.router) {\n      return ssrInfo.router.subscribe((newState) => {\n        if (newState.location !== location) {\n          setLocation(newState.location);\n        }\n      });\n    }\n  }, [location]);\n  invariant(ssrInfo, \"ssrInfo unavailable for HydratedRouter\");\n  useFogOFWarDiscovery(\n    router,\n    ssrInfo.manifest,\n    ssrInfo.routeModules,\n    ssrInfo.context.ssr,\n    ssrInfo.context.routeDiscovery,\n    ssrInfo.context.isSpaMode\n  );\n  return (\n    // This fragment is important to ensure we match the <ServerRouter> JSX\n    // structure so that useId values hydrate correctly\n    /* @__PURE__ */ React2.createElement(React2.Fragment, null, /* @__PURE__ */ React2.createElement(\n      FrameworkContext.Provider,\n      {\n        value: {\n          manifest: ssrInfo.manifest,\n          routeModules: ssrInfo.routeModules,\n          future: ssrInfo.context.future,\n          criticalCss,\n          ssr: ssrInfo.context.ssr,\n          isSpaMode: ssrInfo.context.isSpaMode,\n          routeDiscovery: ssrInfo.context.routeDiscovery\n        }\n      },\n      /* @__PURE__ */ React2.createElement(RemixErrorBoundary, { location }, /* @__PURE__ */ React2.createElement(RouterProvider2, { router }))\n    ), /* @__PURE__ */ React2.createElement(React2.Fragment, null))\n  );\n}\nexport {\n  HydratedRouter,\n  RouterProvider2 as RouterProvider\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkCA,YAAuB;AACvB,eAA0B;AAM1B,aAAwB;AALxB,SAAS,gBAAgB,OAAO;AAC9B,SAA6B,oBAAc,gBAAgB,EAAE,WAAoB,oBAAW,GAAG,MAAM,CAAC;AACxG;AAIA,IAAI,UAAU;AACd,IAAI,SAAS;AACb,SAAS,cAAc;AACrB,MAAI,CAAC,WAAW,OAAO,wBAAwB,OAAO,yBAAyB,OAAO,2BAA2B;AAC/G,QAAI,OAAO,sBAAsB,QAAQ,MAAM;AAC7C,YAAM,YAAY,SAAS,cAAc,sBAAsB;AAC/D,UAAI,uCAAW,aAAa;AAC1B,YAAI;AACF,iBAAO,sBAAsB,MAAM,KAAK;AAAA,YACtC,UAAU;AAAA,UACZ,EAAE;AAAA,QACJ,SAAS,KAAK;AACZ,kBAAQ,MAAM,8BAA8B,GAAG;AAAA,QACjD;AAAA,MACF;AAAA,IACF;AACA,cAAU;AAAA,MACR,SAAS,OAAO;AAAA,MAChB,UAAU,OAAO;AAAA,MACjB,cAAc,OAAO;AAAA,MACrB,sBAAsB;AAAA,MACtB,QAAQ;AAAA,MACR,mBAAmB;AAAA,IACrB;AAAA,EACF;AACF;AACA,SAAS,qBAAqB;AAAA,EAC5B;AACF,GAAG;AAtEH;AAuEE,cAAY;AACZ,MAAI,CAAC,SAAS;AACZ,UAAM,IAAI;AAAA,MACR;AAAA,IACF;AAAA,EACF;AACA,MAAI,eAAe;AACnB,MAAI,CAAC,QAAQ,sBAAsB;AACjC,QAAI,SAAS,QAAQ,QAAQ;AAC7B,cAAU,QAAQ,2CAA2C;AAC7D,YAAQ,QAAQ,SAAS;AACzB,YAAQ,uBAAuB,qBAAqB,QAAQ,MAAM,EAAE,KAAK,CAAC,UAAU;AAClF,cAAQ,QAAQ,QAAQ,MAAM;AAC9B,mBAAa,qBAAqB,QAAQ;AAAA,IAC5C,CAAC,EAAE,MAAM,CAAC,MAAM;AACd,mBAAa,qBAAqB,QAAQ;AAAA,IAC5C,CAAC;AAAA,EACH;AACA,MAAI,QAAQ,qBAAqB,OAAO;AACtC,UAAM,QAAQ,qBAAqB;AAAA,EACrC;AACA,MAAI,CAAC,QAAQ,qBAAqB,OAAO;AACvC,UAAM,QAAQ;AAAA,EAChB;AACA,MAAI,SAAS;AAAA,IACX,QAAQ,SAAS;AAAA,IACjB,QAAQ;AAAA,IACR,QAAQ,QAAQ;AAAA,IAChB,QAAQ,QAAQ;AAAA,IAChB,QAAQ,QAAQ;AAAA,EAClB;AACA,MAAI,gBAAgB;AACpB,MAAI,QAAQ,QAAQ,WAAW;AAC7B,QAAI,EAAE,WAAW,IAAI,QAAQ,QAAQ;AACrC,UAAI,aAAQ,SAAS,OAAO,SAAxB,mBAA8B,cAAa,cAAc,UAAU,YAAY;AACjF,sBAAgB;AAAA,QACd,YAAY;AAAA,UACV,MAAM,WAAW;AAAA,QACnB;AAAA,MACF;AAAA,IACF;AAAA,EACF,OAAO;AACL,oBAAgB;AAAA,MACd,QAAQ,QAAQ;AAAA,MAChB;AAAA,MACA,CAAC,YAAS;AApHhB,YAAAA,KAAAC,KAAA;AAoHoB;AAAA,UACZ,eAAcD,MAAA,QAAQ,aAAa,OAAO,MAA5B,gBAAAA,IAA+B;AAAA,UAC7C,aAAWC,MAAA,QAAQ,SAAS,OAAO,OAAO,MAA/B,gBAAAA,IAAkC,eAAc;AAAA,UAC3D,sBAAoB,aAAQ,aAAa,OAAO,MAA5B,mBAA+B,oBAAmB;AAAA,QACxE;AAAA;AAAA,MACA,OAAO;AAAA,OACP,YAAO,yBAAP,mBAA6B;AAAA,MAC7B,QAAQ,QAAQ;AAAA,IAClB;AACA,QAAI,iBAAiB,cAAc,QAAQ;AACzC,oBAAc,SAAS,kBAAkB,cAAc,MAAM;AAAA,IAC/D;AAAA,EACF;AACA,MAAI,UAAU,aAAa;AAAA,IACzB;AAAA,IACA,SAAS,qBAAqB;AAAA,IAC9B,UAAU,QAAQ,QAAQ;AAAA,IAC1B;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,QAAQ;AAAA,MACN,qBAAqB,QAAQ,QAAQ,OAAO;AAAA,IAC9C;AAAA,IACA,cAAc;AAAA,MACZ,MAAM;AAAA,MACN,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,QAAQ,QAAQ;AAAA,MAChB,QAAQ,QAAQ;AAAA,IAClB;AAAA,IACA,yBAAyB;AAAA,MACvB,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,QAAQ,QAAQ;AAAA,MAChB,QAAQ,QAAQ;AAAA,MAChB,QAAQ,QAAQ;AAAA,MAChB,QAAQ,QAAQ;AAAA,IAClB;AAAA,EACF,CAAC;AACD,UAAQ,SAAS;AACjB,MAAI,QAAQ,MAAM,aAAa;AAC7B,YAAQ,oBAAoB;AAC5B,YAAQ,WAAW;AAAA,EACrB;AACA,UAAQ;AAAA,EACR;AACA,SAAO,0BAA0B;AACjC,SAAO;AACT;AACA,SAAS,eAAe,OAAO;AAC7B,MAAI,CAAC,QAAQ;AACX,aAAS,qBAAqB;AAAA,MAC5B,qBAAqB,MAAM;AAAA,IAC7B,CAAC;AAAA,EACH;AACA,MAAI,CAAC,aAAa,cAAc,IAAW;AAAA,IACzC,OAAyC,mCAAS,QAAQ,cAAc;AAAA,EAC1E;AACA,EAAO,iBAAU,MAAM;AACrB,QAAI,MAAwC;AAC1C,qBAAe,MAAM;AAAA,IACvB;AAAA,EACF,GAAG,CAAC,CAAC;AACL,EAAO,iBAAU,MAAM;AACrB,QAA8C,gBAAgB,QAAQ;AACpE,eAAS,iBAAiB,IAAI,2BAA2B,GAAG,EAAE,QAAQ,CAAC,YAAY,QAAQ,OAAO,CAAC;AAAA,IACrG;AAAA,EACF,GAAG,CAAC,WAAW,CAAC;AAChB,MAAI,CAAC,UAAU,WAAW,IAAW,gBAAS,OAAO,MAAM,QAAQ;AACnE,EAAO,uBAAgB,MAAM;AAC3B,QAAI,WAAW,QAAQ,UAAU,CAAC,QAAQ,mBAAmB;AAC3D,cAAQ,oBAAoB;AAC5B,cAAQ,OAAO,WAAW;AAAA,IAC5B;AAAA,EACF,GAAG,CAAC,CAAC;AACL,EAAO,uBAAgB,MAAM;AAC3B,QAAI,WAAW,QAAQ,QAAQ;AAC7B,aAAO,QAAQ,OAAO,UAAU,CAAC,aAAa;AAC5C,YAAI,SAAS,aAAa,UAAU;AAClC,sBAAY,SAAS,QAAQ;AAAA,QAC/B;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF,GAAG,CAAC,QAAQ,CAAC;AACb,YAAU,SAAS,wCAAwC;AAC3D;AAAA,IACE;AAAA,IACA,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,QAAQ,QAAQ;AAAA,IAChB,QAAQ,QAAQ;AAAA,IAChB,QAAQ,QAAQ;AAAA,EAClB;AACA;AAAA;AAAA;AAAA,IAGyB,qBAAqB,iBAAU,MAA6B;AAAA,MACjF,iBAAiB;AAAA,MACjB;AAAA,QACE,OAAO;AAAA,UACL,UAAU,QAAQ;AAAA,UAClB,cAAc,QAAQ;AAAA,UACtB,QAAQ,QAAQ,QAAQ;AAAA,UACxB;AAAA,UACA,KAAK,QAAQ,QAAQ;AAAA,UACrB,WAAW,QAAQ,QAAQ;AAAA,UAC3B,gBAAgB,QAAQ,QAAQ;AAAA,QAClC;AAAA,MACF;AAAA,MACuB,qBAAc,oBAAoB,EAAE,SAAS,GAA0B,qBAAc,iBAAiB,EAAE,OAAO,CAAC,CAAC;AAAA,IAC1I,GAA0B,qBAAqB,iBAAU,IAAI,CAAC;AAAA;AAElE;", "names": ["_a", "_b"]}