import {
  invoke
} from "./chunk-Y3KOJBAE.js";
import "./chunk-DC5AMYBS.js";

// ../node_modules/.pnpm/@tauri-apps+plugin-dialog@2.3.1/node_modules/@tauri-apps/plugin-dialog/dist-js/index.js
async function open(options = {}) {
  if (typeof options === "object") {
    Object.freeze(options);
  }
  return await invoke("plugin:dialog|open", { options });
}
async function save(options = {}) {
  if (typeof options === "object") {
    Object.freeze(options);
  }
  return await invoke("plugin:dialog|save", { options });
}
async function message(message2, options) {
  var _a, _b;
  const opts = typeof options === "string" ? { title: options } : options;
  await invoke("plugin:dialog|message", {
    message: message2.toString(),
    title: (_a = opts == null ? void 0 : opts.title) == null ? void 0 : _a.toString(),
    kind: opts == null ? void 0 : opts.kind,
    okButtonLabel: (_b = opts == null ? void 0 : opts.okLabel) == null ? void 0 : _b.toString()
  });
}
async function ask(message2, options) {
  var _a, _b, _c;
  const opts = typeof options === "string" ? { title: options } : options;
  return await invoke("plugin:dialog|ask", {
    message: message2.toString(),
    title: (_a = opts == null ? void 0 : opts.title) == null ? void 0 : _a.toString(),
    kind: opts == null ? void 0 : opts.kind,
    yesButtonLabel: (_b = opts == null ? void 0 : opts.okLabel) == null ? void 0 : _b.toString(),
    noButtonLabel: (_c = opts == null ? void 0 : opts.cancelLabel) == null ? void 0 : _c.toString()
  });
}
async function confirm(message2, options) {
  var _a, _b, _c;
  const opts = typeof options === "string" ? { title: options } : options;
  return await invoke("plugin:dialog|confirm", {
    message: message2.toString(),
    title: (_a = opts == null ? void 0 : opts.title) == null ? void 0 : _a.toString(),
    kind: opts == null ? void 0 : opts.kind,
    okButtonLabel: (_b = opts == null ? void 0 : opts.okLabel) == null ? void 0 : _b.toString(),
    cancelButtonLabel: (_c = opts == null ? void 0 : opts.cancelLabel) == null ? void 0 : _c.toString()
  });
}
export {
  ask,
  confirm,
  message,
  open,
  save
};
//# sourceMappingURL=@tauri-apps_plugin-dialog.js.map
