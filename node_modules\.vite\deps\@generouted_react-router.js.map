{"version": 3, "sources": ["../../../../node_modules/.pnpm/@generouted+react-router@1._1974585da0cb1a02784f2afc823e2a72/node_modules/@generouted/react-router/dist/chunk-H43QLJSW.js", "../../../../node_modules/.pnpm/@generouted+react-router@1._1974585da0cb1a02784f2afc823e2a72/node_modules/@generouted/react-router/dist/chunk-DZAGGWNQ.js", "../../../../node_modules/.pnpm/@generouted+react-router@1._1974585da0cb1a02784f2afc823e2a72/node_modules/@generouted/react-router/dist/index.js"], "sourcesContent": ["// ../generouted/src/core.ts\nvar patterns = {\n  route: [/^.*\\/src\\/pages\\/|\\.(jsx|tsx|mdx)$/g, \"\"],\n  splat: [/\\[\\.{3}\\w+\\]/g, \"*\"],\n  param: [/\\[([^\\]]+)\\]/g, \":$1\"],\n  slash: [/^index$|\\./g, \"/\"],\n  optional: [/^-(:?[\\w-]+|\\*)/, \"$1?\"]\n};\nvar generatePreservedRoutes = (files) => {\n  return Object.keys(files).reduce((routes, key) => {\n    const path = key.replace(...patterns.route);\n    return { ...routes, [path]: files[key] };\n  }, {});\n};\nvar generateRegularRoutes = (files, buildRoute) => {\n  const filteredRoutes = Object.keys(files).filter((key) => !key.includes(\"/_\") || /_layout\\.(jsx|tsx)$/.test(key));\n  return filteredRoutes.reduce((routes, key) => {\n    const module = files[key];\n    const route = { id: key.replace(...patterns.route), ...buildRoute(module, key) };\n    const segments = key.replace(...patterns.route).replace(...patterns.splat).replace(...patterns.param).split(\"/\").filter(Boolean);\n    segments.reduce((parent, segment, index) => {\n      const path = segment.replace(...patterns.slash).replace(...patterns.optional);\n      const root = index === 0;\n      const leaf = index === segments.length - 1 && segments.length > 1;\n      const node = !root && !leaf;\n      const layout = segment === \"_layout\";\n      const group = /\\([\\w-]+\\)/.test(path);\n      const insert = /^\\w|\\//.test(path) ? \"unshift\" : \"push\";\n      if (root) {\n        const last = segments.length === 1;\n        if (last) {\n          routes.push({ path, ...route });\n          return parent;\n        }\n      }\n      if (root || node) {\n        const current = root ? routes : parent.children;\n        const found = current?.find((r) => r.path === path || r.id?.replace(\"/_layout\", \"\").split(\"/\").pop() === path);\n        const props = group ? route?.component ? { id: path, path: \"/\" } : { id: path } : { path };\n        if (found) found.children ??= [];\n        else current?.[insert]({ ...props, children: [] });\n        return found || current?.[insert === \"unshift\" ? 0 : current.length - 1];\n      }\n      if (layout) {\n        return Object.assign(parent, route);\n      }\n      if (leaf) {\n        parent?.children?.[insert](route?.index ? route : { path, ...route });\n      }\n      return parent;\n    }, {});\n    return routes;\n  }, []);\n};\nvar generateModalRoutes = (files) => {\n  return Object.keys(files).reduce((modals, key) => {\n    const path = key.replace(...patterns.route).replace(/\\+|\\([\\w-]+\\)\\//g, \"\").replace(/(\\/)?index/g, \"\").replace(/\\./g, \"/\");\n    return { ...modals, [`/${path}`]: files[key]?.default };\n  }, {});\n};\n\nexport {\n  patterns,\n  generatePreservedRoutes,\n  generateRegularRoutes,\n  generateModalRoutes\n};\n", "// src/react.js\nimport React from \"react\";\n", "import {\n  generateModalRoutes,\n  generatePreservedRoutes,\n  generateRegularRoutes\n} from \"./chunk-H43QLJSW.js\";\nimport \"./chunk-DZAGGWNQ.js\";\n\n// ../generouted/src/react-router.tsx\nimport { Fragment, Suspense } from \"react\";\nimport { createBrowserRouter, Outlet, RouterProvider, useLocation } from \"react-router\";\nimport { Fragment as Fragment2, jsx, jsxs } from \"react/jsx-runtime\";\nvar PRESERVED = import.meta.glob(\"/src/pages/(_app|404).{jsx,tsx}\", { eager: true });\nvar MODALS = import.meta.glob(\"/src/pages/**/[+]*.{jsx,tsx}\", { eager: true });\nvar ROUTES = import.meta.glob(\n  [\"/src/pages/**/[\\\\w[-]*.{jsx,tsx,mdx}\", \"!/src/pages/**/(_!(layout)*(/*)?|_app|404)*\"],\n  { eager: true }\n);\nvar preservedRoutes = generatePreservedRoutes(PRESERVED);\nvar modalRoutes = generateModalRoutes(MODALS);\nvar regularRoutes = generateRegularRoutes(ROUTES, (module, key) => {\n  const index = /index\\.(jsx|tsx|mdx)$/.test(key) && !key.includes(\"pages/index\") ? { index: true } : {};\n  const Default2 = module?.default || Fragment;\n  const Page = () => module?.Pending ? /* @__PURE__ */ jsx(Suspense, { fallback: /* @__PURE__ */ jsx(module.Pending, {}), children: /* @__PURE__ */ jsx(Default2, {}) }) : /* @__PURE__ */ jsx(Default2, {});\n  return { ...index, Component: Page, ErrorBoundary: module?.Catch, loader: module?.Loader, action: module?.Action };\n});\nvar _app = preservedRoutes?.[\"_app\"];\nvar _404 = preservedRoutes?.[\"404\"];\nvar Default = _app?.default || Outlet;\nvar Modals_ = () => {\n  const Modal = modalRoutes[useLocation().state?.modal] || Fragment;\n  return /* @__PURE__ */ jsx(Modal, {});\n};\nvar Layout = () => /* @__PURE__ */ jsxs(Fragment2, { children: [\n  /* @__PURE__ */ jsx(Default, {}),\n  \" \",\n  /* @__PURE__ */ jsx(Modals_, {})\n] });\nvar App = () => _app?.Pending ? /* @__PURE__ */ jsx(Suspense, { fallback: /* @__PURE__ */ jsx(_app.Pending, {}), children: /* @__PURE__ */ jsx(Layout, {}) }) : /* @__PURE__ */ jsx(Layout, {});\nvar app = { Component: _app?.default ? App : Layout, ErrorBoundary: _app?.Catch, loader: _app?.Loader };\nvar fallback = { path: \"*\", Component: _404?.default || Fragment };\nvar routes = [{ ...app, children: [...regularRoutes, fallback] }];\nvar router;\nvar createRouter = () => (router ??= createBrowserRouter(routes), router);\nvar Routes = () => /* @__PURE__ */ jsx(RouterProvider, { router: createRouter() });\nvar Modals = () => (console.warn(\"[generouted] `<Modals />` will be removed in future releases\"), null);\nexport {\n  Modals,\n  Routes,\n  routes\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;AACA,IAAI,WAAW;AAAA,EACb,OAAO,CAAC,uCAAuC,EAAE;AAAA,EACjD,OAAO,CAAC,iBAAiB,GAAG;AAAA,EAC5B,OAAO,CAAC,iBAAiB,KAAK;AAAA,EAC9B,OAAO,CAAC,eAAe,GAAG;AAAA,EAC1B,UAAU,CAAC,mBAAmB,KAAK;AACrC;AACA,IAAI,0BAA0B,CAAC,UAAU;AACvC,SAAO,OAAO,KAAK,KAAK,EAAE,OAAO,CAACA,SAAQ,QAAQ;AAChD,UAAM,OAAO,IAAI,QAAQ,GAAG,SAAS,KAAK;AAC1C,WAAO,EAAE,GAAGA,SAAQ,CAAC,IAAI,GAAG,MAAM,GAAG,EAAE;AAAA,EACzC,GAAG,CAAC,CAAC;AACP;AACA,IAAI,wBAAwB,CAAC,OAAO,eAAe;AACjD,QAAM,iBAAiB,OAAO,KAAK,KAAK,EAAE,OAAO,CAAC,QAAQ,CAAC,IAAI,SAAS,IAAI,KAAK,sBAAsB,KAAK,GAAG,CAAC;AAChH,SAAO,eAAe,OAAO,CAACA,SAAQ,QAAQ;AAC5C,UAAM,SAAS,MAAM,GAAG;AACxB,UAAM,QAAQ,EAAE,IAAI,IAAI,QAAQ,GAAG,SAAS,KAAK,GAAG,GAAG,WAAW,QAAQ,GAAG,EAAE;AAC/E,UAAM,WAAW,IAAI,QAAQ,GAAG,SAAS,KAAK,EAAE,QAAQ,GAAG,SAAS,KAAK,EAAE,QAAQ,GAAG,SAAS,KAAK,EAAE,MAAM,GAAG,EAAE,OAAO,OAAO;AAC/H,aAAS,OAAO,CAAC,QAAQ,SAAS,UAAU;AApBhD;AAqBM,YAAM,OAAO,QAAQ,QAAQ,GAAG,SAAS,KAAK,EAAE,QAAQ,GAAG,SAAS,QAAQ;AAC5E,YAAM,OAAO,UAAU;AACvB,YAAM,OAAO,UAAU,SAAS,SAAS,KAAK,SAAS,SAAS;AAChE,YAAM,OAAO,CAAC,QAAQ,CAAC;AACvB,YAAM,SAAS,YAAY;AAC3B,YAAM,QAAQ,aAAa,KAAK,IAAI;AACpC,YAAM,SAAS,SAAS,KAAK,IAAI,IAAI,YAAY;AACjD,UAAI,MAAM;AACR,cAAM,OAAO,SAAS,WAAW;AACjC,YAAI,MAAM;AACR,UAAAA,QAAO,KAAK,EAAE,MAAM,GAAG,MAAM,CAAC;AAC9B,iBAAO;AAAA,QACT;AAAA,MACF;AACA,UAAI,QAAQ,MAAM;AAChB,cAAM,UAAU,OAAOA,UAAS,OAAO;AACvC,cAAM,QAAQ,mCAAS,KAAK,CAAC,MAAG;AArCxC,cAAAC;AAqC2C,mBAAE,SAAS,UAAQA,MAAA,EAAE,OAAF,gBAAAA,IAAM,QAAQ,YAAY,IAAI,MAAM,KAAK,WAAU;AAAA;AACzG,cAAM,QAAQ,SAAQ,+BAAO,aAAY,EAAE,IAAI,MAAM,MAAM,IAAI,IAAI,EAAE,IAAI,KAAK,IAAI,EAAE,KAAK;AACzF,YAAI,MAAO,OAAM,aAAN,MAAM,WAAa,CAAC;AAAA,YAC1B,oCAAU,QAAQ,EAAE,GAAG,OAAO,UAAU,CAAC,EAAE;AAChD,eAAO,UAAS,mCAAU,WAAW,YAAY,IAAI,QAAQ,SAAS;AAAA,MACxE;AACA,UAAI,QAAQ;AACV,eAAO,OAAO,OAAO,QAAQ,KAAK;AAAA,MACpC;AACA,UAAI,MAAM;AACR,+CAAQ,aAAR,mBAAmB,SAAQ,+BAAO,SAAQ,QAAQ,EAAE,MAAM,GAAG,MAAM;AAAA,MACrE;AACA,aAAO;AAAA,IACT,GAAG,CAAC,CAAC;AACL,WAAOD;AAAA,EACT,GAAG,CAAC,CAAC;AACP;AACA,IAAI,sBAAsB,CAAC,UAAU;AACnC,SAAO,OAAO,KAAK,KAAK,EAAE,OAAO,CAAC,QAAQ,QAAQ;AAvDpD;AAwDI,UAAM,OAAO,IAAI,QAAQ,GAAG,SAAS,KAAK,EAAE,QAAQ,oBAAoB,EAAE,EAAE,QAAQ,eAAe,EAAE,EAAE,QAAQ,OAAO,GAAG;AACzH,WAAO,EAAE,GAAG,QAAQ,CAAC,IAAI,IAAI,EAAE,IAAG,WAAM,GAAG,MAAT,mBAAY,QAAQ;AAAA,EACxD,GAAG,CAAC,CAAC;AACP;;;AC1DA,mBAAkB;;;ACOlB,IAAAE,gBAAmC;AAEnC,yBAAiD;AACjD,IAAI,YAAY,YAAY,KAAK,mCAAmC,EAAE,OAAO,KAAK,CAAC;AACnF,IAAI,SAAS,YAAY,KAAK,gCAAgC,EAAE,OAAO,KAAK,CAAC;AAC7E,IAAI,SAAS,YAAY;AAAA,EACvB,CAAC,wCAAwC,6CAA6C;AAAA,EACtF,EAAE,OAAO,KAAK;AAChB;AACA,IAAI,kBAAkB,wBAAwB,SAAS;AACvD,IAAI,cAAc,oBAAoB,MAAM;AAC5C,IAAI,gBAAgB,sBAAsB,QAAQ,CAAC,QAAQ,QAAQ;AACjE,QAAM,QAAQ,wBAAwB,KAAK,GAAG,KAAK,CAAC,IAAI,SAAS,aAAa,IAAI,EAAE,OAAO,KAAK,IAAI,CAAC;AACrG,QAAM,YAAW,iCAAQ,YAAW;AACpC,QAAM,OAAO,OAAM,iCAAQ,eAA0B,wBAAI,wBAAU,EAAE,cAA0B,wBAAI,OAAO,SAAS,CAAC,CAAC,GAAG,cAA0B,wBAAI,UAAU,CAAC,CAAC,EAAE,CAAC,QAAoB,wBAAI,UAAU,CAAC,CAAC;AACzM,SAAO,EAAE,GAAG,OAAO,WAAW,MAAM,eAAe,iCAAQ,OAAO,QAAQ,iCAAQ,QAAQ,QAAQ,iCAAQ,OAAO;AACnH,CAAC;AACD,IAAI,OAAO,mDAAkB;AAC7B,IAAI,OAAO,mDAAkB;AAC7B,IAAI,WAAU,6BAAM,YAAW;AAC/B,IAAI,UAAU,MAAM;AA5BpB;AA6BE,QAAM,QAAQ,aAAY,iBAAY,EAAE,UAAd,mBAAqB,KAAK,KAAK;AACzD,aAAuB,wBAAI,OAAO,CAAC,CAAC;AACtC;AACA,IAAI,SAAS,UAAsB,yBAAK,mBAAAC,UAAW,EAAE,UAAU;AAAA,MAC7C,wBAAI,SAAS,CAAC,CAAC;AAAA,EAC/B;AAAA,MACgB,wBAAI,SAAS,CAAC,CAAC;AACjC,EAAE,CAAC;AACH,IAAI,MAAM,OAAM,6BAAM,eAA0B,wBAAI,wBAAU,EAAE,cAA0B,wBAAI,KAAK,SAAS,CAAC,CAAC,GAAG,cAA0B,wBAAI,QAAQ,CAAC,CAAC,EAAE,CAAC,QAAoB,wBAAI,QAAQ,CAAC,CAAC;AAC9L,IAAI,MAAM,EAAE,YAAW,6BAAM,WAAU,MAAM,QAAQ,eAAe,6BAAM,OAAO,QAAQ,6BAAM,OAAO;AACtG,IAAI,WAAW,EAAE,MAAM,KAAK,YAAW,6BAAM,YAAW,uBAAS;AACjE,IAAI,SAAS,CAAC,EAAE,GAAG,KAAK,UAAU,CAAC,GAAG,eAAe,QAAQ,EAAE,CAAC;AAChE,IAAI;AACJ,IAAI,eAAe,OAAO,oBAAW,oBAAoB,MAAM,IAAG;AAClE,IAAI,SAAS,UAAsB,wBAAI,gBAAgB,EAAE,QAAQ,aAAa,EAAE,CAAC;AACjF,IAAI,SAAS,OAAO,QAAQ,KAAK,8DAA8D,GAAG;", "names": ["routes", "_a", "import_react", "Fragment2"]}