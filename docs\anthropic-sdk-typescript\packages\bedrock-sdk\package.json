{"name": "@anthropic-ai/bedrock-sdk", "version": "0.23.0", "description": "The official TypeScript library for the Anthropic Bedrock API", "author": "Anthropic <<EMAIL>>", "types": "dist/index.d.ts", "main": "dist/index.js", "type": "commonjs", "repository": "github:anthropics/anthropic-sdk-typescript", "license": "MIT", "packageManager": "yarn@1.22.21", "private": false, "scripts": {"test": "jest", "build": "bash ./build", "prepack": "echo 'to pack, run yarn build && (cd dist; yarn pack)' && exit 1", "prepublishOnly": "echo 'to publish, run yarn build && (cd dist; yarn publish)' && exit 1", "format": "prettier --write --cache --cache-strategy metadata . !dist", "prepare": "if [ $(basename $(dirname $PWD)) = 'node_modules' ]; then npm run build; fi", "tsn": "ts-node -r tsconfig-paths/register", "lint": "eslint --ext ts,js .", "fix": "eslint --fix --ext ts,js ."}, "dependencies": {"@anthropic-ai/sdk": "file:../../dist/", "@aws-crypto/sha256-js": "^4.0.0", "@aws-sdk/client-bedrock-runtime": "^3.797.0", "@aws-sdk/credential-providers": "^3.796.0", "@smithy/eventstream-serde-node": "^2.0.10", "@smithy/fetch-http-handler": "^5.0.4", "@smithy/protocol-http": "^3.0.6", "@smithy/signature-v4": "^3.1.1", "@smithy/smithy-client": "^2.1.9", "@smithy/types": "^2.3.4", "@smithy/util-base64": "^2.0.0"}, "devDependencies": {"@swc/core": "^1.3.101", "@swc/jest": "^0.2.29", "@types/node": "^20.17.6", "@types/jest": "^29.4.0", "@typescript-eslint/eslint-plugin": "^6.7.0", "@typescript-eslint/parser": "^6.7.0", "eslint": "^8.49.0", "eslint-plugin-prettier": "^5.0.1", "eslint-plugin-unused-imports": "^3.0.0", "jest": "^29.4.0", "prettier": "^3.0.0", "ts-jest": "^29.1.0", "ts-morph": "^19.0.0", "ts-node": "^10.5.0", "tsc-multi": "https://github.com/stainless-api/tsc-multi/releases/download/v1.1.3/tsc-multi.tgz", "tsconfig-paths": "^4.0.0", "typescript": "^4.8.2"}, "imports": {"@anthropic-ai/bedrock-sdk": ".", "@anthropic-ai/bedrock-sdk/*": "./src/*"}, "exports": {".": {"require": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "types": "./dist/index.d.mts", "default": "./dist/index.mjs"}, "./*.mjs": {"types": "./dist/*.d.ts", "default": "./dist/*.mjs"}, "./*.js": {"types": "./dist/*.d.ts", "default": "./dist/*.js"}, "./*": {"types": "./dist/*.d.ts", "require": "./dist/*.js", "default": "./dist/*.mjs"}}}